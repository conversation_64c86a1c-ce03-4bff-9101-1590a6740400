name: FLEEX
description: FLEEX

publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 0.0.5+2

environment:
  sdk: ">=3.6.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  english_words: ^4.0.0
  provider: ^6.1.2
  http: ^1.1.0
  intl: 0.19.0
  url_launcher: ^6.1.10
  flutter_secure_storage: ^8.1.0
  shared_preferences: ^2.2.2
  dropdown_search: ^5.0.6
  flutter_map: ^6.1.0
  latlong2: ^0.9.0
  image_picker: ^1.1.2
  geolocator: ^11.0.0  
  permission_handler: ^11.3.0  
  flutter_background_service: ^5.0.5
  flutter_background_service_android: ^6.3.0
  flutter_background_service_ios: ^5.0.0  
  device_info_plus: ^9.1.2
  excel: ^4.0.6
  file_picker: ^8.1.4
  path_provider: ^2.1.5
  flutter_gen: ^5.10.0
  # Firebase dependencies for push notifications
  firebase_core: ^3.10.0
  firebase_messaging: ^15.1.5
  flutter_local_notifications: ^18.0.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.13.1

flutter:
  uses-material-design: true
  generate: true
  assets:
    - lib/assets/login-image.jpg
