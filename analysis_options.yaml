include: package:flutter_lints/flutter.yaml

linter:
  rules:
    # Enable these rules for better code quality
    unnecessary_breaks: true
    prefer_single_quotes: true
    sort_child_properties_last: true
    always_declare_return_types: true
    avoid_empty_else: true
    avoid_relative_lib_imports: true
    avoid_unused_constructor_parameters: true
    directives_ordering: true
    prefer_final_locals: true
    prefer_is_empty: true
    prefer_is_not_empty: true
    prefer_spread_collections: true

    # Disabled rules (consider enabling these in the future)
    avoid_print: false  # Keep for now, but consider enabling later
    prefer_const_constructors_in_immutables: false
    prefer_const_constructors: false
    prefer_const_literals_to_create_immutables: false
    prefer_final_fields: false
    use_key_in_widget_constructors: false