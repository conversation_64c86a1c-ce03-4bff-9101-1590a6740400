# FLEEX Frontend

This directory contains the Flutter frontend for the FLEEX fleet management system. The frontend provides a user-friendly interface for interacting with the FLEEX backend API.

## Recent Refactoring

The codebase has recently undergone significant refactoring to improve code quality, maintainability, and performance. Key improvements include:

1. **Centralized Configuration**: All configuration values are now managed in `lib/config/app_config.dart`
2. **Structured Logging**: A new logging system in `lib/utils/logger.dart` provides consistent logging across the application
3. **Enhanced HTTP Client**: The HTTP client now includes better error handling, retry logic, and timeout handling
4. **Improved API Service**: The API service now provides typed responses and consistent error handling
5. **Refactored Authentication**: The authentication service has been improved with better token handling and error management

For more details on the refactoring changes, see [REFACTORING_CHANGES.md](REFACTORING_CHANGES.md).

## Setup

### Prerequisites

- Flutter SDK (latest stable version)
- Dart SDK (included with Flutter)
- Android Studio or Visual Studio Code with Flutter extensions (for development)
- Chrome browser (for web development)
- Backend API server running (see the Express backend README)

### Detailed Installation

#### Installing Flutter and Dart

1. Download the Flutter SDK from [flutter.dev](https://flutter.dev/docs/get-started/install)
2. Extract the downloaded zip file to a location on your computer (e.g., `C:\dev\flutter` on Windows)
3. Add the Flutter `bin` directory to your PATH environment variable
4. Verify the installation by running:
   ```bash
   flutter --version
   ```

5. Run Flutter doctor to check for any missing dependencies:
   ```bash
   flutter doctor
   ```

6. Install any missing dependencies identified by Flutter doctor

#### Setting up an IDE

**Option 1: Visual Studio Code**
1. Install [Visual Studio Code](https://code.visualstudio.com/)
2. Install the Flutter extension from the VS Code marketplace
3. Reload VS Code after installing the extension

**Option 2: Android Studio**
1. Install [Android Studio](https://developer.android.com/studio)
2. Install the Flutter plugin from the marketplace
3. Restart Android Studio

#### Installing Project Dependencies

1. Navigate to the Flutter project directory:
   ```bash
   cd fleex
   ```

2. Install the project dependencies:
   ```bash
   flutter pub get
   ```
   This will download and install all packages specified in `pubspec.yaml`

#### Configuring the Application

1. Ensure the backend API server is running (see the Express backend README)

2. Update the API base URL if needed:
   - Open `lib/config/app_config.dart`
   - Update the `baseUrl` in the `ApiConfig` class to match your backend server (default is `https://fleex.ad-ins.com`)

3. Configure other application settings:
   - Adjust timeout values, logging settings, and other configuration options in `app_config.dart`

#### Running the Application

**For Web Development (Recommended for Getting Started)**

1. Run the application in Chrome:
   ```bash
   flutter run -d chrome
   ```

**For Mobile Development**

1. Connect a physical device or start an emulator

2. Run the application:
   ```bash
   flutter run
   ```

**For Desktop Development**

1. Enable desktop support (if not already enabled):
   ```bash
   flutter config --enable-windows-desktop  # For Windows
   flutter config --enable-macos-desktop    # For macOS
   flutter config --enable-linux-desktop    # For Linux
   ```

2. Run the application on desktop:
   ```bash
   flutter run -d windows  # For Windows
   flutter run -d macos    # For macOS
   flutter run -d linux    # For Linux
   ```

#### Troubleshooting Installation

- **Flutter Not Found**: Ensure Flutter is properly added to your PATH
- **Missing Dependencies**: Run `flutter doctor` and follow the instructions to install missing components
- **Build Failures**: Check that you have the latest Flutter SDK with `flutter upgrade`
- **Connection Issues**: Verify that the backend server is running and accessible

## Project Structure

```
fleex/
├── lib/                      # Main source code
│   ├── assets/               # Static assets (images, etc.)
│   ├── models/               # Data models
│   ├── screens/              # UI screens
│   ├── services/             # API services
│   ├── utils/                # Utility functions
│   └── main.dart             # Main Flutter app
├── android/                  # Android-specific code
├── ios/                      # iOS-specific code
├── web/                      # Web-specific code
├── windows/                  # Windows-specific code
├── macos/                    # macOS-specific code
├── linux/                    # Linux-specific code
└── pubspec.yaml              # Flutter dependencies
```

## Features

### Authentication

- Login with Microsoft Entra ID (Azure AD)
- Session management
- Role-based access control

### User Interface

The application provides different interfaces based on the user's role:

#### Trip Manager Dashboard

- Overview of all trips and their statuses
- Access to all settings and management screens
- Trip approval and management

#### Trip Requestor Interface

- Request new trips
- View trip status
- Manage personal trips

#### Driver Interface

- View assigned trips
- Update trip status
- Manage vehicle information

### Screens

- **Login Screen**: Authentication with Microsoft Entra ID
- **Admin Dashboard**: Overview for administrators
- **Users Screen**: User management
- **Driver Settings**: Driver management
- **Car Settings**: Vehicle management
- **Cargo Settings**: Cargo management
- **Destination Settings**: Destination management
- **Request Trip**: Trip request form
- **Trip Approval**: Trip review and approval

## API Integration

The frontend communicates with the backend API using HTTP requests. API services are located in the `services` directory.

### Authentication Flow

1. User clicks login button
2. User is redirected to Microsoft login page
3. After successful authentication, user is redirected back to the application
4. The application stores authentication tokens securely
5. Subsequent API requests include the authentication token

## UI Features

- **Round Trip Support**: Users can request round trips with a single form
- **Waiting Time Support**: Users can request trips with waiting time
- **Custom Locations**: Users can specify custom locations for trips
- **Connected Trips**: Support for trips that are connected (e.g., return journeys)
- **Responsive Design**: Works on mobile, tablet, and desktop

## Development

### Adding New Screens

1. Create a new Dart file in the `screens` directory
2. Implement the screen using Flutter widgets
3. Add the screen to the route generator in `main.dart`

### Adding New Models

1. Create a new Dart file in the `models` directory
2. Implement the model class with JSON serialization support
3. Use the model in the appropriate screens and services

### Adding New API Services

1. Create a new Dart file in the `services` directory
2. Implement the service class with HTTP requests to the backend API
3. Use the service in the appropriate screens

## Building for Production

To build the application for production:

```bash
# For Android
flutter build apk --release

# For iOS
flutter build ios --release

# For Web
flutter build web --release

# For Windows
flutter build windows --release

# For macOS
flutter build macos --release

# For Linux
flutter build linux --release
```

## Troubleshooting

### Common Issues

- **Authentication Issues**: Make sure the Azure AD configuration is correct in both the frontend and backend
- **API Connection Issues**: Check that the backend API is running and accessible
- **Build Errors**: Make sure all dependencies are up to date with `flutter pub get`

### Debugging

To run the application in debug mode:

```bash
flutter run --debug
```

This will enable hot reload and provide detailed error messages.

## Additional Resources

- [Flutter Documentation](https://docs.flutter.dev/)
- [Dart Documentation](https://dart.dev/guides)
- [Microsoft Entra ID Documentation](https://learn.microsoft.com/en-us/entra/identity/)

## Code Quality Guidelines

### Linting Rules

The project uses Flutter's linting rules with some customizations defined in `analysis_options.yaml`. Key rules include:

- Use single quotes for strings
- Sort child properties last
- Always declare return types
- Prefer final locals where appropriate
- Use proper collection methods (isEmpty, isNotEmpty)

### Debugging

- Use the `Logger` class for all logging:
  ```dart
  final logger = Logger('MyComponent');
  logger.debug('Detailed information');
  logger.info('General information');
  logger.warning('Warning message');
  logger.error('Error message', exception, stackTrace);
  ```
- Control logging verbosity through `AppConfig.logging` settings
- Use structured error handling with try/catch blocks
- Use the `ApiResult` class for handling API responses

### Code Organization

- Keep files focused on a single responsibility
- Use consistent naming conventions
- Group related functionality in the same directory
- Follow the Flutter style guide for widget structure
