package com.example.fleex

import android.app.ActivityManager
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.core.app.NotificationCompat
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import io.flutter.plugin.common.MethodChannel

class FirebaseMessagingService : FirebaseMessagingService() {    companion object {
        private const val CHANNEL_ID = "FLEEX_NOTIFICATIONS"
        private const val CHANNEL_NAME = "FLEEX Notifications"
        private const val CHANNEL_DESCRIPTION = "Notifications for FLEEX app"
    }    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        super.onMessageReceived(remoteMessage)

        println("Firebase message received - Data: ${remoteMessage.data}")

        // Check if app is in foreground
        val isAppInForeground = isAppInForeground()
        println("App is in foreground: $isAppInForeground")

        if (remoteMessage.data.isNotEmpty()) {
            println("Data message received: ${remoteMessage.data}")
            
            // Only show notification if app is not in foreground
            // When app is in foreground, Flutter handles notifications
            // When app is in background/closed, we handle it here
            if (!isAppInForeground) {
                println("App in background/closed - showing native notification")
                handleDataMessage(remoteMessage.data)
            } else {
                println("App in foreground - letting Flutter handle notification")
            }
        }

        // Handle traditional notification messages (fallback)
        remoteMessage.notification?.let {
            println("Notification message received: ${it.body}")
            if (!isAppInForeground) {
                showNotification(it.title, it.body, remoteMessage.data)
            }
        }
    }

    override fun onNewToken(token: String) {
        super.onNewToken(token)
        println("Refreshed token: $token")
        
        // Send token to app server if needed
        sendTokenToServer(token)
    }

    private fun handleDataMessage(data: Map<String, String>) {
        // Handle data message
        val title = data["title"]
        val body = data["body"]
        val notificationType = data["type"]
        val userId = data["user_id"]
        
        // You can process the data and show custom notifications
        if (title != null && body != null) {
            showNotification(title, body, data)
        }
    }

    private fun showNotification(title: String?, body: String?, data: Map<String, String>) {
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        
        // Create notification channel for Android O and above
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = CHANNEL_DESCRIPTION
            }
            notificationManager.createNotificationChannel(channel)
        }

        // Create an intent to open the app when notification is tapped
        val intent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            // Add extra data if needed
            data.forEach { (key, value) ->
                putExtra(key, value)
            }
        }

        val pendingIntent: PendingIntent = PendingIntent.getActivity(
            this, 
            0, 
            intent, 
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )        // Build the notification
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_fleex_notification) // Updated to use FLEEX logo
            .setContentTitle(title ?: "FLEEX")
            .setContentText(body ?: "New notification")
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setAutoCancel(true)
            .setContentIntent(pendingIntent)
            .build()

        // Show the notification
        notificationManager.notify(System.currentTimeMillis().toInt(), notification)
    }

    private fun sendTokenToServer(token: String) {
        // TODO: Send token to your server
        // This could be done via HTTP request to your backend
        println("FCM Token: $token")
    }

    private fun isAppInForeground(): Boolean {
        val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val runningProcesses = activityManager.runningAppProcesses
        
        for (processInfo in runningProcesses) {
            if (processInfo.processName == packageName) {
                return processInfo.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND
            }
        }
        return false
    }
}
