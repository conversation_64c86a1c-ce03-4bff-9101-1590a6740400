package com.example.fleex

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity: FlutterActivity() {
    private val CHANNEL = "com.example.fleex/webview_bridge"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        // Set up method channel for handling deep links
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            if (call.method == "handleDeepLink") {
                val url = call.argument<String>("url")
                if (url != null) {
                    handleDeepLink(url, result)
                } else {
                    result.error("INVALID_ARGUMENTS", "URL is required", null)
                }
            } else {
                result.notImplemented()
            }
        }
    }

    private fun handleDeepLink(url: String, result: MethodChannel.Result) {
        try {
            println("MainActivity: Processing deep link URL: $url")
            // For SSO callback URLs, we'll handle them specially
            if (url.startsWith("com.example.fleex://auth/callback") ||
                url.startsWith("com.example.fleex:/auth/callback") ||
                url.startsWith("com.example.fleex://callback") ||
                url.startsWith("com.example.fleex:/callback") ||
                (url.contains("/auth/callback") && url.startsWith("com.example.fleex:")) ||
                (url.contains("/callback") && url.startsWith("com.example.fleex:")) ||
                (url.contains("code=") && url.startsWith("com.example.fleex:"))) {
                println("MainActivity: Detected SSO callback URL")
                // Send the URL to Flutter via the method channel
                flutterEngine?.dartExecutor?.binaryMessenger?.let { messenger ->
                    println("MainActivity: Sending SSO callback to Flutter")
                    MethodChannel(messenger, CHANNEL).invokeMethod("fromWeb", mapOf(
                        "event" to "deepLink",
                        "data" to url
                    ))
                    println("MainActivity: SSO callback sent to Flutter")
                }
            }
            result.success(true)
        } catch (e: Exception) {
            println("MainActivity: Error handling deep link: ${e.message}")
            result.error("DEEP_LINK_ERROR", e.message, null)
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        handleIntent(intent)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        handleIntent(intent)
    }

    private fun handleIntent(intent: Intent) {
        // Handle incoming intents (e.g., deep links)
        val action = intent.action
        val data = intent.data
        if (Intent.ACTION_VIEW == action && data != null) {
            val url = data.toString()
            println("MainActivity: Received deep link: $url")
            println("MainActivity: URL components - scheme: ${data.scheme}, host: ${data.host}, path: ${data.path}, query: ${data.query}")

            // Special handling for SSO callback URLs - handle all possible formats
            if (url.startsWith("com.example.fleex://auth/callback") ||
                url.startsWith("com.example.fleex:/auth/callback") ||
                url.startsWith("com.example.fleex://callback") ||
                url.startsWith("com.example.fleex:/callback") ||
                (url.startsWith("com.example.fleex:") && data.path?.startsWith("/auth/callback") == true) ||
                (url.startsWith("com.example.fleex:") && data.path?.startsWith("/callback") == true) ||
                (url.startsWith("com.example.fleex:") && data.query?.contains("code=") == true)) {
                println("MainActivity: Detected SSO callback URL")
                // Send the URL to Flutter via the method channel
                flutterEngine?.dartExecutor?.binaryMessenger?.let { messenger ->
                    println("MainActivity: Sending SSO callback to Flutter")
                    // Use a slight delay to ensure Flutter is ready
                    Handler(Looper.getMainLooper()).postDelayed({
                        MethodChannel(messenger, CHANNEL).invokeMethod("fromWeb", mapOf(
                            "event" to "deepLink",
                            "data" to url
                        ))
                        println("MainActivity: SSO callback sent to Flutter")
                    }, 1000) // 1 second delay
                }
            } else {
                // Send the URL to Flutter for other deep links
                flutterEngine?.dartExecutor?.binaryMessenger?.let { messenger ->
                    println("MainActivity: Sending deep link to Flutter: $url")
                    MethodChannel(messenger, CHANNEL).invokeMethod("fromWeb", mapOf(
                        "event" to "deepLink",
                        "data" to url
                    ))
                    println("MainActivity: Deep link sent to Flutter")
                }
            }
        } else {
            println("MainActivity: Received intent but not a deep link: action=$action, data=$data")
        }
    }
}
