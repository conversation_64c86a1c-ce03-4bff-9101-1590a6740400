package com.example.fleex

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.webkit.WebView
import androidx.annotation.NonNull
// Remove unused imports
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result

/** WebViewBridgePlugin */
class WebViewBridgePlugin: FlutterPlugin, MethodCallHandler {
    private lateinit var channel: MethodChannel
    private lateinit var context: Context
    private var webView: WebView? = null

    override fun onAttachedToEngine(@NonNull flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
        channel = MethodChannel(flutterPluginBinding.binaryMessenger, "com.example.fleex/webview_bridge")
        channel.setMethodCallHandler(this)
        context = flutterPluginBinding.applicationContext
    }

    override fun onMethodCall(@NonNull call: MethodCall, @NonNull result: Result) {
        when (call.method) {
            "initialize" -> {
                initializeWebView()
                result.success(null)
            }
            "sendToWeb" -> {
                val event = call.argument<String>("event")
                val data = call.argument<Any>("data")
                if (event != null) {
                    sendToWeb(event, data)
                    result.success(null)
                } else {
                    result.error("INVALID_ARGUMENTS", "Event name is required", null)
                }
            }
            "handleDeepLink" -> {
                val url = call.argument<String>("url")
                if (url != null) {
                    val handled = handleDeepLink(url)
                    result.success(handled)
                } else {
                    result.error("INVALID_ARGUMENTS", "URL is required", null)
                }
            }
            "isWebView" -> {
                result.success(isRunningInWebView())
            }
            else -> {
                result.notImplemented()
            }
        }
    }

    private fun initializeWebView() {
        // Initialize WebView if needed
        if (webView == null) {
            webView = WebView(context)
            webView?.settings?.javaScriptEnabled = true

            // Enable modern WebView features for Android 12
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // Use standard Android WebView dark mode settings
                webView?.settings?.apply {
                    // Set any other WebView settings as needed
                    javaScriptEnabled = true
                    domStorageEnabled = true
                }
            }
        }
    }

    private fun sendToWeb(event: String, data: Any?) {
        webView?.let {
            val jsonData = data.toString().replace("\"", "\\\"")
            val script = "window.dispatchEvent(new CustomEvent('$event', { detail: JSON.parse(\"$jsonData\") }));"
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                it.evaluateJavascript(script, null)
            }
        }
    }

    private fun handleDeepLink(url: String): Boolean {
        try {
            println("WebViewBridgePlugin: handleDeepLink called with URL: $url")

            // Special handling for SSO callback URLs
            if (url.startsWith("com.example.fleex://auth/callback")) {
                println("WebViewBridgePlugin: Detected SSO callback URL, forwarding to Flutter")
                // Send the URL directly to Flutter via the method channel
                channel.invokeMethod("fromWeb", mapOf(
                    "event" to "deepLink",
                    "data" to url
                ))
                println("WebViewBridgePlugin: SSO callback sent to Flutter")
                return true
            } else {
                // For other URLs, launch an intent
                println("WebViewBridgePlugin: Launching intent for URL: $url")
                val uri = Uri.parse(url)
                val intent = Intent(Intent.ACTION_VIEW, uri)
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(intent)
                return true
            }
        } catch (e: Exception) {
            e.printStackTrace()
            println("WebViewBridgePlugin: Error handling deep link: ${e.message}")
            return false
        }
    }

    private fun isRunningInWebView(): Boolean {
        // Check if the app is running inside a WebView
        return false // This is a placeholder - actual implementation would depend on specific requirements
    }

    override fun onDetachedFromEngine(@NonNull binding: FlutterPlugin.FlutterPluginBinding) {
        channel.setMethodCallHandler(null)
        webView?.destroy()
        webView = null
    }
}
