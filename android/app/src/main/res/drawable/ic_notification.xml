<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24"
    android:tint="@android:color/white">
  <!-- FLEEX logo path - replace this with your actual logo path data -->
  <!-- You can get path data from your SVG logo or create a custom design -->
  <path
      android:fillColor="@android:color/white"
      android:pathData="M8,3C6.9,3 6,3.9 6,5v14c0,1.1 0.9,2 2,2h8c1.1,0 2,-0.9 2,-2V5c0,-1.1 -0.9,-2 -2,-2H8zM12,6c0.55,0 1,0.45 1,1s-0.45,1 -1,1 -1,-0.45 -1,-1 0.45,-1 1,-1zM12,19c-0.55,0 -1,-0.45 -1,-1s0.45,-1 1,-1 1,0.45 1,1 -0.45,1 -1,1zM8,10h8v6H8V10z"/>
</vector>
