<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">FLEEX</string>
    <string name="notification_channel_id">fleex_location_tracking</string>
    <string name="notification_channel_name">Location Tracking</string>
    <string name="notification_channel_description">Used for tracking driver location in the background</string>
    <string name="notification_title">FLEEX Driver Tracking</string>
    <string name="notification_text">Your location is being tracked</string>
    
    <!-- Firebase notification strings -->
    <string name="fcm_notification_channel_id">FLEEX_NOTIFICATIONS</string>
    <string name="fcm_notification_channel_name">FLEEX Notifications</string>
    <string name="fcm_notification_channel_description">Notifications for FLEEX app</string>
</resources>
