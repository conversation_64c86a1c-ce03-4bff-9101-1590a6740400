{"trips": [{"trip_id": 20, "trip_code": "ORD0157", "from_destination": "work", "to_destination": "office", "date": "2025-05-16", "time": "11:30:00", "status": "REQUEST", "driver_id": null, "driver_code": null, "driver_name": "Unassigned", "requestor_id": "3", "requestor_name": "User123", "total_duration": null, "rejection_reason": null, "rating": null, "comments": null, "completion_notes": null, "completion_image_path": null, "passengers": [], "cargos": []}, {"trip_id": 24, "trip_code": "ORD0165", "from_destination": "DESTINATION", "to_destination": "IDFK", "date": "2025-06-03", "time": "13:16:00", "status": "REQUEST", "driver_id": null, "driver_code": null, "driver_name": "Unassigned", "requestor_id": "3", "requestor_name": "User123", "total_duration": null, "rejection_reason": null, "rating": null, "comments": null, "completion_notes": null, "completion_image_path": null, "passengers": [{"id": 6, "name": "<PERSON><PERSON>"}, {"id": 3, "name": "User123"}], "cargos": [{"id": 3, "name": "AK45", "code": "CRG0014"}, {"id": 2, "name": "TEST CARGO", "code": "CRG013"}]}, {"trip_id": 8, "trip_code": "ORD0148", "from_destination": "baba", "to_destination": "zx", "date": "2025-05-14", "time": "14:25:00", "status": "REQUEST", "driver_id": null, "driver_code": null, "driver_name": "Unassigned", "requestor_id": "3", "requestor_name": "User123", "total_duration": null, "rejection_reason": null, "rating": null, "comments": null, "completion_notes": null, "completion_image_path": null, "passengers": [], "cargos": []}]}