import 'dart:io';
import 'package:flutter/foundation.dart' show kIsWeb, defaultTargetPlatform, TargetPlatform;

/// Application configuration
class AppConfig {
  /// Debug mode flag - set to false in production
  static bool debugMode = true;

  /// API configuration
  static final ApiConfig api = ApiConfig();

  /// Authentication configuration
  static final AuthConfig auth = AuthConfig();

  /// Logging configuration
  static final LoggingConfig logging = LoggingConfig();
}

/// API configuration
class ApiConfig {  /// Base URL for API requests
  String get baseUrl {
    return 'https://fleex.ad-ins.com';
    // if (kIsWeb) {
    //   return 'http://localhost:3000';
    // } else {
    //   // For Android emulator, use ******** to reach host machine
    //   // For iOS simulator, use localhost or 127.0.0.1
    //   // For real devices, use your computer's actual IP address on the local network
    //   if (_isAndroidEmulator()) {
    //     return 'http://********:3000';
    //   } else {
    //     // Real device access - use your computer's IP address
    //     return 'http://**********:3000'; // Updated to your current IP
    //   }
    // }
  }

  /// Helper method to detect Android emulator
  bool _isAndroidEmulator() {
    // This is a simple detection - you might want to make this more robust
    return !kIsWeb && 
           defaultTargetPlatform == TargetPlatform.android &&
           (Platform.environment['ANDROID_EMULATOR'] != null ||
            Platform.environment.containsKey('FLUTTER_TEST'));
  }

  /// API base URL (includes /api path)
  String get apiBaseUrl => '$baseUrl/api';

  /// Request timeout in seconds
  int get timeoutSeconds => 30;

  /// Maximum retry attempts for failed requests
  int get maxRetries => 3;
}

/// Authentication configuration
class AuthConfig {
  /// Token storage keys
  String get accessTokenKey => 'access_token';
  String get idTokenKey => 'id_token';
  String get userDataKey => 'user_data';
  String get lastLoginTimeKey => 'last_login_time';

  /// Session timeout in minutes
  /// After this time, the user will be logged out automatically
  int get sessionTimeoutMinutes => 60;

  /// Token refresh threshold in minutes
  /// When the token is this close to expiring, it will be refreshed
  int get tokenRefreshThresholdMinutes => 5;
}

/// Logging configuration
class LoggingConfig {
  /// Whether to enable verbose logging
  bool get enableVerboseLogging => AppConfig.debugMode;

  /// Whether to log HTTP requests and responses
  bool get logHttpRequests => AppConfig.debugMode;

  /// Whether to log authentication events
  bool get logAuthEvents => AppConfig.debugMode;
}


