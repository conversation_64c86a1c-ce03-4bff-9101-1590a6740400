import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_id.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('id')
  ];

  /// The application title
  ///
  /// In en, this message translates to:
  /// **'FLEEX'**
  String get appTitle;

  /// Welcome message on login screen
  ///
  /// In en, this message translates to:
  /// **'Welcome Back'**
  String get welcomeBack;

  /// Login screen subtitle
  ///
  /// In en, this message translates to:
  /// **'Sign in to continue to FLEEX'**
  String get signInToContinue;

  /// Username field label
  ///
  /// In en, this message translates to:
  /// **'Username'**
  String get username;

  /// Password field label
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get password;

  /// Login button text
  ///
  /// In en, this message translates to:
  /// **'Login'**
  String get login;

  /// Username validation message
  ///
  /// In en, this message translates to:
  /// **'Please enter your username'**
  String get pleaseEnterUsername;

  /// Validation message for password field
  ///
  /// In en, this message translates to:
  /// **'Please enter password'**
  String get pleaseEnterPassword;

  /// OR text between login options
  ///
  /// In en, this message translates to:
  /// **'OR'**
  String get or;

  /// Sign in with text
  ///
  /// In en, this message translates to:
  /// **'Sign in with'**
  String get signInWith;

  /// Microsoft sign in button text
  ///
  /// In en, this message translates to:
  /// **'Sign in with Microsoft'**
  String get signInWithMicrosoft;

  /// Forgot password link text
  ///
  /// In en, this message translates to:
  /// **'Forgot Password?'**
  String get forgotPassword;

  /// Reset Password menu item
  ///
  /// In en, this message translates to:
  /// **'Reset Password'**
  String get resetPassword;

  /// Go back button text
  ///
  /// In en, this message translates to:
  /// **'Go Back'**
  String get goBack;

  /// New password field label
  ///
  /// In en, this message translates to:
  /// **'New Password'**
  String get newPassword;

  /// Confirm new password field label
  ///
  /// In en, this message translates to:
  /// **'Confirm New Password'**
  String get confirmNewPassword;

  /// New password validation message
  ///
  /// In en, this message translates to:
  /// **'Please enter new password'**
  String get pleaseEnterNewPassword;

  /// Confirm new password validation message
  ///
  /// In en, this message translates to:
  /// **'Please confirm new password'**
  String get pleaseConfirmNewPassword;

  /// Validation message for password length
  ///
  /// In en, this message translates to:
  /// **'Password must be at least 6 characters'**
  String get passwordMustBeAtLeast6Characters;

  /// Validation message when passwords don't match
  ///
  /// In en, this message translates to:
  /// **'Passwords do not match'**
  String get passwordsDoNotMatch;

  /// Success message after password reset
  ///
  /// In en, this message translates to:
  /// **'Password reset successful.'**
  String get passwordResetSuccessful;

  /// Dashboard menu item
  ///
  /// In en, this message translates to:
  /// **'Dashboard'**
  String get dashboard;

  /// Request trip screen title
  ///
  /// In en, this message translates to:
  /// **'Request Trip'**
  String get requestTrip;

  /// Trip history tooltip and navigation label
  ///
  /// In en, this message translates to:
  /// **'Trip History'**
  String get tripHistory;

  /// Settings menu item
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// Logout menu item
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get logout;

  /// Search field label
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get search;

  /// Date field label
  ///
  /// In en, this message translates to:
  /// **'Date'**
  String get date;

  /// Time field label
  ///
  /// In en, this message translates to:
  /// **'Time'**
  String get time;

  /// From label with colon
  ///
  /// In en, this message translates to:
  /// **'From:'**
  String get from;

  /// To label with colon
  ///
  /// In en, this message translates to:
  /// **'To:'**
  String get to;

  /// Destination label
  ///
  /// In en, this message translates to:
  /// **'Destination'**
  String get destination;

  /// Notes field label
  ///
  /// In en, this message translates to:
  /// **'Notes'**
  String get notes;

  /// Save button text
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// Cancel button text
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// Edit button text
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get edit;

  /// Delete button text
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// Add button text
  ///
  /// In en, this message translates to:
  /// **'Add'**
  String get add;

  /// Required field indicator
  ///
  /// In en, this message translates to:
  /// **'Required'**
  String get required;

  /// Optional field indicator
  ///
  /// In en, this message translates to:
  /// **'Optional'**
  String get optional;

  /// Loading indicator text
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get loading;

  /// Error label
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get error;

  /// Success message prefix
  ///
  /// In en, this message translates to:
  /// **'Success'**
  String get success;

  /// Trip code column header
  ///
  /// In en, this message translates to:
  /// **'Trip Code'**
  String get tripCode;

  /// Status label
  ///
  /// In en, this message translates to:
  /// **'Status'**
  String get status;

  /// Driver role label
  ///
  /// In en, this message translates to:
  /// **'Driver'**
  String get driver;

  /// Car column header
  ///
  /// In en, this message translates to:
  /// **'Car'**
  String get car;

  /// Passenger field label
  ///
  /// In en, this message translates to:
  /// **'Passenger'**
  String get passenger;

  /// Passengers section label
  ///
  /// In en, this message translates to:
  /// **'Passengers'**
  String get passengers;

  /// Duration label
  ///
  /// In en, this message translates to:
  /// **'Duration'**
  String get duration;

  /// Actions column header
  ///
  /// In en, this message translates to:
  /// **'Actions'**
  String get actions;

  /// Filter by date field label
  ///
  /// In en, this message translates to:
  /// **'Filter by Date'**
  String get filterByDate;

  /// Dropdown hint for status filter
  ///
  /// In en, this message translates to:
  /// **'Filter by Status'**
  String get filterByStatus;

  /// All records filter option
  ///
  /// In en, this message translates to:
  /// **'All Records'**
  String get allRecords;

  /// Clear filter button text
  ///
  /// In en, this message translates to:
  /// **'Clear Filter'**
  String get clearFilter;

  /// Refresh button text
  ///
  /// In en, this message translates to:
  /// **'Refresh'**
  String get refresh;

  /// No data found message
  ///
  /// In en, this message translates to:
  /// **'No data found'**
  String get noDataFound;

  /// Date validation message
  ///
  /// In en, this message translates to:
  /// **'Please enter date'**
  String get pleaseEnterDate;

  /// Time validation message
  ///
  /// In en, this message translates to:
  /// **'Please enter time'**
  String get pleaseEnterTime;

  /// From destination validation message
  ///
  /// In en, this message translates to:
  /// **'Please select from destination'**
  String get pleaseSelectFromDestination;

  /// To destination validation message
  ///
  /// In en, this message translates to:
  /// **'Please select to destination'**
  String get pleaseSelectToDestination;

  /// Add trip screen title
  ///
  /// In en, this message translates to:
  /// **'Add Trip'**
  String get addTrip;

  /// Tooltip for edit trip button
  ///
  /// In en, this message translates to:
  /// **'Edit Trip'**
  String get editTrip;

  /// Requestor name field label
  ///
  /// In en, this message translates to:
  /// **'Requestor Name'**
  String get requestorName;

  /// Custom location field label
  ///
  /// In en, this message translates to:
  /// **'Custom Location'**
  String get customLocation;

  /// From destination hint text
  ///
  /// In en, this message translates to:
  /// **'Select origin'**
  String get selectOrigin;

  /// To destination hint text
  ///
  /// In en, this message translates to:
  /// **'Select destination'**
  String get selectDestination;

  /// Car code field label
  ///
  /// In en, this message translates to:
  /// **'Car Code'**
  String get carCode;

  /// Manufacturer label
  ///
  /// In en, this message translates to:
  /// **'Manufacturer'**
  String get manufacturer;

  /// Model label
  ///
  /// In en, this message translates to:
  /// **'Model'**
  String get model;

  /// Plate number label
  ///
  /// In en, this message translates to:
  /// **'Plate Number'**
  String get plateNumber;

  /// Odometer label
  ///
  /// In en, this message translates to:
  /// **'Odometer'**
  String get odometer;

  /// Color label
  ///
  /// In en, this message translates to:
  /// **'Color'**
  String get color;

  /// Type label
  ///
  /// In en, this message translates to:
  /// **'Type'**
  String get type;

  /// Driver code label
  ///
  /// In en, this message translates to:
  /// **'Driver Code'**
  String get driverCode;

  /// Driver name label
  ///
  /// In en, this message translates to:
  /// **'Driver Name'**
  String get driverName;

  /// Initial field label
  ///
  /// In en, this message translates to:
  /// **'Initial'**
  String get initial;

  /// Address label
  ///
  /// In en, this message translates to:
  /// **'Address'**
  String get address;

  /// Coordinates label
  ///
  /// In en, this message translates to:
  /// **'Coordinates'**
  String get coordinates;

  /// Hours helper text
  ///
  /// In en, this message translates to:
  /// **'Hours'**
  String get hours;

  /// Minutes helper text
  ///
  /// In en, this message translates to:
  /// **'Minutes'**
  String get minutes;

  /// Available status
  ///
  /// In en, this message translates to:
  /// **'Available'**
  String get available;

  /// Unavailable status
  ///
  /// In en, this message translates to:
  /// **'Unavailable'**
  String get unavailable;

  /// Status: pending
  ///
  /// In en, this message translates to:
  /// **'Pending'**
  String get pending;

  /// Approved status
  ///
  /// In en, this message translates to:
  /// **'Approved'**
  String get approved;

  /// In progress status
  ///
  /// In en, this message translates to:
  /// **'In Progress'**
  String get inProgress;

  /// Status: completed
  ///
  /// In en, this message translates to:
  /// **'Completed'**
  String get completed;

  /// Cancelled status
  ///
  /// In en, this message translates to:
  /// **'Cancelled'**
  String get cancelled;

  /// Delete confirmation dialog title
  ///
  /// In en, this message translates to:
  /// **'Confirm Delete'**
  String get confirmDelete;

  /// Delete confirmation message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this item?'**
  String get areYouSureDelete;

  /// Yes value
  ///
  /// In en, this message translates to:
  /// **'YES'**
  String get yes;

  /// No value
  ///
  /// In en, this message translates to:
  /// **'NO'**
  String get no;

  /// Success message when data refresh succeeds
  ///
  /// In en, this message translates to:
  /// **'Data refreshed successfully'**
  String get dataRefreshedSuccessfully;

  /// Data loading error message
  ///
  /// In en, this message translates to:
  /// **'Failed to load data'**
  String get failedToLoadData;

  /// Language selection menu item
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get selectLanguage;

  /// Language selection instruction
  ///
  /// In en, this message translates to:
  /// **'Choose your preferred language:'**
  String get chooseLanguage;

  /// Save language button text
  ///
  /// In en, this message translates to:
  /// **'Save Language'**
  String get saveLanguage;

  /// Language change success message
  ///
  /// In en, this message translates to:
  /// **'Language changed successfully'**
  String get languageChanged;

  /// Language change restart note
  ///
  /// In en, this message translates to:
  /// **'Note: The app will need to be restarted for some changes to take effect.'**
  String get languageChangeNote;

  /// Language settings menu item
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// Common Settings menu item
  ///
  /// In en, this message translates to:
  /// **'Common Settings'**
  String get commonSettings;

  /// Driver Setting menu item
  ///
  /// In en, this message translates to:
  /// **'Driver Setting'**
  String get driverSetting;

  /// Car Setting menu item
  ///
  /// In en, this message translates to:
  /// **'Car Setting'**
  String get carSetting;

  /// Cargo Setting menu item
  ///
  /// In en, this message translates to:
  /// **'Cargo Setting'**
  String get cargoSetting;

  /// Destination Setting menu item
  ///
  /// In en, this message translates to:
  /// **'Destination Setting'**
  String get destinationSetting;

  /// Back to Home menu item
  ///
  /// In en, this message translates to:
  /// **'Back to Home'**
  String get backToHome;

  /// Welcome text
  ///
  /// In en, this message translates to:
  /// **'Welcome'**
  String get welcome;

  /// Refresh button tooltip
  ///
  /// In en, this message translates to:
  /// **'Refresh now'**
  String get refreshNow;

  /// Login required message
  ///
  /// In en, this message translates to:
  /// **'You must be logged in'**
  String get youMustBeLoggedIn;

  /// Trip status counts loading error
  ///
  /// In en, this message translates to:
  /// **'Failed to load trip status counts'**
  String get failedToLoadTripStatusCounts;

  /// Generic data loading error
  ///
  /// In en, this message translates to:
  /// **'Error loading data'**
  String get errorLoading;

  /// Trip approvals screen title
  ///
  /// In en, this message translates to:
  /// **'Trip Approvals'**
  String get tripApprovals;

  /// Trip manager dashboard title
  ///
  /// In en, this message translates to:
  /// **'Trip Manager Dashboard'**
  String get dashboardTitle;

  /// Requestor dashboard title
  ///
  /// In en, this message translates to:
  /// **'Requestor Dashboard'**
  String get requestorDashboardTitle;

  /// Trip Monitoring menu item
  ///
  /// In en, this message translates to:
  /// **'Trip Monitoring'**
  String get tripMonitoring;

  /// Driver Tracking menu item
  ///
  /// In en, this message translates to:
  /// **'Driver Tracking'**
  String get driverTracking;

  /// Driver delete confirmation message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to remove {name} as a driver?'**
  String areYouSureDeleteDriver(String name);

  /// Driver deletion error message
  ///
  /// In en, this message translates to:
  /// **'Failed to delete driver'**
  String get failedToDeleteDriver;

  /// Generic error message
  ///
  /// In en, this message translates to:
  /// **'An error occurred: {error}'**
  String anErrorOccurred(String error);

  /// Driver settings screen title
  ///
  /// In en, this message translates to:
  /// **'Driver Settings'**
  String get driverSettingsTitle;

  /// Car settings screen title
  ///
  /// In en, this message translates to:
  /// **'Car Settings'**
  String get carSettingsTitle;

  /// Cargo settings screen title
  ///
  /// In en, this message translates to:
  /// **'Cargo Settings'**
  String get cargoSettingsTitle;

  /// Destination settings screen title
  ///
  /// In en, this message translates to:
  /// **'Destination Settings'**
  String get destinationSettingsTitle;

  /// Search cars hint text
  ///
  /// In en, this message translates to:
  /// **'Search Cars'**
  String get searchCars;

  /// Search cars hint text
  ///
  /// In en, this message translates to:
  /// **'Enter car code, manufacturer, or model'**
  String get searchCarsHint;

  /// No cars found message
  ///
  /// In en, this message translates to:
  /// **'No cars found'**
  String get noCarsFound;

  /// No matching cars found message
  ///
  /// In en, this message translates to:
  /// **'No matching cars found'**
  String get noMatchingCarsFound;

  /// Failed to load cars error message with status code
  ///
  /// In en, this message translates to:
  /// **'Failed to load cars: {statusCode}'**
  String failedToLoadCarsWithCode(String statusCode);

  /// Car deleted successfully message
  ///
  /// In en, this message translates to:
  /// **'Car deleted successfully'**
  String get carDeletedSuccessfully;

  /// Failed to delete car error message
  ///
  /// In en, this message translates to:
  /// **'Failed to delete car'**
  String get failedToDeleteCar;

  /// Confirm delete car message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete car {carCode}?'**
  String confirmDeleteCar(String carCode);

  /// Search cargos placeholder text
  ///
  /// In en, this message translates to:
  /// **'Search cargos'**
  String get searchCargos;

  /// Search cargos hint text
  ///
  /// In en, this message translates to:
  /// **'Enter cargo code or name'**
  String get searchCargosHint;

  /// No cargos found message
  ///
  /// In en, this message translates to:
  /// **'No cargos found'**
  String get noCargosFound;

  /// No matching cargos found message
  ///
  /// In en, this message translates to:
  /// **'No matching cargos found'**
  String get noMatchingCargosFound;

  /// Failed to load cargos error message
  ///
  /// In en, this message translates to:
  /// **'Failed to load cargos: {statusCode}'**
  String failedToLoadCargos(String statusCode);

  /// No cargo data found message
  ///
  /// In en, this message translates to:
  /// **'No cargo data found'**
  String get noCargoDataFound;

  /// Cargo deleted successfully message
  ///
  /// In en, this message translates to:
  /// **'Cargo deleted successfully'**
  String get cargoDeletedSuccessfully;

  /// Failed to delete cargo error message
  ///
  /// In en, this message translates to:
  /// **'Failed to delete cargo'**
  String get failedToDeleteCargo;

  /// Confirm delete cargo message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete cargo {cargoCode}?'**
  String confirmDeleteCargo(String cargoCode);

  /// Cargo code label
  ///
  /// In en, this message translates to:
  /// **'Cargo Code'**
  String get cargoCode;

  /// Cargo name label
  ///
  /// In en, this message translates to:
  /// **'Cargo Name'**
  String get cargoName;

  /// Search destinations placeholder text
  ///
  /// In en, this message translates to:
  /// **'Search destinations'**
  String get searchDestinations;

  /// Search destinations hint text
  ///
  /// In en, this message translates to:
  /// **'Enter destination code, name, address, or initial'**
  String get searchDestinationsHint;

  /// No destinations found message
  ///
  /// In en, this message translates to:
  /// **'No destinations found'**
  String get noDestinationsFound;

  /// No matching destinations found message
  ///
  /// In en, this message translates to:
  /// **'No matching destinations found'**
  String get noMatchingDestinationsFound;

  /// Failed to load destinations error message
  ///
  /// In en, this message translates to:
  /// **'Failed to load destinations: {statusCode}'**
  String failedToLoadDestinations(String statusCode);

  /// Destination deleted successfully message
  ///
  /// In en, this message translates to:
  /// **'Destination deleted successfully'**
  String get destinationDeletedSuccessfully;

  /// Failed to delete destination error message
  ///
  /// In en, this message translates to:
  /// **'Failed to delete destination'**
  String get failedToDeleteDestination;

  /// Confirm delete destination message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete destination {destinationCode}?'**
  String confirmDeleteDestination(String destinationCode);

  /// Destination code label
  ///
  /// In en, this message translates to:
  /// **'Destination Code'**
  String get destinationCode;

  /// Active status value
  ///
  /// In en, this message translates to:
  /// **'ACTIVE'**
  String get active;

  /// Inactive status value
  ///
  /// In en, this message translates to:
  /// **'INACTIVE'**
  String get inactive;

  /// Trip approvals screen title
  ///
  /// In en, this message translates to:
  /// **'Trip Approvals'**
  String get tripApprovalsTitle;

  /// No trips for approval message
  ///
  /// In en, this message translates to:
  /// **'No trips for approval'**
  String get noTripsForApproval;

  /// Failed to fetch trips error message
  ///
  /// In en, this message translates to:
  /// **'Failed to fetch trips'**
  String get failedToFetchTrips;

  /// Requested by label
  ///
  /// In en, this message translates to:
  /// **'Requested by'**
  String get requestedBy;

  /// Request date label
  ///
  /// In en, this message translates to:
  /// **'Request Date'**
  String get requestDate;

  /// Trip date label
  ///
  /// In en, this message translates to:
  /// **'Trip Date'**
  String get tripDate;

  /// Purpose label
  ///
  /// In en, this message translates to:
  /// **'Purpose'**
  String get purpose;

  /// Approve button text
  ///
  /// In en, this message translates to:
  /// **'Approve'**
  String get approve;

  /// Reject button text
  ///
  /// In en, this message translates to:
  /// **'Reject'**
  String get reject;

  /// Tooltip for view changes button
  ///
  /// In en, this message translates to:
  /// **'View Changes'**
  String get viewChanges;

  /// Details button text
  ///
  /// In en, this message translates to:
  /// **'Details'**
  String get details;

  /// Trip approved successfully message
  ///
  /// In en, this message translates to:
  /// **'Trip approved successfully'**
  String get tripApprovedSuccessfully;

  /// Success message when a trip is rejected
  ///
  /// In en, this message translates to:
  /// **'Trip rejected successfully'**
  String get tripRejectedSuccessfully;

  /// Failed to update trip error message
  ///
  /// In en, this message translates to:
  /// **'Failed to update trip'**
  String get failedToUpdateTrip;

  /// Add driver screen title and button text
  ///
  /// In en, this message translates to:
  /// **'Add Driver'**
  String get addDriver;

  /// Search drivers placeholder text
  ///
  /// In en, this message translates to:
  /// **'Search drivers...'**
  String get searchDrivers;

  /// No drivers found message
  ///
  /// In en, this message translates to:
  /// **'No drivers found'**
  String get noDriversFound;

  /// Rate trip screen title
  ///
  /// In en, this message translates to:
  /// **'Rate Trip'**
  String get rateTripTitle;

  /// Rating question text
  ///
  /// In en, this message translates to:
  /// **'How would you rate your trip?'**
  String get howWouldYouRateTrip;

  /// Additional comments section title
  ///
  /// In en, this message translates to:
  /// **'Additional Comments'**
  String get additionalComments;

  /// Submit rating button text
  ///
  /// In en, this message translates to:
  /// **'Submit Rating'**
  String get submitRating;

  /// Error message when rating submission fails
  ///
  /// In en, this message translates to:
  /// **'Error submitting rating'**
  String get errorSubmittingRating;

  /// Trip Change Approval menu item
  ///
  /// In en, this message translates to:
  /// **'Trip Change Approval'**
  String get tripChangeApproval;

  /// Confirm approval dialog title
  ///
  /// In en, this message translates to:
  /// **'Confirm Approval'**
  String get confirmApproval;

  /// Confirm rejection dialog title
  ///
  /// In en, this message translates to:
  /// **'Confirm Rejection'**
  String get confirmRejection;

  /// Trip approval confirmation message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to approve trip {tripCode}? This will change the status to REQUEST.'**
  String areYouSureApproveTrip(String tripCode);

  /// Trip changes approval confirmation message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to approve the changes to trip {tripCode}? This will change the status to REQUEST.'**
  String areYouSureApproveChanges(String tripCode);

  /// Trip cancellation approval confirmation message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to approve the cancellation of trip {tripCode}? This will change the status to DELETED.'**
  String areYouSureApproveCancellation(String tripCode);

  /// Trip rejection confirmation message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to reject trip {tripCode}?'**
  String areYouSureRejectTrip(String tripCode);

  /// Trip changes rejection confirmation message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to reject the changes to trip {tripCode}? The trip will be restored to its original state before the edits were made.'**
  String areYouSureRejectChanges(String tripCode);

  /// Trip cancellation rejection confirmation message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to reject the cancellation of trip {tripCode}? The trip will remain active.'**
  String areYouSureRejectCancellation(String tripCode);

  /// Prompt for rejection reason
  ///
  /// In en, this message translates to:
  /// **'Please provide a reason for rejection:'**
  String get pleaseProvideReason;

  /// Hint text for rejection reason field
  ///
  /// In en, this message translates to:
  /// **'Enter reason for rejecting the trip'**
  String get enterRejectionReason;

  /// Validation message for required reason field
  ///
  /// In en, this message translates to:
  /// **'Reason is required'**
  String get reasonIsRequired;

  /// Requestor column header
  ///
  /// In en, this message translates to:
  /// **'Requestor'**
  String get requestor;

  /// Trip approval success message
  ///
  /// In en, this message translates to:
  /// **'Trip approved and status changed to REQUEST'**
  String get tripApprovedAndStatusChanged;

  /// Trip approval error message
  ///
  /// In en, this message translates to:
  /// **'Failed to approve trip'**
  String get failedToApproveTrip;

  /// Trip rejection error message
  ///
  /// In en, this message translates to:
  /// **'Failed to reject trip'**
  String get failedToRejectTrip;

  /// Trip changes approval error message
  ///
  /// In en, this message translates to:
  /// **'Failed to approve trip changes'**
  String get failedToApproveChanges;

  /// Trip cancellation approval error message
  ///
  /// In en, this message translates to:
  /// **'Failed to approve trip cancellation'**
  String get failedToApproveCancellation;

  /// Trip changes rejection error message
  ///
  /// In en, this message translates to:
  /// **'Failed to reject trip changes'**
  String get failedToRejectChanges;

  /// Trip cancellation rejection error message
  ///
  /// In en, this message translates to:
  /// **'Failed to reject trip cancellation'**
  String get failedToRejectCancellation;

  /// Success message when trip changes are approved
  ///
  /// In en, this message translates to:
  /// **'Trip changes approved successfully. Status changed to REQUEST.'**
  String get tripChangesApprovedSuccessfully;

  /// Success message when trip cancellation is approved
  ///
  /// In en, this message translates to:
  /// **'Trip cancellation approved successfully. Status changed to DELETED.'**
  String get tripCancellationApprovedSuccessfully;

  /// Success message when trip changes are rejected
  ///
  /// In en, this message translates to:
  /// **'Trip changes rejected successfully. Status changed to REQUEST.'**
  String get tripChangesRejectedSuccessfully;

  /// Success message when trip cancellation is rejected
  ///
  /// In en, this message translates to:
  /// **'Trip cancellation rejected successfully. Status changed to REQUEST.'**
  String get tripCancellationRejectedSuccessfully;

  /// Tooltip for approve trip button
  ///
  /// In en, this message translates to:
  /// **'Approve Trip'**
  String get approveTrip;

  /// Tooltip for approve changes button
  ///
  /// In en, this message translates to:
  /// **'Approve Changes'**
  String get approveChanges;

  /// Tooltip for approve cancellation button
  ///
  /// In en, this message translates to:
  /// **'Approve Cancellation'**
  String get approveCancellation;

  /// Tooltip for reject trip button
  ///
  /// In en, this message translates to:
  /// **'Reject Trip'**
  String get rejectTrip;

  /// Tooltip for reject changes button
  ///
  /// In en, this message translates to:
  /// **'Reject Changes'**
  String get rejectChanges;

  /// Tooltip for reject cancellation button
  ///
  /// In en, this message translates to:
  /// **'Reject Cancellation'**
  String get rejectCancellation;

  /// Message when no trips are found
  ///
  /// In en, this message translates to:
  /// **'No trips found'**
  String get noTripsFound;

  /// Message when no trips are found for a specific date
  ///
  /// In en, this message translates to:
  /// **'No trips found for date: {date}'**
  String noTripsFoundForDate(String date);

  /// Trip monitoring screen title
  ///
  /// In en, this message translates to:
  /// **'Trip Monitoring'**
  String get tripMonitoringTitle;

  /// Track drivers button text
  ///
  /// In en, this message translates to:
  /// **'Track Drivers'**
  String get trackDrivers;

  /// All requests button text
  ///
  /// In en, this message translates to:
  /// **'All Requests'**
  String get allRequests;

  /// Refresh data button text
  ///
  /// In en, this message translates to:
  /// **'Refresh Data'**
  String get refreshData;

  /// Instruction text for mobile date navigation
  ///
  /// In en, this message translates to:
  /// **'Swipe to change date'**
  String get swipeToChangeDate;

  /// Error message when data refresh fails
  ///
  /// In en, this message translates to:
  /// **'Error refreshing data'**
  String errorRefreshingData(Object error);

  /// Search drivers hint text
  ///
  /// In en, this message translates to:
  /// **'Enter driver code, initial, or name'**
  String get searchDriversHint;

  /// No matching drivers found message
  ///
  /// In en, this message translates to:
  /// **'No matching drivers found'**
  String get noMatchingDriversFound;

  /// Driver deleted successfully message
  ///
  /// In en, this message translates to:
  /// **'Driver deleted successfully'**
  String get driverDeletedSuccessfully;

  /// Failed to load drivers error message
  ///
  /// In en, this message translates to:
  /// **'Failed to load drivers: {statusCode}'**
  String failedToLoadDrivers(String statusCode);

  /// Driver code label
  ///
  /// In en, this message translates to:
  /// **'Driver Code'**
  String get driverCodeLabel;

  /// Car label in trip card
  ///
  /// In en, this message translates to:
  /// **'Car:'**
  String get carLabel;

  /// Save button text
  ///
  /// In en, this message translates to:
  /// **'Save Changes'**
  String get saveChanges;

  /// Waiting trip checkbox label
  ///
  /// In en, this message translates to:
  /// **'Waiting Trip'**
  String get waitingTrip;

  /// From destination field label
  ///
  /// In en, this message translates to:
  /// **'From Destination'**
  String get fromDestination;

  /// To destination field label
  ///
  /// In en, this message translates to:
  /// **'To Destination'**
  String get toDestination;

  /// Notes field label
  ///
  /// In en, this message translates to:
  /// **'Notes (optional)'**
  String get notesOptional;

  /// Select passengers field label
  ///
  /// In en, this message translates to:
  /// **'Select Passengers'**
  String get selectPassengers;

  /// Select cargos field label
  ///
  /// In en, this message translates to:
  /// **'Select Cargos'**
  String get selectCargos;

  /// Trip section header
  ///
  /// In en, this message translates to:
  /// **'Trip'**
  String get trip;

  /// Cargos section header
  ///
  /// In en, this message translates to:
  /// **'Cargos'**
  String get cargos;

  /// Search placeholder for destinations
  ///
  /// In en, this message translates to:
  /// **'Search for destinations'**
  String get searchForDestinations;

  /// Custom from destination field label
  ///
  /// In en, this message translates to:
  /// **'Custom From Destination'**
  String get customFromDestination;

  /// Custom to destination field label
  ///
  /// In en, this message translates to:
  /// **'Custom To Destination'**
  String get customToDestination;

  /// Hint text for custom location fields
  ///
  /// In en, this message translates to:
  /// **'Enter custom location'**
  String get enterCustomLocation;

  /// Hint text for notes field
  ///
  /// In en, this message translates to:
  /// **'Add any additional notes for this trip'**
  String get addAdditionalNotes;

  /// Search placeholder for passengers
  ///
  /// In en, this message translates to:
  /// **'Search for passengers'**
  String get searchForPassengers;

  /// Hint text for passenger selection
  ///
  /// In en, this message translates to:
  /// **'Choose one or more passengers'**
  String get chooseOneOrMorePassengers;

  /// Search placeholder for cargos
  ///
  /// In en, this message translates to:
  /// **'Search for cargos'**
  String get searchForCargos;

  /// Hint text for cargo selection
  ///
  /// In en, this message translates to:
  /// **'Choose one or more cargos'**
  String get chooseOneOrMoreCargos;

  /// Rate trips navigation label
  ///
  /// In en, this message translates to:
  /// **'Rate Trips'**
  String get rateTrips;

  /// My trips navigation label
  ///
  /// In en, this message translates to:
  /// **'My Trips'**
  String get myTrips;

  /// Check-in navigation label
  ///
  /// In en, this message translates to:
  /// **'Check-In'**
  String get checkIn;

  /// Notifications navigation label
  ///
  /// In en, this message translates to:
  /// **'Notifications'**
  String get notifications;

  /// Admin navigation label
  ///
  /// In en, this message translates to:
  /// **'Admin'**
  String get admin;

  /// Switch role button text
  ///
  /// In en, this message translates to:
  /// **'Switch Role'**
  String get switchRole;

  /// Current role label
  ///
  /// In en, this message translates to:
  /// **'Current Role'**
  String get currentRole;

  /// Role switch confirmation dialog title
  ///
  /// In en, this message translates to:
  /// **'Confirm Role Switch'**
  String get confirmRoleSwitch;

  /// Role switch confirmation message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to switch to {role}? This will change your interface and available functions.'**
  String switchRoleMessage(Object role);

  /// Role switch success message
  ///
  /// In en, this message translates to:
  /// **'Successfully switched to {role}'**
  String roleSwitchedSuccessfully(Object role);

  /// Current time label
  ///
  /// In en, this message translates to:
  /// **'Now'**
  String get now;

  /// Current time label
  ///
  /// In en, this message translates to:
  /// **'Current Time'**
  String get currentTime;

  /// Rating column header
  ///
  /// In en, this message translates to:
  /// **'Rating'**
  String get rating;

  /// Cargo section label
  ///
  /// In en, this message translates to:
  /// **'Cargo'**
  String get cargo;

  /// Clear status filter button
  ///
  /// In en, this message translates to:
  /// **'Clear Status Filter'**
  String get clearStatusFilter;

  /// Specific date filter label
  ///
  /// In en, this message translates to:
  /// **'Specific Date'**
  String get specificDate;

  /// Year filter label
  ///
  /// In en, this message translates to:
  /// **'Year'**
  String get year;

  /// Month filter label
  ///
  /// In en, this message translates to:
  /// **'Month'**
  String get month;

  /// Current filter label for date filter summary
  ///
  /// In en, this message translates to:
  /// **'Current Filter'**
  String get currentFilter;

  /// Select year dialog title
  ///
  /// In en, this message translates to:
  /// **'Select Year'**
  String get selectYear;

  /// Select month dialog title
  ///
  /// In en, this message translates to:
  /// **'Select Month'**
  String get selectMonth;

  /// Search trips placeholder text
  ///
  /// In en, this message translates to:
  /// **'Search trips (code, requestor, locations...)'**
  String get searchTrips;

  /// Odometer reading label
  ///
  /// In en, this message translates to:
  /// **'Odometer Reading'**
  String get odometerReading;

  /// Extended search placeholder for trips
  ///
  /// In en, this message translates to:
  /// **'Search trips (code, requestor, locations...)'**
  String get searchTripsLong;

  /// Dropdown hint for pagination
  ///
  /// In en, this message translates to:
  /// **'Items per page'**
  String get itemsPerPage;

  /// Export to Excel button tooltip
  ///
  /// In en, this message translates to:
  /// **'Export to Excel'**
  String get exportToExcel;

  /// Exporting status text
  ///
  /// In en, this message translates to:
  /// **'Exporting...'**
  String get exporting;

  /// Exporting to Excel loading text
  ///
  /// In en, this message translates to:
  /// **'Exporting to Excel...'**
  String get exportingToExcel;

  /// Message when no trips available for export
  ///
  /// In en, this message translates to:
  /// **'No trips to export'**
  String get noTripsToExport;

  /// Success message for trip history export
  ///
  /// In en, this message translates to:
  /// **'Trip history exported as {fileName}'**
  String tripHistoryExported(String fileName);

  /// Success message for trip history export with file path
  ///
  /// In en, this message translates to:
  /// **'Trip history exported to: {filePath}'**
  String tripHistoryExportedTo(String filePath);

  /// Error message for export failure
  ///
  /// In en, this message translates to:
  /// **'Export failed: {error}'**
  String exportFailed(String error);

  /// Message when no trip history exists
  ///
  /// In en, this message translates to:
  /// **'No trip history found'**
  String get noTripHistoryFound;

  /// Message when no trips match search criteria
  ///
  /// In en, this message translates to:
  /// **'No matching trips found'**
  String get noMatchingTripsFound;

  /// Super Admin role label
  ///
  /// In en, this message translates to:
  /// **'Super Admin'**
  String get superAdmin;

  /// Trip Manager role label
  ///
  /// In en, this message translates to:
  /// **'Trip Manager'**
  String get tripManager;

  /// Default user name when name is not available
  ///
  /// In en, this message translates to:
  /// **'User'**
  String get user;

  /// Location Tracking menu item
  ///
  /// In en, this message translates to:
  /// **'Location Tracking'**
  String get locationTracking;

  /// Trip Rating menu item
  ///
  /// In en, this message translates to:
  /// **'Trip Rating'**
  String get tripRating;

  /// Driver availability management screen title
  ///
  /// In en, this message translates to:
  /// **'Driver Availability Management'**
  String get driverAvailabilityManagement;

  /// Car availability management screen title
  ///
  /// In en, this message translates to:
  /// **'Car Availability Management'**
  String get carAvailabilityManagement;

  /// Driver Check-In Management menu item
  ///
  /// In en, this message translates to:
  /// **'Driver Check-In Management'**
  String get driverCheckInManagement;

  /// Car Odometer History menu item
  ///
  /// In en, this message translates to:
  /// **'Car Odometer History'**
  String get carOdometerHistory;

  /// Driver check-in screen title
  ///
  /// In en, this message translates to:
  /// **'Driver Check-In'**
  String get driverCheckIn;

  /// My Trip History menu item
  ///
  /// In en, this message translates to:
  /// **'My Trip History'**
  String get myTripHistory;

  /// Driver selection dropdown hint
  ///
  /// In en, this message translates to:
  /// **'Select a driver'**
  String get selectADriver;

  /// Assign button text
  ///
  /// In en, this message translates to:
  /// **'Assign'**
  String get assign;

  /// Button text to view rejection reason
  ///
  /// In en, this message translates to:
  /// **'View Reason'**
  String get viewReason;

  /// Close button text
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get close;

  /// All trip requests title
  ///
  /// In en, this message translates to:
  /// **'All Trip Requests'**
  String get allTripRequests;

  /// Rejection reason field label
  ///
  /// In en, this message translates to:
  /// **'Rejection Reason'**
  String get rejectionReason;

  /// Trip changes screen title
  ///
  /// In en, this message translates to:
  /// **'Trip Changes'**
  String get tripChanges;

  /// Confirm cancellation dialog title
  ///
  /// In en, this message translates to:
  /// **'Confirm Cancellation'**
  String get confirmCancellation;

  /// Message shown when trip is approved
  ///
  /// In en, this message translates to:
  /// **'Trip approved and status changed to REQUEST'**
  String get tripApprovedMessage;

  /// Validation message for rating selection
  ///
  /// In en, this message translates to:
  /// **'Please select a rating'**
  String get pleaseSelectRating;

  /// Success message for rating submission
  ///
  /// In en, this message translates to:
  /// **'Rating submitted successfully'**
  String get ratingSubmittedSuccessfully;

  /// Error message when trip details fail to load
  ///
  /// In en, this message translates to:
  /// **'Failed to load trip details'**
  String get failedToLoadTripDetails;

  /// Text shown when trip is not rated
  ///
  /// In en, this message translates to:
  /// **'Not rated'**
  String get notRated;

  /// Previous button text in pagination
  ///
  /// In en, this message translates to:
  /// **'Previous'**
  String get previous;

  /// Next button text in pagination
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get next;

  /// Message when no trip data is available
  ///
  /// In en, this message translates to:
  /// **'No trip data available'**
  String get noTripDataAvailable;

  /// Validation message for destination selection
  ///
  /// In en, this message translates to:
  /// **'Please select both destinations'**
  String get pleaseSelectBothDestinations;

  /// Validation message for same destinations
  ///
  /// In en, this message translates to:
  /// **'From and To destinations cannot be the same'**
  String get destinationsCannotBeSame;

  /// Validation message for same custom destinations
  ///
  /// In en, this message translates to:
  /// **'From and To custom destinations cannot be the same'**
  String get customDestinationsCannotBeSame;

  /// Validation message for waiting duration
  ///
  /// In en, this message translates to:
  /// **'Please enter waiting duration'**
  String get pleaseEnterWaitingDuration;

  /// Validation message for custom from destination
  ///
  /// In en, this message translates to:
  /// **'Please enter custom from destination'**
  String get pleaseEnterCustomFromDestination;

  /// Validation message for custom to destination
  ///
  /// In en, this message translates to:
  /// **'Please enter custom to destination'**
  String get pleaseEnterCustomToDestination;

  /// Error message when cars fail to load
  ///
  /// In en, this message translates to:
  /// **'Failed to load available cars'**
  String get failedToLoadAvailableCars;

  /// Request trips button text
  ///
  /// In en, this message translates to:
  /// **'Request Trips'**
  String get requestTrips;

  /// Other trip statuses section title
  ///
  /// In en, this message translates to:
  /// **'Other Trip Statuses'**
  String get otherTripStatuses;

  /// Multiple trips section title
  ///
  /// In en, this message translates to:
  /// **'Multiple Trips'**
  String get multipleTrips;

  /// Trip duration label
  ///
  /// In en, this message translates to:
  /// **'Trip Duration:'**
  String get tripDuration;

  /// Message when no notes are available for a trip
  ///
  /// In en, this message translates to:
  /// **'No notes provided for this trip.'**
  String get noNotesProvided;

  /// Message when there are no notifications
  ///
  /// In en, this message translates to:
  /// **'No notifications'**
  String get noNotifications;

  /// Generic authentication failure message
  ///
  /// In en, this message translates to:
  /// **'Authentication failed'**
  String get authenticationFailed;

  /// Message when no trip data is available in requestor dashboard
  ///
  /// In en, this message translates to:
  /// **'No trip data available'**
  String get noTripDataAvailableRequestor;

  /// Title for trip requests section
  ///
  /// In en, this message translates to:
  /// **'My Trip Requests'**
  String get myTripRequests;

  /// Edit driver screen title
  ///
  /// In en, this message translates to:
  /// **'Edit Driver'**
  String get editDriver;

  /// Error message when cars fail to load
  ///
  /// In en, this message translates to:
  /// **'Failed to load available cars'**
  String get failedToLoadCars;

  /// Error message when driver update fails
  ///
  /// In en, this message translates to:
  /// **'Failed to update driver'**
  String get failedToUpdateDriver;

  /// Name field label
  ///
  /// In en, this message translates to:
  /// **'Name'**
  String get name;

  /// Helper text for name field
  ///
  /// In en, this message translates to:
  /// **'Name will be converted to uppercase'**
  String get nameWillBeUppercase;

  /// Validation message for required name field
  ///
  /// In en, this message translates to:
  /// **'Name is required'**
  String get nameIsRequired;

  /// Helper text for initial field
  ///
  /// In en, this message translates to:
  /// **'Must be exactly 3 characters (will be converted to uppercase)'**
  String get initialHelperText;

  /// Validation message for required initial field
  ///
  /// In en, this message translates to:
  /// **'Initial is required'**
  String get initialIsRequired;

  /// Validation message for initial field length
  ///
  /// In en, this message translates to:
  /// **'Initial must be exactly 3 characters'**
  String get initialMustBe3Characters;

  /// Loading text for cars dropdown
  ///
  /// In en, this message translates to:
  /// **'Loading cars...'**
  String get loadingCars;

  /// Placeholder text for car selection
  ///
  /// In en, this message translates to:
  /// **'Select a car'**
  String get selectACar;

  /// Search hint text in car dropdown
  ///
  /// In en, this message translates to:
  /// **'Search for a car'**
  String get searchForCar;

  /// Validation message for car selection
  ///
  /// In en, this message translates to:
  /// **'Please select a car'**
  String get pleaseSelectCar;

  /// Password field label
  ///
  /// In en, this message translates to:
  /// **'New Password (optional)'**
  String get newPasswordOptional;

  /// Helper text for password field
  ///
  /// In en, this message translates to:
  /// **'Leave empty to keep current password'**
  String get leaveEmptyToKeepPassword;

  /// Validation message for from destination
  ///
  /// In en, this message translates to:
  /// **'From destination is required'**
  String get fromDestinationRequired;

  /// Validation message for to destination
  ///
  /// In en, this message translates to:
  /// **'To destination is required'**
  String get toDestinationRequired;

  /// Validation message for required date
  ///
  /// In en, this message translates to:
  /// **'Date is required'**
  String get dateIsRequired;

  /// Validation message for required time
  ///
  /// In en, this message translates to:
  /// **'Time is required'**
  String get timeIsRequired;

  /// Validation message for odometer field
  ///
  /// In en, this message translates to:
  /// **'Please enter odometer reading'**
  String get pleaseEnterOdometerReading;

  /// Validation message when odometer reading is empty
  ///
  /// In en, this message translates to:
  /// **'Odometer reading is required'**
  String get odometerReadingRequired;

  /// Validation message for invalid number input
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid number'**
  String get pleaseEnterValidNumber;

  /// Dropdown option for all drivers
  ///
  /// In en, this message translates to:
  /// **'All Drivers'**
  String get allDrivers;

  /// Validation status filter label
  ///
  /// In en, this message translates to:
  /// **'Validation Status'**
  String get validationStatus;

  /// Validation status: validated
  ///
  /// In en, this message translates to:
  /// **'Validated'**
  String get validated;

  /// Validation status: not validated
  ///
  /// In en, this message translates to:
  /// **'Not Validated'**
  String get notValidated;

  /// Success message for check-in validation
  ///
  /// In en, this message translates to:
  /// **'Check-in validated successfully'**
  String get checkInValidatedSuccessfully;

  /// Message when validation is removed
  ///
  /// In en, this message translates to:
  /// **'Validation removed'**
  String get validationRemoved;

  /// Reset button text
  ///
  /// In en, this message translates to:
  /// **'Reset'**
  String get reset;

  /// Section title for check-in details
  ///
  /// In en, this message translates to:
  /// **'Check-In Details'**
  String get checkInDetails;

  /// Check-out details section title
  ///
  /// In en, this message translates to:
  /// **'Check-Out Details'**
  String get checkOutDetails;

  /// Rate trip button text
  ///
  /// In en, this message translates to:
  /// **'Rate Trip'**
  String get rateTrip;

  /// Rate this trip button text
  ///
  /// In en, this message translates to:
  /// **'Rate This Trip'**
  String get rateThisTrip;

  /// Error message when trip ID is missing for duration adjustment
  ///
  /// In en, this message translates to:
  /// **'Cannot adjust duration for this trip - no trip ID'**
  String get cannotAdjustDurationNoTripId;

  /// Generic error message for duration adjustment
  ///
  /// In en, this message translates to:
  /// **'Cannot adjust duration for this trip'**
  String get cannotAdjustDuration;

  /// Error message when duration update fails
  ///
  /// In en, this message translates to:
  /// **'Failed to update duration: {statusCode}'**
  String failedToUpdateDuration(String statusCode);

  /// Success message when trip duration is updated
  ///
  /// In en, this message translates to:
  /// **'Updated duration for trip {tripCode} to {minutes} minutes'**
  String updatedDurationForTrip(String tripCode, String minutes);

  /// Rate your trips screen title
  ///
  /// In en, this message translates to:
  /// **'Rate Your Trips'**
  String get rateYourTrips;

  /// Success message when a trip is created
  ///
  /// In en, this message translates to:
  /// **'Trip ({tripCode}) created successfully'**
  String tripCreatedSuccessfully(String tripCode);

  /// Success message when a round trip is created
  ///
  /// In en, this message translates to:
  /// **'Round trip created successfully: Outbound ({outboundCode}), Return ({returnCode})'**
  String roundTripCreatedSuccessfully(String outboundCode, String returnCode);

  /// Success message when a waiting trip is created
  ///
  /// In en, this message translates to:
  /// **'Waiting trip created successfully: ({tripCode})'**
  String waitingTripCreatedSuccessfully(String tripCode);

  /// Success message when a trip is updated
  ///
  /// In en, this message translates to:
  /// **'Trip ({tripCode}) updated successfully'**
  String tripUpdatedSuccessfully(String tripCode);

  /// Loading message for trip details
  ///
  /// In en, this message translates to:
  /// **'Loading trip details...'**
  String get loadingTripDetails;

  /// Loading message for trip data
  ///
  /// In en, this message translates to:
  /// **'Trip data is loading...'**
  String get tripDataIsLoading;

  /// Loading message for available drivers
  ///
  /// In en, this message translates to:
  /// **'Loading available drivers...'**
  String get loadingAvailableDrivers;

  /// Success message for check-in validation
  ///
  /// In en, this message translates to:
  /// **'Check-in validated successfully'**
  String get checkinValidatedSuccessfully;

  /// Success message when Back to Base trip is created
  ///
  /// In en, this message translates to:
  /// **'Back to Base trip created successfully'**
  String get backToBaseTripCreatedSuccessfully;

  /// Error message for car odometer update failure
  ///
  /// In en, this message translates to:
  /// **'Failed to update car odometer'**
  String get failedToUpdateCarOdometer;

  /// Generic error message when data fails to load
  ///
  /// In en, this message translates to:
  /// **'Error loading data'**
  String get errorLoadingData;

  /// Generic error message with details placeholder
  ///
  /// In en, this message translates to:
  /// **'Error: {details}'**
  String errorWithDetails(String details);

  /// Success message when trip is completed
  ///
  /// In en, this message translates to:
  /// **'Trip completed successfully'**
  String get tripCompletedSuccessfully;

  /// Error message when trip completion fails
  ///
  /// In en, this message translates to:
  /// **'Failed to complete the trip'**
  String get failedToCompleteTrip;

  /// Error message when trip completion throws exception
  ///
  /// In en, this message translates to:
  /// **'Error completing trip: {error}'**
  String errorCompletingTrip(String error);

  /// Authentication failure message for invalid/expired code
  ///
  /// In en, this message translates to:
  /// **'Authentication failed: Invalid or expired authentication code'**
  String get authenticationFailedInvalidCode;

  /// Authentication failure message with specific error
  ///
  /// In en, this message translates to:
  /// **'Authentication failed: {error}'**
  String authenticationFailedWithError(String error);

  /// Error message for invalid login credentials
  ///
  /// In en, this message translates to:
  /// **'Invalid username or password'**
  String get invalidCredentials;

  /// Trip cancellation confirmation message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to cancel trip {tripCode}? This will submit a cancellation request for review.'**
  String areYouSureCancelTrip(String tripCode);

  /// Request cancellation button text
  ///
  /// In en, this message translates to:
  /// **'Request Cancellation'**
  String get requestCancellation;

  /// Success message for trip cancellation request
  ///
  /// In en, this message translates to:
  /// **'Trip cancellation request submitted for review'**
  String get tripCancellationRequestSubmitted;

  /// Error message for trip cancellation request failure
  ///
  /// In en, this message translates to:
  /// **'Failed to submit trip cancellation request'**
  String get failedToSubmitCancellationRequest;

  /// Update button text
  ///
  /// In en, this message translates to:
  /// **'Update'**
  String get update;

  /// Dialog title for assigning driver
  ///
  /// In en, this message translates to:
  /// **'Assign Driver or Online Taxi to Trip {tripCode}'**
  String assignDriverOrOnlineTaxi(String tripCode);

  /// Confirmation dialog for deleting trip
  ///
  /// In en, this message translates to:
  /// **'Delete Trip {tripCode}?'**
  String deleteTripConfirmation(String tripCode);

  /// Retry button text
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retry;

  /// Display number of drivers
  ///
  /// In en, this message translates to:
  /// **'Drivers: {count}'**
  String driversCount(int count);

  /// Information about inactive drivers display
  ///
  /// In en, this message translates to:
  /// **'Gray = Inactive > 10 min'**
  String get inactiveDriversInfo;

  /// Information about location update frequency
  ///
  /// In en, this message translates to:
  /// **'Updates every 5 min or 500m'**
  String get updateFrequencyInfo;

  /// Error message when drivers cannot be fetched
  ///
  /// In en, this message translates to:
  /// **'Error fetching drivers: {error}'**
  String errorFetchingDrivers(String error);

  /// Generic unknown value text
  ///
  /// In en, this message translates to:
  /// **'Unknown'**
  String get unknown;

  /// Default driver name when name is not available
  ///
  /// In en, this message translates to:
  /// **'Unknown Driver'**
  String get unknownDriver;

  /// Message when map is centered on a driver
  ///
  /// In en, this message translates to:
  /// **'Centered on driver {driverName}'**
  String centeredOnDriver(String driverName);

  /// Number of trips for a driver
  ///
  /// In en, this message translates to:
  /// **'Driver Trips ({count})'**
  String driverTrips(int count);

  /// Label for last updated time
  ///
  /// In en, this message translates to:
  /// **'Last Updated'**
  String get lastUpdated;

  /// Trip number display
  ///
  /// In en, this message translates to:
  /// **'Trip {current} of {total}'**
  String tripNumber(String current, String total);

  /// Complete trip button text
  ///
  /// In en, this message translates to:
  /// **'Complete Trip'**
  String get completeTrip;

  /// All statuses option in dropdown
  ///
  /// In en, this message translates to:
  /// **'All Statuses'**
  String get allStatuses;

  /// Clear filters button text
  ///
  /// In en, this message translates to:
  /// **'Clear Filters'**
  String get clearFilters;

  /// Message when no trips match filters
  ///
  /// In en, this message translates to:
  /// **'No trips match the current filters'**
  String get noTripsMatchFilters;

  /// Button text to confirm a trip
  ///
  /// In en, this message translates to:
  /// **'Confirm Trip'**
  String get confirmTrip;

  /// Button text to start a trip
  ///
  /// In en, this message translates to:
  /// **'Start Trip'**
  String get startTrip;

  /// Finish trip button text
  ///
  /// In en, this message translates to:
  /// **'Finish Trip'**
  String get finishTrip;

  /// Title for odometer update dialog
  ///
  /// In en, this message translates to:
  /// **'Update Odometer for {carCode}'**
  String updateOdometerFor(String carCode);

  /// Text indicating this is the first trip of the day
  ///
  /// In en, this message translates to:
  /// **'First trip of the day'**
  String get firstTripOfDay;

  /// Hint text for odometer reading input field
  ///
  /// In en, this message translates to:
  /// **'Enter new odometer reading'**
  String get enterNewOdometerReading;

  /// Helper text for odometer reading validation
  ///
  /// In en, this message translates to:
  /// **'Must be greater than or equal to current reading'**
  String get odometerReadingHelper;

  /// Validation message for odometer reading below minimum
  ///
  /// In en, this message translates to:
  /// **'New reading must be at least {minimum}'**
  String newReadingMustBeAtLeast(String minimum);

  /// Add car screen title and button text
  ///
  /// In en, this message translates to:
  /// **'Add Car'**
  String get addCar;

  /// Edit car screen title
  ///
  /// In en, this message translates to:
  /// **'Edit Car'**
  String get editCar;

  /// Error message when car code generation fails
  ///
  /// In en, this message translates to:
  /// **'Failed to generate car code'**
  String get failedToGenerateCarCode;

  /// Validation message for manufacturer field
  ///
  /// In en, this message translates to:
  /// **'Please enter manufacturer'**
  String get pleaseEnterManufacturer;

  /// Validation message for model name field
  ///
  /// In en, this message translates to:
  /// **'Please enter model name'**
  String get pleaseEnterModelName;

  /// Validation message for car type field
  ///
  /// In en, this message translates to:
  /// **'Please enter car type'**
  String get pleaseEnterCarType;

  /// Validation message for plate number field
  ///
  /// In en, this message translates to:
  /// **'Please enter plate number'**
  String get pleaseEnterPlateNumber;

  /// Validation message for color field
  ///
  /// In en, this message translates to:
  /// **'Please enter color'**
  String get pleaseEnterColor;

  /// Error message when car addition fails
  ///
  /// In en, this message translates to:
  /// **'Failed to add car'**
  String get failedToAddCar;

  /// Add cargo screen title and button text
  ///
  /// In en, this message translates to:
  /// **'Add Cargo'**
  String get addCargo;

  /// Edit cargo screen title
  ///
  /// In en, this message translates to:
  /// **'Edit Cargo'**
  String get editCargo;

  /// Error message when cargo code generation fails
  ///
  /// In en, this message translates to:
  /// **'Failed to generate cargo code'**
  String get failedToGenerateCargoCode;

  /// Validation message for cargo name field
  ///
  /// In en, this message translates to:
  /// **'Please enter cargo name'**
  String get pleaseEnterCargoName;

  /// Error message when cargo addition fails
  ///
  /// In en, this message translates to:
  /// **'Failed to add cargo'**
  String get failedToAddCargo;

  /// Submit button text
  ///
  /// In en, this message translates to:
  /// **'Submit'**
  String get submit;

  /// Loading state text for adding operation
  ///
  /// In en, this message translates to:
  /// **'Adding...'**
  String get adding;

  /// Add destination screen title and button text
  ///
  /// In en, this message translates to:
  /// **'Add Destination'**
  String get addDestination;

  /// Edit destination screen title
  ///
  /// In en, this message translates to:
  /// **'Edit Destination'**
  String get editDestination;

  /// Error message when destination code generation fails
  ///
  /// In en, this message translates to:
  /// **'Failed to generate destination code'**
  String get failedToGenerateDestinationCode;

  /// Validation message for destination name field
  ///
  /// In en, this message translates to:
  /// **'Please enter destination name'**
  String get pleaseEnterDestinationName;

  /// Validation message for address field
  ///
  /// In en, this message translates to:
  /// **'Please enter address'**
  String get pleaseEnterAddress;

  /// Validation message for initial field
  ///
  /// In en, this message translates to:
  /// **'Please enter initial'**
  String get pleaseEnterInitial;

  /// Error message when destination addition fails
  ///
  /// In en, this message translates to:
  /// **'Failed to add destination'**
  String get failedToAddDestination;

  /// Destination name field label
  ///
  /// In en, this message translates to:
  /// **'Destination Name'**
  String get destinationName;

  /// Duration hours field label
  ///
  /// In en, this message translates to:
  /// **'Duration (Hours)'**
  String get durationHours;

  /// Duration minutes field label
  ///
  /// In en, this message translates to:
  /// **'Duration (Minutes)'**
  String get durationMinutes;

  /// Coordinates field label with description
  ///
  /// In en, this message translates to:
  /// **'Coordinates (Latitude, Longitude)'**
  String get coordinatesLatLong;

  /// Coordinates format hint text
  ///
  /// In en, this message translates to:
  /// **'Format: lat,long (e.g., 12.3456,78.9012)'**
  String get coordinatesFormat;

  /// Validation message for coordinates field
  ///
  /// In en, this message translates to:
  /// **'Please enter coordinates'**
  String get pleaseEnterCoordinates;

  /// Validation message for invalid hours
  ///
  /// In en, this message translates to:
  /// **'Invalid hours'**
  String get invalidHours;

  /// Validation message for minutes range
  ///
  /// In en, this message translates to:
  /// **'Minutes must be 0-59'**
  String get minutesMustBe0To59;

  /// Helper text for initial field
  ///
  /// In en, this message translates to:
  /// **'Must be exactly 3 characters (will be converted to uppercase)'**
  String get mustBe3Characters;

  /// Validation message for required initial field
  ///
  /// In en, this message translates to:
  /// **'Initial is required'**
  String get initialRequired;

  /// Error message when driver code generation fails
  ///
  /// In en, this message translates to:
  /// **'Failed to generate driver code'**
  String get failedToGenerateDriverCode;

  /// Validation message for name field
  ///
  /// In en, this message translates to:
  /// **'Please enter name'**
  String get pleaseEnterName;

  /// Validation message for password confirmation
  ///
  /// In en, this message translates to:
  /// **'Please confirm password'**
  String get pleaseConfirmPassword;

  /// Loading text for code generation
  ///
  /// In en, this message translates to:
  /// **'Generating code...'**
  String get generatingCode;

  /// Helper text for name field
  ///
  /// In en, this message translates to:
  /// **'Name will be converted to uppercase'**
  String get nameUppercaseHelper;

  /// Search hint for car dropdown
  ///
  /// In en, this message translates to:
  /// **'Search for a car'**
  String get searchForACar;

  /// Validation message for car selection
  ///
  /// In en, this message translates to:
  /// **'Please select a car'**
  String get pleaseSelectACar;

  /// Confirm password field label
  ///
  /// In en, this message translates to:
  /// **'Confirm Password'**
  String get confirmPassword;

  /// Password minimum length validation message
  ///
  /// In en, this message translates to:
  /// **'Password must be at least 6 characters'**
  String get passwordMinLength;

  /// Error message when driver addition fails
  ///
  /// In en, this message translates to:
  /// **'Failed to add driver'**
  String get failedToAddDriver;

  /// Update car button text
  ///
  /// In en, this message translates to:
  /// **'Update Car'**
  String get updateCar;

  /// Update cargo button text
  ///
  /// In en, this message translates to:
  /// **'Update Cargo'**
  String get updateCargo;

  /// Updating button text when in progress
  ///
  /// In en, this message translates to:
  /// **'Updating...'**
  String get updating;

  /// Update destination button text
  ///
  /// In en, this message translates to:
  /// **'Update Destination'**
  String get updateDestination;

  /// Error message when car update fails
  ///
  /// In en, this message translates to:
  /// **'Failed to update car'**
  String get failedToUpdateCar;

  /// Error message when cargo update fails
  ///
  /// In en, this message translates to:
  /// **'Failed to update cargo'**
  String get failedToUpdateCargo;

  /// Error message when destination update fails
  ///
  /// In en, this message translates to:
  /// **'Failed to update destination'**
  String get failedToUpdateDestination;

  /// Model name field label
  ///
  /// In en, this message translates to:
  /// **'Model Name'**
  String get modelName;

  /// Mobile date range limitation message
  ///
  /// In en, this message translates to:
  /// **'On mobile, date range is limited to 9 days maximum'**
  String get onMobileDateRangeLimited;

  /// Demo unavailable reason
  ///
  /// In en, this message translates to:
  /// **'Unavailable: Demo reason'**
  String get unavailableDemoReason;

  /// Success message for availability update
  ///
  /// In en, this message translates to:
  /// **'Updated {driverName}\'s availability for {date}'**
  String updatedDriverAvailability(String driverName, String date);

  /// Dialog title for editing availability for a specific driver
  ///
  /// In en, this message translates to:
  /// **'Edit Availability for {driverName}'**
  String editAvailabilityFor(String driverName, Object carCode);

  /// Trip notes section header
  ///
  /// In en, this message translates to:
  /// **'Trip Notes:'**
  String get tripNotes;

  /// Assign to label
  ///
  /// In en, this message translates to:
  /// **'Assign to:'**
  String get assignTo;

  /// Loading message when fetching trips
  ///
  /// In en, this message translates to:
  /// **'Fetching trips...'**
  String get fetchingTrips;

  /// Message when no trips with REQUEST status are found
  ///
  /// In en, this message translates to:
  /// **'No trips with status REQUEST found.'**
  String get noTripsWithStatusRequest;

  /// Message when no trips with DRIVER REJECTED status are found
  ///
  /// In en, this message translates to:
  /// **'No trips with status DRIVER REJECTED found.'**
  String get noTripsWithStatusDriverRejected;

  /// Message shown when assigning a driver to a trip
  ///
  /// In en, this message translates to:
  /// **'Assigning driver...'**
  String get assigningDriver;

  /// Progress message shown while deleting trip
  ///
  /// In en, this message translates to:
  /// **'Deleting trip...'**
  String get deletingTrip;

  /// Delete action button text
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get deleteAction;

  /// Assign action button text
  ///
  /// In en, this message translates to:
  /// **'Assign'**
  String get assignAction;

  /// Hint text when no driver is selected
  ///
  /// In en, this message translates to:
  /// **'Select a driver first'**
  String get selectDriverFirst;

  /// Hint text for car selection
  ///
  /// In en, this message translates to:
  /// **'Select a car'**
  String get selectCar;

  /// Completed trip status
  ///
  /// In en, this message translates to:
  /// **'COMPLETED'**
  String get completedStatus;

  /// Completed trip duration label
  ///
  /// In en, this message translates to:
  /// **'COMPLETED Duration'**
  String get completedDuration;

  /// Trip in progress status
  ///
  /// In en, this message translates to:
  /// **'TRIP IN PROGRESS'**
  String get tripInProgress;

  /// Trip in progress duration label
  ///
  /// In en, this message translates to:
  /// **'TRIP IN PROGRESS Duration'**
  String get tripInProgressDuration;

  /// Waiting for rating status
  ///
  /// In en, this message translates to:
  /// **'WAITING FOR RATING'**
  String get waitingForRating;

  /// Waiting for rating duration label
  ///
  /// In en, this message translates to:
  /// **'WAITING FOR RATING Duration'**
  String get waitingForRatingDuration;

  /// Assign to driver duration label
  ///
  /// In en, this message translates to:
  /// **'ASSIGN TO DRIVER Duration'**
  String get assignToDriverDuration;

  /// Number of request trips found
  ///
  /// In en, this message translates to:
  /// **'Found {count} request trips:'**
  String foundRequestTrips(int count);

  /// Message when no request trips are found
  ///
  /// In en, this message translates to:
  /// **'No trips with status REQUEST found.'**
  String get noRequestTripsFound;

  /// Number of driver rejected trips found
  ///
  /// In en, this message translates to:
  /// **'Found {count} driver rejected trips:'**
  String foundDriverRejectedTrips(int count);

  /// Message when no driver rejected trips are found
  ///
  /// In en, this message translates to:
  /// **'No trips with status DRIVER REJECTED found.'**
  String get noDriverRejectedTripsFound;

  /// Success message when availability is updated
  ///
  /// In en, this message translates to:
  /// **'Availability updated for {driverName}'**
  String availabilityUpdatedFor(String driverName);

  /// Sick status option
  ///
  /// In en, this message translates to:
  /// **'Sick'**
  String get sick;

  /// Break status option
  ///
  /// In en, this message translates to:
  /// **'Break'**
  String get breakStatus;

  /// On break status text
  ///
  /// In en, this message translates to:
  /// **'On Break'**
  String get onBreak;

  /// No reason option
  ///
  /// In en, this message translates to:
  /// **'No Reason'**
  String get noReason;

  /// Default text when no reason is provided
  ///
  /// In en, this message translates to:
  /// **'No reason provided'**
  String get noReasonProvided;

  /// Set status button text
  ///
  /// In en, this message translates to:
  /// **'Set Status'**
  String get setStatus;

  /// Start date label
  ///
  /// In en, this message translates to:
  /// **'Start Date'**
  String get startDate;

  /// End date label
  ///
  /// In en, this message translates to:
  /// **'End Date'**
  String get endDate;

  /// This week button text
  ///
  /// In en, this message translates to:
  /// **'This Week'**
  String get thisWeek;

  /// Next week button text
  ///
  /// In en, this message translates to:
  /// **'Next Week'**
  String get nextWeek;

  /// Apply button text
  ///
  /// In en, this message translates to:
  /// **'Apply'**
  String get apply;

  /// Scroll dates instruction text
  ///
  /// In en, this message translates to:
  /// **'Scroll Dates'**
  String get scrollDates;

  /// Date range picker dialog title
  ///
  /// In en, this message translates to:
  /// **'Select Date Range'**
  String get selectDateRange;

  /// Edit availability dialog title
  ///
  /// In en, this message translates to:
  /// **'Edit Availability'**
  String get editAvailability;

  /// Maintenance status
  ///
  /// In en, this message translates to:
  /// **'Maintenance'**
  String get maintenance;

  /// Broken status
  ///
  /// In en, this message translates to:
  /// **'Broken'**
  String get broken;

  /// Borrowed status
  ///
  /// In en, this message translates to:
  /// **'Borrowed'**
  String get borrowed;

  /// Date range label
  ///
  /// In en, this message translates to:
  /// **'Date Range'**
  String get dateRange;

  /// Status legend title
  ///
  /// In en, this message translates to:
  /// **'Status Legend'**
  String get statusLegend;

  /// Plate number prefix
  ///
  /// In en, this message translates to:
  /// **'Plate'**
  String get plate;

  /// Trip label with colon
  ///
  /// In en, this message translates to:
  /// **'Trip:'**
  String get tripLabel;

  /// Label for driver
  ///
  /// In en, this message translates to:
  /// **'Driver: '**
  String get driverLabel;

  /// Notes label in trip details
  ///
  /// In en, this message translates to:
  /// **'Notes'**
  String get notesLabel;

  /// Date/Time column header
  ///
  /// In en, this message translates to:
  /// **'Date/Time'**
  String get dateTime;

  /// Pagination text showing current page of total pages
  ///
  /// In en, this message translates to:
  /// **'Page {current} of {total}'**
  String pageXofY(int current, int total);

  /// Single car view option
  ///
  /// In en, this message translates to:
  /// **'Single Car'**
  String get singleCar;

  /// All cars view option
  ///
  /// In en, this message translates to:
  /// **'All Cars'**
  String get allCars;

  /// Date picker placeholder text
  ///
  /// In en, this message translates to:
  /// **'Select Date'**
  String get selectDate;

  /// Search field hint text
  ///
  /// In en, this message translates to:
  /// **'Search by driver, trip, notes...'**
  String get searchByDriverTripNotes;

  /// Message when no odometer history records are found
  ///
  /// In en, this message translates to:
  /// **'No odometer history records found'**
  String get noOdometerHistoryRecordsFound;

  /// Date label with colon
  ///
  /// In en, this message translates to:
  /// **'Date:'**
  String get dateColon;

  /// Start date label with colon
  ///
  /// In en, this message translates to:
  /// **'Start Date:'**
  String get startDateColon;

  /// End date label with colon
  ///
  /// In en, this message translates to:
  /// **'End Date:'**
  String get endDateColon;

  /// Driver information section title
  ///
  /// In en, this message translates to:
  /// **'Driver Information'**
  String get driverInformation;

  /// Check-in information section title
  ///
  /// In en, this message translates to:
  /// **'Check-In Information'**
  String get checkInInformation;

  /// Complete check-in button text
  ///
  /// In en, this message translates to:
  /// **'Complete Check-In'**
  String get completeCheckIn;

  /// Logging out progress message
  ///
  /// In en, this message translates to:
  /// **'Logging out...'**
  String get loggingOut;

  /// Check-in instruction text
  ///
  /// In en, this message translates to:
  /// **'Click the button below to complete your check-in.'**
  String get clickButtonCompleteCheckIn;

  /// Driver check-out screen title
  ///
  /// In en, this message translates to:
  /// **'Driver Check-Out'**
  String get driverCheckOut;

  /// Confirm check-out dialog title
  ///
  /// In en, this message translates to:
  /// **'Confirm Check-Out'**
  String get confirmCheckOut;

  /// Complete check-out button text
  ///
  /// In en, this message translates to:
  /// **'Complete Check-Out'**
  String get completeCheckOut;

  /// Check-out success message
  ///
  /// In en, this message translates to:
  /// **'Check-out successful! Redirecting to check-in...'**
  String get checkOutSuccessfulRedirecting;

  /// Check-out confirmation dialog message
  ///
  /// In en, this message translates to:
  /// **'After checking out, you will be redirected to the check-in screen. Do you want to proceed?'**
  String get afterCheckOutRedirectMessage;

  /// Check-out information section title
  ///
  /// In en, this message translates to:
  /// **'Check-Out Information'**
  String get checkOutInformation;

  /// Check-out instruction text
  ///
  /// In en, this message translates to:
  /// **'Click the button below to complete your check-out.'**
  String get clickButtonCompleteCheckOut;

  /// Filter trips section title
  ///
  /// In en, this message translates to:
  /// **'Filter Trips'**
  String get filterTrips;

  /// Search field hint text
  ///
  /// In en, this message translates to:
  /// **'Search by trip code, location, requestor, rating, comments, etc.'**
  String get searchByTripCode;

  /// Clear all filters button text
  ///
  /// In en, this message translates to:
  /// **'Clear All Filters'**
  String get clearAllFilters;

  /// Comments column header
  ///
  /// In en, this message translates to:
  /// **'Comments'**
  String get comments;

  /// Check-in time label
  ///
  /// In en, this message translates to:
  /// **'Check-In Time'**
  String get checkInTime;

  /// Driver code label with colon
  ///
  /// In en, this message translates to:
  /// **'Driver Code:'**
  String get driverCodeColon;

  /// Check-in time label with colon
  ///
  /// In en, this message translates to:
  /// **'Check-In Time:'**
  String get checkInTimeColon;

  /// Proceed button text
  ///
  /// In en, this message translates to:
  /// **'Proceed'**
  String get proceed;

  /// Trip label with colon
  ///
  /// In en, this message translates to:
  /// **'Trip:'**
  String get tripColon;

  /// From label with colon
  ///
  /// In en, this message translates to:
  /// **'From:'**
  String get fromColon;

  /// To label with colon
  ///
  /// In en, this message translates to:
  /// **'To:'**
  String get toColon;

  /// Driver label with colon
  ///
  /// In en, this message translates to:
  /// **'Driver:'**
  String get driverColon;

  /// Preposition for time display
  ///
  /// In en, this message translates to:
  /// **'at'**
  String get atTime;

  /// View button text
  ///
  /// In en, this message translates to:
  /// **'View'**
  String get view;

  /// View rejection reason button text
  ///
  /// In en, this message translates to:
  /// **'View Rejection'**
  String get viewRejection;

  /// Date filter field label
  ///
  /// In en, this message translates to:
  /// **'Filter by Date'**
  String get filterByDateLabel;

  /// Search field label
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get searchLabel;

  /// Driver unassigned status
  ///
  /// In en, this message translates to:
  /// **'Unassigned'**
  String get unassigned;

  /// Rejection reason dialog title
  ///
  /// In en, this message translates to:
  /// **'Rejection Reason'**
  String get rejectionReasonTitle;

  /// Title for trip details dialog
  ///
  /// In en, this message translates to:
  /// **'Trip {tripCode} Details'**
  String tripDetailsTitle(String tripCode);

  /// Finish trip confirmation dialog title
  ///
  /// In en, this message translates to:
  /// **'Finish Trip Confirmation'**
  String get finishTripConfirmTitle;

  /// Finish trip confirmation message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to mark this trip as finished?'**
  String get finishTripConfirmMessage;

  /// Mark as finished button text
  ///
  /// In en, this message translates to:
  /// **'Mark as Finished'**
  String get markAsFinished;

  /// Message shown in rejection reason dialog
  ///
  /// In en, this message translates to:
  /// **'This trip was rejected for the following reason:'**
  String get tripRejectedMessage;

  /// Trip details dialog title prefix
  ///
  /// In en, this message translates to:
  /// **'Trip Details:'**
  String get tripDetailsColon;

  /// From destination label in trip card
  ///
  /// In en, this message translates to:
  /// **'From:'**
  String get fromLabel;

  /// To destination label in trip card
  ///
  /// In en, this message translates to:
  /// **'To:'**
  String get toLabel;

  /// Date label in trip card
  ///
  /// In en, this message translates to:
  /// **'Date:'**
  String get dateLabel;

  /// Time label in trip details
  ///
  /// In en, this message translates to:
  /// **'Time'**
  String get timeLabel;

  /// Label for tracking status
  ///
  /// In en, this message translates to:
  /// **'Status:'**
  String get statusLabel;

  /// Driver ID label
  ///
  /// In en, this message translates to:
  /// **'Driver ID'**
  String get driverIdLabel;

  /// Rating label in trip details
  ///
  /// In en, this message translates to:
  /// **'Rating'**
  String get ratingLabel;

  /// Passengers section label
  ///
  /// In en, this message translates to:
  /// **'Passengers'**
  String get passengersLabel;

  /// Cargo section label
  ///
  /// In en, this message translates to:
  /// **'Cargo'**
  String get cargoLabel;

  /// None value label
  ///
  /// In en, this message translates to:
  /// **'None'**
  String get noneLabel;

  /// Trip code column header
  ///
  /// In en, this message translates to:
  /// **'Trip Code'**
  String get tripCodeHeader;

  /// From destination column header
  ///
  /// In en, this message translates to:
  /// **'From'**
  String get fromHeader;

  /// To destination column header
  ///
  /// In en, this message translates to:
  /// **'To'**
  String get toHeader;

  /// Date column header
  ///
  /// In en, this message translates to:
  /// **'Date'**
  String get dateHeader;

  /// Time column header
  ///
  /// In en, this message translates to:
  /// **'Time'**
  String get timeHeader;

  /// Status column header
  ///
  /// In en, this message translates to:
  /// **'Status'**
  String get statusHeader;

  /// Driver column header
  ///
  /// In en, this message translates to:
  /// **'Driver'**
  String get driverHeader;

  /// Actions column header
  ///
  /// In en, this message translates to:
  /// **'Actions'**
  String get actionsHeader;

  /// View rejection reason button text
  ///
  /// In en, this message translates to:
  /// **'View Rejection Reason'**
  String get viewRejectionReason;

  /// Success message when trip is completed
  ///
  /// In en, this message translates to:
  /// **'Trip has been marked as completed'**
  String get tripMarkedAsCompleted;

  /// Success message for trip data refresh
  ///
  /// In en, this message translates to:
  /// **'Trip data refreshed successfully'**
  String get tripDataRefreshedSuccessfully;

  /// Round trip checkbox label
  ///
  /// In en, this message translates to:
  /// **'Round Trip'**
  String get roundTrip;

  /// Waiting trip checkbox label
  ///
  /// In en, this message translates to:
  /// **'Waiting'**
  String get waiting;

  /// Outbound journey section title
  ///
  /// In en, this message translates to:
  /// **'Outbound Journey'**
  String get outboundJourney;

  /// Return journey section title
  ///
  /// In en, this message translates to:
  /// **'Return Journey'**
  String get returnJourney;

  /// Waiting time section title
  ///
  /// In en, this message translates to:
  /// **'Waiting Time'**
  String get waitingTime;

  /// Return date field label
  ///
  /// In en, this message translates to:
  /// **'Return Date'**
  String get returnDate;

  /// Return time field label
  ///
  /// In en, this message translates to:
  /// **'Return Time'**
  String get returnTime;

  /// Submit trip button text
  ///
  /// In en, this message translates to:
  /// **'Submit Trip'**
  String get submitTrip;

  /// Custom from destination field label
  ///
  /// In en, this message translates to:
  /// **'Custom From'**
  String get customFrom;

  /// Custom to destination field label
  ///
  /// In en, this message translates to:
  /// **'Custom To'**
  String get customTo;

  /// Others option in destination dropdown
  ///
  /// In en, this message translates to:
  /// **'Others'**
  String get others;

  /// Validation message for return date
  ///
  /// In en, this message translates to:
  /// **'Return date cannot be earlier than outbound date'**
  String get returnDateCannotBeEarlier;

  /// Validation message for outbound date
  ///
  /// In en, this message translates to:
  /// **'Outbound date cannot be later than return date'**
  String get outboundDateCannotBeLater;

  /// Info message when return date is adjusted
  ///
  /// In en, this message translates to:
  /// **'Return date was automatically adjusted to match outbound date'**
  String get returnDateAutomaticallyAdjusted;

  /// Info message when return time is adjusted
  ///
  /// In en, this message translates to:
  /// **'Return time was automatically adjusted to be after outbound time'**
  String get returnTimeAutomaticallyAdjusted;

  /// Info message when both return date and time are adjusted
  ///
  /// In en, this message translates to:
  /// **'Return date and time were automatically adjusted to be after outbound date and time'**
  String get returnDateTimeAutomaticallyAdjusted;

  /// Validation message for return time
  ///
  /// In en, this message translates to:
  /// **'Return time must be after outbound time on the same day'**
  String get returnTimeMustBeAfterOutbound;

  /// Validation message for outbound time
  ///
  /// In en, this message translates to:
  /// **'Outbound time must be before return time on the same day'**
  String get outboundTimeMustBeBeforeReturn;

  /// Validation message for outbound datetime
  ///
  /// In en, this message translates to:
  /// **'Outbound date and time must be before return date and time'**
  String get outboundDateTimeMustBeBeforeReturn;

  /// Validation message for return datetime
  ///
  /// In en, this message translates to:
  /// **'Return date and time must be after outbound date and time'**
  String get returnDateTimeMustBeAfterOutbound;

  /// Warning message for invalid return datetime
  ///
  /// In en, this message translates to:
  /// **'Warning: Return date and time must be after outbound date and time. Please adjust your selection.'**
  String get returnTimeMustBeAfterOutboundWarning;

  /// Validation message for outbound time on same day
  ///
  /// In en, this message translates to:
  /// **'Outbound time must be before return time on the same day'**
  String get outboundTimeMustBeBeforeReturnOnSameDay;

  /// Validation message for return destinations
  ///
  /// In en, this message translates to:
  /// **'Return From and To destinations cannot be the same'**
  String get returnFromAndToCannotBeSame;

  /// Validation message for return custom destinations
  ///
  /// In en, this message translates to:
  /// **'Return From and To custom destinations cannot be the same'**
  String get returnFromAndToCustomCannotBeSame;

  /// Validation message for custom return to destination
  ///
  /// In en, this message translates to:
  /// **'Please enter custom return to destination'**
  String get pleaseEnterCustomReturnTo;

  /// Validation message for outbound date and time
  ///
  /// In en, this message translates to:
  /// **'Please select outbound date and time'**
  String get pleaseSelectOutboundDateTime;

  /// Validation message for return date and time
  ///
  /// In en, this message translates to:
  /// **'Please select return date and time'**
  String get pleaseSelectReturnDateTime;

  /// Validation message for date and time
  ///
  /// In en, this message translates to:
  /// **'Please select date and time'**
  String get pleaseSelectDateTime;

  /// Validation message for invalid date/time format
  ///
  /// In en, this message translates to:
  /// **'Invalid date or time format'**
  String get invalidDateTimeFormat;

  /// Validation message for custom from destination
  ///
  /// In en, this message translates to:
  /// **'Please enter custom from'**
  String get pleaseEnterCustomFrom;

  /// Validation message for custom to destination
  ///
  /// In en, this message translates to:
  /// **'Please enter custom to'**
  String get pleaseEnterCustomTo;

  /// Hint text for notes field
  ///
  /// In en, this message translates to:
  /// **'Enter any notes about the trip (optional)'**
  String get enterNotesOptional;

  /// Photo section label
  ///
  /// In en, this message translates to:
  /// **'Photo:'**
  String get photo;

  /// Text asking user to provide rejection reason
  ///
  /// In en, this message translates to:
  /// **'Please provide a reason for rejecting this trip:'**
  String get pleaseProvideRejectionReason;

  /// Take photo button text
  ///
  /// In en, this message translates to:
  /// **'Take Photo'**
  String get takePhoto;

  /// Instructions for adding notes and photo
  ///
  /// In en, this message translates to:
  /// **'Add notes and/or photo (optional):'**
  String get addNotesPhotoOptional;

  /// Hint text for rating comment field
  ///
  /// In en, this message translates to:
  /// **'Share your experience (optional)'**
  String get shareYourExperienceOptional;

  /// Confirm trip completion dialog title
  ///
  /// In en, this message translates to:
  /// **'Confirm Trip Completion'**
  String get confirmTripCompletion;

  /// Confirm trip completion message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to mark this trip as completed?'**
  String get areYouSureMarkTripCompleted;

  /// Time label with colon
  ///
  /// In en, this message translates to:
  /// **'Time:'**
  String get timeColon;

  /// Notes label with colon
  ///
  /// In en, this message translates to:
  /// **'Notes:'**
  String get notesColon;

  /// Dashboard section title for trip status overview
  ///
  /// In en, this message translates to:
  /// **'Trip Requests by Status'**
  String get tripRequestsByStatus;

  /// Description for REQUEST status
  ///
  /// In en, this message translates to:
  /// **'New trip requests'**
  String get newTripRequests;

  /// Description for EDIT ON REVIEW status
  ///
  /// In en, this message translates to:
  /// **'Trip edit requests pending approval'**
  String get tripEditRequestsPendingApproval;

  /// Description for CANCEL ON REVIEW status
  ///
  /// In en, this message translates to:
  /// **'Trip cancellation requests pending approval'**
  String get tripCancellationRequestsPendingApproval;

  /// Description for ASSIGN TO DRIVER status
  ///
  /// In en, this message translates to:
  /// **'Trips ready to be assigned to drivers'**
  String get tripsReadyToBeAssignedToDrivers;

  /// Description for ASSIGN TO ONLINE TAXI status
  ///
  /// In en, this message translates to:
  /// **'Trips assigned to online taxi'**
  String get tripsAssignedToOnlineTaxi;

  /// Description for DRIVER REJECTED status
  ///
  /// In en, this message translates to:
  /// **'Trips rejected by drivers'**
  String get tripsRejectedByDrivers;

  /// Description for DRIVER CONFIRMATION status
  ///
  /// In en, this message translates to:
  /// **'Trips confirmed by drivers'**
  String get tripsConfirmedByDrivers;

  /// Description for TRIP IN PROGRESS status
  ///
  /// In en, this message translates to:
  /// **'Trips currently in progress'**
  String get tripsCurrentlyInProgress;

  /// Description for WAITING FOR RATING status
  ///
  /// In en, this message translates to:
  /// **'Completed trips waiting for rating'**
  String get completedTripsWaitingForRating;

  /// Explanation shown in assign driver dialog about status changes
  ///
  /// In en, this message translates to:
  /// **'Assigning a driver will change the trip status to \"ASSIGN TO DRIVER\". Selecting \"ONLINE\" will change the status to \"ASSIGN TO ONLINE TAXI\".'**
  String get assignDriverStatusExplanation;

  /// Label showing trip date information
  ///
  /// In en, this message translates to:
  /// **'Trip Date: {date} ({formattedDate})'**
  String tripDateLabel(String date, String formattedDate);

  /// Message shown while looking up trip ID
  ///
  /// In en, this message translates to:
  /// **'Looking up trip ID for {tripCode}...'**
  String lookingUpTripId(String tripCode);

  /// Error message when trip ID is missing
  ///
  /// In en, this message translates to:
  /// **'Cannot assign driver: Trip ID is missing for trip code {tripCode}'**
  String cannotAssignDriverMissingId(String tripCode);

  /// Label showing current date and time
  ///
  /// In en, this message translates to:
  /// **'Current DateTime: {dateTime}'**
  String currentDateTime(String dateTime);

  /// Warning message shown before deleting a trip
  ///
  /// In en, this message translates to:
  /// **'This will change the trip status to DELETED. This action cannot be undone.\n\nAre you sure you want to continue?'**
  String get deleteTripWarning;

  /// Error message when trip deletion fails
  ///
  /// In en, this message translates to:
  /// **'Error deleting trip: {error}'**
  String errorDeletingTrip(String error);

  /// Label showing driver rejected status
  ///
  /// In en, this message translates to:
  /// **'Status: DRIVER REJECTED'**
  String get statusDriverRejected;

  /// Message shown when trip was rejected with a reason
  ///
  /// In en, this message translates to:
  /// **'This trip was rejected by the driver for the following reason:'**
  String get tripRejectedWithReason;

  /// Message shown when trip was rejected without a reason
  ///
  /// In en, this message translates to:
  /// **'This trip was rejected by the driver, but no reason was provided.'**
  String get tripRejectedNoReason;

  /// Label showing trip code
  ///
  /// In en, this message translates to:
  /// **'Trip Code: {tripCode}'**
  String tripCodeLabel(String tripCode);

  /// Legend label for trip duration in schedule table
  ///
  /// In en, this message translates to:
  /// **'Trip Duration'**
  String get tripDurationLegend;

  /// Message shown when no trip requests exist for selected date
  ///
  /// In en, this message translates to:
  /// **'No trip requests for this date'**
  String get noTripRequestsForDate;

  /// Label showing currently selected date
  ///
  /// In en, this message translates to:
  /// **'Selected date: {date}'**
  String selectedDateLabel(String date);

  /// Title for time periods legend
  ///
  /// In en, this message translates to:
  /// **'Time Periods:'**
  String get timePeriodsTitle;

  /// Label for night time period
  ///
  /// In en, this message translates to:
  /// **'Night (00:00-06:00)'**
  String get nightPeriod;

  /// Label for morning time period
  ///
  /// In en, this message translates to:
  /// **'Morning (06:00-12:00)'**
  String get morningPeriod;

  /// Label for afternoon time period
  ///
  /// In en, this message translates to:
  /// **'Afternoon (12:00-18:00)'**
  String get afternoonPeriod;

  /// Label for evening time period
  ///
  /// In en, this message translates to:
  /// **'Evening (18:00-24:00)'**
  String get eveningPeriod;

  /// Message when no cars are available for a date
  ///
  /// In en, this message translates to:
  /// **'No cars are available for this date. All cars may be marked as UNAVAILABLE.'**
  String get noCarsAvailableMessage;

  /// Message when no drivers are available for a date
  ///
  /// In en, this message translates to:
  /// **'No drivers are available for this date. All drivers may be marked as UNAVAILABLE.'**
  String get noDriversAvailableMessage;

  /// Title for driver rejected trips section
  ///
  /// In en, this message translates to:
  /// **'DRIVER REJECTED Trips'**
  String get driverRejectedTripsTitle;

  /// Label for time slot navigation
  ///
  /// In en, this message translates to:
  /// **'Navigate time slots:'**
  String get navigateTimeSlots;

  /// Legend label
  ///
  /// In en, this message translates to:
  /// **'Legend:'**
  String get legend;

  /// Legend label for trips assigned to driver with driver info
  ///
  /// In en, this message translates to:
  /// **'ASSIGN TO DRIVER (with driver)'**
  String get assignToDriverWithDriver;

  /// Legend label for trips assigned to driver without driver info
  ///
  /// In en, this message translates to:
  /// **'ASSIGN TO DRIVER (without driver)'**
  String get assignToDriverWithoutDriver;

  /// Legend label for trip duration without driver info
  ///
  /// In en, this message translates to:
  /// **'ASSIGN TO DRIVER (without driver) Duration'**
  String get assignToDriverWithoutDriverDuration;

  /// Tooltip for refresh trip requests button
  ///
  /// In en, this message translates to:
  /// **'Refresh trip requests'**
  String get refreshTripRequestsTooltip;

  /// Tooltip for refresh driver rejected trips button
  ///
  /// In en, this message translates to:
  /// **'Refresh driver rejected trips'**
  String get refreshDriverRejectedTripsTooltip;

  /// Title for driver rejected trips section with date
  ///
  /// In en, this message translates to:
  /// **'Driver Rejected Trips for {date}'**
  String driverRejectedTripsForDate(String date);

  /// Message when no driver rejected trips are found for a specific date
  ///
  /// In en, this message translates to:
  /// **'No driver rejected trips for this date'**
  String get noDriverRejectedTripsForDate;

  /// Error message when fetching online taxi trips fails
  ///
  /// In en, this message translates to:
  /// **'Error fetching online taxi trips list: {error}'**
  String errorFetchingOnlineTaxiTrips(String error);

  /// Error message when fetching trips fails
  ///
  /// In en, this message translates to:
  /// **'Error fetching trips: {error}'**
  String errorFetchingTrips(String error);

  /// Error message when fetching driver rejected trips fails
  ///
  /// In en, this message translates to:
  /// **'Error fetching driver rejected trips: {error}'**
  String errorFetchingDriverRejectedTrips(String error);

  /// Error message when fetching trips fails with status codes
  ///
  /// In en, this message translates to:
  /// **'Error fetching trips: {status1} / {status2}'**
  String errorFetchingTripsWithStatus(String status1, String status2);

  /// Error message when fetching trips fails with status codes
  ///
  /// In en, this message translates to:
  /// **'Error fetching trips: {statusCodes}'**
  String errorFetchingTripsWithStatusCodes(String statusCodes);

  /// Message shown when a trip status is changed to online taxi
  ///
  /// In en, this message translates to:
  /// **'Trip {tripCode} status changed to ASSIGN TO ONLINE TAXI.'**
  String tripStatusChangedToOnlineTaxi(String tripCode);

  /// Error message when assigning a driver fails
  ///
  /// In en, this message translates to:
  /// **'Error assigning driver: {error}'**
  String errorAssigningDriver(String error);

  /// Message shown when a trip has been deleted
  ///
  /// In en, this message translates to:
  /// **'Trip {tripCode} has been deleted.'**
  String tripHasBeenDeleted(String tripCode);

  /// Header for trip requests section showing specific date
  ///
  /// In en, this message translates to:
  /// **'Trip Requests for {date}'**
  String tripRequestsForDate(String date);

  /// Count of trip requests with proper singular/plural form
  ///
  /// In en, this message translates to:
  /// **'{count} {count, plural, =1{request} other{requests}}'**
  String requestCount(int count);

  /// Count of trips with proper singular/plural form
  ///
  /// In en, this message translates to:
  /// **'{count} {count, plural, =1{trip} other{trips}}'**
  String tripCount(int count);

  /// Count of trips with proper singular/plural form
  ///
  /// In en, this message translates to:
  /// **'{count} {count, plural, =1{Trip} other{Trips}}'**
  String tripsCount(int count);

  /// Label for online driver
  ///
  /// In en, this message translates to:
  /// **'Online: '**
  String get onlineLabel;

  /// Duration label in trip card
  ///
  /// In en, this message translates to:
  /// **'Duration:'**
  String get durationLabel;

  /// Text displayed for online taxi
  ///
  /// In en, this message translates to:
  /// **'TAXI'**
  String get taxiText;

  /// Text displayed when no car is assigned
  ///
  /// In en, this message translates to:
  /// **'No Car Assigned'**
  String get noCarAssigned;

  /// Status label in trip details
  ///
  /// In en, this message translates to:
  /// **'Status'**
  String get statusDetailLabel;

  /// Start time label in trip card
  ///
  /// In en, this message translates to:
  /// **'Start Time:'**
  String get startTimeLabel;

  /// End time label in trip card
  ///
  /// In en, this message translates to:
  /// **'End Time:'**
  String get endTimeLabel;

  /// Duration label in trip details
  ///
  /// In en, this message translates to:
  /// **'Duration'**
  String get durationDetailLabel;

  /// Driver label in trip details
  ///
  /// In en, this message translates to:
  /// **'Driver'**
  String get driverDetailLabel;

  /// Driver code label in trip details
  ///
  /// In en, this message translates to:
  /// **'Driver Code'**
  String get driverCodeDetailLabel;

  /// From destination label in trip details
  ///
  /// In en, this message translates to:
  /// **'From'**
  String get fromDetailLabel;

  /// To destination label in trip details
  ///
  /// In en, this message translates to:
  /// **'To'**
  String get toDetailLabel;

  /// Section title for additional information
  ///
  /// In en, this message translates to:
  /// **'Additional Information'**
  String get additionalInformation;

  /// Trip ID label in trip details
  ///
  /// In en, this message translates to:
  /// **'Trip ID'**
  String get tripIdLabel;

  /// Text displayed when trip ID is not assigned
  ///
  /// In en, this message translates to:
  /// **'Not assigned'**
  String get notAssigned;

  /// Section title for car information
  ///
  /// In en, this message translates to:
  /// **'Car Information'**
  String get carInformation;

  /// Car ID label in trip details
  ///
  /// In en, this message translates to:
  /// **'Car ID'**
  String get carIdLabel;

  /// Car code label in trip details
  ///
  /// In en, this message translates to:
  /// **'Car Code'**
  String get carCodeDetailLabel;

  /// Plate number label in trip details
  ///
  /// In en, this message translates to:
  /// **'Plate Number'**
  String get plateNumberLabel;

  /// Passengers section title with colon
  ///
  /// In en, this message translates to:
  /// **'Passengers:'**
  String get passengersSection;

  /// Cargo section title with colon
  ///
  /// In en, this message translates to:
  /// **'Cargo:'**
  String get cargoSection;

  /// Button text to return trip to REQUEST status
  ///
  /// In en, this message translates to:
  /// **'Return to REQUEST'**
  String get returnToRequest;

  /// Delete trip button text
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get deleteTrip;

  /// Delete trip confirmation dialog title
  ///
  /// In en, this message translates to:
  /// **'Delete Trip {tripCode}?'**
  String deleteTripTitle(String tripCode);

  /// Delete trip confirmation dialog content
  ///
  /// In en, this message translates to:
  /// **'This will change the trip status to DELETED. This action cannot be undone.\\n\\nAre you sure you want to continue?'**
  String get deleteTripWarningMessage;

  /// Minutes time unit
  ///
  /// In en, this message translates to:
  /// **'minutes'**
  String get minutesUnit;

  /// Text displayed for online taxi service
  ///
  /// In en, this message translates to:
  /// **'ONLINE TAXI'**
  String get onlineTaxiText;

  /// Text displayed when no car is assigned
  ///
  /// In en, this message translates to:
  /// **'No Car'**
  String get noCar;

  /// Text displayed when no plate number is available
  ///
  /// In en, this message translates to:
  /// **'No Plate'**
  String get noPlate;

  /// Message shown while updating trip duration
  ///
  /// In en, this message translates to:
  /// **'Updating trip duration...'**
  String get updatingTripDuration;

  /// Error message when updating duration fails
  ///
  /// In en, this message translates to:
  /// **'Error updating duration: {error}'**
  String errorUpdatingDuration(String error);

  /// Message shown while updating trip status
  ///
  /// In en, this message translates to:
  /// **'Updating trip status...'**
  String get updatingTripStatus;

  /// Message shown when trip status changed to REQUEST
  ///
  /// In en, this message translates to:
  /// **'Trip {tripCode} status changed to REQUEST.'**
  String tripStatusChangedToRequest(String tripCode);

  /// Message shown while refreshing data
  ///
  /// In en, this message translates to:
  /// **'Refreshing all data...'**
  String get refreshingAllData;

  /// Generic error message
  ///
  /// In en, this message translates to:
  /// **'Error: {error}'**
  String errorGeneric(String error);

  /// Message shown while deleting trip
  ///
  /// In en, this message translates to:
  /// **'Deleting trip...'**
  String get deletingTripMessage;

  /// Error message when deleting trip fails
  ///
  /// In en, this message translates to:
  /// **'Error deleting trip: {error}'**
  String errorDeletingTripMessage(String error);

  /// Error message when driver code is missing
  ///
  /// In en, this message translates to:
  /// **'Error: Driver code is missing. Please try again.'**
  String get errorDriverCodeMissing;

  /// Back to Base button label
  ///
  /// In en, this message translates to:
  /// **'Back to Base'**
  String get backToBase;

  /// Check Out button label
  ///
  /// In en, this message translates to:
  /// **'Check Out'**
  String get checkOut;

  /// Count of trips assigned to driver
  ///
  /// In en, this message translates to:
  /// **'Assign To Driver: {count}'**
  String assignToDriverCount(int count);

  /// Status dropdown placeholder text
  ///
  /// In en, this message translates to:
  /// **'Select Status'**
  String get selectStatus;

  /// Trip number display format
  ///
  /// In en, this message translates to:
  /// **'Trip #{tripCode}'**
  String tripCodeNumber(String tripCode);

  /// Duration in minutes format
  ///
  /// In en, this message translates to:
  /// **'{minutes} minutes'**
  String durationInMinutes(int minutes);

  /// Requestor label in trip card
  ///
  /// In en, this message translates to:
  /// **'Requestor:'**
  String get requestorLabel;

  /// Rejection reason label in trip card
  ///
  /// In en, this message translates to:
  /// **'Rejection:'**
  String get rejectionLabel;

  /// Tooltip for active location tracking
  ///
  /// In en, this message translates to:
  /// **'Location tracking is active (driver has active trips)'**
  String get locationTrackingActive;

  /// Tooltip for tracking ready state
  ///
  /// In en, this message translates to:
  /// **'Tracking ready (driver has active trips)'**
  String get trackingReady;

  /// Tooltip when tracking is disabled due to no active trips
  ///
  /// In en, this message translates to:
  /// **'Tracking disabled (no active trips)'**
  String get trackingDisabledNoActiveTrips;

  /// Tooltip when automatic tracking is disabled
  ///
  /// In en, this message translates to:
  /// **'Automatic tracking is disabled'**
  String get automaticTrackingDisabled;

  /// Error message when Back to Base trip creation fails
  ///
  /// In en, this message translates to:
  /// **'Failed to create Back to Base trip: {statusCode}'**
  String failedToCreateBackToBaseTrip(int statusCode);

  /// Error message when Back to Base trip creation throws an exception
  ///
  /// In en, this message translates to:
  /// **'Error creating Back to Base trip: {error}'**
  String errorCreatingBackToBaseTrip(String error);

  /// Title of the location tracking status dialog
  ///
  /// In en, this message translates to:
  /// **'Location Tracking Status'**
  String get locationTrackingStatus;

  /// Label for auto tracking status
  ///
  /// In en, this message translates to:
  /// **'Auto Tracking:'**
  String get autoTrackingLabel;

  /// Enabled status value
  ///
  /// In en, this message translates to:
  /// **'ENABLED'**
  String get enabled;

  /// Disabled status value
  ///
  /// In en, this message translates to:
  /// **'DISABLED'**
  String get disabled;

  /// Label for active trips status
  ///
  /// In en, this message translates to:
  /// **'Active Trips:'**
  String get activeTripsLabel;

  /// Label for current time display
  ///
  /// In en, this message translates to:
  /// **'Current Time:'**
  String get currentTimeLabel;

  /// Label for today's date display
  ///
  /// In en, this message translates to:
  /// **'Today:'**
  String get todayLabel;

  /// Label for location coordinates
  ///
  /// In en, this message translates to:
  /// **'Location:'**
  String get locationLabel;

  /// Label for last update time
  ///
  /// In en, this message translates to:
  /// **'Last Update:'**
  String get lastUpdateLabel;

  /// Time format for seconds ago
  ///
  /// In en, this message translates to:
  /// **'{seconds} seconds ago'**
  String secondsAgo(int seconds);

  /// Time format for minutes ago
  ///
  /// In en, this message translates to:
  /// **'{minutes} minutes ago'**
  String minutesAgo(int minutes);

  /// Explanation text about how location tracking works
  ///
  /// In en, this message translates to:
  /// **'Your location is tracked only when you have active trips (TRIP IN PROGRESS or BACK TO BASE):\n• Tracking is automatically enabled when you have active trips\n• Tracking is automatically disabled when you have no active trips\n• Location updates are sent every minute when tracking is active'**
  String get trackingExplanation;

  /// Trip code column header
  ///
  /// In en, this message translates to:
  /// **'Trip Code'**
  String get tripCodeColumn;

  /// Requestor column header
  ///
  /// In en, this message translates to:
  /// **'Requestor'**
  String get requestorColumn;

  /// From destination column header
  ///
  /// In en, this message translates to:
  /// **'From'**
  String get fromColumn;

  /// To destination column header
  ///
  /// In en, this message translates to:
  /// **'To'**
  String get toColumn;

  /// Date column header
  ///
  /// In en, this message translates to:
  /// **'Date'**
  String get dateColumn;

  /// Time column header
  ///
  /// In en, this message translates to:
  /// **'Time'**
  String get timeColumn;

  /// Driver column header
  ///
  /// In en, this message translates to:
  /// **'Driver'**
  String get driverColumn;

  /// Status column header
  ///
  /// In en, this message translates to:
  /// **'Status'**
  String get statusColumn;

  /// Rating column header
  ///
  /// In en, this message translates to:
  /// **'Rating'**
  String get ratingColumn;

  /// Page indicator in pagination
  ///
  /// In en, this message translates to:
  /// **'Page {current} of {total}'**
  String pageOf(int current, int total);

  /// Text for items in items per page dropdown
  ///
  /// In en, this message translates to:
  /// **'items'**
  String get itemsText;

  /// Title for notification when trip is assigned to online taxi
  ///
  /// In en, this message translates to:
  /// **'Trip Assigned to Online Taxi'**
  String get tripAssignedToOnlineTaxiTitle;

  /// Message for notification when trip is assigned to online taxi
  ///
  /// In en, this message translates to:
  /// **'Your trip {tripCode} from {fromDestination} to {toDestination} has been assigned to an online taxi service.'**
  String tripAssignedToOnlineTaxiMessage(String tripCode, String fromDestination, String toDestination);

  /// Success message when trip is assigned to online taxi and notification is sent
  ///
  /// In en, this message translates to:
  /// **'Trip {tripCode} assigned to online taxi and notification sent.'**
  String tripAssignedToOnlineTaxiSuccess(String tripCode);

  /// Notification title when a trip is assigned
  ///
  /// In en, this message translates to:
  /// **'Trip Assigned: {tripCode}'**
  String notificationTripAssigned(String tripCode);

  /// Notification title for driver when assigned to a trip
  ///
  /// In en, this message translates to:
  /// **'New Trip Assignment: {tripCode}'**
  String notificationNewTripAssignment(String tripCode);

  /// Notification title when a trip is approved
  ///
  /// In en, this message translates to:
  /// **'Trip Approved: {tripCode}'**
  String notificationTripApproved(String tripCode);

  /// Notification title when a trip is rejected
  ///
  /// In en, this message translates to:
  /// **'Trip Rejected: {tripCode}'**
  String notificationTripRejected(String tripCode);

  /// Notification title when a trip is completed
  ///
  /// In en, this message translates to:
  /// **'Trip Completed: {tripCode}'**
  String notificationTripCompleted(String tripCode);

  /// Notification title when trip status changes
  ///
  /// In en, this message translates to:
  /// **'Trip Status Update: {tripCode}'**
  String notificationTripStatusUpdate(String tripCode);

  /// Notification title when a trip is rated
  ///
  /// In en, this message translates to:
  /// **'Trip Rated: {tripCode}'**
  String notificationTripRated(String tripCode);

  /// Notification title when trip is returned to request status
  ///
  /// In en, this message translates to:
  /// **'Trip Returned to Request: {tripCode}'**
  String notificationTripReturnedToRequest(String tripCode);

  /// Notification title when a trip is deleted
  ///
  /// In en, this message translates to:
  /// **'Trip Deleted: {tripCode}'**
  String notificationTripDeleted(String tripCode);

  /// Notification title when back to base trip is completed
  ///
  /// In en, this message translates to:
  /// **'Back to Base Trip Completed: {tripCode}'**
  String notificationBackToBaseTripCompleted(String tripCode);

  /// Notification title when trip is assigned to online taxi
  ///
  /// In en, this message translates to:
  /// **'Trip Assigned to Online Taxi: {tripCode}'**
  String notificationTripAssignedToOnlineTaxi(String tripCode);

  /// Notification title when online taxi trip is completed
  ///
  /// In en, this message translates to:
  /// **'Online Taxi Trip Completed: {tripCode}'**
  String notificationOnlineTaxiTripCompleted(String tripCode);

  /// Notification title when back to base trip is created
  ///
  /// In en, this message translates to:
  /// **'Back to Base Trip Created: {tripCode}'**
  String notificationBackToBaseTripCreated(String tripCode);

  /// Notification title when trip edit changes are approved
  ///
  /// In en, this message translates to:
  /// **'Trip Changes Approved: {tripCode}'**
  String notificationTripChangesApproved(String tripCode);

  /// No description provided for @notificationNewTripRequest.
  ///
  /// In en, this message translates to:
  /// **'New Trip Request: {tripCode}'**
  String notificationNewTripRequest(String tripCode);

  /// Notification title when a trip edit is requested
  ///
  /// In en, this message translates to:
  /// **'Trip Edit Request: {tripCode}'**
  String notificationTripEditRequest(String tripCode);

  /// Notification title when a trip cancellation is requested
  ///
  /// In en, this message translates to:
  /// **'Trip Cancellation Request: {tripCode}'**
  String notificationTripCancellationRequest(String tripCode);

  /// Notification message when trip is assigned to driver for requestor
  ///
  /// In en, this message translates to:
  /// **'Your trip {tripCode} has been assigned to a driver and is scheduled for {date} at {time}.'**
  String notificationMessageTripAssignedToDriver(String tripCode, String date, String time);

  /// Notification message when driver is assigned to trip
  ///
  /// In en, this message translates to:
  /// **'You have been assigned to trip {tripCode} from {fromDestination} to {toDestination} on {date} at {time}.'**
  String notificationMessageDriverAssignedToTrip(String tripCode, String fromDestination, String toDestination, String date, String time);

  /// Notification message when trip is approved
  ///
  /// In en, this message translates to:
  /// **'Your trip {tripCode} from {fromDestination} to {toDestination} has been approved.'**
  String notificationMessageTripApproved(String tripCode, String fromDestination, String toDestination);

  /// Notification message when trip is rejected
  ///
  /// In en, this message translates to:
  /// **'Your trip {tripCode} has been rejected. Reason: {rejectionReason}.'**
  String notificationMessageTripRejected(String tripCode, String rejectionReason);

  /// Notification message when trip is rejected without reason
  ///
  /// In en, this message translates to:
  /// **'Your trip {tripCode} has been rejected. Reason: No reason provided.'**
  String notificationMessageTripRejectedNoReason(String tripCode);

  /// Notification message when trip status changes for requestor
  ///
  /// In en, this message translates to:
  /// **'Your trip {tripCode} status has changed from {oldStatus} to {newStatus}.'**
  String notificationMessageTripStatusChanged(String tripCode, String oldStatus, String newStatus);

  /// Notification message when trip status changes for driver
  ///
  /// In en, this message translates to:
  /// **'Trip {tripCode} status has changed from {oldStatus} to {newStatus}.'**
  String notificationMessageDriverTripStatusChanged(String tripCode, String oldStatus, String newStatus);

  /// Notification message when trip is rated
  ///
  /// In en, this message translates to:
  /// **'Your trip {tripCode} has been rated {rating} stars by the passenger. {ratingMessage}'**
  String notificationMessageTripRated(String tripCode, String rating, String ratingMessage);

  /// Notification message when trip is rated without message
  ///
  /// In en, this message translates to:
  /// **'Your trip {tripCode} has been rated {rating} stars by the passenger.'**
  String notificationMessageTripRatedNoMessage(String tripCode, String rating);

  /// Notification message when trip is returned to request for requestor
  ///
  /// In en, this message translates to:
  /// **'Your trip {tripCode} from {fromDestination} to {toDestination} has been returned to REQUEST status by a trip manager.'**
  String notificationMessageTripReturnedToRequestor(String tripCode, String fromDestination, String toDestination);

  /// Notification message when trip is returned to request for driver
  ///
  /// In en, this message translates to:
  /// **'Trip {tripCode} from {fromDestination} to {toDestination} has been returned to REQUEST status by a trip manager.'**
  String notificationMessageTripReturnedToDriver(String tripCode, String fromDestination, String toDestination);

  /// Notification message when trip is deleted for requestor
  ///
  /// In en, this message translates to:
  /// **'Your trip {tripCode} from {fromDestination} to {toDestination} has been deleted by a trip manager.'**
  String notificationMessageTripDeletedRequestor(String tripCode, String fromDestination, String toDestination);

  /// Notification message when trip is deleted for driver
  ///
  /// In en, this message translates to:
  /// **'Trip {tripCode} from {fromDestination} to {toDestination} has been deleted by a trip manager.'**
  String notificationMessageTripDeletedDriver(String tripCode, String fromDestination, String toDestination);

  /// Notification message when trip is completed for requestor
  ///
  /// In en, this message translates to:
  /// **'Your trip {tripCode} has been completed successfully. Thank you for using our service!'**
  String notificationMessageTripCompletedRequestor(String tripCode);

  /// Notification message when trip is completed for managers
  ///
  /// In en, this message translates to:
  /// **'Trip {tripCode} has been marked as completed by the driver.'**
  String notificationMessageTripCompletedManager(String tripCode);

  /// Notification message when back to base trip is completed for requestor
  ///
  /// In en, this message translates to:
  /// **'Your back to base trip {tripCode} has been completed successfully.'**
  String notificationMessageBackToBaseTripCompletedRequestor(String tripCode);

  /// Notification message when back to base trip is completed for managers
  ///
  /// In en, this message translates to:
  /// **'Back to base trip {tripCode} has been completed.'**
  String notificationMessageBackToBaseTripCompletedManager(String tripCode);

  /// Notification message when trip is assigned to online taxi
  ///
  /// In en, this message translates to:
  /// **'Your trip {tripCode} from {fromDestination} to {toDestination} has been assigned to an online taxi service.'**
  String notificationMessageTripAssignedToOnlineTaxi(String tripCode, String fromDestination, String toDestination);

  /// Notification message when online taxi trip is completed for requestor
  ///
  /// In en, this message translates to:
  /// **'Your online taxi trip {tripCode} has been successfully completed. Thank you for using our service!'**
  String notificationMessageOnlineTaxiTripCompletedRequestor(String tripCode);

  /// Notification message when online taxi trip is completed for managers
  ///
  /// In en, this message translates to:
  /// **'Online taxi trip {tripCode} has been marked as completed by the requestor.'**
  String notificationMessageOnlineTaxiTripCompletedManager(String tripCode);

  /// Notification message when back to base trip is created
  ///
  /// In en, this message translates to:
  /// **'{driverName} has created a \"Back to Base\" trip {tripCode} from {fromDestination} to {toDestination} on {date} at {time}.'**
  String notificationMessageBackToBaseTripCreated(String driverName, String tripCode, String fromDestination, String toDestination, String date, String time);

  /// Notification message when trip edit changes are approved
  ///
  /// In en, this message translates to:
  /// **'Your changes to trip {tripCode} from {fromDestination} to {toDestination} have been approved. The trip is now back to REQUEST status.'**
  String notificationMessageTripChangesApproved(String tripCode, String fromDestination, String toDestination);

  /// Notification message for new trip request to managers
  ///
  /// In en, this message translates to:
  /// **'{requestorName} has requested a new trip {tripCode} from {fromDestination} to {toDestination} on {date} at {time}.'**
  String notificationMessageNewTripRequest(String requestorName, String tripCode, String fromDestination, String toDestination, String date, String time);

  /// Notification message when a trip edit is requested
  ///
  /// In en, this message translates to:
  /// **'{userName} has requested changes to trip {tripCode} from {fromDestination} to {toDestination} on {date} at {time}.'**
  String notificationMessageTripEditRequest(String userName, String tripCode, String fromDestination, String toDestination, String date, String time);

  /// Notification message when a trip cancellation is requested
  ///
  /// In en, this message translates to:
  /// **'{userName} has requested to cancel trip {tripCode} from {fromDestination} to {toDestination} on {date} at {time}.'**
  String notificationMessageTripCancellationRequest(String userName, String tripCode, String fromDestination, String toDestination, String date, String time);

  /// No description provided for @notificationTripStarted.
  ///
  /// In en, this message translates to:
  /// **'Trip Started: {tripCode}'**
  String notificationTripStarted(String tripCode);

  /// No description provided for @notificationDriverConfirmedTrip.
  ///
  /// In en, this message translates to:
  /// **'Driver Confirmed Trip: {tripCode}'**
  String notificationDriverConfirmedTrip(String tripCode);

  /// No description provided for @notificationMessageTripStarted.
  ///
  /// In en, this message translates to:
  /// **'Trip {tripCode} has been started by the driver.'**
  String notificationMessageTripStarted(String tripCode);

  /// No description provided for @notificationMessageDriverConfirmedTrip.
  ///
  /// In en, this message translates to:
  /// **'{driverName} has confirmed trip {tripCode}.'**
  String notificationMessageDriverConfirmedTrip(String driverName, String tripCode);

  /// No description provided for @notificationDriverRejectedTrip.
  ///
  /// In en, this message translates to:
  /// **'Driver Rejected Trip: {tripCode}'**
  String notificationDriverRejectedTrip(String tripCode);

  /// No description provided for @notificationMessageDriverRejectedTrip.
  ///
  /// In en, this message translates to:
  /// **'{driverName} has rejected trip {tripCode}. Reason: {rejectionReason}.'**
  String notificationMessageDriverRejectedTrip(String driverName, String tripCode, String rejectionReason);

  /// No description provided for @notificationMessageDriverRejectedTripGeneric.
  ///
  /// In en, this message translates to:
  /// **'The driver has rejected trip {tripCode}. Reason: {rejectionReason}.'**
  String notificationMessageDriverRejectedTripGeneric(String tripCode, String rejectionReason);

  /// No description provided for @notificationTripChangesRejected.
  ///
  /// In en, this message translates to:
  /// **'Trip Changes Rejected: {tripCode}'**
  String notificationTripChangesRejected(String tripCode);

  /// No description provided for @notificationMessageTripChangesRejected.
  ///
  /// In en, this message translates to:
  /// **'The requested changes to trip {tripCode} have been rejected. Reason: {rejectionReason}.'**
  String notificationMessageTripChangesRejected(String tripCode, String rejectionReason);

  /// No description provided for @notificationTripCancellationRejected.
  ///
  /// In en, this message translates to:
  /// **'Trip Cancellation Rejected: {tripCode}'**
  String notificationTripCancellationRejected(String tripCode);

  /// No description provided for @notificationMessageTripCancellationRejected.
  ///
  /// In en, this message translates to:
  /// **'The cancellation request for trip {tripCode} has been rejected. Reason: {rejectionReason}.'**
  String notificationMessageTripCancellationRejected(String tripCode, String rejectionReason);
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['en', 'id'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en': return AppLocalizationsEn();
    case 'id': return AppLocalizationsId();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}
