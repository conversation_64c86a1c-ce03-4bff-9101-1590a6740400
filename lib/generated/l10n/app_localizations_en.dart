// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'FLEEX';

  @override
  String get welcomeBack => 'Welcome Back';

  @override
  String get signInToContinue => 'Sign in to continue to FLEEX';

  @override
  String get username => 'Username';

  @override
  String get password => 'Password';

  @override
  String get login => 'Login';

  @override
  String get pleaseEnterUsername => 'Please enter your username';

  @override
  String get pleaseEnterPassword => 'Please enter password';

  @override
  String get or => 'OR';

  @override
  String get signInWith => 'Sign in with';

  @override
  String get signInWithMicrosoft => 'Sign in with Microsoft';

  @override
  String get forgotPassword => 'Forgot Password?';

  @override
  String get resetPassword => 'Reset Password';

  @override
  String get goBack => 'Go Back';

  @override
  String get newPassword => 'New Password';

  @override
  String get confirmNewPassword => 'Confirm New Password';

  @override
  String get pleaseEnterNewPassword => 'Please enter new password';

  @override
  String get pleaseConfirmNewPassword => 'Please confirm new password';

  @override
  String get passwordMustBeAtLeast6Characters => 'Password must be at least 6 characters';

  @override
  String get passwordsDoNotMatch => 'Passwords do not match';

  @override
  String get passwordResetSuccessful => 'Password reset successful.';

  @override
  String get dashboard => 'Dashboard';

  @override
  String get requestTrip => 'Request Trip';

  @override
  String get tripHistory => 'Trip History';

  @override
  String get settings => 'Settings';

  @override
  String get logout => 'Logout';

  @override
  String get search => 'Search';

  @override
  String get date => 'Date';

  @override
  String get time => 'Time';

  @override
  String get from => 'From:';

  @override
  String get to => 'To:';

  @override
  String get destination => 'Destination';

  @override
  String get notes => 'Notes';

  @override
  String get save => 'Save';

  @override
  String get cancel => 'Cancel';

  @override
  String get edit => 'Edit';

  @override
  String get delete => 'Delete';

  @override
  String get add => 'Add';

  @override
  String get required => 'Required';

  @override
  String get optional => 'Optional';

  @override
  String get loading => 'Loading...';

  @override
  String get error => 'Error';

  @override
  String get success => 'Success';

  @override
  String get tripCode => 'Trip Code';

  @override
  String get status => 'Status';

  @override
  String get driver => 'Driver';

  @override
  String get car => 'Car';

  @override
  String get passenger => 'Passenger';

  @override
  String get passengers => 'Passengers';

  @override
  String get duration => 'Duration';

  @override
  String get actions => 'Actions';

  @override
  String get filterByDate => 'Filter by Date';

  @override
  String get filterByStatus => 'Filter by Status';

  @override
  String get allRecords => 'All Records';

  @override
  String get clearFilter => 'Clear Filter';

  @override
  String get refresh => 'Refresh';

  @override
  String get noDataFound => 'No data found';

  @override
  String get pleaseEnterDate => 'Please enter date';

  @override
  String get pleaseEnterTime => 'Please enter time';

  @override
  String get pleaseSelectFromDestination => 'Please select from destination';

  @override
  String get pleaseSelectToDestination => 'Please select to destination';

  @override
  String get addTrip => 'Add Trip';

  @override
  String get editTrip => 'Edit Trip';

  @override
  String get requestorName => 'Requestor Name';

  @override
  String get customLocation => 'Custom Location';

  @override
  String get selectOrigin => 'Select origin';

  @override
  String get selectDestination => 'Select destination';

  @override
  String get carCode => 'Car Code';

  @override
  String get manufacturer => 'Manufacturer';

  @override
  String get model => 'Model';

  @override
  String get plateNumber => 'Plate Number';

  @override
  String get odometer => 'Odometer';

  @override
  String get color => 'Color';

  @override
  String get type => 'Type';

  @override
  String get driverCode => 'Driver Code';

  @override
  String get driverName => 'Driver Name';

  @override
  String get initial => 'Initial';

  @override
  String get address => 'Address';

  @override
  String get coordinates => 'Coordinates';

  @override
  String get hours => 'Hours';

  @override
  String get minutes => 'Minutes';

  @override
  String get available => 'Available';

  @override
  String get unavailable => 'Unavailable';

  @override
  String get pending => 'Pending';

  @override
  String get approved => 'Approved';

  @override
  String get inProgress => 'In Progress';

  @override
  String get completed => 'Completed';

  @override
  String get cancelled => 'Cancelled';

  @override
  String get confirmDelete => 'Confirm Delete';

  @override
  String get areYouSureDelete => 'Are you sure you want to delete this item?';

  @override
  String get yes => 'YES';

  @override
  String get no => 'NO';

  @override
  String get dataRefreshedSuccessfully => 'Data refreshed successfully';

  @override
  String get failedToLoadData => 'Failed to load data';

  @override
  String get selectLanguage => 'Language';

  @override
  String get chooseLanguage => 'Choose your preferred language:';

  @override
  String get saveLanguage => 'Save Language';

  @override
  String get languageChanged => 'Language changed successfully';

  @override
  String get languageChangeNote => 'Note: The app will need to be restarted for some changes to take effect.';

  @override
  String get language => 'Language';

  @override
  String get commonSettings => 'Common Settings';

  @override
  String get driverSetting => 'Driver Setting';

  @override
  String get carSetting => 'Car Setting';

  @override
  String get cargoSetting => 'Cargo Setting';

  @override
  String get destinationSetting => 'Destination Setting';

  @override
  String get backToHome => 'Back to Home';

  @override
  String get welcome => 'Welcome';

  @override
  String get refreshNow => 'Refresh now';

  @override
  String get youMustBeLoggedIn => 'You must be logged in';

  @override
  String get failedToLoadTripStatusCounts => 'Failed to load trip status counts';

  @override
  String get errorLoading => 'Error loading data';

  @override
  String get tripApprovals => 'Trip Approvals';

  @override
  String get dashboardTitle => 'Trip Manager Dashboard';

  @override
  String get requestorDashboardTitle => 'Requestor Dashboard';

  @override
  String get tripMonitoring => 'Trip Monitoring';

  @override
  String get driverTracking => 'Driver Tracking';

  @override
  String areYouSureDeleteDriver(String name) {
    return 'Are you sure you want to remove $name as a driver?';
  }

  @override
  String get failedToDeleteDriver => 'Failed to delete driver';

  @override
  String anErrorOccurred(String error) {
    return 'An error occurred: $error';
  }

  @override
  String get driverSettingsTitle => 'Driver Settings';

  @override
  String get carSettingsTitle => 'Car Settings';

  @override
  String get cargoSettingsTitle => 'Cargo Settings';

  @override
  String get destinationSettingsTitle => 'Destination Settings';

  @override
  String get searchCars => 'Search Cars';

  @override
  String get searchCarsHint => 'Enter car code, manufacturer, or model';

  @override
  String get noCarsFound => 'No cars found';

  @override
  String get noMatchingCarsFound => 'No matching cars found';

  @override
  String failedToLoadCarsWithCode(String statusCode) {
    return 'Failed to load cars: $statusCode';
  }

  @override
  String get carDeletedSuccessfully => 'Car deleted successfully';

  @override
  String get failedToDeleteCar => 'Failed to delete car';

  @override
  String confirmDeleteCar(String carCode) {
    return 'Are you sure you want to delete car $carCode?';
  }

  @override
  String get searchCargos => 'Search cargos';

  @override
  String get searchCargosHint => 'Enter cargo code or name';

  @override
  String get noCargosFound => 'No cargos found';

  @override
  String get noMatchingCargosFound => 'No matching cargos found';

  @override
  String failedToLoadCargos(String statusCode) {
    return 'Failed to load cargos: $statusCode';
  }

  @override
  String get noCargoDataFound => 'No cargo data found';

  @override
  String get cargoDeletedSuccessfully => 'Cargo deleted successfully';

  @override
  String get failedToDeleteCargo => 'Failed to delete cargo';

  @override
  String confirmDeleteCargo(String cargoCode) {
    return 'Are you sure you want to delete cargo $cargoCode?';
  }

  @override
  String get cargoCode => 'Cargo Code';

  @override
  String get cargoName => 'Cargo Name';

  @override
  String get searchDestinations => 'Search destinations';

  @override
  String get searchDestinationsHint => 'Enter destination code, name, address, or initial';

  @override
  String get noDestinationsFound => 'No destinations found';

  @override
  String get noMatchingDestinationsFound => 'No matching destinations found';

  @override
  String failedToLoadDestinations(String statusCode) {
    return 'Failed to load destinations: $statusCode';
  }

  @override
  String get destinationDeletedSuccessfully => 'Destination deleted successfully';

  @override
  String get failedToDeleteDestination => 'Failed to delete destination';

  @override
  String confirmDeleteDestination(String destinationCode) {
    return 'Are you sure you want to delete destination $destinationCode?';
  }

  @override
  String get destinationCode => 'Destination Code';

  @override
  String get active => 'ACTIVE';

  @override
  String get inactive => 'INACTIVE';

  @override
  String get tripApprovalsTitle => 'Trip Approvals';

  @override
  String get noTripsForApproval => 'No trips for approval';

  @override
  String get failedToFetchTrips => 'Failed to fetch trips';

  @override
  String get requestedBy => 'Requested by';

  @override
  String get requestDate => 'Request Date';

  @override
  String get tripDate => 'Trip Date';

  @override
  String get purpose => 'Purpose';

  @override
  String get approve => 'Approve';

  @override
  String get reject => 'Reject';

  @override
  String get viewChanges => 'View Changes';

  @override
  String get details => 'Details';

  @override
  String get tripApprovedSuccessfully => 'Trip approved successfully';

  @override
  String get tripRejectedSuccessfully => 'Trip rejected successfully';

  @override
  String get failedToUpdateTrip => 'Failed to update trip';

  @override
  String get addDriver => 'Add Driver';

  @override
  String get searchDrivers => 'Search drivers...';

  @override
  String get noDriversFound => 'No drivers found';

  @override
  String get rateTripTitle => 'Rate Trip';

  @override
  String get howWouldYouRateTrip => 'How would you rate your trip?';

  @override
  String get additionalComments => 'Additional Comments';

  @override
  String get submitRating => 'Submit Rating';

  @override
  String get errorSubmittingRating => 'Error submitting rating';

  @override
  String get tripChangeApproval => 'Trip Change Approval';

  @override
  String get confirmApproval => 'Confirm Approval';

  @override
  String get confirmRejection => 'Confirm Rejection';

  @override
  String areYouSureApproveTrip(String tripCode) {
    return 'Are you sure you want to approve trip $tripCode? This will change the status to REQUEST.';
  }

  @override
  String areYouSureApproveChanges(String tripCode) {
    return 'Are you sure you want to approve the changes to trip $tripCode? This will change the status to REQUEST.';
  }

  @override
  String areYouSureApproveCancellation(String tripCode) {
    return 'Are you sure you want to approve the cancellation of trip $tripCode? This will change the status to DELETED.';
  }

  @override
  String areYouSureRejectTrip(String tripCode) {
    return 'Are you sure you want to reject trip $tripCode?';
  }

  @override
  String areYouSureRejectChanges(String tripCode) {
    return 'Are you sure you want to reject the changes to trip $tripCode? The trip will be restored to its original state before the edits were made.';
  }

  @override
  String areYouSureRejectCancellation(String tripCode) {
    return 'Are you sure you want to reject the cancellation of trip $tripCode? The trip will remain active.';
  }

  @override
  String get pleaseProvideReason => 'Please provide a reason for rejection:';

  @override
  String get enterRejectionReason => 'Enter reason for rejecting the trip';

  @override
  String get reasonIsRequired => 'Reason is required';

  @override
  String get requestor => 'Requestor';

  @override
  String get tripApprovedAndStatusChanged => 'Trip approved and status changed to REQUEST';

  @override
  String get failedToApproveTrip => 'Failed to approve trip';

  @override
  String get failedToRejectTrip => 'Failed to reject trip';

  @override
  String get failedToApproveChanges => 'Failed to approve trip changes';

  @override
  String get failedToApproveCancellation => 'Failed to approve trip cancellation';

  @override
  String get failedToRejectChanges => 'Failed to reject trip changes';

  @override
  String get failedToRejectCancellation => 'Failed to reject trip cancellation';

  @override
  String get tripChangesApprovedSuccessfully => 'Trip changes approved successfully. Status changed to REQUEST.';

  @override
  String get tripCancellationApprovedSuccessfully => 'Trip cancellation approved successfully. Status changed to DELETED.';

  @override
  String get tripChangesRejectedSuccessfully => 'Trip changes rejected successfully. Status changed to REQUEST.';

  @override
  String get tripCancellationRejectedSuccessfully => 'Trip cancellation rejected successfully. Status changed to REQUEST.';

  @override
  String get approveTrip => 'Approve Trip';

  @override
  String get approveChanges => 'Approve Changes';

  @override
  String get approveCancellation => 'Approve Cancellation';

  @override
  String get rejectTrip => 'Reject Trip';

  @override
  String get rejectChanges => 'Reject Changes';

  @override
  String get rejectCancellation => 'Reject Cancellation';

  @override
  String get noTripsFound => 'No trips found';

  @override
  String noTripsFoundForDate(String date) {
    return 'No trips found for date: $date';
  }

  @override
  String get tripMonitoringTitle => 'Trip Monitoring';

  @override
  String get trackDrivers => 'Track Drivers';

  @override
  String get allRequests => 'All Requests';

  @override
  String get refreshData => 'Refresh Data';

  @override
  String get swipeToChangeDate => 'Swipe to change date';

  @override
  String errorRefreshingData(Object error) {
    return 'Error refreshing data';
  }

  @override
  String get searchDriversHint => 'Enter driver code, initial, or name';

  @override
  String get noMatchingDriversFound => 'No matching drivers found';

  @override
  String get driverDeletedSuccessfully => 'Driver deleted successfully';

  @override
  String failedToLoadDrivers(String statusCode) {
    return 'Failed to load drivers: $statusCode';
  }

  @override
  String get driverCodeLabel => 'Driver Code';

  @override
  String get carLabel => 'Car:';

  @override
  String get saveChanges => 'Save Changes';

  @override
  String get waitingTrip => 'Waiting Trip';

  @override
  String get fromDestination => 'From Destination';

  @override
  String get toDestination => 'To Destination';

  @override
  String get notesOptional => 'Notes (optional)';

  @override
  String get selectPassengers => 'Select Passengers';

  @override
  String get selectCargos => 'Select Cargos';

  @override
  String get trip => 'Trip';

  @override
  String get cargos => 'Cargos';

  @override
  String get searchForDestinations => 'Search for destinations';

  @override
  String get customFromDestination => 'Custom From Destination';

  @override
  String get customToDestination => 'Custom To Destination';

  @override
  String get enterCustomLocation => 'Enter custom location';

  @override
  String get addAdditionalNotes => 'Add any additional notes for this trip';

  @override
  String get searchForPassengers => 'Search for passengers';

  @override
  String get chooseOneOrMorePassengers => 'Choose one or more passengers';

  @override
  String get searchForCargos => 'Search for cargos';

  @override
  String get chooseOneOrMoreCargos => 'Choose one or more cargos';

  @override
  String get rateTrips => 'Rate Trips';

  @override
  String get myTrips => 'My Trips';

  @override
  String get checkIn => 'Check-In';

  @override
  String get notifications => 'Notifications';

  @override
  String get admin => 'Admin';

  @override
  String get switchRole => 'Switch Role';

  @override
  String get currentRole => 'Current Role';

  @override
  String get confirmRoleSwitch => 'Confirm Role Switch';

  @override
  String switchRoleMessage(Object role) {
    return 'Are you sure you want to switch to $role? This will change your interface and available functions.';
  }

  @override
  String roleSwitchedSuccessfully(Object role) {
    return 'Successfully switched to $role';
  }

  @override
  String get now => 'Now';

  @override
  String get currentTime => 'Current Time';

  @override
  String get rating => 'Rating';

  @override
  String get cargo => 'Cargo';

  @override
  String get clearStatusFilter => 'Clear Status Filter';

  @override
  String get specificDate => 'Specific Date';

  @override
  String get year => 'Year';

  @override
  String get month => 'Month';

  @override
  String get currentFilter => 'Current Filter';

  @override
  String get selectYear => 'Select Year';

  @override
  String get selectMonth => 'Select Month';

  @override
  String get searchTrips => 'Search trips (code, requestor, locations...)';

  @override
  String get odometerReading => 'Odometer Reading';

  @override
  String get searchTripsLong => 'Search trips (code, requestor, locations...)';

  @override
  String get itemsPerPage => 'Items per page';

  @override
  String get exportToExcel => 'Export to Excel';

  @override
  String get exporting => 'Exporting...';

  @override
  String get exportingToExcel => 'Exporting to Excel...';

  @override
  String get noTripsToExport => 'No trips to export';

  @override
  String tripHistoryExported(String fileName) {
    return 'Trip history exported as $fileName';
  }

  @override
  String tripHistoryExportedTo(String filePath) {
    return 'Trip history exported to: $filePath';
  }

  @override
  String exportFailed(String error) {
    return 'Export failed: $error';
  }

  @override
  String get noTripHistoryFound => 'No trip history found';

  @override
  String get noMatchingTripsFound => 'No matching trips found';

  @override
  String get superAdmin => 'Super Admin';

  @override
  String get tripManager => 'Trip Manager';

  @override
  String get user => 'User';

  @override
  String get locationTracking => 'Location Tracking';

  @override
  String get tripRating => 'Trip Rating';

  @override
  String get driverAvailabilityManagement => 'Driver Availability Management';

  @override
  String get carAvailabilityManagement => 'Car Availability Management';

  @override
  String get driverCheckInManagement => 'Driver Check-In Management';

  @override
  String get carOdometerHistory => 'Car Odometer History';

  @override
  String get driverCheckIn => 'Driver Check-In';

  @override
  String get myTripHistory => 'My Trip History';

  @override
  String get selectADriver => 'Select a driver';

  @override
  String get assign => 'Assign';

  @override
  String get viewReason => 'View Reason';

  @override
  String get close => 'Close';

  @override
  String get allTripRequests => 'All Trip Requests';

  @override
  String get rejectionReason => 'Rejection Reason';

  @override
  String get tripChanges => 'Trip Changes';

  @override
  String get confirmCancellation => 'Confirm Cancellation';

  @override
  String get tripApprovedMessage => 'Trip approved and status changed to REQUEST';

  @override
  String get pleaseSelectRating => 'Please select a rating';

  @override
  String get ratingSubmittedSuccessfully => 'Rating submitted successfully';

  @override
  String get failedToLoadTripDetails => 'Failed to load trip details';

  @override
  String get notRated => 'Not rated';

  @override
  String get previous => 'Previous';

  @override
  String get next => 'Next';

  @override
  String get noTripDataAvailable => 'No trip data available';

  @override
  String get pleaseSelectBothDestinations => 'Please select both destinations';

  @override
  String get destinationsCannotBeSame => 'From and To destinations cannot be the same';

  @override
  String get customDestinationsCannotBeSame => 'From and To custom destinations cannot be the same';

  @override
  String get pleaseEnterWaitingDuration => 'Please enter waiting duration';

  @override
  String get pleaseEnterCustomFromDestination => 'Please enter custom from destination';

  @override
  String get pleaseEnterCustomToDestination => 'Please enter custom to destination';

  @override
  String get failedToLoadAvailableCars => 'Failed to load available cars';

  @override
  String get requestTrips => 'Request Trips';

  @override
  String get otherTripStatuses => 'Other Trip Statuses';

  @override
  String get multipleTrips => 'Multiple Trips';

  @override
  String get tripDuration => 'Trip Duration:';

  @override
  String get noNotesProvided => 'No notes provided for this trip.';

  @override
  String get noNotifications => 'No notifications';

  @override
  String get authenticationFailed => 'Authentication failed';

  @override
  String get noTripDataAvailableRequestor => 'No trip data available';

  @override
  String get myTripRequests => 'My Trip Requests';

  @override
  String get editDriver => 'Edit Driver';

  @override
  String get failedToLoadCars => 'Failed to load available cars';

  @override
  String get failedToUpdateDriver => 'Failed to update driver';

  @override
  String get name => 'Name';

  @override
  String get nameWillBeUppercase => 'Name will be converted to uppercase';

  @override
  String get nameIsRequired => 'Name is required';

  @override
  String get initialHelperText => 'Must be exactly 3 characters (will be converted to uppercase)';

  @override
  String get initialIsRequired => 'Initial is required';

  @override
  String get initialMustBe3Characters => 'Initial must be exactly 3 characters';

  @override
  String get loadingCars => 'Loading cars...';

  @override
  String get selectACar => 'Select a car';

  @override
  String get searchForCar => 'Search for a car';

  @override
  String get pleaseSelectCar => 'Please select a car';

  @override
  String get newPasswordOptional => 'New Password (optional)';

  @override
  String get leaveEmptyToKeepPassword => 'Leave empty to keep current password';

  @override
  String get fromDestinationRequired => 'From destination is required';

  @override
  String get toDestinationRequired => 'To destination is required';

  @override
  String get dateIsRequired => 'Date is required';

  @override
  String get timeIsRequired => 'Time is required';

  @override
  String get pleaseEnterOdometerReading => 'Please enter odometer reading';

  @override
  String get odometerReadingRequired => 'Odometer reading is required';

  @override
  String get pleaseEnterValidNumber => 'Please enter a valid number';

  @override
  String get allDrivers => 'All Drivers';

  @override
  String get validationStatus => 'Validation Status';

  @override
  String get validated => 'Validated';

  @override
  String get notValidated => 'Not Validated';

  @override
  String get checkInValidatedSuccessfully => 'Check-in validated successfully';

  @override
  String get validationRemoved => 'Validation removed';

  @override
  String get reset => 'Reset';

  @override
  String get checkInDetails => 'Check-In Details';

  @override
  String get checkOutDetails => 'Check-Out Details';

  @override
  String get rateTrip => 'Rate Trip';

  @override
  String get rateThisTrip => 'Rate This Trip';

  @override
  String get cannotAdjustDurationNoTripId => 'Cannot adjust duration for this trip - no trip ID';

  @override
  String get cannotAdjustDuration => 'Cannot adjust duration for this trip';

  @override
  String failedToUpdateDuration(String statusCode) {
    return 'Failed to update duration: $statusCode';
  }

  @override
  String updatedDurationForTrip(String tripCode, String minutes) {
    return 'Updated duration for trip $tripCode to $minutes minutes';
  }

  @override
  String get rateYourTrips => 'Rate Your Trips';

  @override
  String tripCreatedSuccessfully(String tripCode) {
    return 'Trip ($tripCode) created successfully';
  }

  @override
  String roundTripCreatedSuccessfully(String outboundCode, String returnCode) {
    return 'Round trip created successfully: Outbound ($outboundCode), Return ($returnCode)';
  }

  @override
  String waitingTripCreatedSuccessfully(String tripCode) {
    return 'Waiting trip created successfully: ($tripCode)';
  }

  @override
  String tripUpdatedSuccessfully(String tripCode) {
    return 'Trip ($tripCode) updated successfully';
  }

  @override
  String get loadingTripDetails => 'Loading trip details...';

  @override
  String get tripDataIsLoading => 'Trip data is loading...';

  @override
  String get loadingAvailableDrivers => 'Loading available drivers...';

  @override
  String get checkinValidatedSuccessfully => 'Check-in validated successfully';

  @override
  String get backToBaseTripCreatedSuccessfully => 'Back to Base trip created successfully';

  @override
  String get failedToUpdateCarOdometer => 'Failed to update car odometer';

  @override
  String get errorLoadingData => 'Error loading data';

  @override
  String errorWithDetails(String details) {
    return 'Error: $details';
  }

  @override
  String get tripCompletedSuccessfully => 'Trip completed successfully';

  @override
  String get failedToCompleteTrip => 'Failed to complete the trip';

  @override
  String errorCompletingTrip(String error) {
    return 'Error completing trip: $error';
  }

  @override
  String get authenticationFailedInvalidCode => 'Authentication failed: Invalid or expired authentication code';

  @override
  String authenticationFailedWithError(String error) {
    return 'Authentication failed: $error';
  }

  @override
  String get invalidCredentials => 'Invalid username or password';

  @override
  String areYouSureCancelTrip(String tripCode) {
    return 'Are you sure you want to cancel trip $tripCode? This will submit a cancellation request for review.';
  }

  @override
  String get requestCancellation => 'Request Cancellation';

  @override
  String get tripCancellationRequestSubmitted => 'Trip cancellation request submitted for review';

  @override
  String get failedToSubmitCancellationRequest => 'Failed to submit trip cancellation request';

  @override
  String get update => 'Update';

  @override
  String assignDriverOrOnlineTaxi(String tripCode) {
    return 'Assign Driver or Online Taxi to Trip $tripCode';
  }

  @override
  String deleteTripConfirmation(String tripCode) {
    return 'Delete Trip $tripCode?';
  }

  @override
  String get retry => 'Retry';

  @override
  String driversCount(int count) {
    return 'Drivers: $count';
  }

  @override
  String get inactiveDriversInfo => 'Gray = Inactive > 10 min';

  @override
  String get updateFrequencyInfo => 'Updates every 5 min or 500m';

  @override
  String errorFetchingDrivers(String error) {
    return 'Error fetching drivers: $error';
  }

  @override
  String get unknown => 'Unknown';

  @override
  String get unknownDriver => 'Unknown Driver';

  @override
  String centeredOnDriver(String driverName) {
    return 'Centered on driver $driverName';
  }

  @override
  String driverTrips(int count) {
    return 'Driver Trips ($count)';
  }

  @override
  String get lastUpdated => 'Last Updated';

  @override
  String tripNumber(String current, String total) {
    return 'Trip $current of $total';
  }

  @override
  String get completeTrip => 'Complete Trip';

  @override
  String get allStatuses => 'All Statuses';

  @override
  String get clearFilters => 'Clear Filters';

  @override
  String get noTripsMatchFilters => 'No trips match the current filters';

  @override
  String get confirmTrip => 'Confirm Trip';

  @override
  String get startTrip => 'Start Trip';

  @override
  String get finishTrip => 'Finish Trip';

  @override
  String updateOdometerFor(String carCode) {
    return 'Update Odometer for $carCode';
  }

  @override
  String get firstTripOfDay => 'First trip of the day';

  @override
  String get enterNewOdometerReading => 'Enter new odometer reading';

  @override
  String get odometerReadingHelper => 'Must be greater than or equal to current reading';

  @override
  String newReadingMustBeAtLeast(String minimum) {
    return 'New reading must be at least $minimum';
  }

  @override
  String get addCar => 'Add Car';

  @override
  String get editCar => 'Edit Car';

  @override
  String get failedToGenerateCarCode => 'Failed to generate car code';

  @override
  String get pleaseEnterManufacturer => 'Please enter manufacturer';

  @override
  String get pleaseEnterModelName => 'Please enter model name';

  @override
  String get pleaseEnterCarType => 'Please enter car type';

  @override
  String get pleaseEnterPlateNumber => 'Please enter plate number';

  @override
  String get pleaseEnterColor => 'Please enter color';

  @override
  String get failedToAddCar => 'Failed to add car';

  @override
  String get addCargo => 'Add Cargo';

  @override
  String get editCargo => 'Edit Cargo';

  @override
  String get failedToGenerateCargoCode => 'Failed to generate cargo code';

  @override
  String get pleaseEnterCargoName => 'Please enter cargo name';

  @override
  String get failedToAddCargo => 'Failed to add cargo';

  @override
  String get submit => 'Submit';

  @override
  String get adding => 'Adding...';

  @override
  String get addDestination => 'Add Destination';

  @override
  String get editDestination => 'Edit Destination';

  @override
  String get failedToGenerateDestinationCode => 'Failed to generate destination code';

  @override
  String get pleaseEnterDestinationName => 'Please enter destination name';

  @override
  String get pleaseEnterAddress => 'Please enter address';

  @override
  String get pleaseEnterInitial => 'Please enter initial';

  @override
  String get failedToAddDestination => 'Failed to add destination';

  @override
  String get destinationName => 'Destination Name';

  @override
  String get durationHours => 'Duration (Hours)';

  @override
  String get durationMinutes => 'Duration (Minutes)';

  @override
  String get coordinatesLatLong => 'Coordinates (Latitude, Longitude)';

  @override
  String get coordinatesFormat => 'Format: lat,long (e.g., 12.3456,78.9012)';

  @override
  String get pleaseEnterCoordinates => 'Please enter coordinates';

  @override
  String get invalidHours => 'Invalid hours';

  @override
  String get minutesMustBe0To59 => 'Minutes must be 0-59';

  @override
  String get mustBe3Characters => 'Must be exactly 3 characters (will be converted to uppercase)';

  @override
  String get initialRequired => 'Initial is required';

  @override
  String get failedToGenerateDriverCode => 'Failed to generate driver code';

  @override
  String get pleaseEnterName => 'Please enter name';

  @override
  String get pleaseConfirmPassword => 'Please confirm password';

  @override
  String get generatingCode => 'Generating code...';

  @override
  String get nameUppercaseHelper => 'Name will be converted to uppercase';

  @override
  String get searchForACar => 'Search for a car';

  @override
  String get pleaseSelectACar => 'Please select a car';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get passwordMinLength => 'Password must be at least 6 characters';

  @override
  String get failedToAddDriver => 'Failed to add driver';

  @override
  String get updateCar => 'Update Car';

  @override
  String get updateCargo => 'Update Cargo';

  @override
  String get updating => 'Updating...';

  @override
  String get updateDestination => 'Update Destination';

  @override
  String get failedToUpdateCar => 'Failed to update car';

  @override
  String get failedToUpdateCargo => 'Failed to update cargo';

  @override
  String get failedToUpdateDestination => 'Failed to update destination';

  @override
  String get modelName => 'Model Name';

  @override
  String get onMobileDateRangeLimited => 'On mobile, date range is limited to 9 days maximum';

  @override
  String get unavailableDemoReason => 'Unavailable: Demo reason';

  @override
  String updatedDriverAvailability(String driverName, String date) {
    return 'Updated $driverName\'s availability for $date';
  }

  @override
  String editAvailabilityFor(String driverName, Object carCode) {
    return 'Edit Availability for $driverName';
  }

  @override
  String get tripNotes => 'Trip Notes:';

  @override
  String get assignTo => 'Assign to:';

  @override
  String get fetchingTrips => 'Fetching trips...';

  @override
  String get noTripsWithStatusRequest => 'No trips with status REQUEST found.';

  @override
  String get noTripsWithStatusDriverRejected => 'No trips with status DRIVER REJECTED found.';

  @override
  String get assigningDriver => 'Assigning driver...';

  @override
  String get deletingTrip => 'Deleting trip...';

  @override
  String get deleteAction => 'Delete';

  @override
  String get assignAction => 'Assign';

  @override
  String get selectDriverFirst => 'Select a driver first';

  @override
  String get selectCar => 'Select a car';

  @override
  String get completedStatus => 'COMPLETED';

  @override
  String get completedDuration => 'COMPLETED Duration';

  @override
  String get tripInProgress => 'TRIP IN PROGRESS';

  @override
  String get tripInProgressDuration => 'TRIP IN PROGRESS Duration';

  @override
  String get waitingForRating => 'WAITING FOR RATING';

  @override
  String get waitingForRatingDuration => 'WAITING FOR RATING Duration';

  @override
  String get assignToDriverDuration => 'ASSIGN TO DRIVER Duration';

  @override
  String foundRequestTrips(int count) {
    return 'Found $count request trips:';
  }

  @override
  String get noRequestTripsFound => 'No trips with status REQUEST found.';

  @override
  String foundDriverRejectedTrips(int count) {
    return 'Found $count driver rejected trips:';
  }

  @override
  String get noDriverRejectedTripsFound => 'No trips with status DRIVER REJECTED found.';

  @override
  String availabilityUpdatedFor(String driverName) {
    return 'Availability updated for $driverName';
  }

  @override
  String get sick => 'Sick';

  @override
  String get breakStatus => 'Break';

  @override
  String get onBreak => 'On Break';

  @override
  String get noReason => 'No Reason';

  @override
  String get noReasonProvided => 'No reason provided';

  @override
  String get setStatus => 'Set Status';

  @override
  String get startDate => 'Start Date';

  @override
  String get endDate => 'End Date';

  @override
  String get thisWeek => 'This Week';

  @override
  String get nextWeek => 'Next Week';

  @override
  String get apply => 'Apply';

  @override
  String get scrollDates => 'Scroll Dates';

  @override
  String get selectDateRange => 'Select Date Range';

  @override
  String get editAvailability => 'Edit Availability';

  @override
  String get maintenance => 'Maintenance';

  @override
  String get broken => 'Broken';

  @override
  String get borrowed => 'Borrowed';

  @override
  String get dateRange => 'Date Range';

  @override
  String get statusLegend => 'Status Legend';

  @override
  String get plate => 'Plate';

  @override
  String get tripLabel => 'Trip:';

  @override
  String get driverLabel => 'Driver: ';

  @override
  String get notesLabel => 'Notes';

  @override
  String get dateTime => 'Date/Time';

  @override
  String pageXofY(int current, int total) {
    return 'Page $current of $total';
  }

  @override
  String get singleCar => 'Single Car';

  @override
  String get allCars => 'All Cars';

  @override
  String get selectDate => 'Select Date';

  @override
  String get searchByDriverTripNotes => 'Search by driver, trip, notes...';

  @override
  String get noOdometerHistoryRecordsFound => 'No odometer history records found';

  @override
  String get dateColon => 'Date:';

  @override
  String get startDateColon => 'Start Date:';

  @override
  String get endDateColon => 'End Date:';

  @override
  String get driverInformation => 'Driver Information';

  @override
  String get checkInInformation => 'Check-In Information';

  @override
  String get completeCheckIn => 'Complete Check-In';

  @override
  String get loggingOut => 'Logging out...';

  @override
  String get clickButtonCompleteCheckIn => 'Click the button below to complete your check-in.';

  @override
  String get driverCheckOut => 'Driver Check-Out';

  @override
  String get confirmCheckOut => 'Confirm Check-Out';

  @override
  String get completeCheckOut => 'Complete Check-Out';

  @override
  String get checkOutSuccessfulRedirecting => 'Check-out successful! Redirecting to check-in...';

  @override
  String get afterCheckOutRedirectMessage => 'After checking out, you will be redirected to the check-in screen. Do you want to proceed?';

  @override
  String get checkOutInformation => 'Check-Out Information';

  @override
  String get clickButtonCompleteCheckOut => 'Click the button below to complete your check-out.';

  @override
  String get filterTrips => 'Filter Trips';

  @override
  String get searchByTripCode => 'Search by trip code, location, requestor, rating, comments, etc.';

  @override
  String get clearAllFilters => 'Clear All Filters';

  @override
  String get comments => 'Comments';

  @override
  String get checkInTime => 'Check-In Time';

  @override
  String get driverCodeColon => 'Driver Code:';

  @override
  String get checkInTimeColon => 'Check-In Time:';

  @override
  String get proceed => 'Proceed';

  @override
  String get tripColon => 'Trip:';

  @override
  String get fromColon => 'From:';

  @override
  String get toColon => 'To:';

  @override
  String get driverColon => 'Driver:';

  @override
  String get atTime => 'at';

  @override
  String get view => 'View';

  @override
  String get viewRejection => 'View Rejection';

  @override
  String get filterByDateLabel => 'Filter by Date';

  @override
  String get searchLabel => 'Search';

  @override
  String get unassigned => 'Unassigned';

  @override
  String get rejectionReasonTitle => 'Rejection Reason';

  @override
  String tripDetailsTitle(String tripCode) {
    return 'Trip $tripCode Details';
  }

  @override
  String get finishTripConfirmTitle => 'Finish Trip Confirmation';

  @override
  String get finishTripConfirmMessage => 'Are you sure you want to mark this trip as finished?';

  @override
  String get markAsFinished => 'Mark as Finished';

  @override
  String get tripRejectedMessage => 'This trip was rejected for the following reason:';

  @override
  String get tripDetailsColon => 'Trip Details:';

  @override
  String get fromLabel => 'From:';

  @override
  String get toLabel => 'To:';

  @override
  String get dateLabel => 'Date:';

  @override
  String get timeLabel => 'Time';

  @override
  String get statusLabel => 'Status:';

  @override
  String get driverIdLabel => 'Driver ID';

  @override
  String get ratingLabel => 'Rating';

  @override
  String get passengersLabel => 'Passengers';

  @override
  String get cargoLabel => 'Cargo';

  @override
  String get noneLabel => 'None';

  @override
  String get tripCodeHeader => 'Trip Code';

  @override
  String get fromHeader => 'From';

  @override
  String get toHeader => 'To';

  @override
  String get dateHeader => 'Date';

  @override
  String get timeHeader => 'Time';

  @override
  String get statusHeader => 'Status';

  @override
  String get driverHeader => 'Driver';

  @override
  String get actionsHeader => 'Actions';

  @override
  String get viewRejectionReason => 'View Rejection Reason';

  @override
  String get tripMarkedAsCompleted => 'Trip has been marked as completed';

  @override
  String get tripDataRefreshedSuccessfully => 'Trip data refreshed successfully';

  @override
  String get roundTrip => 'Round Trip';

  @override
  String get waiting => 'Waiting';

  @override
  String get outboundJourney => 'Outbound Journey';

  @override
  String get returnJourney => 'Return Journey';

  @override
  String get waitingTime => 'Waiting Time';

  @override
  String get returnDate => 'Return Date';

  @override
  String get returnTime => 'Return Time';

  @override
  String get submitTrip => 'Submit Trip';

  @override
  String get customFrom => 'Custom From';

  @override
  String get customTo => 'Custom To';

  @override
  String get others => 'Others';

  @override
  String get returnDateCannotBeEarlier => 'Return date cannot be earlier than outbound date';

  @override
  String get outboundDateCannotBeLater => 'Outbound date cannot be later than return date';

  @override
  String get returnDateAutomaticallyAdjusted => 'Return date was automatically adjusted to match outbound date';

  @override
  String get returnTimeAutomaticallyAdjusted => 'Return time was automatically adjusted to be after outbound time';

  @override
  String get returnDateTimeAutomaticallyAdjusted => 'Return date and time were automatically adjusted to be after outbound date and time';

  @override
  String get returnTimeMustBeAfterOutbound => 'Return time must be after outbound time on the same day';

  @override
  String get outboundTimeMustBeBeforeReturn => 'Outbound time must be before return time on the same day';

  @override
  String get outboundDateTimeMustBeBeforeReturn => 'Outbound date and time must be before return date and time';

  @override
  String get returnDateTimeMustBeAfterOutbound => 'Return date and time must be after outbound date and time';

  @override
  String get returnTimeMustBeAfterOutboundWarning => 'Warning: Return date and time must be after outbound date and time. Please adjust your selection.';

  @override
  String get outboundTimeMustBeBeforeReturnOnSameDay => 'Outbound time must be before return time on the same day';

  @override
  String get returnFromAndToCannotBeSame => 'Return From and To destinations cannot be the same';

  @override
  String get returnFromAndToCustomCannotBeSame => 'Return From and To custom destinations cannot be the same';

  @override
  String get pleaseEnterCustomReturnTo => 'Please enter custom return to destination';

  @override
  String get pleaseSelectOutboundDateTime => 'Please select outbound date and time';

  @override
  String get pleaseSelectReturnDateTime => 'Please select return date and time';

  @override
  String get pleaseSelectDateTime => 'Please select date and time';

  @override
  String get invalidDateTimeFormat => 'Invalid date or time format';

  @override
  String get pleaseEnterCustomFrom => 'Please enter custom from';

  @override
  String get pleaseEnterCustomTo => 'Please enter custom to';

  @override
  String get enterNotesOptional => 'Enter any notes about the trip (optional)';

  @override
  String get photo => 'Photo:';

  @override
  String get pleaseProvideRejectionReason => 'Please provide a reason for rejecting this trip:';

  @override
  String get takePhoto => 'Take Photo';

  @override
  String get addNotesPhotoOptional => 'Add notes and/or photo (optional):';

  @override
  String get shareYourExperienceOptional => 'Share your experience (optional)';

  @override
  String get confirmTripCompletion => 'Confirm Trip Completion';

  @override
  String get areYouSureMarkTripCompleted => 'Are you sure you want to mark this trip as completed?';

  @override
  String get timeColon => 'Time:';

  @override
  String get notesColon => 'Notes:';

  @override
  String get tripRequestsByStatus => 'Trip Requests by Status';

  @override
  String get newTripRequests => 'New trip requests';

  @override
  String get tripEditRequestsPendingApproval => 'Trip edit requests pending approval';

  @override
  String get tripCancellationRequestsPendingApproval => 'Trip cancellation requests pending approval';

  @override
  String get tripsReadyToBeAssignedToDrivers => 'Trips ready to be assigned to drivers';

  @override
  String get tripsAssignedToOnlineTaxi => 'Trips assigned to online taxi';

  @override
  String get tripsRejectedByDrivers => 'Trips rejected by drivers';

  @override
  String get tripsConfirmedByDrivers => 'Trips confirmed by drivers';

  @override
  String get tripsCurrentlyInProgress => 'Trips currently in progress';

  @override
  String get completedTripsWaitingForRating => 'Completed trips waiting for rating';

  @override
  String get assignDriverStatusExplanation => 'Assigning a driver will change the trip status to \"ASSIGN TO DRIVER\". Selecting \"ONLINE\" will change the status to \"ASSIGN TO ONLINE TAXI\".';

  @override
  String tripDateLabel(String date, String formattedDate) {
    return 'Trip Date: $date ($formattedDate)';
  }

  @override
  String lookingUpTripId(String tripCode) {
    return 'Looking up trip ID for $tripCode...';
  }

  @override
  String cannotAssignDriverMissingId(String tripCode) {
    return 'Cannot assign driver: Trip ID is missing for trip code $tripCode';
  }

  @override
  String currentDateTime(String dateTime) {
    return 'Current DateTime: $dateTime';
  }

  @override
  String get deleteTripWarning => 'This will change the trip status to DELETED. This action cannot be undone.\n\nAre you sure you want to continue?';

  @override
  String errorDeletingTrip(String error) {
    return 'Error deleting trip: $error';
  }

  @override
  String get statusDriverRejected => 'Status: DRIVER REJECTED';

  @override
  String get tripRejectedWithReason => 'This trip was rejected by the driver for the following reason:';

  @override
  String get tripRejectedNoReason => 'This trip was rejected by the driver, but no reason was provided.';

  @override
  String tripCodeLabel(String tripCode) {
    return 'Trip Code: $tripCode';
  }

  @override
  String get tripDurationLegend => 'Trip Duration';

  @override
  String get noTripRequestsForDate => 'No trip requests for this date';

  @override
  String selectedDateLabel(String date) {
    return 'Selected date: $date';
  }

  @override
  String get timePeriodsTitle => 'Time Periods:';

  @override
  String get nightPeriod => 'Night (00:00-06:00)';

  @override
  String get morningPeriod => 'Morning (06:00-12:00)';

  @override
  String get afternoonPeriod => 'Afternoon (12:00-18:00)';

  @override
  String get eveningPeriod => 'Evening (18:00-24:00)';

  @override
  String get noCarsAvailableMessage => 'No cars are available for this date. All cars may be marked as UNAVAILABLE.';

  @override
  String get noDriversAvailableMessage => 'No drivers are available for this date. All drivers may be marked as UNAVAILABLE.';

  @override
  String get driverRejectedTripsTitle => 'DRIVER REJECTED Trips';

  @override
  String get navigateTimeSlots => 'Navigate time slots:';

  @override
  String get legend => 'Legend:';

  @override
  String get assignToDriverWithDriver => 'ASSIGN TO DRIVER (with driver)';

  @override
  String get assignToDriverWithoutDriver => 'ASSIGN TO DRIVER (without driver)';

  @override
  String get assignToDriverWithoutDriverDuration => 'ASSIGN TO DRIVER (without driver) Duration';

  @override
  String get refreshTripRequestsTooltip => 'Refresh trip requests';

  @override
  String get refreshDriverRejectedTripsTooltip => 'Refresh driver rejected trips';

  @override
  String driverRejectedTripsForDate(String date) {
    return 'Driver Rejected Trips for $date';
  }

  @override
  String get noDriverRejectedTripsForDate => 'No driver rejected trips for this date';

  @override
  String errorFetchingOnlineTaxiTrips(String error) {
    return 'Error fetching online taxi trips list: $error';
  }

  @override
  String errorFetchingTrips(String error) {
    return 'Error fetching trips: $error';
  }

  @override
  String errorFetchingDriverRejectedTrips(String error) {
    return 'Error fetching driver rejected trips: $error';
  }

  @override
  String errorFetchingTripsWithStatus(String status1, String status2) {
    return 'Error fetching trips: $status1 / $status2';
  }

  @override
  String errorFetchingTripsWithStatusCodes(String statusCodes) {
    return 'Error fetching trips: $statusCodes';
  }

  @override
  String tripStatusChangedToOnlineTaxi(String tripCode) {
    return 'Trip $tripCode status changed to ASSIGN TO ONLINE TAXI.';
  }

  @override
  String errorAssigningDriver(String error) {
    return 'Error assigning driver: $error';
  }

  @override
  String tripHasBeenDeleted(String tripCode) {
    return 'Trip $tripCode has been deleted.';
  }

  @override
  String tripRequestsForDate(String date) {
    return 'Trip Requests for $date';
  }

  @override
  String requestCount(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'requests',
      one: 'request',
    );
    return '$count $_temp0';
  }

  @override
  String tripCount(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'trips',
      one: 'trip',
    );
    return '$count $_temp0';
  }

  @override
  String tripsCount(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Trips',
      one: 'Trip',
    );
    return '$count $_temp0';
  }

  @override
  String get onlineLabel => 'Online: ';

  @override
  String get durationLabel => 'Duration:';

  @override
  String get taxiText => 'TAXI';

  @override
  String get noCarAssigned => 'No Car Assigned';

  @override
  String get statusDetailLabel => 'Status';

  @override
  String get startTimeLabel => 'Start Time:';

  @override
  String get endTimeLabel => 'End Time:';

  @override
  String get durationDetailLabel => 'Duration';

  @override
  String get driverDetailLabel => 'Driver';

  @override
  String get driverCodeDetailLabel => 'Driver Code';

  @override
  String get fromDetailLabel => 'From';

  @override
  String get toDetailLabel => 'To';

  @override
  String get additionalInformation => 'Additional Information';

  @override
  String get tripIdLabel => 'Trip ID';

  @override
  String get notAssigned => 'Not assigned';

  @override
  String get carInformation => 'Car Information';

  @override
  String get carIdLabel => 'Car ID';

  @override
  String get carCodeDetailLabel => 'Car Code';

  @override
  String get plateNumberLabel => 'Plate Number';

  @override
  String get passengersSection => 'Passengers:';

  @override
  String get cargoSection => 'Cargo:';

  @override
  String get returnToRequest => 'Return to REQUEST';

  @override
  String get deleteTrip => 'Delete';

  @override
  String deleteTripTitle(String tripCode) {
    return 'Delete Trip $tripCode?';
  }

  @override
  String get deleteTripWarningMessage => 'This will change the trip status to DELETED. This action cannot be undone.\\n\\nAre you sure you want to continue?';

  @override
  String get minutesUnit => 'minutes';

  @override
  String get onlineTaxiText => 'ONLINE TAXI';

  @override
  String get noCar => 'No Car';

  @override
  String get noPlate => 'No Plate';

  @override
  String get updatingTripDuration => 'Updating trip duration...';

  @override
  String errorUpdatingDuration(String error) {
    return 'Error updating duration: $error';
  }

  @override
  String get updatingTripStatus => 'Updating trip status...';

  @override
  String tripStatusChangedToRequest(String tripCode) {
    return 'Trip $tripCode status changed to REQUEST.';
  }

  @override
  String get refreshingAllData => 'Refreshing all data...';

  @override
  String errorGeneric(String error) {
    return 'Error: $error';
  }

  @override
  String get deletingTripMessage => 'Deleting trip...';

  @override
  String errorDeletingTripMessage(String error) {
    return 'Error deleting trip: $error';
  }

  @override
  String get errorDriverCodeMissing => 'Error: Driver code is missing. Please try again.';

  @override
  String get backToBase => 'Back to Base';

  @override
  String get checkOut => 'Check Out';

  @override
  String assignToDriverCount(int count) {
    return 'Assign To Driver: $count';
  }

  @override
  String get selectStatus => 'Select Status';

  @override
  String tripCodeNumber(String tripCode) {
    return 'Trip #$tripCode';
  }

  @override
  String durationInMinutes(int minutes) {
    return '$minutes minutes';
  }

  @override
  String get requestorLabel => 'Requestor:';

  @override
  String get rejectionLabel => 'Rejection:';

  @override
  String get locationTrackingActive => 'Location tracking is active (driver has active trips)';

  @override
  String get trackingReady => 'Tracking ready (driver has active trips)';

  @override
  String get trackingDisabledNoActiveTrips => 'Tracking disabled (no active trips)';

  @override
  String get automaticTrackingDisabled => 'Automatic tracking is disabled';

  @override
  String failedToCreateBackToBaseTrip(int statusCode) {
    return 'Failed to create Back to Base trip: $statusCode';
  }

  @override
  String errorCreatingBackToBaseTrip(String error) {
    return 'Error creating Back to Base trip: $error';
  }

  @override
  String get locationTrackingStatus => 'Location Tracking Status';

  @override
  String get autoTrackingLabel => 'Auto Tracking:';

  @override
  String get enabled => 'ENABLED';

  @override
  String get disabled => 'DISABLED';

  @override
  String get activeTripsLabel => 'Active Trips:';

  @override
  String get currentTimeLabel => 'Current Time:';

  @override
  String get todayLabel => 'Today:';

  @override
  String get locationLabel => 'Location:';

  @override
  String get lastUpdateLabel => 'Last Update:';

  @override
  String secondsAgo(int seconds) {
    return '$seconds seconds ago';
  }

  @override
  String minutesAgo(int minutes) {
    return '$minutes minutes ago';
  }

  @override
  String get trackingExplanation => 'Your location is tracked only when you have active trips (TRIP IN PROGRESS or BACK TO BASE):\n• Tracking is automatically enabled when you have active trips\n• Tracking is automatically disabled when you have no active trips\n• Location updates are sent every minute when tracking is active';

  @override
  String get tripCodeColumn => 'Trip Code';

  @override
  String get requestorColumn => 'Requestor';

  @override
  String get fromColumn => 'From';

  @override
  String get toColumn => 'To';

  @override
  String get dateColumn => 'Date';

  @override
  String get timeColumn => 'Time';

  @override
  String get driverColumn => 'Driver';

  @override
  String get statusColumn => 'Status';

  @override
  String get ratingColumn => 'Rating';

  @override
  String pageOf(int current, int total) {
    return 'Page $current of $total';
  }

  @override
  String get itemsText => 'items';

  @override
  String get tripAssignedToOnlineTaxiTitle => 'Trip Assigned to Online Taxi';

  @override
  String tripAssignedToOnlineTaxiMessage(String tripCode, String fromDestination, String toDestination) {
    return 'Your trip $tripCode from $fromDestination to $toDestination has been assigned to an online taxi service.';
  }

  @override
  String tripAssignedToOnlineTaxiSuccess(String tripCode) {
    return 'Trip $tripCode assigned to online taxi and notification sent.';
  }

  @override
  String notificationTripAssigned(String tripCode) {
    return 'Trip Assigned: $tripCode';
  }

  @override
  String notificationNewTripAssignment(String tripCode) {
    return 'New Trip Assignment: $tripCode';
  }

  @override
  String notificationTripApproved(String tripCode) {
    return 'Trip Approved: $tripCode';
  }

  @override
  String notificationTripRejected(String tripCode) {
    return 'Trip Rejected: $tripCode';
  }

  @override
  String notificationTripCompleted(String tripCode) {
    return 'Trip Completed: $tripCode';
  }

  @override
  String notificationTripStatusUpdate(String tripCode) {
    return 'Trip Status Update: $tripCode';
  }

  @override
  String notificationTripRated(String tripCode) {
    return 'Trip Rated: $tripCode';
  }

  @override
  String notificationTripReturnedToRequest(String tripCode) {
    return 'Trip Returned to Request: $tripCode';
  }

  @override
  String notificationTripDeleted(String tripCode) {
    return 'Trip Deleted: $tripCode';
  }

  @override
  String notificationBackToBaseTripCompleted(String tripCode) {
    return 'Back to Base Trip Completed: $tripCode';
  }

  @override
  String notificationTripAssignedToOnlineTaxi(String tripCode) {
    return 'Trip Assigned to Online Taxi: $tripCode';
  }

  @override
  String notificationOnlineTaxiTripCompleted(String tripCode) {
    return 'Online Taxi Trip Completed: $tripCode';
  }

  @override
  String notificationBackToBaseTripCreated(String tripCode) {
    return 'Back to Base Trip Created: $tripCode';
  }

  @override
  String notificationTripChangesApproved(String tripCode) {
    return 'Trip Changes Approved: $tripCode';
  }

  @override
  String notificationNewTripRequest(String tripCode) {
    return 'New Trip Request: $tripCode';
  }

  @override
  String notificationTripEditRequest(String tripCode) {
    return 'Trip Edit Request: $tripCode';
  }

  @override
  String notificationTripCancellationRequest(String tripCode) {
    return 'Trip Cancellation Request: $tripCode';
  }

  @override
  String notificationMessageTripAssignedToDriver(String tripCode, String date, String time) {
    return 'Your trip $tripCode has been assigned to a driver and is scheduled for $date at $time.';
  }

  @override
  String notificationMessageDriverAssignedToTrip(String tripCode, String fromDestination, String toDestination, String date, String time) {
    return 'You have been assigned to trip $tripCode from $fromDestination to $toDestination on $date at $time.';
  }

  @override
  String notificationMessageTripApproved(String tripCode, String fromDestination, String toDestination) {
    return 'Your trip $tripCode from $fromDestination to $toDestination has been approved.';
  }

  @override
  String notificationMessageTripRejected(String tripCode, String rejectionReason) {
    return 'Your trip $tripCode has been rejected. Reason: $rejectionReason.';
  }

  @override
  String notificationMessageTripRejectedNoReason(String tripCode) {
    return 'Your trip $tripCode has been rejected. Reason: No reason provided.';
  }

  @override
  String notificationMessageTripStatusChanged(String tripCode, String oldStatus, String newStatus) {
    return 'Your trip $tripCode status has changed from $oldStatus to $newStatus.';
  }

  @override
  String notificationMessageDriverTripStatusChanged(String tripCode, String oldStatus, String newStatus) {
    return 'Trip $tripCode status has changed from $oldStatus to $newStatus.';
  }

  @override
  String notificationMessageTripRated(String tripCode, String rating, String ratingMessage) {
    return 'Your trip $tripCode has been rated $rating stars by the passenger. $ratingMessage';
  }

  @override
  String notificationMessageTripRatedNoMessage(String tripCode, String rating) {
    return 'Your trip $tripCode has been rated $rating stars by the passenger.';
  }

  @override
  String notificationMessageTripReturnedToRequestor(String tripCode, String fromDestination, String toDestination) {
    return 'Your trip $tripCode from $fromDestination to $toDestination has been returned to REQUEST status by a trip manager.';
  }

  @override
  String notificationMessageTripReturnedToDriver(String tripCode, String fromDestination, String toDestination) {
    return 'Trip $tripCode from $fromDestination to $toDestination has been returned to REQUEST status by a trip manager.';
  }

  @override
  String notificationMessageTripDeletedRequestor(String tripCode, String fromDestination, String toDestination) {
    return 'Your trip $tripCode from $fromDestination to $toDestination has been deleted by a trip manager.';
  }

  @override
  String notificationMessageTripDeletedDriver(String tripCode, String fromDestination, String toDestination) {
    return 'Trip $tripCode from $fromDestination to $toDestination has been deleted by a trip manager.';
  }

  @override
  String notificationMessageTripCompletedRequestor(String tripCode) {
    return 'Your trip $tripCode has been completed successfully. Thank you for using our service!';
  }

  @override
  String notificationMessageTripCompletedManager(String tripCode) {
    return 'Trip $tripCode has been marked as completed by the driver.';
  }

  @override
  String notificationMessageBackToBaseTripCompletedRequestor(String tripCode) {
    return 'Your back to base trip $tripCode has been completed successfully.';
  }

  @override
  String notificationMessageBackToBaseTripCompletedManager(String tripCode) {
    return 'Back to base trip $tripCode has been completed.';
  }

  @override
  String notificationMessageTripAssignedToOnlineTaxi(String tripCode, String fromDestination, String toDestination) {
    return 'Your trip $tripCode from $fromDestination to $toDestination has been assigned to an online taxi service.';
  }

  @override
  String notificationMessageOnlineTaxiTripCompletedRequestor(String tripCode) {
    return 'Your online taxi trip $tripCode has been successfully completed. Thank you for using our service!';
  }

  @override
  String notificationMessageOnlineTaxiTripCompletedManager(String tripCode) {
    return 'Online taxi trip $tripCode has been marked as completed by the requestor.';
  }

  @override
  String notificationMessageBackToBaseTripCreated(String driverName, String tripCode, String fromDestination, String toDestination, String date, String time) {
    return '$driverName has created a \"Back to Base\" trip $tripCode from $fromDestination to $toDestination on $date at $time.';
  }

  @override
  String notificationMessageTripChangesApproved(String tripCode, String fromDestination, String toDestination) {
    return 'Your changes to trip $tripCode from $fromDestination to $toDestination have been approved. The trip is now back to REQUEST status.';
  }

  @override
  String notificationMessageNewTripRequest(String requestorName, String tripCode, String fromDestination, String toDestination, String date, String time) {
    return '$requestorName has requested a new trip $tripCode from $fromDestination to $toDestination on $date at $time.';
  }

  @override
  String notificationMessageTripEditRequest(String userName, String tripCode, String fromDestination, String toDestination, String date, String time) {
    return '$userName has requested changes to trip $tripCode from $fromDestination to $toDestination on $date at $time.';
  }

  @override
  String notificationMessageTripCancellationRequest(String userName, String tripCode, String fromDestination, String toDestination, String date, String time) {
    return '$userName has requested to cancel trip $tripCode from $fromDestination to $toDestination on $date at $time.';
  }

  @override
  String notificationTripStarted(String tripCode) {
    return 'Trip Started: $tripCode';
  }

  @override
  String notificationDriverConfirmedTrip(String tripCode) {
    return 'Driver Confirmed Trip: $tripCode';
  }

  @override
  String notificationMessageTripStarted(String tripCode) {
    return 'Trip $tripCode has been started by the driver.';
  }

  @override
  String notificationMessageDriverConfirmedTrip(String driverName, String tripCode) {
    return '$driverName has confirmed trip $tripCode.';
  }

  @override
  String notificationDriverRejectedTrip(String tripCode) {
    return 'Driver Rejected Trip: $tripCode';
  }

  @override
  String notificationMessageDriverRejectedTrip(String driverName, String tripCode, String rejectionReason) {
    return '$driverName has rejected trip $tripCode. Reason: $rejectionReason.';
  }

  @override
  String notificationMessageDriverRejectedTripGeneric(String tripCode, String rejectionReason) {
    return 'The driver has rejected trip $tripCode. Reason: $rejectionReason.';
  }

  @override
  String notificationTripChangesRejected(String tripCode) {
    return 'Trip Changes Rejected: $tripCode';
  }

  @override
  String notificationMessageTripChangesRejected(String tripCode, String rejectionReason) {
    return 'The requested changes to trip $tripCode have been rejected. Reason: $rejectionReason.';
  }

  @override
  String notificationTripCancellationRejected(String tripCode) {
    return 'Trip Cancellation Rejected: $tripCode';
  }

  @override
  String notificationMessageTripCancellationRejected(String tripCode, String rejectionReason) {
    return 'The cancellation request for trip $tripCode has been rejected. Reason: $rejectionReason.';
  }
}
