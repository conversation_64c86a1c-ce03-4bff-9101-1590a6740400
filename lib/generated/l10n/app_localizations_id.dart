// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Indonesian (`id`).
class AppLocalizationsId extends AppLocalizations {
  AppLocalizationsId([String locale = 'id']) : super(locale);

  @override
  String get appTitle => 'FLEEX';

  @override
  String get welcomeBack => 'Selamat Datang Kembali';

  @override
  String get signInToContinue => 'Masuk untuk melanjutkan ke FLEEX';

  @override
  String get username => 'Nama Pengguna';

  @override
  String get password => 'Kata Sandi';

  @override
  String get login => 'Masuk';

  @override
  String get pleaseEnterUsername => 'Silakan masukkan nama pengguna Anda';

  @override
  String get pleaseEnterPassword => 'Silakan masukkan kata sandi';

  @override
  String get or => 'ATAU';

  @override
  String get signInWith => 'Masuk dengan';

  @override
  String get signInWithMicrosoft => 'Masuk dengan Microsoft';

  @override
  String get forgotPassword => 'Lupa Kata Sandi?';

  @override
  String get resetPassword => 'Reset Kata Sandi';

  @override
  String get goBack => 'Kembali';

  @override
  String get newPassword => 'Kata Sandi Baru';

  @override
  String get confirmNewPassword => 'Konfirmasi Kata Sandi Baru';

  @override
  String get pleaseEnterNewPassword => 'Silakan masukkan kata sandi baru';

  @override
  String get pleaseConfirmNewPassword => 'Silakan konfirmasi kata sandi baru';

  @override
  String get passwordMustBeAtLeast6Characters => 'Kata sandi harus minimal 6 karakter';

  @override
  String get passwordsDoNotMatch => 'Kata sandi tidak cocok';

  @override
  String get passwordResetSuccessful => 'Reset password berhasil.';

  @override
  String get dashboard => 'Dashboard';

  @override
  String get requestTrip => 'Minta Perjalanan';

  @override
  String get tripHistory => 'Riwayat Perjalanan';

  @override
  String get settings => 'Pengaturan';

  @override
  String get logout => 'Keluar';

  @override
  String get search => 'Cari';

  @override
  String get date => 'Tanggal';

  @override
  String get time => 'Waktu';

  @override
  String get from => 'Dari:';

  @override
  String get to => 'Ke:';

  @override
  String get destination => 'Tujuan';

  @override
  String get notes => 'Catatan';

  @override
  String get save => 'Simpan';

  @override
  String get cancel => 'Batal';

  @override
  String get edit => 'Edit';

  @override
  String get delete => 'Hapus';

  @override
  String get add => 'Tambah';

  @override
  String get required => 'Wajib';

  @override
  String get optional => 'Opsional';

  @override
  String get loading => 'Memuat...';

  @override
  String get error => 'Kesalahan';

  @override
  String get success => 'Berhasil';

  @override
  String get tripCode => 'Kode Perjalanan';

  @override
  String get status => 'Status';

  @override
  String get driver => 'Pengemudi';

  @override
  String get car => 'Mobil:';

  @override
  String get passenger => 'Penumpang';

  @override
  String get passengers => 'Penumpang';

  @override
  String get duration => 'Durasi';

  @override
  String get actions => 'Aksi';

  @override
  String get filterByDate => 'Filter berdasarkan Tanggal';

  @override
  String get filterByStatus => 'Filter berdasarkan Status';

  @override
  String get allRecords => 'Semua Data';

  @override
  String get clearFilter => 'Hapus Filter';

  @override
  String get refresh => 'Refresh';

  @override
  String get noDataFound => 'Data tidak ditemukan';

  @override
  String get pleaseEnterDate => 'Silakan masukkan tanggal';

  @override
  String get pleaseEnterTime => 'Silakan masukkan waktu';

  @override
  String get pleaseSelectFromDestination => 'Silakan pilih asal tujuan';

  @override
  String get pleaseSelectToDestination => 'Silakan pilih tujuan';

  @override
  String get addTrip => 'Tambah Perjalanan';

  @override
  String get editTrip => 'Edit Perjalanan';

  @override
  String get requestorName => 'Nama Pemohon';

  @override
  String get customLocation => 'Lokasi Kustom';

  @override
  String get selectOrigin => 'Pilih asal';

  @override
  String get selectDestination => 'Pilih tujuan';

  @override
  String get carCode => 'Kode Mobil';

  @override
  String get manufacturer => 'Pabrikan';

  @override
  String get model => 'Model';

  @override
  String get plateNumber => 'Nomor Plat';

  @override
  String get odometer => 'Odometer';

  @override
  String get color => 'Warna';

  @override
  String get type => 'Tipe';

  @override
  String get driverCode => 'Kode Pengemudi';

  @override
  String get driverName => 'Nama Pengemudi';

  @override
  String get initial => 'Inisial';

  @override
  String get address => 'Alamat';

  @override
  String get coordinates => 'Koordinat';

  @override
  String get hours => 'Jam';

  @override
  String get minutes => 'Menit';

  @override
  String get available => 'Tersedia';

  @override
  String get unavailable => 'Tidak Tersedia';

  @override
  String get pending => 'Menunggu';

  @override
  String get approved => 'Disetujui';

  @override
  String get inProgress => 'Dalam Proses';

  @override
  String get completed => 'Selesai';

  @override
  String get cancelled => 'Dibatalkan';

  @override
  String get confirmDelete => 'Konfirmasi Hapus';

  @override
  String get areYouSureDelete => 'Apakah Anda yakin ingin menghapus item ini?';

  @override
  String get yes => 'YA';

  @override
  String get no => 'TIDAK';

  @override
  String get dataRefreshedSuccessfully => 'Data berhasil disegarkan';

  @override
  String get failedToLoadData => 'Gagal memuat data';

  @override
  String get selectLanguage => 'Bahasa';

  @override
  String get chooseLanguage => 'Pilih bahasa yang Anda inginkan:';

  @override
  String get saveLanguage => 'Simpan Bahasa';

  @override
  String get languageChanged => 'Bahasa berhasil diubah';

  @override
  String get languageChangeNote => 'Catatan: Aplikasi perlu direstart untuk beberapa perubahan dapat berlaku.';

  @override
  String get language => 'Bahasa';

  @override
  String get commonSettings => 'Pengaturan Umum';

  @override
  String get driverSetting => 'Pengaturan Sopir';

  @override
  String get carSetting => 'Pengaturan Mobil';

  @override
  String get cargoSetting => 'Pengaturan Kargo';

  @override
  String get destinationSetting => 'Pengaturan Tujuan';

  @override
  String get backToHome => 'Kembali ke Beranda';

  @override
  String get welcome => 'Selamat Datang';

  @override
  String get refreshNow => 'Refresh sekarang';

  @override
  String get youMustBeLoggedIn => 'Anda harus masuk terlebih dahulu';

  @override
  String get failedToLoadTripStatusCounts => 'Gagal memuat jumlah status perjalanan';

  @override
  String get errorLoading => 'Error memuat data';

  @override
  String get tripApprovals => 'Persetujuan Perjalanan';

  @override
  String get dashboardTitle => 'Dashboard Trip Manager';

  @override
  String get requestorDashboardTitle => 'Dashboard Pemohon';

  @override
  String get tripMonitoring => 'Pemantauan Perjalanan';

  @override
  String get driverTracking => 'Pelacakan Sopir';

  @override
  String areYouSureDeleteDriver(String name) {
    return 'Apakah Anda yakin ingin menghapus $name sebagai pengemudi?';
  }

  @override
  String get failedToDeleteDriver => 'Gagal menghapus pengemudi';

  @override
  String anErrorOccurred(String error) {
    return 'Terjadi kesalahan: $error';
  }

  @override
  String get driverSettingsTitle => 'Pengaturan Pengemudi';

  @override
  String get carSettingsTitle => 'Pengaturan Mobil';

  @override
  String get cargoSettingsTitle => 'Pengaturan Kargo';

  @override
  String get destinationSettingsTitle => 'Pengaturan Tujuan';

  @override
  String get searchCars => 'Cari Mobil';

  @override
  String get searchCarsHint => 'Cari berdasarkan kode mobil, pabrikan, model, atau nomor plat';

  @override
  String get noCarsFound => 'Tidak ada mobil ditemukan';

  @override
  String get noMatchingCarsFound => 'Tidak ada mobil yang cocok ditemukan';

  @override
  String failedToLoadCarsWithCode(String statusCode) {
    return 'Gagal memuat mobil: $statusCode';
  }

  @override
  String get carDeletedSuccessfully => 'Mobil berhasil dihapus';

  @override
  String get failedToDeleteCar => 'Gagal menghapus mobil';

  @override
  String confirmDeleteCar(String carCode) {
    return 'Apakah Anda yakin ingin menghapus mobil $carCode?';
  }

  @override
  String get searchCargos => 'Cari kargo';

  @override
  String get searchCargosHint => 'Cari berdasarkan kode kargo atau nama';

  @override
  String get noCargosFound => 'Tidak ada kargo ditemukan';

  @override
  String get noMatchingCargosFound => 'Tidak ada kargo yang cocok ditemukan';

  @override
  String failedToLoadCargos(String statusCode) {
    return 'Gagal memuat kargo: $statusCode';
  }

  @override
  String get noCargoDataFound => 'Data kargo tidak ditemukan';

  @override
  String get cargoDeletedSuccessfully => 'Kargo berhasil dihapus';

  @override
  String get failedToDeleteCargo => 'Gagal menghapus kargo';

  @override
  String confirmDeleteCargo(String cargoCode) {
    return 'Apakah Anda yakin ingin menghapus kargo $cargoCode?';
  }

  @override
  String get cargoCode => 'Kode Kargo';

  @override
  String get cargoName => 'Nama Kargo';

  @override
  String get searchDestinations => 'Cari tujuan';

  @override
  String get searchDestinationsHint => 'Cari berdasarkan kode tujuan, nama, atau alamat';

  @override
  String get noDestinationsFound => 'Tidak ada tujuan ditemukan';

  @override
  String get noMatchingDestinationsFound => 'Tidak ada tujuan yang cocok ditemukan';

  @override
  String failedToLoadDestinations(String statusCode) {
    return 'Gagal memuat tujuan: $statusCode';
  }

  @override
  String get destinationDeletedSuccessfully => 'Tujuan berhasil dihapus';

  @override
  String get failedToDeleteDestination => 'Gagal menghapus tujuan';

  @override
  String confirmDeleteDestination(String destinationCode) {
    return 'Apakah Anda yakin ingin menghapus tujuan $destinationCode?';
  }

  @override
  String get destinationCode => 'Kode Tujuan';

  @override
  String get active => 'AKTIF';

  @override
  String get inactive => 'TIDAK AKTIF';

  @override
  String get tripApprovalsTitle => 'Persetujuan Perjalanan';

  @override
  String get noTripsForApproval => 'Tidak ada perjalanan untuk disetujui';

  @override
  String get failedToFetchTrips => 'Gagal mengambil data perjalanan';

  @override
  String get requestedBy => 'Diminta oleh';

  @override
  String get requestDate => 'Tanggal Permintaan';

  @override
  String get tripDate => 'Tanggal Perjalanan';

  @override
  String get purpose => 'Tujuan';

  @override
  String get approve => 'Setujui';

  @override
  String get reject => 'Tolak';

  @override
  String get viewChanges => 'Lihat Perubahan';

  @override
  String get details => 'Detail';

  @override
  String get tripApprovedSuccessfully => 'Perjalanan berhasil disetujui';

  @override
  String get tripRejectedSuccessfully => 'Perjalanan berhasil ditolak';

  @override
  String get failedToUpdateTrip => 'Gagal memperbarui perjalanan';

  @override
  String get addDriver => 'Tambah Pengemudi';

  @override
  String get searchDrivers => 'Cari pengemudi';

  @override
  String get noDriversFound => 'Tidak ada pengemudi ditemukan';

  @override
  String get rateTripTitle => 'Beri Rating Perjalanan';

  @override
  String get howWouldYouRateTrip => 'Bagaimana penilaian Anda terhadap perjalanan ini?';

  @override
  String get additionalComments => 'Komentar Tambahan';

  @override
  String get submitRating => 'Kirim Rating';

  @override
  String get errorSubmittingRating => 'Gagal mengirim rating';

  @override
  String get tripChangeApproval => 'Persetujuan Perubahan Perjalanan';

  @override
  String get confirmApproval => 'Konfirmasi Persetujuan';

  @override
  String get confirmRejection => 'Konfirmasi Penolakan';

  @override
  String areYouSureApproveTrip(String tripCode) {
    return 'Apakah Anda yakin ingin menyetujui perjalanan $tripCode? Ini akan mengubah status menjadi REQUEST.';
  }

  @override
  String areYouSureApproveChanges(String tripCode) {
    return 'Apakah Anda yakin ingin menyetujui perubahan perjalanan $tripCode? Ini akan mengubah status menjadi REQUEST.';
  }

  @override
  String areYouSureApproveCancellation(String tripCode) {
    return 'Apakah Anda yakin ingin menyetujui pembatalan perjalanan $tripCode? Ini akan mengubah status menjadi DELETED.';
  }

  @override
  String areYouSureRejectTrip(String tripCode) {
    return 'Apakah Anda yakin ingin menolak perjalanan $tripCode?';
  }

  @override
  String areYouSureRejectChanges(String tripCode) {
    return 'Apakah Anda yakin ingin menolak perubahan perjalanan $tripCode? Perjalanan akan dikembalikan ke keadaan semula sebelum diedit.';
  }

  @override
  String areYouSureRejectCancellation(String tripCode) {
    return 'Apakah Anda yakin ingin menolak pembatalan perjalanan $tripCode? Perjalanan akan tetap aktif.';
  }

  @override
  String get pleaseProvideReason => 'Silakan berikan alasan penolakan:';

  @override
  String get enterRejectionReason => 'Masukkan alasan penolakan perjalanan';

  @override
  String get reasonIsRequired => 'Alasan diperlukan';

  @override
  String get requestor => 'Pemohon';

  @override
  String get tripApprovedAndStatusChanged => 'Perjalanan disetujui dan status diubah menjadi REQUEST';

  @override
  String get failedToApproveTrip => 'Gagal menyetujui perjalanan';

  @override
  String get failedToRejectTrip => 'Gagal menolak perjalanan';

  @override
  String get failedToApproveChanges => 'Gagal menyetujui perubahan perjalanan';

  @override
  String get failedToApproveCancellation => 'Gagal menyetujui pembatalan perjalanan';

  @override
  String get failedToRejectChanges => 'Gagal menolak perubahan perjalanan';

  @override
  String get failedToRejectCancellation => 'Gagal menolak pembatalan perjalanan';

  @override
  String get tripChangesApprovedSuccessfully => 'Perubahan perjalanan berhasil disetujui. Status diubah menjadi REQUEST.';

  @override
  String get tripCancellationApprovedSuccessfully => 'Pembatalan perjalanan berhasil disetujui. Status diubah menjadi DELETED.';

  @override
  String get tripChangesRejectedSuccessfully => 'Perubahan perjalanan berhasil ditolak. Status diubah menjadi REQUEST.';

  @override
  String get tripCancellationRejectedSuccessfully => 'Pembatalan perjalanan berhasil ditolak. Status diubah menjadi REQUEST.';

  @override
  String get approveTrip => 'Setujui Perjalanan';

  @override
  String get approveChanges => 'Setujui Perubahan';

  @override
  String get approveCancellation => 'Setujui Pembatalan';

  @override
  String get rejectTrip => 'Tolak Perjalanan';

  @override
  String get rejectChanges => 'Tolak Perubahan';

  @override
  String get rejectCancellation => 'Tolak Pembatalan';

  @override
  String get noTripsFound => 'Tidak ada perjalanan ditemukan';

  @override
  String noTripsFoundForDate(String date) {
    return 'Tidak ada perjalanan ditemukan untuk tanggal: $date';
  }

  @override
  String get tripMonitoringTitle => 'Pemantauan Perjalanan';

  @override
  String get trackDrivers => 'Lacak Pengemudi';

  @override
  String get allRequests => 'Semua Permintaan';

  @override
  String get refreshData => 'Segarkan Data';

  @override
  String get swipeToChangeDate => 'Geser untuk mengubah tanggal';

  @override
  String errorRefreshingData(Object error) {
    return 'Kesalahan menyegarkan data: $error';
  }

  @override
  String get searchDriversHint => 'Cari berdasarkan kode pengemudi, nama, atau inisial';

  @override
  String get noMatchingDriversFound => 'Tidak ada pengemudi yang cocok ditemukan';

  @override
  String get driverDeletedSuccessfully => 'Pengemudi berhasil dihapus';

  @override
  String failedToLoadDrivers(String statusCode) {
    return 'Gagal memuat pengemudi: $statusCode';
  }

  @override
  String get driverCodeLabel => 'Kode Pengemudi';

  @override
  String get carLabel => 'Mobil:';

  @override
  String get saveChanges => 'Simpan Perubahan';

  @override
  String get waitingTrip => 'Perjalanan Menunggu';

  @override
  String get fromDestination => 'Tujuan Asal';

  @override
  String get toDestination => 'Tujuan Akhir';

  @override
  String get notesOptional => 'Catatan (opsional)';

  @override
  String get selectPassengers => 'Pilih Penumpang';

  @override
  String get selectCargos => 'Pilih Kargo';

  @override
  String get trip => 'Perjalanan';

  @override
  String get cargos => 'Kargo';

  @override
  String get searchForDestinations => 'Cari tujuan';

  @override
  String get customFromDestination => 'Tujuan Asal Khusus';

  @override
  String get customToDestination => 'Tujuan Akhir Khusus';

  @override
  String get enterCustomLocation => 'Masukkan lokasi khusus';

  @override
  String get addAdditionalNotes => 'Tambahkan catatan tambahan untuk perjalanan ini';

  @override
  String get searchForPassengers => 'Cari penumpang';

  @override
  String get chooseOneOrMorePassengers => 'Pilih satu atau lebih penumpang';

  @override
  String get searchForCargos => 'Cari kargo';

  @override
  String get chooseOneOrMoreCargos => 'Pilih satu atau lebih kargo';

  @override
  String get rateTrips => 'Beri Rating Perjalanan';

  @override
  String get myTrips => 'Perjalanan Saya';

  @override
  String get checkIn => 'Check-In';

  @override
  String get notifications => 'Notifikasi';

  @override
  String get admin => 'Admin';

  @override
  String get switchRole => 'Ganti Peran';

  @override
  String get currentRole => 'Peran Saat Ini';

  @override
  String get confirmRoleSwitch => 'Konfirmasi Pergantian Peran';

  @override
  String switchRoleMessage(Object role) {
    return 'Apakah Anda yakin ingin beralih ke $role? Ini akan mengubah antarmuka dan fungsi yang tersedia.';
  }

  @override
  String roleSwitchedSuccessfully(Object role) {
    return 'Berhasil beralih ke $role';
  }

  @override
  String get now => 'Sekarang';

  @override
  String get currentTime => 'Waktu Sekarang';

  @override
  String get rating => 'Rating';

  @override
  String get cargo => 'Kargo';

  @override
  String get clearStatusFilter => 'Hapus Filter Status';

  @override
  String get specificDate => 'Tanggal Spesifik';

  @override
  String get year => 'Tahun';

  @override
  String get month => 'Bulan';

  @override
  String get currentFilter => 'Filter Saat Ini';

  @override
  String get selectYear => 'Pilih Tahun';

  @override
  String get selectMonth => 'Pilih Bulan';

  @override
  String get searchTrips => 'Cari perjalanan (kode, pemohon, lokasi...)';

  @override
  String get odometerReading => 'Pembacaan Odometer';

  @override
  String get searchTripsLong => 'Cari perjalanan (kode, pemohon, lokasi...)';

  @override
  String get itemsPerPage => 'Item per halaman';

  @override
  String get exportToExcel => 'Ekspor ke Excel';

  @override
  String get exporting => 'Mengekspor...';

  @override
  String get exportingToExcel => 'Mengekspor ke Excel...';

  @override
  String get noTripsToExport => 'Tidak ada perjalanan untuk diekspor';

  @override
  String tripHistoryExported(String fileName) {
    return 'Riwayat perjalanan diekspor sebagai $fileName';
  }

  @override
  String tripHistoryExportedTo(String filePath) {
    return 'Riwayat perjalanan diekspor ke: $filePath';
  }

  @override
  String exportFailed(String error) {
    return 'Ekspor gagal: $error';
  }

  @override
  String get noTripHistoryFound => 'Tidak ada riwayat perjalanan ditemukan';

  @override
  String get noMatchingTripsFound => 'Tidak ada perjalanan yang sesuai';

  @override
  String get superAdmin => 'Super Admin';

  @override
  String get tripManager => 'Manajer Perjalanan';

  @override
  String get user => 'Pengguna';

  @override
  String get locationTracking => 'Pelacakan Lokasi';

  @override
  String get tripRating => 'Penilaian Perjalanan';

  @override
  String get driverAvailabilityManagement => 'Manajemen Ketersediaan Pengemudi';

  @override
  String get carAvailabilityManagement => 'Manajemen Ketersediaan Mobil';

  @override
  String get driverCheckInManagement => 'Manajemen Check-In Sopir';

  @override
  String get carOdometerHistory => 'Riwayat Odometer Mobil';

  @override
  String get driverCheckIn => 'Check-In Driver';

  @override
  String get myTripHistory => 'Riwayat Perjalanan Saya';

  @override
  String get selectADriver => 'Pilih sopir';

  @override
  String get assign => 'Tugaskan';

  @override
  String get viewReason => 'Lihat Alasan';

  @override
  String get close => 'Tutup';

  @override
  String get allTripRequests => 'Semua Permintaan Perjalanan';

  @override
  String get rejectionReason => 'Alasan Penolakan';

  @override
  String get tripChanges => 'Perubahan Perjalanan';

  @override
  String get confirmCancellation => 'Konfirmasi Pembatalan';

  @override
  String get tripApprovedMessage => 'Perjalanan disetujui dan status diubah menjadi PERMINTAAN';

  @override
  String get pleaseSelectRating => 'Silakan pilih rating';

  @override
  String get ratingSubmittedSuccessfully => 'Rating berhasil dikirim';

  @override
  String get failedToLoadTripDetails => 'Gagal memuat detail perjalanan';

  @override
  String get notRated => 'Belum dinilai';

  @override
  String get previous => 'Sebelumnya';

  @override
  String get next => 'Selanjutnya';

  @override
  String get noTripDataAvailable => 'Tidak ada data perjalanan tersedia';

  @override
  String get pleaseSelectBothDestinations => 'Silakan pilih kedua tujuan';

  @override
  String get destinationsCannotBeSame => 'Tujuan asal dan tujuan tidak boleh sama';

  @override
  String get customDestinationsCannotBeSame => 'Tujuan khusus asal dan tujuan tidak boleh sama';

  @override
  String get pleaseEnterWaitingDuration => 'Silakan masukkan durasi menunggu';

  @override
  String get pleaseEnterCustomFromDestination => 'Silakan masukkan tujuan asal khusus';

  @override
  String get pleaseEnterCustomToDestination => 'Silakan masukkan tujuan akhir khusus';

  @override
  String get failedToLoadAvailableCars => 'Gagal memuat mobil yang tersedia';

  @override
  String get requestTrips => 'Permintaan Perjalanan';

  @override
  String get otherTripStatuses => 'Status Perjalanan Lainnya';

  @override
  String get multipleTrips => 'Perjalanan Berganda';

  @override
  String get tripDuration => 'Durasi Perjalanan:';

  @override
  String get noNotesProvided => 'Tidak ada catatan tersedia untuk perjalanan ini.';

  @override
  String get noNotifications => 'Tidak ada notifikasi';

  @override
  String get authenticationFailed => 'Autentikasi gagal';

  @override
  String get noTripDataAvailableRequestor => 'Tidak ada data perjalanan tersedia';

  @override
  String get myTripRequests => 'Permintaan Perjalanan Saya';

  @override
  String get editDriver => 'Edit Driver';

  @override
  String get failedToLoadCars => 'Gagal memuat mobil yang tersedia';

  @override
  String get failedToUpdateDriver => 'Gagal memperbarui driver';

  @override
  String get name => 'Nama';

  @override
  String get nameWillBeUppercase => 'Nama akan dikonversi ke huruf kapital';

  @override
  String get nameIsRequired => 'Nama diperlukan';

  @override
  String get initialHelperText => 'Harus tepat 3 karakter (akan dikonversi ke huruf kapital)';

  @override
  String get initialIsRequired => 'Inisial diperlukan';

  @override
  String get initialMustBe3Characters => 'Inisial harus tepat 3 karakter';

  @override
  String get loadingCars => 'Memuat mobil...';

  @override
  String get selectACar => 'Pilih mobil';

  @override
  String get searchForCar => 'Cari mobil';

  @override
  String get pleaseSelectCar => 'Silakan pilih mobil';

  @override
  String get newPasswordOptional => 'Password Baru (opsional)';

  @override
  String get leaveEmptyToKeepPassword => 'Biarkan kosong untuk mempertahankan password saat ini';

  @override
  String get fromDestinationRequired => 'Tujuan asal diperlukan';

  @override
  String get toDestinationRequired => 'Tujuan akhir diperlukan';

  @override
  String get dateIsRequired => 'Tanggal diperlukan';

  @override
  String get timeIsRequired => 'Waktu diperlukan';

  @override
  String get pleaseEnterOdometerReading => 'Silakan masukkan pembacaan odometer';

  @override
  String get odometerReadingRequired => 'Pembacaan odometer diperlukan';

  @override
  String get pleaseEnterValidNumber => 'Silakan masukkan angka yang valid';

  @override
  String get allDrivers => 'Semua Driver';

  @override
  String get validationStatus => 'Status Validasi';

  @override
  String get validated => 'Tervalidasi';

  @override
  String get notValidated => 'Tidak Tervalidasi';

  @override
  String get checkInValidatedSuccessfully => 'Check-in berhasil divalidasi';

  @override
  String get validationRemoved => 'Validasi dihapus';

  @override
  String get reset => 'Reset';

  @override
  String get checkInDetails => 'Detail Check-In';

  @override
  String get checkOutDetails => 'Detail Check-Out';

  @override
  String get rateTrip => 'Beri Rating Perjalanan';

  @override
  String get rateThisTrip => 'Beri Rating Perjalanan Ini';

  @override
  String get cannotAdjustDurationNoTripId => 'Tidak dapat menyesuaikan durasi untuk perjalanan ini - tidak ada ID perjalanan';

  @override
  String get cannotAdjustDuration => 'Tidak dapat menyesuaikan durasi untuk perjalanan ini';

  @override
  String failedToUpdateDuration(String statusCode) {
    return 'Gagal memperbarui durasi: $statusCode';
  }

  @override
  String updatedDurationForTrip(String tripCode, String minutes) {
    return 'Durasi perjalanan $tripCode diperbarui menjadi $minutes menit';
  }

  @override
  String get rateYourTrips => 'Beri Rating Perjalanan Anda';

  @override
  String tripCreatedSuccessfully(String tripCode) {
    return 'Perjalanan ($tripCode) berhasil dibuat';
  }

  @override
  String roundTripCreatedSuccessfully(String outboundCode, String returnCode) {
    return 'Perjalanan pulang-pergi berhasil dibuat: Berangkat ($outboundCode), Pulang ($returnCode)';
  }

  @override
  String waitingTripCreatedSuccessfully(String tripCode) {
    return 'Perjalanan tunggu berhasil dibuat: ($tripCode)';
  }

  @override
  String tripUpdatedSuccessfully(String tripCode) {
    return 'Perjalanan ($tripCode) berhasil diperbarui';
  }

  @override
  String get loadingTripDetails => 'Memuat detail perjalanan...';

  @override
  String get tripDataIsLoading => 'Data perjalanan sedang dimuat...';

  @override
  String get loadingAvailableDrivers => 'Memuat driver yang tersedia...';

  @override
  String get checkinValidatedSuccessfully => 'Check-in berhasil divalidasi';

  @override
  String get backToBaseTripCreatedSuccessfully => 'Perjalanan Kembali ke Base berhasil dibuat';

  @override
  String get failedToUpdateCarOdometer => 'Gagal memperbarui odometer mobil';

  @override
  String get errorLoadingData => 'Gagal memuat data';

  @override
  String errorWithDetails(String details) {
    return 'Error: $details';
  }

  @override
  String get tripCompletedSuccessfully => 'Perjalanan berhasil diselesaikan';

  @override
  String get failedToCompleteTrip => 'Gagal menyelesaikan perjalanan';

  @override
  String errorCompletingTrip(String error) {
    return 'Error menyelesaikan perjalanan: $error';
  }

  @override
  String get authenticationFailedInvalidCode => 'Autentikasi gagal: Kode autentikasi tidak valid atau kedaluwarsa';

  @override
  String authenticationFailedWithError(String error) {
    return 'Autentikasi gagal: $error';
  }

  @override
  String get invalidCredentials => 'Nama pengguna atau kata sandi tidak valid';

  @override
  String areYouSureCancelTrip(String tripCode) {
    return 'Apakah Anda yakin ingin membatalkan perjalanan $tripCode? Ini akan mengirimkan permintaan pembatalan untuk ditinjau.';
  }

  @override
  String get requestCancellation => 'Minta Pembatalan';

  @override
  String get tripCancellationRequestSubmitted => 'Permintaan pembatalan perjalanan telah diajukan untuk ditinjau';

  @override
  String get failedToSubmitCancellationRequest => 'Gagal mengirimkan permintaan pembatalan perjalanan';

  @override
  String get update => 'Perbarui';

  @override
  String assignDriverOrOnlineTaxi(String tripCode) {
    return 'Tugaskan Driver atau Taksi Online ke Perjalanan $tripCode';
  }

  @override
  String deleteTripConfirmation(String tripCode) {
    return 'Ini akan mengubah status perjalanan menjadi DELETED. Tindakan ini tidak dapat dibatalkan.\\n\\nApakah Anda yakin ingin melanjutkan?';
  }

  @override
  String get retry => 'Coba Lagi';

  @override
  String driversCount(int count) {
    return 'Pengemudi: $count';
  }

  @override
  String get inactiveDriversInfo => 'Abu-abu = Tidak aktif > 10 menit';

  @override
  String get updateFrequencyInfo => 'Diperbarui setiap 5 menit atau 500m';

  @override
  String errorFetchingDrivers(String error) {
    return 'Kesalahan mengambil data pengemudi: $error';
  }

  @override
  String get unknown => 'Tidak Diketahui';

  @override
  String get unknownDriver => 'Pengemudi Tidak Diketahui';

  @override
  String centeredOnDriver(String driverName) {
    return 'Dipusatkan pada $driverName';
  }

  @override
  String driverTrips(int count) {
    return 'Perjalanan Pengemudi ($count)';
  }

  @override
  String get lastUpdated => 'Terakhir Diperbarui';

  @override
  String tripNumber(String current, String total) {
    return 'Perjalanan $current dari $total';
  }

  @override
  String get completeTrip => 'Selesaikan Perjalanan';

  @override
  String get allStatuses => 'Semua Status';

  @override
  String get clearFilters => 'Hapus Filter';

  @override
  String get noTripsMatchFilters => 'Tidak ada perjalanan yang sesuai dengan filter Anda';

  @override
  String get confirmTrip => 'Konfirmasi Perjalanan';

  @override
  String get startTrip => 'Mulai Perjalanan';

  @override
  String get finishTrip => 'Selesaikan Perjalanan';

  @override
  String updateOdometerFor(String carCode) {
    return 'Perbarui Odometer untuk $carCode';
  }

  @override
  String get firstTripOfDay => 'Ini adalah perjalanan pertama hari ini untuk mobil ini.';

  @override
  String get enterNewOdometerReading => 'Masukkan pembacaan odometer baru';

  @override
  String get odometerReadingHelper => 'Nilai harus sama atau lebih besar dari pembacaan saat ini';

  @override
  String newReadingMustBeAtLeast(String minimum) {
    return 'Pembacaan baru harus minimal $minimum';
  }

  @override
  String get addCar => 'Tambah Mobil';

  @override
  String get editCar => 'Edit Mobil';

  @override
  String get failedToGenerateCarCode => 'Gagal menghasilkan kode mobil';

  @override
  String get pleaseEnterManufacturer => 'Silakan masukkan pabrikan';

  @override
  String get pleaseEnterModelName => 'Silakan masukkan nama model';

  @override
  String get pleaseEnterCarType => 'Silakan masukkan jenis mobil';

  @override
  String get pleaseEnterPlateNumber => 'Silakan masukkan nomor plat';

  @override
  String get pleaseEnterColor => 'Silakan masukkan warna';

  @override
  String get failedToAddCar => 'Gagal menambah mobil';

  @override
  String get addCargo => 'Tambah Kargo';

  @override
  String get editCargo => 'Edit Kargo';

  @override
  String get failedToGenerateCargoCode => 'Gagal menghasilkan kode kargo';

  @override
  String get pleaseEnterCargoName => 'Silakan masukkan nama kargo';

  @override
  String get failedToAddCargo => 'Gagal menambah kargo';

  @override
  String get submit => 'Kirim';

  @override
  String get adding => 'Menambahkan...';

  @override
  String get addDestination => 'Tambah Tujuan';

  @override
  String get editDestination => 'Edit Tujuan';

  @override
  String get failedToGenerateDestinationCode => 'Gagal menghasilkan kode tujuan';

  @override
  String get pleaseEnterDestinationName => 'Silakan masukkan nama tujuan';

  @override
  String get pleaseEnterAddress => 'Silakan masukkan alamat';

  @override
  String get pleaseEnterInitial => 'Silakan masukkan inisial';

  @override
  String get failedToAddDestination => 'Gagal menambah tujuan';

  @override
  String get destinationName => 'Nama Tujuan';

  @override
  String get durationHours => 'Durasi (Jam)';

  @override
  String get durationMinutes => 'Durasi (Menit)';

  @override
  String get coordinatesLatLong => 'Koordinat (Lintang, Bujur)';

  @override
  String get coordinatesFormat => 'Format: lat,long (mis. 12.3456,78.9012)';

  @override
  String get pleaseEnterCoordinates => 'Silakan masukkan koordinat';

  @override
  String get invalidHours => 'Jam tidak valid';

  @override
  String get minutesMustBe0To59 => 'Menit harus 0-59';

  @override
  String get mustBe3Characters => 'Harus tepat 3 karakter (akan diubah ke huruf besar)';

  @override
  String get initialRequired => 'Inisial wajib diisi';

  @override
  String get failedToGenerateDriverCode => 'Gagal menghasilkan kode pengemudi';

  @override
  String get pleaseEnterName => 'Silakan masukkan nama';

  @override
  String get pleaseConfirmPassword => 'Silakan konfirmasi kata sandi';

  @override
  String get generatingCode => 'Menghasilkan kode...';

  @override
  String get nameUppercaseHelper => 'Nama akan diubah ke huruf besar';

  @override
  String get searchForACar => 'Cari mobil';

  @override
  String get pleaseSelectACar => 'Silakan pilih mobil';

  @override
  String get confirmPassword => 'Konfirmasi Kata Sandi';

  @override
  String get passwordMinLength => 'Kata sandi harus minimal 6 karakter';

  @override
  String get failedToAddDriver => 'Gagal menambah pengemudi';

  @override
  String get updateCar => 'Perbarui Mobil';

  @override
  String get updateCargo => 'Perbarui Kargo';

  @override
  String get updating => 'Memperbarui...';

  @override
  String get updateDestination => 'Perbarui Tujuan';

  @override
  String get failedToUpdateCar => 'Gagal memperbarui mobil';

  @override
  String get failedToUpdateCargo => 'Gagal memperbarui kargo';

  @override
  String get failedToUpdateDestination => 'Gagal memperbarui tujuan';

  @override
  String get modelName => 'Nama Model';

  @override
  String get onMobileDateRangeLimited => 'Di mobile, rentang tanggal dibatasi maksimal 9 hari';

  @override
  String get unavailableDemoReason => 'Tidak tersedia: Alasan demo';

  @override
  String updatedDriverAvailability(String driverName, String date) {
    return 'Memperbarui ketersediaan $driverName untuk $date';
  }

  @override
  String editAvailabilityFor(String driverName, Object carCode) {
    return 'Edit Ketersediaan untuk $carCode';
  }

  @override
  String get tripNotes => 'Catatan Perjalanan:';

  @override
  String get assignTo => 'Tugaskan ke:';

  @override
  String get fetchingTrips => 'Mengambil perjalanan...';

  @override
  String get noTripsWithStatusRequest => 'Tidak ada perjalanan dengan status REQUEST ditemukan.';

  @override
  String get noTripsWithStatusDriverRejected => 'Tidak ada perjalanan dengan status DRIVER REJECTED ditemukan.';

  @override
  String get assigningDriver => 'Menugaskan pengemudi...';

  @override
  String get deletingTrip => 'Menghapus perjalanan...';

  @override
  String get deleteAction => 'Hapus';

  @override
  String get assignAction => 'Tugaskan';

  @override
  String get selectDriverFirst => 'Pilih pengemudi terlebih dahulu';

  @override
  String get selectCar => 'Pilih mobil';

  @override
  String get completedStatus => 'SELESAI';

  @override
  String get completedDuration => 'Durasi SELESAI';

  @override
  String get tripInProgress => 'PERJALANAN SEDANG BERLANGSUNG';

  @override
  String get tripInProgressDuration => 'Durasi PERJALANAN SEDANG BERLANGSUNG';

  @override
  String get waitingForRating => 'MENUNGGU PENILAIAN';

  @override
  String get waitingForRatingDuration => 'Durasi MENUNGGU PENILAIAN';

  @override
  String get assignToDriverDuration => 'Durasi TUGASKAN KE PENGEMUDI';

  @override
  String foundRequestTrips(int count) {
    return 'Ditemukan $count perjalanan permintaan:';
  }

  @override
  String get noRequestTripsFound => 'Tidak ada perjalanan dengan status PERMINTAAN ditemukan.';

  @override
  String foundDriverRejectedTrips(int count) {
    return 'Ditemukan $count perjalanan yang ditolak pengemudi:';
  }

  @override
  String get noDriverRejectedTripsFound => 'Tidak ada perjalanan dengan status DITOLAK PENGEMUDI ditemukan.';

  @override
  String availabilityUpdatedFor(String driverName) {
    return 'Ketersediaan diperbarui untuk $driverName';
  }

  @override
  String get sick => 'Sakit';

  @override
  String get breakStatus => 'Istirahat';

  @override
  String get onBreak => 'Sedang Istirahat';

  @override
  String get noReason => 'Tanpa Alasan';

  @override
  String get noReasonProvided => 'Tidak ada alasan yang diberikan';

  @override
  String get setStatus => 'Atur Status';

  @override
  String get startDate => 'Tanggal Mulai';

  @override
  String get endDate => 'Tanggal Selesai';

  @override
  String get thisWeek => 'Minggu Ini';

  @override
  String get nextWeek => 'Minggu Depan';

  @override
  String get apply => 'Terapkan';

  @override
  String get scrollDates => 'Gulir Tanggal';

  @override
  String get selectDateRange => 'Pilih Rentang Tanggal';

  @override
  String get editAvailability => 'Edit Ketersediaan';

  @override
  String get maintenance => 'Perawatan';

  @override
  String get broken => 'Rusak';

  @override
  String get borrowed => 'Dipinjam';

  @override
  String get dateRange => 'Rentang Tanggal';

  @override
  String get statusLegend => 'Legenda Status:';

  @override
  String get plate => 'Plat:';

  @override
  String get tripLabel => 'Perjalanan:';

  @override
  String get driverLabel => 'Pengemudi: ';

  @override
  String get notesLabel => 'Catatan';

  @override
  String get dateTime => 'Tanggal/Waktu';

  @override
  String pageXofY(int current, int total) {
    return 'Halaman $current dari $total';
  }

  @override
  String get singleCar => 'Mobil Tunggal';

  @override
  String get allCars => 'Semua Mobil';

  @override
  String get selectDate => 'Pilih Tanggal';

  @override
  String get searchByDriverTripNotes => 'Cari berdasarkan pengemudi, perjalanan, catatan...';

  @override
  String get noOdometerHistoryRecordsFound => 'Tidak ada catatan riwayat odometer ditemukan';

  @override
  String get dateColon => 'Tanggal:';

  @override
  String get startDateColon => 'Tanggal Mulai:';

  @override
  String get endDateColon => 'Tanggal Selesai:';

  @override
  String get driverInformation => 'Informasi Driver';

  @override
  String get checkInInformation => 'Informasi Check-In';

  @override
  String get completeCheckIn => 'Selesaikan Check-In';

  @override
  String get loggingOut => 'Sedang logout...';

  @override
  String get clickButtonCompleteCheckIn => 'Klik tombol di bawah untuk menyelesaikan check-in Anda.';

  @override
  String get driverCheckOut => 'Check-Out Driver';

  @override
  String get confirmCheckOut => 'Konfirmasi Check-Out';

  @override
  String get completeCheckOut => 'Selesaikan Check-Out';

  @override
  String get checkOutSuccessfulRedirecting => 'Check-out berhasil! Mengalihkan ke check-in...';

  @override
  String get afterCheckOutRedirectMessage => 'Setelah check-out, Anda akan dialihkan ke layar check-in. Apakah Anda ingin melanjutkan?';

  @override
  String get checkOutInformation => 'Informasi Check-Out';

  @override
  String get clickButtonCompleteCheckOut => 'Klik tombol di bawah untuk menyelesaikan check-out Anda.';

  @override
  String get filterTrips => 'Filter Perjalanan';

  @override
  String get searchByTripCode => 'Cari berdasarkan kode perjalanan, lokasi, pemohon, rating, komentar, dll.';

  @override
  String get clearAllFilters => 'Hapus Semua Filter';

  @override
  String get comments => 'Komentar';

  @override
  String get checkInTime => 'Waktu Check-In';

  @override
  String get driverCodeColon => 'Kode Driver:';

  @override
  String get checkInTimeColon => 'Waktu Check-In:';

  @override
  String get proceed => 'Lanjutkan';

  @override
  String get tripColon => 'Perjalanan:';

  @override
  String get fromColon => 'Dari:';

  @override
  String get toColon => 'Ke:';

  @override
  String get driverColon => 'Pengemudi:';

  @override
  String get atTime => 'pada';

  @override
  String get view => 'Lihat';

  @override
  String get viewRejection => 'Lihat Penolakan';

  @override
  String get filterByDateLabel => 'Filter berdasarkan Tanggal';

  @override
  String get searchLabel => 'Cari';

  @override
  String get unassigned => 'Belum Ditugaskan';

  @override
  String get rejectionReasonTitle => 'Alasan Penolakan';

  @override
  String tripDetailsTitle(String tripCode) {
    return 'Detail Perjalanan $tripCode';
  }

  @override
  String get finishTripConfirmTitle => 'Konfirmasi Selesaikan Perjalanan';

  @override
  String get finishTripConfirmMessage => 'Apakah Anda yakin ingin menandai perjalanan ini sebagai selesai?';

  @override
  String get markAsFinished => 'Tandai Selesai';

  @override
  String get tripRejectedMessage => 'Perjalanan ini ditolak dengan alasan berikut:';

  @override
  String get tripDetailsColon => 'Detail Perjalanan:';

  @override
  String get fromLabel => 'Dari:';

  @override
  String get toLabel => 'Ke:';

  @override
  String get dateLabel => 'Tanggal:';

  @override
  String get timeLabel => 'Waktu';

  @override
  String get statusLabel => 'Status:';

  @override
  String get driverIdLabel => 'ID Pengemudi';

  @override
  String get ratingLabel => 'Rating';

  @override
  String get passengersLabel => 'Penumpang';

  @override
  String get cargoLabel => 'Kargo';

  @override
  String get noneLabel => 'Tidak ada';

  @override
  String get tripCodeHeader => 'Kode Perjalanan';

  @override
  String get fromHeader => 'Dari';

  @override
  String get toHeader => 'Ke';

  @override
  String get dateHeader => 'Tanggal';

  @override
  String get timeHeader => 'Waktu';

  @override
  String get statusHeader => 'Status';

  @override
  String get driverHeader => 'Pengemudi';

  @override
  String get actionsHeader => 'Aksi';

  @override
  String get viewRejectionReason => 'Lihat Alasan Penolakan';

  @override
  String get tripMarkedAsCompleted => 'Perjalanan telah ditandai sebagai selesai';

  @override
  String get tripDataRefreshedSuccessfully => 'Data perjalanan berhasil disegarkan';

  @override
  String get roundTrip => 'Perjalanan Pulang Pergi';

  @override
  String get waiting => 'Menunggu';

  @override
  String get outboundJourney => 'Perjalanan Berangkat';

  @override
  String get returnJourney => 'Perjalanan Pulang';

  @override
  String get waitingTime => 'Waktu Tunggu';

  @override
  String get returnDate => 'Tanggal Pulang';

  @override
  String get returnTime => 'Waktu Pulang';

  @override
  String get submitTrip => 'Kirim Perjalanan';

  @override
  String get customFrom => 'Dari Khusus';

  @override
  String get customTo => 'Ke Khusus';

  @override
  String get others => 'Lainnya';

  @override
  String get returnDateCannotBeEarlier => 'Tanggal pulang tidak boleh lebih awal dari tanggal berangkat';

  @override
  String get outboundDateCannotBeLater => 'Tanggal berangkat tidak boleh lebih lambat dari tanggal pulang';

  @override
  String get returnDateAutomaticallyAdjusted => 'Tanggal pulang otomatis disesuaikan dengan tanggal berangkat';

  @override
  String get returnTimeAutomaticallyAdjusted => 'Waktu pulang otomatis disesuaikan agar setelah waktu berangkat';

  @override
  String get returnDateTimeAutomaticallyAdjusted => 'Tanggal dan waktu pulang otomatis disesuaikan agar setelah tanggal dan waktu berangkat';

  @override
  String get returnTimeMustBeAfterOutbound => 'Waktu pulang harus setelah waktu berangkat pada hari yang sama';

  @override
  String get outboundTimeMustBeBeforeReturn => 'Waktu berangkat harus sebelum waktu pulang pada hari yang sama';

  @override
  String get outboundDateTimeMustBeBeforeReturn => 'Tanggal dan waktu berangkat harus sebelum tanggal dan waktu pulang';

  @override
  String get returnDateTimeMustBeAfterOutbound => 'Tanggal dan waktu pulang harus setelah tanggal dan waktu berangkat';

  @override
  String get returnTimeMustBeAfterOutboundWarning => 'Peringatan: Tanggal dan waktu pulang harus setelah tanggal dan waktu berangkat. Silakan sesuaikan pilihan Anda.';

  @override
  String get outboundTimeMustBeBeforeReturnOnSameDay => 'Waktu berangkat harus sebelum waktu pulang pada hari yang sama';

  @override
  String get returnFromAndToCannotBeSame => 'Tujuan Dari dan Ke untuk perjalanan pulang tidak boleh sama';

  @override
  String get returnFromAndToCustomCannotBeSame => 'Tujuan khusus Dari dan Ke untuk perjalanan pulang tidak boleh sama';

  @override
  String get pleaseEnterCustomReturnTo => 'Silakan masukkan tujuan khusus ke untuk perjalanan pulang';

  @override
  String get pleaseSelectOutboundDateTime => 'Silakan pilih tanggal dan waktu berangkat';

  @override
  String get pleaseSelectReturnDateTime => 'Silakan pilih tanggal dan waktu pulang';

  @override
  String get pleaseSelectDateTime => 'Silakan pilih tanggal dan waktu';

  @override
  String get invalidDateTimeFormat => 'Format tanggal atau waktu tidak valid';

  @override
  String get pleaseEnterCustomFrom => 'Silakan masukkan dari khusus';

  @override
  String get pleaseEnterCustomTo => 'Silakan masukkan ke khusus';

  @override
  String get enterNotesOptional => 'Masukkan catatan perjalanan (opsional)';

  @override
  String get photo => 'Foto:';

  @override
  String get pleaseProvideRejectionReason => 'Silakan berikan alasan penolakan perjalanan ini:';

  @override
  String get takePhoto => 'Ambil Foto';

  @override
  String get addNotesPhotoOptional => 'Tambahkan catatan dan/atau foto (opsional):';

  @override
  String get shareYourExperienceOptional => 'Bagikan pengalaman Anda (opsional)';

  @override
  String get confirmTripCompletion => 'Konfirmasi Penyelesaian Perjalanan';

  @override
  String get areYouSureMarkTripCompleted => 'Apakah Anda yakin ingin menandai perjalanan ini sebagai selesai?';

  @override
  String get timeColon => 'Waktu:';

  @override
  String get notesColon => 'Catatan:';

  @override
  String get tripRequestsByStatus => 'Permintaan Perjalanan berdasarkan Status';

  @override
  String get newTripRequests => 'Permintaan perjalanan baru';

  @override
  String get tripEditRequestsPendingApproval => 'Permintaan edit perjalanan menunggu persetujuan';

  @override
  String get tripCancellationRequestsPendingApproval => 'Permintaan pembatalan perjalanan menunggu persetujuan';

  @override
  String get tripsReadyToBeAssignedToDrivers => 'Perjalanan siap ditugaskan ke pengemudi';

  @override
  String get tripsAssignedToOnlineTaxi => 'Perjalanan ditugaskan ke taksi online';

  @override
  String get tripsRejectedByDrivers => 'Perjalanan ditolak oleh pengemudi';

  @override
  String get tripsConfirmedByDrivers => 'Perjalanan dikonfirmasi oleh pengemudi';

  @override
  String get tripsCurrentlyInProgress => 'Perjalanan sedang berlangsung';

  @override
  String get completedTripsWaitingForRating => 'Perjalanan selesai menunggu penilaian';

  @override
  String get assignDriverStatusExplanation => 'Menugaskan driver akan mengubah status perjalanan menjadi \"ASSIGN TO DRIVER\". Memilih \"ONLINE\" akan mengubah status menjadi \"ASSIGN TO ONLINE TAXI\".';

  @override
  String tripDateLabel(String date, String formattedDate) {
    return 'Tanggal Perjalanan: $date ($formattedDate)';
  }

  @override
  String lookingUpTripId(String tripCode) {
    return 'Mencari ID perjalanan untuk $tripCode...';
  }

  @override
  String cannotAssignDriverMissingId(String tripCode) {
    return 'Tidak dapat menugaskan driver: ID perjalanan hilang untuk kode perjalanan $tripCode';
  }

  @override
  String currentDateTime(String dateTime) {
    return 'Tanggal dan Waktu Saat Ini: $dateTime';
  }

  @override
  String get deleteTripWarning => 'Ini akan mengubah status perjalanan menjadi DELETED. Tindakan ini tidak dapat dibatalkan.\n\nApakah Anda yakin ingin melanjutkan?';

  @override
  String errorDeletingTrip(String error) {
    return 'Kesalahan menghapus perjalanan: $error';
  }

  @override
  String get statusDriverRejected => 'Status: DITOLAK DRIVER';

  @override
  String get tripRejectedWithReason => 'Perjalanan ini ditolak oleh driver dengan alasan berikut:';

  @override
  String get tripRejectedNoReason => 'Perjalanan ini ditolak oleh driver, tetapi tidak ada alasan yang diberikan.';

  @override
  String tripCodeLabel(String tripCode) {
    return 'Kode Perjalanan: $tripCode';
  }

  @override
  String get tripDurationLegend => 'Durasi Perjalanan';

  @override
  String get noTripRequestsForDate => 'Tidak ada permintaan perjalanan untuk tanggal ini';

  @override
  String selectedDateLabel(String date) {
    return 'Tanggal yang dipilih: $date';
  }

  @override
  String get timePeriodsTitle => 'Periode Waktu:';

  @override
  String get nightPeriod => 'Malam (00:00-06:00)';

  @override
  String get morningPeriod => 'Pagi (06:00-12:00)';

  @override
  String get afternoonPeriod => 'Siang (12:00-18:00)';

  @override
  String get eveningPeriod => 'Malam (18:00-24:00)';

  @override
  String get noCarsAvailableMessage => 'Tidak ada mobil tersedia untuk tanggal ini. Semua mobil mungkin ditandai sebagai TIDAK TERSEDIA.';

  @override
  String get noDriversAvailableMessage => 'Tidak ada pengemudi tersedia untuk tanggal ini. Semua pengemudi mungkin ditandai sebagai TIDAK TERSEDIA.';

  @override
  String get driverRejectedTripsTitle => 'Perjalanan DITOLAK PENGEMUDI';

  @override
  String get navigateTimeSlots => 'Navigasi slot waktu:';

  @override
  String get legend => 'Legenda:';

  @override
  String get assignToDriverWithDriver => 'TUGASKAN KE PENGEMUDI (dengan pengemudi)';

  @override
  String get assignToDriverWithoutDriver => 'TUGASKAN KE PENGEMUDI (tanpa pengemudi)';

  @override
  String get assignToDriverWithoutDriverDuration => 'TUGASKAN KE PENGEMUDI (tanpa pengemudi) Durasi';

  @override
  String get refreshTripRequestsTooltip => 'Segarkan permintaan perjalanan';

  @override
  String get refreshDriverRejectedTripsTooltip => 'Segarkan perjalanan yang ditolak pengemudi';

  @override
  String driverRejectedTripsForDate(String date) {
    return 'Perjalanan Ditolak Pengemudi untuk $date';
  }

  @override
  String get noDriverRejectedTripsForDate => 'Tidak ada perjalanan yang ditolak pengemudi untuk tanggal ini';

  @override
  String errorFetchingOnlineTaxiTrips(String error) {
    return 'Kesalahan mengambil daftar perjalanan taksi online: $error';
  }

  @override
  String errorFetchingTrips(String error) {
    return 'Kesalahan mengambil perjalanan: $error';
  }

  @override
  String errorFetchingDriverRejectedTrips(String error) {
    return 'Kesalahan mengambil perjalanan yang ditolak pengemudi: $error';
  }

  @override
  String errorFetchingTripsWithStatus(String status1, String status2) {
    return 'Kesalahan mengambil perjalanan: $status1 / $status2';
  }

  @override
  String errorFetchingTripsWithStatusCodes(String statusCodes) {
    return 'Kesalahan mengambil perjalanan: $statusCodes';
  }

  @override
  String tripStatusChangedToOnlineTaxi(String tripCode) {
    return 'Status perjalanan $tripCode diubah ke ASSIGN TO ONLINE TAXI.';
  }

  @override
  String errorAssigningDriver(String error) {
    return 'Kesalahan menugaskan pengemudi: $error';
  }

  @override
  String tripHasBeenDeleted(String tripCode) {
    return 'Perjalanan $tripCode telah dihapus.';
  }

  @override
  String tripRequestsForDate(String date) {
    return 'Permintaan Perjalanan untuk $date';
  }

  @override
  String requestCount(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'permintaan',
      one: 'permintaan',
    );
    return '$count $_temp0';
  }

  @override
  String tripCount(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'perjalanan',
      one: 'perjalanan',
    );
    return '$count $_temp0';
  }

  @override
  String tripsCount(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Perjalanan',
      one: 'Perjalanan',
    );
    return '$count $_temp0';
  }

  @override
  String get onlineLabel => 'Online: ';

  @override
  String get durationLabel => 'Durasi:';

  @override
  String get taxiText => 'TAKSI';

  @override
  String get noCarAssigned => 'Tidak Ada Mobil Ditugaskan';

  @override
  String get statusDetailLabel => 'Status';

  @override
  String get startTimeLabel => 'Waktu Mulai:';

  @override
  String get endTimeLabel => 'Waktu Selesai:';

  @override
  String get durationDetailLabel => 'Durasi';

  @override
  String get driverDetailLabel => 'Pengemudi';

  @override
  String get driverCodeDetailLabel => 'Kode Pengemudi';

  @override
  String get fromDetailLabel => 'Dari';

  @override
  String get toDetailLabel => 'Ke';

  @override
  String get additionalInformation => 'Informasi Tambahan';

  @override
  String get tripIdLabel => 'ID Perjalanan';

  @override
  String get notAssigned => 'Tidak ditugaskan';

  @override
  String get carInformation => 'Informasi Mobil';

  @override
  String get carIdLabel => 'ID Mobil';

  @override
  String get carCodeDetailLabel => 'Kode Mobil';

  @override
  String get plateNumberLabel => 'Nomor Plat';

  @override
  String get passengersSection => 'Penumpang:';

  @override
  String get cargoSection => 'Kargo:';

  @override
  String get returnToRequest => 'Kembali ke REQUEST';

  @override
  String get deleteTrip => 'Hapus';

  @override
  String deleteTripTitle(String tripCode) {
    return 'Hapus Perjalanan $tripCode?';
  }

  @override
  String get deleteTripWarningMessage => 'Ini akan mengubah status perjalanan menjadi DELETED. Tindakan ini tidak dapat dibatalkan.\\n\\nApakah Anda yakin ingin melanjutkan?';

  @override
  String get minutesUnit => 'menit';

  @override
  String get onlineTaxiText => 'TAKSI ONLINE';

  @override
  String get noCar => 'Tidak Ada Mobil';

  @override
  String get noPlate => 'Tidak Ada Plat';

  @override
  String get updatingTripDuration => 'Memperbarui durasi perjalanan...';

  @override
  String errorUpdatingDuration(String error) {
    return 'Kesalahan memperbarui durasi: $error';
  }

  @override
  String get updatingTripStatus => 'Memperbarui status perjalanan...';

  @override
  String tripStatusChangedToRequest(String tripCode) {
    return 'Status perjalanan $tripCode diubah ke REQUEST.';
  }

  @override
  String get refreshingAllData => 'Menyegarkan semua data...';

  @override
  String errorGeneric(String error) {
    return 'Kesalahan: $error';
  }

  @override
  String get deletingTripMessage => 'Menghapus perjalanan...';

  @override
  String errorDeletingTripMessage(String error) {
    return 'Kesalahan menghapus perjalanan: $error';
  }

  @override
  String get errorDriverCodeMissing => 'Error: Kode pengemudi hilang. Silakan coba lagi.';

  @override
  String get backToBase => 'Kembali ke Base';

  @override
  String get checkOut => 'Check Out';

  @override
  String assignToDriverCount(int count) {
    return 'Tugaskan ke Pengemudi: $count';
  }

  @override
  String get selectStatus => 'Pilih Status';

  @override
  String tripCodeNumber(String tripCode) {
    return 'Perjalanan #$tripCode';
  }

  @override
  String durationInMinutes(int minutes) {
    return '$minutes menit';
  }

  @override
  String get requestorLabel => 'Pemohon:';

  @override
  String get rejectionLabel => 'Penolakan:';

  @override
  String get locationTrackingActive => 'Pelacakan lokasi aktif (pengemudi memiliki perjalanan aktif)';

  @override
  String get trackingReady => 'Pelacakan siap (pengemudi memiliki perjalanan aktif)';

  @override
  String get trackingDisabledNoActiveTrips => 'Pelacakan dinonaktifkan (tidak ada perjalanan aktif)';

  @override
  String get automaticTrackingDisabled => 'Pelacakan otomatis dinonaktifkan';

  @override
  String failedToCreateBackToBaseTrip(int statusCode) {
    return 'Gagal membuat perjalanan Kembali ke Base: $statusCode';
  }

  @override
  String errorCreatingBackToBaseTrip(String error) {
    return 'Error membuat perjalanan Kembali ke Base: $error';
  }

  @override
  String get locationTrackingStatus => 'Status Pelacakan Lokasi';

  @override
  String get autoTrackingLabel => 'Pelacakan Otomatis:';

  @override
  String get enabled => 'DIAKTIFKAN';

  @override
  String get disabled => 'DINONAKTIFKAN';

  @override
  String get activeTripsLabel => 'Perjalanan Aktif:';

  @override
  String get currentTimeLabel => 'Waktu Saat Ini:';

  @override
  String get todayLabel => 'Hari Ini:';

  @override
  String get locationLabel => 'Lokasi:';

  @override
  String get lastUpdateLabel => 'Pembaruan Terakhir:';

  @override
  String secondsAgo(int seconds) {
    return '$seconds detik yang lalu';
  }

  @override
  String minutesAgo(int minutes) {
    return '$minutes menit yang lalu';
  }

  @override
  String get trackingExplanation => 'Lokasi Anda dilacak hanya ketika Anda memiliki perjalanan aktif (TRIP IN PROGRESS atau BACK TO BASE):\n• Pelacakan secara otomatis diaktifkan ketika Anda memiliki perjalanan aktif\n• Pelacakan secara otomatis dinonaktifkan ketika Anda tidak memiliki perjalanan aktif\n• Pembaruan lokasi dikirim setiap menit ketika pelacakan aktif';

  @override
  String get tripCodeColumn => 'Kode Perjalanan';

  @override
  String get requestorColumn => 'Pemohon';

  @override
  String get fromColumn => 'Dari';

  @override
  String get toColumn => 'Ke';

  @override
  String get dateColumn => 'Tanggal';

  @override
  String get timeColumn => 'Waktu';

  @override
  String get driverColumn => 'Pengemudi';

  @override
  String get statusColumn => 'Status';

  @override
  String get ratingColumn => 'Rating';

  @override
  String pageOf(int current, int total) {
    return 'Halaman $current dari $total';
  }

  @override
  String get itemsText => 'item';

  @override
  String get tripAssignedToOnlineTaxiTitle => 'Perjalanan Ditugaskan ke Taksi Online';

  @override
  String tripAssignedToOnlineTaxiMessage(String tripCode, String fromDestination, String toDestination) {
    return 'Perjalanan Anda $tripCode dari $fromDestination ke $toDestination telah ditugaskan ke layanan taksi online.';
  }

  @override
  String tripAssignedToOnlineTaxiSuccess(String tripCode) {
    return 'Perjalanan $tripCode ditugaskan ke taksi online dan notifikasi terkirim.';
  }

  @override
  String notificationTripAssigned(String tripCode) {
    return 'Perjalanan Ditugaskan: $tripCode';
  }

  @override
  String notificationNewTripAssignment(String tripCode) {
    return 'Penugasan Perjalanan Baru: $tripCode';
  }

  @override
  String notificationTripApproved(String tripCode) {
    return 'Perjalanan Disetujui: $tripCode';
  }

  @override
  String notificationTripRejected(String tripCode) {
    return 'Perjalanan Ditolak: $tripCode';
  }

  @override
  String notificationTripCompleted(String tripCode) {
    return 'Perjalanan Selesai: $tripCode';
  }

  @override
  String notificationTripStatusUpdate(String tripCode) {
    return 'Pembaruan Status Perjalanan: $tripCode';
  }

  @override
  String notificationTripRated(String tripCode) {
    return 'Perjalanan Dinilai: $tripCode';
  }

  @override
  String notificationTripReturnedToRequest(String tripCode) {
    return 'Perjalanan Dikembalikan ke Permintaan: $tripCode';
  }

  @override
  String notificationTripDeleted(String tripCode) {
    return 'Perjalanan Dihapus: $tripCode';
  }

  @override
  String notificationBackToBaseTripCompleted(String tripCode) {
    return 'Perjalanan Kembali ke Base Selesai: $tripCode';
  }

  @override
  String notificationTripAssignedToOnlineTaxi(String tripCode) {
    return 'Perjalanan Ditugaskan ke Taksi Online: $tripCode';
  }

  @override
  String notificationOnlineTaxiTripCompleted(String tripCode) {
    return 'Perjalanan Taksi Online Selesai: $tripCode';
  }

  @override
  String notificationBackToBaseTripCreated(String tripCode) {
    return 'Perjalanan Kembali ke Base Dibuat: $tripCode';
  }

  @override
  String notificationTripChangesApproved(String tripCode) {
    return 'Perubahan Perjalanan Disetujui: $tripCode';
  }

  @override
  String notificationNewTripRequest(String tripCode) {
    return 'Permintaan Perjalanan Baru: $tripCode';
  }

  @override
  String notificationTripEditRequest(String tripCode) {
    return 'Permintaan Edit Perjalanan: $tripCode';
  }

  @override
  String notificationTripCancellationRequest(String tripCode) {
    return 'Permintaan Pembatalan Perjalanan: $tripCode';
  }

  @override
  String notificationMessageTripAssignedToDriver(String tripCode, String date, String time) {
    return 'Perjalanan Anda $tripCode telah ditugaskan ke sopir dan dijadwalkan pada $date pukul $time.';
  }

  @override
  String notificationMessageDriverAssignedToTrip(String tripCode, String fromDestination, String toDestination, String date, String time) {
    return 'Anda telah ditugaskan untuk perjalanan $tripCode dari $fromDestination ke $toDestination pada $date pukul $time.';
  }

  @override
  String notificationMessageTripApproved(String tripCode, String fromDestination, String toDestination) {
    return 'Perjalanan Anda $tripCode dari $fromDestination ke $toDestination telah disetujui.';
  }

  @override
  String notificationMessageTripRejected(String tripCode, String rejectionReason) {
    return 'Perjalanan Anda $tripCode telah ditolak. Alasan: $rejectionReason.';
  }

  @override
  String notificationMessageTripRejectedNoReason(String tripCode) {
    return 'Perjalanan Anda $tripCode telah ditolak.';
  }

  @override
  String notificationMessageTripStatusChanged(String tripCode, String oldStatus, String newStatus) {
    return 'Status perjalanan Anda $tripCode telah berubah dari $oldStatus menjadi $newStatus.';
  }

  @override
  String notificationMessageDriverTripStatusChanged(String tripCode, String oldStatus, String newStatus) {
    return 'Status perjalanan $tripCode telah berubah dari $oldStatus menjadi $newStatus.';
  }

  @override
  String notificationMessageTripRated(String tripCode, String rating, String ratingMessage) {
    return 'Perjalanan Anda $tripCode telah dinilai $rating bintang oleh penumpang. $ratingMessage';
  }

  @override
  String notificationMessageTripRatedNoMessage(String tripCode, String rating) {
    return 'Perjalanan Anda $tripCode telah dinilai $rating bintang oleh penumpang.';
  }

  @override
  String notificationMessageTripReturnedToRequestor(String tripCode, String fromDestination, String toDestination) {
    return 'Perjalanan Anda $tripCode dari $fromDestination ke $toDestination telah dikembalikan ke status permintaan untuk penyesuaian.';
  }

  @override
  String notificationMessageTripReturnedToDriver(String tripCode, String fromDestination, String toDestination) {
    return 'Perjalanan $tripCode dari $fromDestination ke $toDestination telah dikembalikan ke status permintaan.';
  }

  @override
  String notificationMessageTripDeletedRequestor(String tripCode, String fromDestination, String toDestination) {
    return 'Perjalanan Anda $tripCode dari $fromDestination ke $toDestination telah dihapus oleh manajer perjalanan.';
  }

  @override
  String notificationMessageTripDeletedDriver(String tripCode, String fromDestination, String toDestination) {
    return 'Perjalanan $tripCode dari $fromDestination ke $toDestination telah dihapus oleh manajer perjalanan.';
  }

  @override
  String notificationMessageTripCompletedRequestor(String tripCode) {
    return 'Perjalanan Anda $tripCode telah selesai. Terima kasih telah menggunakan layanan kami.';
  }

  @override
  String notificationMessageTripCompletedManager(String tripCode) {
    return 'Perjalanan $tripCode telah selesai.';
  }

  @override
  String notificationMessageBackToBaseTripCompletedRequestor(String tripCode) {
    return 'Perjalanan kembali ke base Anda $tripCode telah berhasil diselesaikan.';
  }

  @override
  String notificationMessageBackToBaseTripCompletedManager(String tripCode) {
    return 'Perjalanan kembali ke base $tripCode telah selesai.';
  }

  @override
  String notificationMessageTripAssignedToOnlineTaxi(String tripCode, String fromDestination, String toDestination) {
    return 'Perjalanan $tripCode dari $fromDestination ke $toDestination telah ditugaskan ke taksi online.';
  }

  @override
  String notificationMessageOnlineTaxiTripCompletedRequestor(String tripCode) {
    return 'Perjalanan taksi online Anda $tripCode telah berhasil diselesaikan. Terima kasih telah menggunakan layanan kami!';
  }

  @override
  String notificationMessageOnlineTaxiTripCompletedManager(String tripCode) {
    return 'Perjalanan taksi online $tripCode telah ditandai selesai oleh peminta.';
  }

  @override
  String notificationMessageBackToBaseTripCreated(String driverName, String tripCode, String fromDestination, String toDestination, String date, String time) {
    return '$driverName telah membuat perjalanan \"Kembali ke Base\" $tripCode dari $fromDestination ke $toDestination pada $date pukul $time.';
  }

  @override
  String notificationMessageTripChangesApproved(String tripCode, String fromDestination, String toDestination) {
    return 'Perubahan Anda pada perjalanan $tripCode dari $fromDestination ke $toDestination telah disetujui. Perjalanan sekarang kembali ke status PERMINTAAN.';
  }

  @override
  String notificationMessageNewTripRequest(String requestorName, String tripCode, String fromDestination, String toDestination, String date, String time) {
    return '$requestorName telah meminta perjalanan baru $tripCode dari $fromDestination ke $toDestination pada $date pukul $time.';
  }

  @override
  String notificationMessageTripEditRequest(String userName, String tripCode, String fromDestination, String toDestination, String date, String time) {
    return '$userName telah meminta perubahan pada perjalanan $tripCode dari $fromDestination ke $toDestination pada $date pukul $time.';
  }

  @override
  String notificationMessageTripCancellationRequest(String userName, String tripCode, String fromDestination, String toDestination, String date, String time) {
    return '$userName telah meminta pembatalan perjalanan $tripCode dari $fromDestination ke $toDestination pada $date pukul $time.';
  }

  @override
  String notificationTripStarted(String tripCode) {
    return 'Perjalanan Dimulai: $tripCode';
  }

  @override
  String notificationDriverConfirmedTrip(String tripCode) {
    return 'Sopir Konfirmasi Perjalanan: $tripCode';
  }

  @override
  String notificationMessageTripStarted(String tripCode) {
    return 'Perjalanan $tripCode telah dimulai oleh sopir.';
  }

  @override
  String notificationMessageDriverConfirmedTrip(String driverName, String tripCode) {
    return '$driverName telah mengkonfirmasi perjalanan $tripCode.';
  }

  @override
  String notificationDriverRejectedTrip(String tripCode) {
    return 'Sopir Menolak Perjalanan: $tripCode';
  }

  @override
  String notificationMessageDriverRejectedTrip(String driverName, String tripCode, String rejectionReason) {
    return '$driverName telah menolak perjalanan $tripCode. Alasan: $rejectionReason.';
  }

  @override
  String notificationMessageDriverRejectedTripGeneric(String tripCode, String rejectionReason) {
    return 'Sopir telah menolak perjalanan $tripCode. Alasan: $rejectionReason.';
  }

  @override
  String notificationTripChangesRejected(String tripCode) {
    return 'Perubahan Perjalanan Ditolak: $tripCode';
  }

  @override
  String notificationMessageTripChangesRejected(String tripCode, String rejectionReason) {
    return 'Permintaan perubahan untuk perjalanan $tripCode telah ditolak. Alasan: $rejectionReason.';
  }

  @override
  String notificationTripCancellationRejected(String tripCode) {
    return 'Pembatalan Perjalanan Ditolak: $tripCode';
  }

  @override
  String notificationMessageTripCancellationRejected(String tripCode, String rejectionReason) {
    return 'Permintaan pembatalan untuk perjalanan $tripCode telah ditolak. Alasan: $rejectionReason.';
  }
}
