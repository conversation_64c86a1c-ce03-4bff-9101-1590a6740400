{"@@locale": "id", "appTitle": "FLEEX", "welcomeBack": "Selamat Datang Kembali", "signInToContinue": "Masuk untuk melanjutkan ke FLEEX", "username": "<PERSON><PERSON>", "password": "<PERSON><PERSON>", "login": "<PERSON><PERSON><PERSON>", "pleaseEnterUsername": "<PERSON><PERSON><PERSON> masukkan nama pengguna <PERSON>a", "pleaseEnterPassword": "<PERSON><PERSON><PERSON> masukkan kata sandi", "or": "ATAU", "signInWith": "<PERSON><PERSON><PERSON>", "signInWithMicrosoft": "Masuk dengan <PERSON>", "forgotPassword": "Lupa Kata Sandi?", "resetPassword": "Reset <PERSON>", "goBack": "Kembali", "newPassword": "<PERSON><PERSON>", "confirmNewPassword": "Konfirmasi <PERSON>", "pleaseEnterNewPassword": "<PERSON><PERSON><PERSON> masukkan kata sandi baru", "pleaseConfirmNewPassword": "<PERSON><PERSON><PERSON> konfirmasi kata sandi baru", "passwordMustBeAtLeast6Characters": "<PERSON><PERSON> sandi harus minimal 6 karakter", "passwordsDoNotMatch": "Kata sandi tidak cocok", "passwordResetSuccessful": "Reset password berhasil.", "dashboard": "Dashboard", "requestTrip": "<PERSON><PERSON>", "tripHistory": "Riwayat Per<PERSON>lanan", "settings": "<PERSON><PERSON><PERSON><PERSON>", "logout": "<PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON>", "date": "Tanggal", "time": "<PERSON><PERSON><PERSON>", "from": "Dari:", "to": "Ke:", "destination": "<PERSON><PERSON><PERSON>", "notes": "Catatan", "save": "Simpan", "cancel": "<PERSON><PERSON>", "edit": "Edit", "delete": "Hapus", "add": "Tambah", "required": "<PERSON><PERSON><PERSON>", "optional": "Opsional", "loading": "Memuat...", "error": "<PERSON><PERSON><PERSON>", "success": "<PERSON><PERSON><PERSON><PERSON>", "tripCode": "<PERSON><PERSON>", "status": "Status", "driver": "<PERSON><PERSON><PERSON><PERSON>", "car": "Mobil:", "passenger": "Penumpang", "passengers": "Penumpang", "duration": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON>", "filterByDate": "<PERSON><PERSON> be<PERSON><PERSON><PERSON>", "filterByStatus": "Filter berdasarkan Status", "allRecords": "<PERSON><PERSON><PERSON>", "clearFilter": "<PERSON><PERSON>", "refresh": "Refresh", "noDataFound": "Data tidak ditemukan", "pleaseEnterDate": "<PERSON><PERSON><PERSON> masukkan tanggal", "pleaseEnterTime": "<PERSON><PERSON><PERSON> masukkan waktu", "pleaseSelectFromDestination": "<PERSON><PERSON><PERSON> pilih asal tujuan", "pleaseSelectToDestination": "<PERSON><PERSON><PERSON> pilih tujuan", "addTrip": "Tambah Perjalanan", "editTrip": "<PERSON>", "requestorName": "<PERSON><PERSON>", "customLocation": "Lok<PERSON>", "selectOrigin": "<PERSON><PERSON><PERSON>", "selectDestination": "<PERSON><PERSON><PERSON> tu<PERSON>", "carCode": "Kode Mobil", "manufacturer": "Pabrikan", "model": "Model", "plateNumber": "<PERSON><PERSON>", "odometer": "Odometer", "color": "<PERSON><PERSON>", "type": "Tipe", "driverCode": "<PERSON><PERSON>", "driverName": "<PERSON><PERSON>", "initial": "Inisial", "address": "<PERSON><PERSON><PERSON>", "coordinates": "Ko<PERSON>inat", "hours": "Jam", "minutes": "Menit", "available": "Tersedia", "unavailable": "Tidak Tersedia", "pending": "<PERSON><PERSON><PERSON>", "approved": "Disetuju<PERSON>", "inProgress": "<PERSON><PERSON>", "completed": "Se<PERSON><PERSON>", "cancelled": "Di<PERSON><PERSON><PERSON>", "confirmDelete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "areYouSureDelete": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus item ini?", "yes": "YA", "no": "TIDAK", "dataRefreshedSuccessfully": "Data berhasil disegarkan", "failedToLoadData": "Gagal memuat data", "selectLanguage": "Bahasa", "chooseLanguage": "<PERSON><PERSON><PERSON> bahasa yang <PERSON>a <PERSON>:", "saveLanguage": "Simpan Bahasa", "languageChanged": "<PERSON><PERSON> be<PERSON><PERSON><PERSON>", "languageChangeNote": "Catatan: Aplikasi perlu direstart untuk beberapa perubahan dapat berlaku.", "language": "Bahasa", "commonSettings": "<PERSON><PERSON><PERSON><PERSON>", "driverSetting": "Pengat<PERSON><PERSON>", "carSetting": "Pengaturan <PERSON>", "cargoSetting": "<PERSON><PERSON><PERSON><PERSON>", "destinationSetting": "<PERSON><PERSON><PERSON><PERSON>", "backToHome": "<PERSON><PERSON><PERSON> ke Beranda", "tripMonitoring": "<PERSON><PERSON><PERSON><PERSON>", "driverTracking": "Pelacakan Sopir", "welcome": "Selamat Datang", "refreshNow": "<PERSON><PERSON><PERSON>", "youMustBeLoggedIn": "<PERSON>a harus masuk terle<PERSON>h dahulu", "failedToLoadTripStatusCounts": "Gagal memuat jumlah status perjalanan", "errorLoading": "Error memuat data", "tripApprovals": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dashboardTitle": "Dashboard Trip Manager", "requestorDashboardTitle": "Dashboard Pemohon", "areYouSureDeleteDriver": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus {name} sebagai pengemudi?", "failedToDeleteDriver": "<PERSON><PERSON><PERSON> pen<PERSON>", "anErrorOccurred": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>: {error}", "driverSettingsTitle": "<PERSON><PERSON><PERSON><PERSON>", "carSettingsTitle": "Pengaturan <PERSON>", "cargoSettingsTitle": "<PERSON><PERSON><PERSON><PERSON>", "destinationSettingsTitle": "<PERSON><PERSON><PERSON><PERSON>", "searchCars": "Cari <PERSON>", "searchCarsHint": "<PERSON><PERSON> be<PERSON> kode mobil, pabrikan, model, atau nomor plat", "noCarsFound": "Tidak ada mobil di<PERSON>ukan", "noMatchingCarsFound": "Tidak ada mobil yang cocok ditemukan", "failedToLoadCarsWithCode": "Gagal memuat mobil: {statusCode}", "carDeletedSuccessfully": "<PERSON><PERSON>", "failedToDeleteCar": "<PERSON><PERSON> mengh<PERSON>us mobil", "confirmDeleteCar": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus mobil {carCode}?", "searchCargos": "<PERSON><PERSON> ka<PERSON>", "searchCargosHint": "<PERSON>i berda<PERSON>kan kode kargo atau nama", "noCargosFound": "Tidak ada kargo ditemukan", "noMatchingCargosFound": "Tidak ada kargo yang cocok ditemukan", "failedToLoadCargos": "Gagal memuat kargo: {statusCode}", "noCargoDataFound": "Data kargo tidak ditemukan", "cargoDeletedSuccessfully": "<PERSON><PERSON> be<PERSON><PERSON>", "failedToDeleteCargo": "<PERSON><PERSON><PERSON> kargo", "confirmDeleteCargo": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus kargo {cargoCode}?", "cargoCode": "<PERSON><PERSON>", "cargoName": "<PERSON><PERSON>", "searchDestinations": "<PERSON><PERSON> t<PERSON>", "searchDestinationsHint": "<PERSON>i be<PERSON> kode tujuan, nama, atau alamat", "noDestinationsFound": "Tidak ada tujuan di<PERSON>n", "noMatchingDestinationsFound": "Tidak ada tujuan yang cocok ditemukan", "failedToLoadDestinations": "Gagal memuat tujuan: {statusCode}", "destinationDeletedSuccessfully": "<PERSON><PERSON><PERSON>", "failedToDeleteDestination": "<PERSON><PERSON> tu<PERSON>", "confirmDeleteDestination": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus tujuan {destinationCode}?", "destinationCode": "<PERSON><PERSON>", "active": "AKTIF", "inactive": "TIDAK AKTIF", "tripApprovalsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "noTripsForApproval": "Tidak ada perjalanan untuk disetujui", "failedToFetchTrips": "Gagal mengambil data perjalanan", "requestedBy": "<PERSON><PERSON><PERSON> o<PERSON>", "requestDate": "<PERSON><PERSON>", "tripDate": "<PERSON><PERSON>", "purpose": "<PERSON><PERSON><PERSON>", "approve": "<PERSON><PERSON><PERSON><PERSON>", "reject": "<PERSON><PERSON>", "viewChanges": "<PERSON><PERSON>", "details": "Detail", "tripApprovedSuccessfully": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tripRejectedSuccessfully": "<PERSON><PERSON><PERSON><PERSON> be<PERSON><PERSON><PERSON>", "failedToUpdateTrip": "<PERSON><PERSON>", "addDriver": "Tambah Pengemudi", "searchDrivers": "<PERSON><PERSON> pen<PERSON>", "noDriversFound": "Tidak ada pengemudi di<PERSON>n", "rateTripTitle": "<PERSON><PERSON>", "howWouldYouRateTrip": "<PERSON><PERSON><PERSON> pen<PERSON>ian Anda terhadap perjalanan ini?", "additionalComments": "<PERSON><PERSON><PERSON>", "submitRating": "<PERSON><PERSON>", "errorSubmittingRating": "Gagal mengirim rating", "tripChangeApproval": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confirmApproval": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confirmRejection": "Konfi<PERSON><PERSON>", "areYouSureApproveTrip": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menyetujui perjalanan {tripCode}? Ini akan mengubah status menjadi REQUEST.", "areYouSureApproveChanges": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menyetujui perubahan perjalanan {tripCode}? Ini akan mengubah status menjadi REQUEST.", "areYouSureApproveCancellation": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menyetujui pembatalan perjalanan {tripCode}? Ini akan mengubah status menjadi DELETED.", "areYouSureRejectTrip": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menolak perjalanan {tripCode}?", "areYouSureRejectChanges": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menolak perubahan perjalanan {tripCode}? Perjalanan akan dikembalikan ke keadaan semula sebelum diedit.", "areYouSureRejectCancellation": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menolak pembatalan perjalanan {tripCode}? Perjalanan akan tetap aktif.", "pleaseProvideReason": "<PERSON><PERSON>an berikan alasan penolakan:", "enterRejectionReason": "<PERSON><PERSON><PERSON><PERSON> alasan penolakan perjalanan", "reasonIsRequired": "Alasa<PERSON> diperlukan", "requestor": "<PERSON><PERSON><PERSON><PERSON>", "tripApprovedAndStatusChanged": "<PERSON><PERSON><PERSON><PERSON> disetujui dan status diubah menjadi REQUEST", "failedToApproveTrip": "<PERSON><PERSON><PERSON>", "failedToRejectTrip": "<PERSON><PERSON>", "failedToApproveChanges": "<PERSON><PERSON><PERSON> per<PERSON> per<PERSON>n", "failedToApproveCancellation": "Gagal menyetujui pembatalan perjalanan", "failedToRejectChanges": "<PERSON><PERSON> menolak per<PERSON> per<PERSON>n", "failedToRejectCancellation": "Gagal menolak pembatalan perjalanan", "tripChangesApprovedSuccessfully": "<PERSON><PERSON><PERSON> per<PERSON>n ber<PERSON><PERSON> di<PERSON>. Status diubah menjadi REQUEST.", "tripCancellationApprovedSuccessfully": "Pembatalan perjalanan berhasil disetujui. Status diubah menjadi DELETED.", "tripChangesRejectedSuccessfully": "<PERSON><PERSON><PERSON> perja<PERSON>n ber<PERSON><PERSON> di<PERSON>. Status diubah menjadi REQUEST.", "tripCancellationRejectedSuccessfully": "Pembatalan perjalanan ber<PERSON><PERSON> ditolak. Status diubah menjadi REQUEST.", "approveTrip": "<PERSON><PERSON><PERSON><PERSON>", "approveChanges": "<PERSON><PERSON><PERSON><PERSON>", "approveCancellation": "Setujui Pembatalan", "rejectTrip": "<PERSON><PERSON>", "rejectChanges": "<PERSON><PERSON>", "rejectCancellation": "Tolak Pembatalan", "noTripsFound": "Tidak ada perjalanan di<PERSON>n", "tripMonitoringTitle": "<PERSON><PERSON><PERSON><PERSON>", "trackDrivers": "<PERSON><PERSON>", "allRequests": "<PERSON><PERSON><PERSON>", "refreshData": "Segarkan Data", "swipeToChangeDate": "<PERSON><PERSON><PERSON> untuk mengubah tanggal", "errorRefreshingData": "Kesalahan menyegarkan data: {error}", "switchRole": "Ganti Peran", "currentRole": "<PERSON><PERSON>", "confirmRoleSwitch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "switchRoleMessage": "<PERSON><PERSON><PERSON><PERSON>a yakin ingin beralih ke {role}? Ini akan mengubah antarmuka dan fungsi yang tersedia.", "roleSwitchedSuccessfully": "<PERSON><PERSON><PERSON><PERSON> be<PERSON> ke {role}", "searchDriversHint": "<PERSON>i berda<PERSON>kan kode pengemudi, nama, atau inisial", "noMatchingDriversFound": "Tidak ada pengemudi yang cocok ditemukan", "driverDeletedSuccessfully": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "failedToLoadDrivers": "Gagal memuat pengemudi: {statusCode}", "driverCodeLabel": "<PERSON><PERSON>", "carLabel": "Mobil:", "saveChanges": "<PERSON><PERSON><PERSON>", "waitingTrip": "<PERSON><PERSON><PERSON><PERSON>", "fromDestination": "<PERSON><PERSON><PERSON>", "toDestination": "<PERSON><PERSON><PERSON>", "notesOptional": "Catatan (opsional)", "selectPassengers": "<PERSON><PERSON><PERSON>", "selectCargos": "<PERSON><PERSON><PERSON>", "trip": "<PERSON><PERSON><PERSON><PERSON>", "cargos": "<PERSON><PERSON>", "searchForDestinations": "<PERSON><PERSON> t<PERSON>", "customFromDestination": "<PERSON><PERSON><PERSON>", "customToDestination": "<PERSON><PERSON><PERSON>", "enterCustomLocation": "<PERSON><PERSON><PERSON><PERSON> lokasi khusus", "addAdditionalNotes": "Tambahkan catatan tambahan untuk perjalanan ini", "searchForPassengers": "<PERSON><PERSON>", "chooseOneOrMorePassengers": "<PERSON><PERSON>h satu atau lebih penumpang", "searchForCargos": "<PERSON><PERSON> ka<PERSON>", "chooseOneOrMoreCargos": "<PERSON><PERSON>h satu atau lebih kargo", "rateTrips": "<PERSON><PERSON>", "myTrips": "<PERSON><PERSON><PERSON><PERSON>", "checkIn": "Check-In", "notifications": "Notif<PERSON><PERSON>", "admin": "Admin", "now": "<PERSON><PERSON><PERSON>", "currentTime": "<PERSON><PERSON><PERSON>", "rating": "Rating", "cargo": "<PERSON><PERSON>", "clearStatusFilter": "Hapus Filter Status", "specificDate": "Tanggal Spesifik", "year": "<PERSON><PERSON>", "month": "<PERSON><PERSON><PERSON>", "currentFilter": "<PERSON><PERSON>", "selectYear": "<PERSON><PERSON><PERSON>", "selectMonth": "<PERSON><PERSON><PERSON>", "searchTrips": "<PERSON><PERSON> per<PERSON> (kode, pemohon, lokasi...)", "searchTripsLong": "<PERSON><PERSON> per<PERSON> (kode, pemohon, lokasi...)", "itemsPerPage": "Item per halaman", "exportToExcel": "Ekspor ke Excel", "exporting": "Mengekspor...", "exportingToExcel": "Mengekspor ke Excel...", "noTripsToExport": "Tidak ada perjalanan untuk diekspor", "tripHistoryExported": "Riwayat perjalanan diekspor sebagai {fileName}", "tripHistoryExportedTo": "Riwayat perjalanan diekspor ke: {filePath}", "exportFailed": "Eks<PERSON> gagal: {error}", "noTripHistoryFound": "Tidak ada riwayat perjalanan di<PERSON>ukan", "noMatchingTripsFound": "Tidak ada perjalanan yang sesuai", "superAdmin": "Super Admin", "tripManager": "<PERSON><PERSON><PERSON>", "user": "Pengguna", "locationTracking": "Pelacakan Lokasi", "tripRating": "<PERSON><PERSON><PERSON>", "odometerReading": "Pembacaan Odometer", "driverAvailabilityManagement": "<PERSON><PERSON><PERSON><PERSON>", "carAvailabilityManagement": "<PERSON><PERSON><PERSON><PERSON>", "driverCheckInManagement": "Manajemen Check-In Sopir", "carOdometerHistory": "Riwayat Odometer Mobil", "driverCheckIn": "Check-In Driver", "myTripHistory": "Riway<PERSON>", "selectADriver": "<PERSON><PERSON><PERSON>", "assign": "Tugaskan", "viewReason": "<PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "allTripRequests": "<PERSON><PERSON><PERSON>", "rejectionReason": "<PERSON><PERSON><PERSON>", "tripChanges": "<PERSON><PERSON><PERSON>", "confirmCancellation": "Konfirmasi Pembatalan", "tripApprovedMessage": "<PERSON><PERSON><PERSON><PERSON> disetujui dan status diubah menjadi PERMINTAAN", "pleaseSelectRating": "<PERSON><PERSON><PERSON> pilih rating", "ratingSubmittedSuccessfully": "Rating be<PERSON><PERSON><PERSON>", "failedToLoadTripDetails": "Gagal memuat detail perjalanan", "notRated": "<PERSON>um dinilai", "previous": "Sebelumnya", "next": "Selanjutnya", "noTripDataAvailable": "Tidak ada data perjalanan tersedia", "pleaseSelectBothDestinations": "<PERSON><PERSON><PERSON> pilih kedua tujuan", "destinationsCannotBeSame": "<PERSON><PERSON><PERSON> asal dan tujuan tidak boleh sama", "customDestinationsCannotBeSame": "<PERSON><PERSON><PERSON> k<PERSON>us asal dan tujuan tidak boleh sama", "pleaseEnterWaitingDuration": "<PERSON><PERSON><PERSON> masukkan durasi <PERSON>gu", "pleaseEnterCustomFromDestination": "<PERSON><PERSON><PERSON> masukkan tujuan asal khusus", "pleaseEnterCustomToDestination": "<PERSON><PERSON><PERSON> masukkan tujuan akhir k<PERSON>us", "failedToLoadAvailableCars": "Gagal memuat mobil yang tersedia", "requestTrips": "<PERSON><PERSON><PERSON><PERSON>", "otherTripStatuses": "Status <PERSON><PERSON><PERSON><PERSON>", "multipleTrips": "<PERSON><PERSON><PERSON><PERSON>", "tripDuration": "<PERSON><PERSON><PERSON>:", "noNotesProvided": "Tidak ada catatan tersedia untuk perjalanan ini.", "noNotifications": "Tidak ada notifikasi", "authenticationFailed": "<PERSON>ten<PERSON><PERSON><PERSON> gagal", "noTripDataAvailableRequestor": "Tidak ada data perjalanan tersedia", "myTripRequests": "<PERSON><PERSON><PERSON><PERSON>", "editDriver": "Edit Driver", "failedToLoadCars": "Gagal memuat mobil yang tersedia", "failedToUpdateDriver": "<PERSON><PERSON> driver", "name": "<PERSON><PERSON>", "nameWillBeUppercase": "<PERSON><PERSON> akan dikonversi ke huruf kapital", "nameIsRequired": "<PERSON><PERSON>", "initialHelperText": "Harus tepat 3 karakter (akan dikonversi ke huruf kapital)", "initialIsRequired": "In<PERSON><PERSON> diperlukan", "initialMustBe3Characters": "Inisial harus tepat 3 karakter", "loadingCars": "Memuat mobil...", "selectACar": "<PERSON><PERSON>h mobil", "searchForCar": "Cari mobil", "pleaseSelectCar": "<PERSON><PERSON><PERSON> pilih mobil", "newPasswordOptional": "Password <PERSON>u (opsional)", "leaveEmptyToKeepPassword": "Biarkan kosong untuk mempertahankan password saat ini", "fromDestinationRequired": "<PERSON><PERSON><PERSON> asal dip<PERSON>an", "toDestinationRequired": "<PERSON><PERSON><PERSON> a<PERSON>", "dateIsRequired": "<PERSON><PERSON> diperlukan", "timeIsRequired": "<PERSON><PERSON><PERSON>", "pleaseEnterOdometerReading": "<PERSON><PERSON>an masukkan pembacaan odometer", "odometerReadingRequired": "Pembacaan odometer diperlukan", "pleaseEnterValidNumber": "<PERSON><PERSON>an masukkan angka yang valid", "allDrivers": "<PERSON><PERSON><PERSON>", "validationStatus": "Status Validasi", "validated": "<PERSON><PERSON><PERSON><PERSON>", "notValidated": "Tidak Tervalidasi", "checkInValidatedSuccessfully": "Check-in ber<PERSON><PERSON> di<PERSON>i", "validationRemoved": "Validasi di<PERSON>pus", "reset": "Reset", "checkInDetails": "Detail Check-In", "checkOutDetails": "Detail Check-Out", "rateTrip": "<PERSON><PERSON>", "rateThisTrip": "<PERSON><PERSON>", "cannotAdjustDurationNoTripId": "Tidak dapat menyesuaikan durasi untuk perjalanan ini - tidak ada ID perjalanan", "cannotAdjustDuration": "Tidak dapat menyesuaikan durasi untuk perjalanan ini", "failedToUpdateDuration": "<PERSON><PERSON> memper<PERSON> durasi: {statusCode}", "updatedDurationForTrip": "<PERSON><PERSON><PERSON> {tripCode} diperbarui menjadi {minutes} menit", "rateYourTrips": "<PERSON><PERSON>", "tripCreatedSuccessfully": "<PERSON><PERSON><PERSON>n ({tripCode}) berhasil dibuat", "roundTripCreatedSuccessfully": "<PERSON><PERSON><PERSON><PERSON> pulang-pergi berhasil dibuat: Berangkat ({outboundCode}), <PERSON><PERSON>ng ({returnCode})", "waitingTripCreatedSuccessfully": "<PERSON>jalanan tunggu berhasil dibuat: ({tripCode})", "tripUpdatedSuccessfully": "<PERSON><PERSON><PERSON><PERSON> ({tripCode}) ber<PERSON><PERSON> diperbarui", "loadingTripDetails": "Memuat detail perjalanan...", "tripDataIsLoading": "Data perjalanan sedang dimuat...", "loadingAvailableDrivers": "Memuat driver yang tersedia...", "checkinValidatedSuccessfully": "Check-in ber<PERSON><PERSON> di<PERSON>i", "backToBaseTripCreatedSuccessfully": "<PERSON><PERSON><PERSON>n <PERSON> ke Base berhasil dibuat", "failedToUpdateCarOdometer": "<PERSON><PERSON> memperbarui odometer mobil", "errorLoadingData": "Gagal memuat data", "errorWithDetails": "Error: {details}", "tripCompletedSuccessfully": "<PERSON><PERSON><PERSON><PERSON> be<PERSON><PERSON> di<PERSON>", "failedToCompleteTrip": "<PERSON><PERSON><PERSON><PERSON>", "errorCompletingTrip": "Error <PERSON>: {error}", "authenticationFailedInvalidCode": "Autentikasi gagal: Kode autentikasi tidak valid atau kedaluwarsa", "authenticationFailedWithError": "<PERSON><PERSON><PERSON><PERSON><PERSON> gagal: {error}", "invalidCredentials": "<PERSON>a pengguna atau kata sandi tidak valid", "areYouSureCancelTrip": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin membatalkan perjalanan {tripCode}? Ini akan mengirimkan permintaan pembatalan untuk ditinjau.", "requestCancellation": "Minta Pembatalan", "tripCancellationRequestSubmitted": "Permintaan pembatalan perjalanan telah diajukan untuk ditinjau", "failedToSubmitCancellationRequest": "Gagal mengirimkan permintaan pembatalan perjalanan", "noTripsFoundForDate": "Tidak ada perjalanan ditemukan untuk tanggal: {date}", "update": "<PERSON><PERSON><PERSON>", "assignDriverOrOnlineTaxi": "Tugaskan Driver atau Taksi Online ke Perjalanan {tripCode}", "deleteTripConfirmation": "Ini akan mengubah status perjalanan menjadi DELETED. Tindakan ini tidak dapat dibatalkan.\\n\\nApakah Anda yakin ingin melanjutkan?", "retry": "<PERSON><PERSON>", "ok": "OK", "noTripsMatchFilters": "Tidak ada perjalanan yang sesuai dengan filter Anda", "clearFilters": "<PERSON><PERSON>", "allStatuses": "Semua Status", "confirmTrip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "startTrip": "<PERSON><PERSON>", "finishTrip": "<PERSON><PERSON><PERSON><PERSON>", "completeTrip": "<PERSON><PERSON><PERSON><PERSON>", "tripDetails": "<PERSON><PERSON>", "tripDetailsWithCode": "Detail <PERSON>: {tripCode}", "confirmTripCompletion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updateOdometerFor": "Perbarui Odometer untuk {carCode}", "enterNewOdometerReading": "Masukkan pembacaan odometer baru", "odometerReadingHelper": "<PERSON><PERSON> harus sama atau lebih besar dari pembacaan saat ini", "newReadingMustBeAtLeast": "Pembacaan baru harus minimal {minimum}", "firstTripOfDay": "Ini adalah perjalanan pertama hari ini untuk mobil ini.", "errorFetchingDrivers": "Kesalahan mengambil data pengemudi: {error}", "driverTrips": "<PERSON><PERSON><PERSON><PERSON> ({count})", "lastUpdated": "<PERSON><PERSON><PERSON>", "tripNumber": "<PERSON><PERSON><PERSON><PERSON> {current} dari {total}", "unknown": "Tidak Diketahui", "unknownDriver": "Pengemudi <PERSON>", "centeredOnDriver": "Dipusat<PERSON> pada {driverName}", "driversCount": "Pengemudi: {count}", "inactiveDriversInfo": "Abu-<PERSON>bu = Tidak aktif > 10 menit", "updateFrequencyInfo": "<PERSON><PERSON><PERSON><PERSON> setiap 5 menit atau 500m", "addCar": "Tambah Mobil", "editCar": "Edit <PERSON>", "failedToGenerateCarCode": "<PERSON><PERSON> kode mobil", "pleaseEnterManufacturer": "<PERSON><PERSON><PERSON> masukkan pabrikan", "pleaseEnterModelName": "<PERSON><PERSON><PERSON> ma<PERSON>kkan nama model", "pleaseEnterCarType": "<PERSON><PERSON><PERSON> masukkan jenis mobil", "pleaseEnterPlateNumber": "<PERSON><PERSON><PERSON> masukkan nomor plat", "pleaseEnterColor": "<PERSON><PERSON><PERSON> ma<PERSON>kkan warna", "failedToAddCar": "Gagal menambah mobil", "addCargo": "Tambah Kargo", "editCargo": "<PERSON>", "failedToGenerateCargoCode": "<PERSON><PERSON> kode kargo", "pleaseEnterCargoName": "<PERSON><PERSON><PERSON> masukkan nama kargo", "failedToAddCargo": "Gagal menambah kargo", "submit": "<PERSON><PERSON>", "adding": "Menambahkan...", "addDestination": "Tambah Tujuan", "editDestination": "<PERSON>", "failedToGenerateDestinationCode": "<PERSON><PERSON> kode tujuan", "pleaseEnterDestinationName": "<PERSON><PERSON><PERSON> masukkan nama tujuan", "pleaseEnterAddress": "<PERSON><PERSON><PERSON> masukkan al<PERSON>t", "pleaseEnterInitial": "<PERSON><PERSON><PERSON> masukkan inisial", "failedToAddDestination": "<PERSON><PERSON> men<PERSON>bah tujuan", "destinationName": "<PERSON><PERSON>", "durationHours": "<PERSON><PERSON><PERSON> (Jam)", "durationMinutes": "<PERSON><PERSON><PERSON> (Menit)", "coordinatesLatLong": "Koordinat (Lintang, Bujur)", "coordinatesFormat": "Format: lat,long (mis. 12.3456,78.9012)", "pleaseEnterCoordinates": "<PERSON><PERSON><PERSON> masukkan koor<PERSON>t", "invalidHours": "Jam tidak valid", "minutesMustBe0To59": "<PERSON><PERSON> harus 0-59", "mustBe3Characters": "Harus tepat 3 karakter (akan diubah ke huruf besar)", "initialRequired": "<PERSON><PERSON><PERSON> wajib diisi", "failedToGenerateDriverCode": "<PERSON><PERSON> kode pengemudi", "pleaseEnterName": "<PERSON><PERSON><PERSON> masukkan nama", "pleaseConfirmPassword": "<PERSON><PERSON><PERSON> konfirmasi kata sandi", "generatingCode": "<PERSON><PERSON><PERSON><PERSON><PERSON> kode...", "nameUppercaseHelper": "<PERSON><PERSON> akan diubah ke huruf besar", "searchForACar": "Cari mobil", "pleaseSelectACar": "<PERSON><PERSON><PERSON> pilih mobil", "confirmPassword": "<PERSON>n<PERSON><PERSON><PERSON>", "passwordMinLength": "<PERSON><PERSON> sandi harus minimal 6 karakter", "failedToAddDriver": "Gagal menambah pengemudi", "updateCar": "<PERSON><PERSON><PERSON>", "updateCargo": "<PERSON><PERSON><PERSON>", "updating": "Memperbarui...", "updateDestination": "<PERSON><PERSON><PERSON>", "failedToUpdateCar": "<PERSON><PERSON> mobil", "failedToUpdateCargo": "<PERSON><PERSON> kargo", "failedToUpdateDestination": "<PERSON><PERSON> tujuan", "modelName": "<PERSON><PERSON>", "onMobileDateRangeLimited": "Di mobile, rentang tanggal dibatasi maksimal 9 hari", "unavailableDemoReason": "Tidak tersedia: <PERSON><PERSON><PERSON> demo", "updatedDriverAvailability": "<PERSON><PERSON><PERSON><PERSON> ketersediaan {driverName} untuk {date}", "editAvailabilityFor": "<PERSON> <PERSON><PERSON><PERSON><PERSON> untuk {carCode}", "tripNotes": "Catatan Perjalanan:", "assignTo": "Tugaskan ke:", "fetchingTrips": "<PERSON><PERSON><PERSON> per<PERSON>...", "noTripsWithStatusRequest": "Tidak ada perjalanan dengan status REQUEST ditemukan.", "noTripsWithStatusDriverRejected": "Tidak ada perjalanan dengan status DRIVER REJECTED ditemukan.", "assigningDriver": "Menugaskan pengemudi...", "deletingTrip": "<PERSON><PERSON><PERSON><PERSON> per<PERSON>...", "deleteAction": "Hapus", "assignAction": "Tugaskan", "dateColon": "Tanggal:", "maintenance": "Perawatan", "broken": "Rusak", "borrowed": "Dipinjam", "startDateColon": "Tanggal <PERSON>lai:", "endDateColon": "Tanggal <PERSON>lesai:", "thisWeek": "<PERSON><PERSON>", "nextWeek": "<PERSON><PERSON>", "statusLegend": "Legenda Status:", "plate": "Plat:", "tripLabel": "Perjalanan:", "driverLabel": "Pengemudi: ", "notesLabel": "Catatan", "dateTime": "Tanggal/Waktu", "pageXofY": "<PERSON><PERSON> {current} dari {total}", "singleCar": "<PERSON><PERSON>", "allCars": "<PERSON><PERSON><PERSON>", "selectDate": "<PERSON><PERSON><PERSON>", "searchByDriverTripNotes": "<PERSON>i be<PERSON>, per<PERSON><PERSON><PERSON>, catatan...", "noOdometerHistoryRecordsFound": "Tidak ada catatan riwayat odometer ditemukan", "selectDriverFirst": "<PERSON><PERSON><PERSON> pengemudi terle<PERSON>h da<PERSON>u", "selectCar": "<PERSON><PERSON>h mobil", "completedStatus": "SELESAI", "completedDuration": "Durasi SELESAI", "tripInProgress": "PERJALANAN SEDANG BERLANGSUNG", "tripInProgressDuration": "Durasi PERJALANAN SEDANG BERLANGSUNG", "waitingForRating": "MENUNGGU PENILAIAN", "waitingForRatingDuration": "Durasi MENUNGGU PENILAIAN", "assignToDriverDuration": "Durasi TUGASKAN KE PENGEMUDI", "foundRequestTrips": "<PERSON><PERSON><PERSON><PERSON> {count} per<PERSON><PERSON>n permintaan:", "noRequestTripsFound": "Tidak ada perjalanan dengan status PERMINTAAN ditemukan.", "foundDriverRejectedTrips": "<PERSON><PERSON><PERSON>n {count} per<PERSON>lanan yang ditolak pengemudi:", "noDriverRejectedTripsFound": "Tidak ada perjalanan dengan status DITOLAK PENGEMUDI ditemukan.", "availabilityUpdatedFor": "Ketersediaan diperbarui untuk {driverName}", "sick": "Sakit", "breakStatus": "<PERSON><PERSON><PERSON><PERSON>", "onBreak": "Sedang Istirahat", "noReason": "<PERSON><PERSON>", "noReasonProvided": "Tidak ada alasan yang diberikan", "setStatus": "Atur Status", "startDate": "<PERSON><PERSON>", "endDate": "<PERSON><PERSON>", "apply": "Terapkan", "scrollDates": "<PERSON><PERSON><PERSON>", "selectDateRange": "<PERSON><PERSON><PERSON>", "editAvailability": "<PERSON>", "dateRange": "<PERSON><PERSON><PERSON>", "driverInformation": "Informasi Driver", "checkInInformation": "Informasi Check-In", "completeCheckIn": "Selesaikan Check-In", "loggingOut": "Sedang logout...", "clickButtonCompleteCheckIn": "Klik tombol di bawah untuk menyelesaikan check-in Anda.", "driverCheckOut": "Check-Out Driver", "confirmCheckOut": "Konfirmasi Check-Out", "completeCheckOut": "Selesaikan Check-Out", "checkOutSuccessfulRedirecting": "Check-out berhasil! Mengalihkan ke check-in...", "afterCheckOutRedirectMessage": "<PERSON><PERSON><PERSON> check-out, <PERSON><PERSON> akan dial<PERSON>kan ke layar check-in. <PERSON><PERSON><PERSON><PERSON> Anda ingin melanjutkan?", "checkOutInformation": "Informasi Check-Out", "clickButtonCompleteCheckOut": "Klik tombol di bawah untuk menyelesaikan check-out <PERSON><PERSON>.", "filterTrips": "<PERSON><PERSON>", "searchByTripCode": "<PERSON>i be<PERSON>kan kode per<PERSON>n, lokasi, pemohon, rating, komentar, dll.", "clearAllFilters": "<PERSON><PERSON>", "comments": "Komentar", "checkInTime": "Waktu Check-In", "driverCodeColon": "<PERSON>de Driver:", "checkInTimeColon": "Waktu Check-In:", "proceed": "Lanjutkan", "tripColon": "Perjalanan:", "fromColon": "Dari:", "toColon": "Ke:", "driverColon": "Pengemudi:", "atTime": "pada", "view": "Lihat", "viewRejection": "<PERSON><PERSON>", "filterByDateLabel": "<PERSON><PERSON> be<PERSON><PERSON><PERSON>", "searchLabel": "<PERSON><PERSON>", "unassigned": "Belum Ditugaskan", "rejectionReasonTitle": "<PERSON><PERSON><PERSON>", "tripDetailsTitle": "Detail <PERSON> {tripCode}", "finishTripConfirmTitle": "Konfirma<PERSON>", "finishTripConfirmMessage": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menandai perjalanan ini sebagai selesai?", "markAsFinished": "Tandai Selesai", "tripRejectedMessage": "<PERSON><PERSON>lanan ini ditolak dengan alasan berikut:", "tripDetailsColon": "Detail <PERSON>:", "fromLabel": "Dari:", "toLabel": "Ke:", "dateLabel": "Tanggal:", "timeLabel": "<PERSON><PERSON><PERSON>", "statusLabel": "Status:", "driverIdLabel": "ID Pengemudi", "ratingLabel": "Rating", "passengersLabel": "Penumpang", "cargoLabel": "<PERSON><PERSON>", "noneLabel": "Tidak ada", "tripCodeHeader": "<PERSON><PERSON>", "fromHeader": "<PERSON><PERSON>", "toHeader": "<PERSON>", "dateHeader": "Tanggal", "timeHeader": "<PERSON><PERSON><PERSON>", "statusHeader": "Status", "driverHeader": "<PERSON><PERSON><PERSON><PERSON>", "actionsHeader": "<PERSON><PERSON><PERSON>", "viewRejectionReason": "<PERSON><PERSON>", "tripMarkedAsCompleted": "<PERSON><PERSON><PERSON><PERSON> telah ditandai sebagai selesai", "tripDataRefreshedSuccessfully": "Data perjalanan ber<PERSON>il disegarkan", "roundTrip": "<PERSON><PERSON><PERSON><PERSON>", "waiting": "<PERSON><PERSON><PERSON>", "outboundJourney": "<PERSON><PERSON><PERSON><PERSON>", "returnJourney": "<PERSON><PERSON><PERSON><PERSON>", "waitingTime": "<PERSON><PERSON><PERSON>", "returnDate": "<PERSON><PERSON>", "returnTime": "<PERSON><PERSON><PERSON>", "submitTrip": "<PERSON><PERSON>", "customFrom": "<PERSON><PERSON>", "customTo": "<PERSON>", "others": "<PERSON><PERSON><PERSON>", "returnDateCannotBeEarlier": "Tanggal pulang tidak boleh lebih awal dari tanggal berangkat", "outboundDateCannotBeLater": "<PERSON><PERSON> berangkat tidak boleh lebih lambat dari tanggal pulang", "returnDateAutomaticallyAdjusted": "Tanggal pulang otomatis disesuaikan dengan tanggal berangkat", "returnTimeAutomaticallyAdjusted": "<PERSON><PERSON>tu pulang otomatis disesuaikan agar setelah waktu berangkat", "returnDateTimeAutomaticallyAdjusted": "Tanggal dan waktu pulang otomatis disesuaikan agar setelah tanggal dan waktu berangkat", "returnTimeMustBeAfterOutbound": "Waktu pulang harus setelah waktu berangkat pada hari yang sama", "outboundTimeMustBeBeforeReturn": "<PERSON><PERSON><PERSON> berangkat harus sebelum waktu pulang pada hari yang sama", "outboundDateTimeMustBeBeforeReturn": "Tanggal dan waktu berangkat harus sebelum tanggal dan waktu pulang", "returnDateTimeMustBeAfterOutbound": "<PERSON>gal dan waktu pulang harus setelah tanggal dan waktu berangkat", "returnTimeMustBeAfterOutboundWarning": "Peringatan: <PERSON><PERSON> dan waktu pulang harus setelah tanggal dan waktu berangkat. <PERSON><PERSON>an sesuaikan pilihan <PERSON>.", "outboundTimeMustBeBeforeReturnOnSameDay": "<PERSON><PERSON><PERSON> berangkat harus sebelum waktu pulang pada hari yang sama", "returnFromAndToCannotBeSame": "<PERSON><PERSON><PERSON> dan Ke untuk perjalanan pulang tidak boleh sama", "returnFromAndToCustomCannotBeSame": "<PERSON><PERSON><PERSON> k<PERSON> dan Ke untuk perjalanan pulang tidak boleh sama", "pleaseEnterCustomReturnTo": "<PERSON><PERSON><PERSON> masukkan tujuan khusus ke untuk perjalanan pulang", "pleaseSelectOutboundDateTime": "<PERSON><PERSON><PERSON> pilih tanggal dan waktu berangkat", "pleaseSelectReturnDateTime": "<PERSON><PERSON>an pilih tanggal dan waktu pulang", "pleaseSelectDateTime": "<PERSON><PERSON><PERSON> pilih tanggal dan waktu", "invalidDateTimeFormat": "Format tanggal atau waktu tidak valid", "pleaseEnterCustomFrom": "<PERSON><PERSON><PERSON> masukkan dari khusus", "pleaseEnterCustomTo": "<PERSON><PERSON><PERSON> masukkan ke khusus", "enterNotesOptional": "Masukkan catatan per<PERSON>lanan (opsional)", "photo": "Foto:", "pleaseProvideRejectionReason": "<PERSON><PERSON><PERSON> berikan alasan penolakan perjalanan ini:", "takePhoto": "Ambil Foto", "addNotesPhotoOptional": "Tambahkan catatan dan/atau foto (opsional):", "shareYourExperienceOptional": "Bagikan pengalaman Anda (opsional)", "areYouSureMarkTripCompleted": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menandai perjalanan ini sebagai selesai?", "timeColon": "Waktu:", "notesColon": "Catatan:", "tripRequestsByStatus": "<PERSON><PERSON><PERSON><PERSON>", "newTripRequests": "<PERSON><PERSON><PERSON><PERSON> perjalanan baru", "tripEditRequestsPendingApproval": "Permintaan edit per<PERSON><PERSON><PERSON> per<PERSON>", "tripCancellationRequestsPendingApproval": "Permintaan pembatalan perjalanan menunggu per<PERSON>an", "tripsReadyToBeAssignedToDrivers": "Per<PERSON>lanan siap ditugaskan ke pengemudi", "tripsAssignedToOnlineTaxi": "Perjalanan ditugaskan ke taksi online", "tripsRejectedByDrivers": "<PERSON><PERSON><PERSON><PERSON> di<PERSON> o<PERSON>h pen<PERSON>", "tripsConfirmedByDrivers": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>", "tripsCurrentlyInProgress": "Perjalanan sedang berlangsung", "completedTripsWaitingForRating": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>ai <PERSON> penilaian", "assignDriverStatusExplanation": "Menugaskan driver akan mengubah status perjalanan menjadi \"ASSIGN TO DRIVER\". <PERSON><PERSON><PERSON><PERSON> \"ONLINE\" akan mengubah status menjadi \"ASSIGN TO ONLINE TAXI\".", "tripDateLabel": "Tanggal Perjalanan: {date} ({formattedDate})", "lookingUpTripId": "Mencari ID perjalanan untuk {tripCode}...", "cannotAssignDriverMissingId": "Tidak dapat menugaskan driver: ID perjalanan hilang untuk kode perjalanan {tripCode}", "currentDateTime": "<PERSON><PERSON> dan <PERSON>: {dateTime}", "deleteTripWarning": "Ini akan mengubah status perjalanan menjadi DELETED. Tindakan ini tidak dapat dibatalkan.\n\nA<PERSON><PERSON>h Anda yakin ingin melanjutkan?", "errorDeletingTrip": "<PERSON><PERSON><PERSON> men<PERSON><PERSON> per<PERSON>: {error}", "statusDriverRejected": "Status: DITOLAK DRIVER", "tripRejectedWithReason": "<PERSON><PERSON><PERSON><PERSON> ini ditolak o<PERSON>h driver den<PERSON> al<PERSON>n be<PERSON>:", "tripRejectedNoReason": "<PERSON><PERSON><PERSON><PERSON> ini di<PERSON>lak o<PERSON>h driver, tetapi tidak ada alasan yang diberikan.", "tripCodeLabel": "<PERSON><PERSON>: {tripCode}", "tripDurationLegend": "<PERSON><PERSON><PERSON>", "noTripRequestsForDate": "Tidak ada permintaan perjalanan untuk tanggal ini", "selectedDateLabel": "<PERSON>gal yang dipilih: {date}", "timePeriodsTitle": "Periode Waktu:", "nightPeriod": "Malam (00:00-06:00)", "morningPeriod": "<PERSON><PERSON> (06:00-12:00)", "afternoonPeriod": "<PERSON><PERSON> (12:00-18:00)", "eveningPeriod": "Malam (18:00-24:00)", "noCarsAvailableMessage": "Tidak ada mobil tersedia untuk tanggal ini. Semua mobil mungkin ditandai sebagai TIDAK TERSEDIA.", "noDriversAvailableMessage": "Tidak ada pengemudi tersedia untuk tanggal ini. <PERSON><PERSON><PERSON> pengemudi mungkin ditandai sebagai TIDAK TERSEDIA.", "driverRejectedTripsTitle": "Perjalanan DITOLAK PENGEMUDI", "navigateTimeSlots": "Navigasi slot waktu:", "legend": "Legenda:", "assignToDriverWithDriver": "TUGASKAN KE PENGEMUDI (dengan pengemudi)", "assignToDriverWithoutDriver": "TUGASKAN KE PENGEMUDI (tanpa pengemudi)", "assignToDriverWithoutDriverDuration": "TUGASKAN KE PENGEMUDI (tanpa pengemudi) Durasi", "refreshTripRequestsTooltip": "<PERSON><PERSON><PERSON> permin<PERSON> per<PERSON>n", "refreshDriverRejectedTripsTooltip": "<PERSON><PERSON>n perjalanan yang ditolak pengemudi", "driverRejectedTripsForDate": "<PERSON><PERSON><PERSON><PERSON> {date}", "noDriverRejectedTripsForDate": "Tidak ada perjalanan yang ditolak pengemudi untuk tanggal ini", "errorFetchingOnlineTaxiTrips": "Kesalahan mengambil daftar perjalanan taksi online: {error}", "errorFetchingTrips": "Kesalahan mengambil perjalanan: {error}", "errorFetchingDriverRejectedTrips": "Kesalahan mengambil perjalanan yang ditolak pengemudi: {error}", "errorFetchingTripsWithStatus": "Kesalahan mengambil perjalanan: {status1} / {status2}", "errorFetchingTripsWithStatusCodes": "Kesalahan mengambil perjalanan: {statusCodes}", "tripStatusChangedToOnlineTaxi": "Status perjalanan {tripCode} diubah ke ASSIGN TO ONLINE TAXI.", "errorAssigningDriver": "Kesalahan menugaskan pengemudi: {error}", "tripHasBeenDeleted": "<PERSON><PERSON><PERSON><PERSON> {tripCode} telah di<PERSON>.", "tripRequestsForDate": "<PERSON><PERSON><PERSON><PERSON> untuk {date}", "requestCount": "{count} {count, plural, =1{permintaan} other{permintaan}}", "tripCount": "{count} {count, plural, =1{perjalanan} other{perjalanan}}", "tripsCount": "{count} {count, plural, =1{Perjalanan} other{Perjalanan}}", "onlineLabel": "Online: ", "durationLabel": "Du<PERSON>i:", "taxiText": "TAKSI", "noCarAssigned": "Tidak Ada Mobil Ditugaskan", "statusDetailLabel": "Status", "startTimeLabel": "<PERSON><PERSON><PERSON>:", "endTimeLabel": "<PERSON><PERSON><PERSON>:", "durationDetailLabel": "<PERSON><PERSON><PERSON>", "driverDetailLabel": "<PERSON><PERSON><PERSON><PERSON>", "driverCodeDetailLabel": "<PERSON><PERSON>", "fromDetailLabel": "<PERSON><PERSON>", "toDetailLabel": "<PERSON>", "additionalInformation": "Informasi <PERSON>", "tripIdLabel": "ID Perjalanan", "notAssigned": "Tidak ditugaskan", "carInformation": "Informasi Mobil", "carIdLabel": "ID Mobil", "carCodeDetailLabel": "Kode Mobil", "plateNumberLabel": "<PERSON><PERSON>", "passengersSection": "Penumpang:", "cargoSection": "Kargo:", "returnToRequest": "Kembali ke REQUEST", "deleteTrip": "Hapus", "deleteTripTitle": "<PERSON><PERSON> {tripCode}?", "deleteTripWarningMessage": "Ini akan mengubah status perjalanan menjadi DELETED. Tindakan ini tidak dapat dibatalkan.\\n\\nApakah Anda yakin ingin melanjutkan?", "minutesUnit": "menit", "onlineTaxiText": "TAKSI ONLINE", "noCar": "Tidak Ada Mobil", "noPlate": "Tidak Ada Plat", "updatingTripDuration": "<PERSON><PERSON><PERSON><PERSON> durasi per<PERSON>...", "errorUpdatingDuration": "<PERSON><PERSON><PERSON> memper<PERSON>ui durasi: {error}", "updatingTripStatus": "Memperbarui status perjalanan...", "tripStatusChangedToRequest": "Status perjalanan {tripCode} diubah ke REQUEST.", "refreshingAllData": "Menyegarkan semua data...", "errorGeneric": "<PERSON><PERSON><PERSON>: {error}", "deletingTripMessage": "<PERSON><PERSON><PERSON><PERSON> per<PERSON>...", "errorDeletingTripMessage": "<PERSON><PERSON><PERSON> men<PERSON><PERSON> per<PERSON>: {error}", "errorDriverCodeMissing": "Error: <PERSON><PERSON> pen<PERSON> hilang. <PERSON>lakan coba lagi.", "backToBase": "Kembali ke Base", "checkOut": "Check Out", "assignToDriverCount": "Tugaskan ke Pengemudi: {count}", "selectStatus": "Pilih Status", "tripCodeNumber": "Perjalanan #{tripCode}", "durationInMinutes": "{minutes} menit", "requestorLabel": "Pemohon:", "rejectionLabel": "Penolakan:", "locationTrackingActive": "Pelacakan lokasi aktif (pengemudi memiliki perjalanan aktif)", "trackingReady": "Pelacakan siap (pengemudi memiliki perjalanan aktif)", "trackingDisabledNoActiveTrips": "Pelacakan dinonaktifkan (tidak ada perjalanan aktif)", "automaticTrackingDisabled": "Pelacakan otomatis din<PERSON>", "failedToCreateBackToBaseTrip": "Gagal membuat per<PERSON>lanan Kembali ke Base: {statusCode}", "errorCreatingBackToBaseTrip": "Error membuat per<PERSON>n <PERSON>li ke Base: {error}", "locationTrackingStatus": "Status Pelacakan Lokasi", "autoTrackingLabel": "Pelacakan Otomatis:", "enabled": "DIAKTIFKAN", "disabled": "DINONAKTIFKAN", "activeTripsLabel": "Perjalanan Aktif:", "currentTimeLabel": "Waktu <PERSON> Ini:", "todayLabel": "<PERSON>:", "locationLabel": "Lokasi:", "lastUpdateLabel": "Pembaruan Terakhir:", "secondsAgo": "{seconds} detik yang lalu", "minutesAgo": "{minutes} menit yang lalu", "trackingExplanation": "Lokasi Anda dilacak hanya ketika Anda memiliki perjalanan aktif (TRIP IN PROGRESS atau BACK TO BASE):\n• Pelacakan secara otomatis diaktifkan ketika Anda memiliki perjalanan aktif\n• Pelacakan secara otomatis dinonaktifkan ketika Anda tidak memiliki perjalanan aktif\n• Pembaruan lokasi dikirim setiap menit ketika pelacakan aktif", "tripCodeColumn": "<PERSON><PERSON>", "requestorColumn": "<PERSON><PERSON><PERSON><PERSON>", "fromColumn": "<PERSON><PERSON>", "toColumn": "<PERSON>", "dateColumn": "Tanggal", "timeColumn": "<PERSON><PERSON><PERSON>", "driverColumn": "<PERSON><PERSON><PERSON><PERSON>", "statusColumn": "Status", "ratingColumn": "Rating", "pageOf": "<PERSON><PERSON> {current} dari {total}", "itemsText": "item", "tripAssignedToOnlineTaxiTitle": "Perjalanan Ditugaskan ke Taksi Online", "tripAssignedToOnlineTaxiMessage": "<PERSON><PERSON><PERSON><PERSON> {tripCode} dari {fromDestination} ke {toDestination} telah ditugaskan ke layanan taksi online.", "tripAssignedToOnlineTaxiSuccess": "Per<PERSON>lanan {tripCode} ditugaskan ke taksi online dan notifikasi terkirim.", "notificationTripAssigned": "<PERSON><PERSON><PERSON><PERSON>: {tripCode}", "notificationNewTripAssignment": "<PERSON><PERSON><PERSON>: {tripCode}", "notificationTripApproved": "<PERSON><PERSON><PERSON><PERSON>: {tripCode}", "notificationTripRejected": "<PERSON><PERSON><PERSON><PERSON>: {tripCode}", "notificationTripCompleted": "<PERSON><PERSON><PERSON><PERSON>: {tripCode}", "notificationTripStatusUpdate": "Pembaruan Status Perjalanan: {tripCode}", "notificationTripRated": "<PERSON><PERSON><PERSON><PERSON>: {tripCode}", "notificationTripReturnedToRequest": "<PERSON><PERSON><PERSON><PERSON> ke Permintaan: {tripCode}", "notificationTripDeleted": "<PERSON><PERSON><PERSON><PERSON>: {tripCode}", "notificationBackToBaseTripCompleted": "<PERSON><PERSON><PERSON><PERSON> ke Base Selesai: {tripCode}", "notificationTripAssignedToOnlineTaxi": "<PERSON><PERSON>lanan <PERSON> ke Taksi Online: {tripCode}", "notificationOnlineTaxiTripCompleted": "Perjalanan Taksi Online Selesai: {tripCode}", "notificationBackToBaseTripCreated": "Perjalanan <PERSON> ke Base Dibuat: {tripCode}", "notificationTripChangesApproved": "<PERSON><PERSON><PERSON>: {tripCode}", "notificationNewTripRequest": "<PERSON><PERSON><PERSON><PERSON>: {tripCode}", "notificationTripEditRequest": "Permintaan Edit <PERSON>: {tripCode}", "notificationTripCancellationRequest": "Permintaan Pembatalan Perjalanan: {tripCode}", "notificationMessageTripAssignedToDriver": "<PERSON><PERSON><PERSON><PERSON> {tripCode} telah ditugaskan ke sopir dan dijadwalkan pada {date} pukul {time}.", "notificationMessageDriverAssignedToTrip": "Anda telah ditugaskan untuk perjalanan {tripCode} dari {fromDestination} ke {toDestination} pada {date} pukul {time}.", "notificationMessageTripApproved": "<PERSON><PERSON><PERSON><PERSON> {tripCode} dari {fromDestination} ke {toDestination} telah di<PERSON>.", "notificationMessageTripRejected": "<PERSON><PERSON><PERSON><PERSON> {tripCode} telah di<PERSON>. Alasan: {rejectionReason}.", "notificationMessageTripRejectedNoReason": "<PERSON><PERSON><PERSON><PERSON> {tripCode} telah di<PERSON>.", "notificationMessageTripAssignedToOnlineTaxi": "<PERSON><PERSON><PERSON><PERSON> {tripCode} dari {fromDestination} ke {toDestination} telah ditugaskan ke taksi online.", "notificationMessageTripCompletedRequestor": "<PERSON><PERSON><PERSON><PERSON> {tripCode} telah selesai. <PERSON><PERSON> kasih telah menggunakan layanan kami.", "notificationMessageTripCompletedManager": "<PERSON><PERSON><PERSON><PERSON> {tripCode} telah se<PERSON>.", "notificationMessageBackToBaseTripCompletedRequestor": "<PERSON><PERSON><PERSON><PERSON> kembali ke base Anda {tripCode} telah berhasil diselesaikan.", "notificationMessageBackToBaseTripCompletedManager": "<PERSON><PERSON><PERSON><PERSON> kembali ke base {tripCode} telah se<PERSON>.", "notificationMessageOnlineTaxiTripCompletedRequestor": "Perjalanan taksi online Anda {tripCode} telah berhasil diselesaikan. Terima kasih telah menggunakan layanan kami!", "notificationMessageOnlineTaxiTripCompletedManager": "<PERSON><PERSON><PERSON><PERSON> taksi online {tripCode} telah ditandai selesai oleh peminta.", "notificationMessageTripReturnedToRequestor": "<PERSON><PERSON><PERSON><PERSON> {tripCode} dari {fromDestination} ke {toDestination} telah dikembalikan ke status permintaan untuk penyesuaian.", "notificationMessageTripReturnedToDriver": "<PERSON><PERSON><PERSON><PERSON> {tripCode} dari {fromDestination} ke {toDestination} telah dikembalikan ke status permintaan.", "notificationMessageTripDeletedRequestor": "<PERSON><PERSON><PERSON><PERSON>a {tripCode} dari {fromDestination} ke {toDestination} telah dihapus oleh manajer perjalanan.", "notificationMessageTripDeletedDriver": "Per<PERSON><PERSON>n {tripCode} dari {fromDestination} ke {toDestination} telah dihapus oleh manajer perjalanan.", "notificationTripStarted": "<PERSON><PERSON><PERSON><PERSON>: {tripCode}", "notificationDriverConfirmedTrip": "<PERSON><PERSON><PERSON>: {tripCode}", "notificationMessageTripEditRequest": "{userName} telah meminta perubahan pada perjalanan {tripCode} dari {fromDestination} ke {toDestination} pada {date} pukul {time}.", "notificationMessageTripCancellationRequest": "{userName} telah meminta pembatalan perjalanan {tripCode} dari {fromDestination} ke {toDestination} pada {date} pukul {time}.", "notificationMessageTripStarted": "<PERSON><PERSON><PERSON><PERSON> {tripCode} telah dimulai oleh sopir.", "notificationMessageDriverConfirmedTrip": "{driverName} telah men<PERSON> per<PERSON> {tripCode}.", "notificationMessageBackToBaseTripCreated": "{driverName} telah membuat per<PERSON>n \"Kembali ke Base\" {tripCode} dari {fromDestination} ke {toDestination} pada {date} pukul {time}.", "notificationMessageTripChangesApproved": "<PERSON><PERSON>an Anda pada perjalanan {tripCode} dari {fromDestination} ke {toDestination} telah diset<PERSON>. Perjalanan sekarang kembali ke status PERMINTAAN.", "notificationMessageTripStatusChanged": "Status perjalanan <PERSON> {tripCode} telah berubah dari {oldStatus} menja<PERSON> {newStatus}.", "notificationMessageDriverTripStatusChanged": "Status perjalanan {tripCode} telah berubah dari {oldStatus} menja<PERSON> {newStatus}.", "notificationMessageTripRated": "<PERSON><PERSON><PERSON><PERSON> {tripCode} telah dinilai {rating} bintang oleh penumpang. {ratingMessage}", "notificationMessageTripRatedNoMessage": "<PERSON><PERSON><PERSON><PERSON> {tripCode} telah dinilai {rating} bintang oleh pen<PERSON>ang.", "@notificationNewTripRequest": {"placeholders": {"tripCode": {"type": "String"}}}, "notificationMessageNewTripRequest": "{requestorName} telah meminta perjalanan baru {tripCode} dari {fromDestination} ke {toDestination} pada {date} pukul {time}.", "@notificationMessageNewTripRequest": {"placeholders": {"requestorName": {"type": "String"}, "tripCode": {"type": "String"}, "fromDestination": {"type": "String"}, "toDestination": {"type": "String"}, "date": {"type": "String"}, "time": {"type": "String"}}}, "notificationDriverRejectedTrip": "<PERSON><PERSON><PERSON>: {tripCode}", "@notificationDriverRejectedTrip": {"placeholders": {"tripCode": {"type": "String"}}}, "notificationMessageDriverRejectedTrip": "{driverName} telah menolak perjalanan {tripCode}. Alasan: {rejectionReason}.", "@notificationMessageDriverRejectedTrip": {"placeholders": {"driverName": {"type": "String"}, "tripCode": {"type": "String"}, "rejectionReason": {"type": "String"}}}, "notificationMessageDriverRejectedTripGeneric": "<PERSON><PERSON><PERSON> telah menolak perjalanan {tripCode}. Alasan: {rejectionReason}.", "@notificationMessageDriverRejectedTripGeneric": {"placeholders": {"tripCode": {"type": "String"}, "rejectionReason": {"type": "String"}}}, "@notificationTripStarted": {"placeholders": {"tripCode": {"type": "String"}}}, "@notificationMessageTripStarted": {"placeholders": {"tripCode": {"type": "String"}}}, "@notificationDriverConfirmedTrip": {"placeholders": {"tripCode": {"type": "String"}}}, "@notificationMessageDriverConfirmedTrip": {"placeholders": {"driverName": {"type": "String"}, "tripCode": {"type": "String"}}}, "notificationTripChangesRejected": "<PERSON><PERSON><PERSON>: {tripCode}", "@notificationTripChangesRejected": {"placeholders": {"tripCode": {"type": "String"}}}, "notificationMessageTripChangesRejected": "<PERSON><PERSON><PERSON><PERSON> per<PERSON>han untuk perjalanan {tripCode} telah di<PERSON>lak. Alasan: {rejectionReason}.", "@notificationMessageTripChangesRejected": {"placeholders": {"tripCode": {"type": "String"}, "rejectionReason": {"type": "String"}}}, "notificationTripCancellationRejected": "Pembatalan Perjalanan <PERSON>lak: {tripCode}", "@notificationTripCancellationRejected": {"placeholders": {"tripCode": {"type": "String"}}}, "notificationMessageTripCancellationRejected": "Permintaan pembatalan untuk perjalanan {tripCode} telah di<PERSON>lak. Alasan: {rejectionReason}.", "@notificationMessageTripCancellationRejected": {"placeholders": {"tripCode": {"type": "String"}, "rejectionReason": {"type": "String"}}}, "tripDeletedNotificationMessage": "<PERSON><PERSON><PERSON><PERSON> {tripCode} dari {fromDestination} ke {toDestination} yang diminta oleh {requestorName} telah di<PERSON>.", "@tripDeletedNotificationMessage": {"placeholders": {"tripCode": {"type": "String"}, "fromDestination": {"type": "String"}, "toDestination": {"type": "String"}, "requestorName": {"type": "String"}}}, "tripRatedNotificationMessage": "<PERSON><PERSON><PERSON><PERSON> {tripCode} dari {fromDestination} ke {toDestination} dengan sopir {driverName} telah dinilai.", "@tripRatedNotificationMessage": {"placeholders": {"tripCode": {"type": "String"}, "fromDestination": {"type": "String"}, "toDestination": {"type": "String"}, "driverName": {"type": "String"}}}, "driverRejectedTripNotificationMessage": "Sopir {driverName} telah menolak perjalanan {tripCode} dari {fromDestination} ke {toDestination}.", "@driverRejectedTripNotificationMessage": {"placeholders": {"driverName": {"type": "String"}, "tripCode": {"type": "String"}, "fromDestination": {"type": "String"}, "toDestination": {"type": "String"}}}, "tripStatusChangedNotificationMessage": "Status perjalanan {tripCode} dari {fromDestination} ke {toDestination} telah diubah.", "@tripStatusChangedNotificationMessage": {"placeholders": {"tripCode": {"type": "String"}, "fromDestination": {"type": "String"}, "toDestination": {"type": "String"}}}, "backToBaseNotificationMessage": "<PERSON><PERSON><PERSON><PERSON> kembali ke base {tripCode} untuk sopir {driverName} telah dibuat.", "@backToBaseNotificationMessage": {"placeholders": {"tripCode": {"type": "String"}, "driverName": {"type": "String"}}}, "onlineTaxiNotificationMessage": "<PERSON><PERSON><PERSON><PERSON> {tripCode} dari {fromDestination} ke {toDestination} telah ditugaskan ke taksi online.", "@onlineTaxiNotificationMessage": {"placeholders": {"tripCode": {"type": "String"}, "fromDestination": {"type": "String"}, "toDestination": {"type": "String"}}}}