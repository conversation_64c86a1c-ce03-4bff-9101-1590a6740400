{"@@locale": "en", "appTitle": "FLEEX", "@appTitle": {"description": "The application title"}, "welcomeBack": "Welcome Back", "@welcomeBack": {"description": "Welcome message on login screen"}, "signInToContinue": "Sign in to continue to FLEEX", "@signInToContinue": {"description": "Login screen subtitle"}, "username": "Username", "@username": {"description": "Username field label"}, "password": "Password", "@password": {"description": "Password field label"}, "login": "<PERSON><PERSON>", "@login": {"description": "Login button text"}, "pleaseEnterUsername": "Please enter your username", "@pleaseEnterUsername": {"description": "Username validation message"}, "pleaseEnterPassword": "Please enter password", "@pleaseEnterPassword": {"description": "Validation message for password field"}, "or": "OR", "@or": {"description": "OR text between login options"}, "signInWith": "Sign in with", "@signInWith": {"description": "Sign in with text"}, "signInWithMicrosoft": "Sign in with Microsoft", "@signInWithMicrosoft": {"description": "Microsoft sign in button text"}, "forgotPassword": "Forgot Password?", "@forgotPassword": {"description": "Forgot password link text"}, "resetPassword": "Reset Password", "@resetPassword": {"description": "Reset Password menu item"}, "goBack": "Go Back", "@goBack": {"description": "Go back button text"}, "newPassword": "New Password", "@newPassword": {"description": "New password field label"}, "confirmNewPassword": "Confirm New Password", "@confirmNewPassword": {"description": "Confirm new password field label"}, "pleaseEnterNewPassword": "Please enter new password", "@pleaseEnterNewPassword": {"description": "New password validation message"}, "pleaseConfirmNewPassword": "Please confirm new password", "@pleaseConfirmNewPassword": {"description": "Confirm new password validation message"}, "passwordMustBeAtLeast6Characters": "Password must be at least 6 characters", "@passwordMustBeAtLeast6Characters": {"description": "Validation message for password length"}, "passwordsDoNotMatch": "Passwords do not match", "@passwordsDoNotMatch": {"description": "Validation message when passwords don't match"}, "passwordResetSuccessful": "Password reset successful.", "@passwordResetSuccessful": {"description": "Success message after password reset"}, "dashboard": "Dashboard", "@dashboard": {"description": "Dashboard menu item"}, "requestTrip": "Request Trip", "@requestTrip": {"description": "Request trip screen title"}, "tripHistory": "Trip History", "@tripHistory": {"description": "Trip history tooltip and navigation label"}, "settings": "Settings", "@settings": {"description": "Settings menu item"}, "logout": "Logout", "@logout": {"description": "Logout menu item"}, "search": "Search", "@search": {"description": "Search field label"}, "date": "Date", "@date": {"description": "Date field label"}, "time": "Time", "@time": {"description": "Time field label"}, "from": "From:", "@from": {"description": "From label with colon"}, "to": "To:", "@to": {"description": "To label with colon"}, "destination": "Destination", "@destination": {"description": "Destination label"}, "notes": "Notes", "@notes": {"description": "Notes field label"}, "save": "Save", "@save": {"description": "Save button text"}, "cancel": "Cancel", "@cancel": {"description": "Cancel button text"}, "edit": "Edit", "@edit": {"description": "Edit button text"}, "delete": "Delete", "@delete": {"description": "Delete button text"}, "add": "Add", "@add": {"description": "Add button text"}, "required": "Required", "@required": {"description": "Required field indicator"}, "optional": "Optional", "@optional": {"description": "Optional field indicator"}, "loading": "Loading...", "@loading": {"description": "Loading indicator text"}, "error": "Error", "@error": {"description": "Error label"}, "success": "Success", "@success": {"description": "Success message prefix"}, "tripCode": "Trip Code", "@tripCode": {"description": "Trip code column header"}, "status": "Status", "@status": {"description": "Status label"}, "driver": "Driver", "@driver": {"description": "Driver role label"}, "car": "Car", "@car": {"description": "Car column header"}, "passenger": "Passenger", "@passenger": {"description": "Passenger field label"}, "passengers": "Passengers", "@passengers": {"description": "Passengers section label"}, "duration": "Duration", "@duration": {"description": "Duration label"}, "actions": "Actions", "@actions": {"description": "Actions column header"}, "filterByDate": "Filter by Date", "@filterByDate": {"description": "Filter by date field label"}, "filterByStatus": "Filter by Status", "@filterByStatus": {"description": "Dropdown hint for status filter"}, "allRecords": "All Records", "@allRecords": {"description": "All records filter option"}, "clearFilter": "Clear Filter", "@clearFilter": {"description": "Clear filter button text"}, "refresh": "Refresh", "@refresh": {"description": "Refresh button text"}, "noDataFound": "No data found", "@noDataFound": {"description": "No data found message"}, "pleaseEnterDate": "Please enter date", "@pleaseEnterDate": {"description": "Date validation message"}, "pleaseEnterTime": "Please enter time", "@pleaseEnterTime": {"description": "Time validation message"}, "pleaseSelectFromDestination": "Please select from destination", "@pleaseSelectFromDestination": {"description": "From destination validation message"}, "pleaseSelectToDestination": "Please select to destination", "@pleaseSelectToDestination": {"description": "To destination validation message"}, "addTrip": "Add Trip", "@addTrip": {"description": "Add trip screen title"}, "editTrip": "Edit Trip", "@editTrip": {"description": "Tooltip for edit trip button"}, "requestorName": "Requestor Name", "@requestorName": {"description": "Requestor name field label"}, "customLocation": "Custom Location", "@customLocation": {"description": "Custom location field label"}, "selectOrigin": "Select origin", "@selectOrigin": {"description": "From destination hint text"}, "selectDestination": "Select destination", "@selectDestination": {"description": "To destination hint text"}, "carCode": "Car Code", "@carCode": {"description": "Car code field label"}, "manufacturer": "Manufacturer", "@manufacturer": {"description": "Manufacturer label"}, "model": "Model", "@model": {"description": "Model label"}, "plateNumber": "Plate Number", "@plateNumber": {"description": "Plate number label"}, "odometer": "Odometer", "@odometer": {"description": "Odometer label"}, "color": "Color", "@color": {"description": "Color label"}, "type": "Type", "@type": {"description": "Type label"}, "driverCode": "Driver Code", "@driverCode": {"description": "Driver code label"}, "driverName": "Driver Name", "@driverName": {"description": "Driver name label"}, "initial": "Initial", "@initial": {"description": "Initial field label"}, "address": "Address", "@address": {"description": "Address label"}, "coordinates": "Coordinates", "@coordinates": {"description": "Coordinates label"}, "hours": "Hours", "@hours": {"description": "Hours helper text"}, "minutes": "Minutes", "@minutes": {"description": "Minutes helper text"}, "available": "Available", "@available": {"description": "Available status"}, "unavailable": "Unavailable", "@unavailable": {"description": "Unavailable status"}, "pending": "Pending", "@pending": {"description": "Status: pending"}, "approved": "Approved", "@approved": {"description": "Approved status"}, "inProgress": "In Progress", "@inProgress": {"description": "In progress status"}, "completed": "Completed", "@completed": {"description": "Status: completed"}, "cancelled": "Cancelled", "@cancelled": {"description": "Cancelled status"}, "confirmDelete": "Confirm Delete", "@confirmDelete": {"description": "Delete confirmation dialog title"}, "areYouSureDelete": "Are you sure you want to delete this item?", "@areYouSureDelete": {"description": "Delete confirmation message"}, "yes": "YES", "@yes": {"description": "Yes value"}, "no": "NO", "@no": {"description": "No value"}, "dataRefreshedSuccessfully": "Data refreshed successfully", "@dataRefreshedSuccessfully": {"description": "Success message when data refresh succeeds"}, "failedToLoadData": "Failed to load data", "@failedToLoadData": {"description": "Data loading error message"}, "selectLanguage": "Language", "@selectLanguage": {"description": "Language selection menu item"}, "chooseLanguage": "Choose your preferred language:", "@chooseLanguage": {"description": "Language selection instruction"}, "saveLanguage": "Save Language", "@saveLanguage": {"description": "Save language button text"}, "languageChanged": "Language changed successfully", "@languageChanged": {"description": "Language change success message"}, "languageChangeNote": "Note: The app will need to be restarted for some changes to take effect.", "@languageChangeNote": {"description": "Language change restart note"}, "language": "Language", "@language": {"description": "Language settings menu item"}, "commonSettings": "Common Settings", "@commonSettings": {"description": "Common Settings menu item"}, "driverSetting": "Driver Setting", "@driverSetting": {"description": "Driver Setting menu item"}, "carSetting": "Car Setting", "@carSetting": {"description": "Car Setting menu item"}, "cargoSetting": "Cargo Setting", "@cargoSetting": {"description": "Cargo Setting menu item"}, "destinationSetting": "Destination Setting", "@destinationSetting": {"description": "Destination Setting menu item"}, "backToHome": "Back to Home", "@backToHome": {"description": "Back to Home menu item"}, "welcome": "Welcome", "@welcome": {"description": "Welcome text"}, "refreshNow": "Refresh now", "@refreshNow": {"description": "Refresh button tooltip"}, "youMustBeLoggedIn": "You must be logged in", "@youMustBeLoggedIn": {"description": "Login required message"}, "failedToLoadTripStatusCounts": "Failed to load trip status counts", "@failedToLoadTripStatusCounts": {"description": "Trip status counts loading error"}, "errorLoading": "Error loading data", "@errorLoading": {"description": "Generic data loading error"}, "tripApprovals": "Trip Approvals", "@tripApprovals": {"description": "Trip approvals screen title"}, "dashboardTitle": "Trip Manager Dashboard", "@dashboardTitle": {"description": "Trip manager dashboard title"}, "requestorDashboardTitle": "Requestor Dashboard", "@requestorDashboardTitle": {"description": "Requestor dashboard title"}, "tripMonitoring": "Trip Monitoring", "@tripMonitoring": {"description": "Trip Monitoring menu item"}, "driverTracking": "Driver Tracking", "@driverTracking": {"description": "Driver Tracking menu item"}, "areYouSureDeleteDriver": "Are you sure you want to remove {name} as a driver?", "@areYouSureDeleteDriver": {"description": "Driver delete confirmation message", "placeholders": {"name": {"type": "String"}}}, "failedToDeleteDriver": "Failed to delete driver", "@failedToDeleteDriver": {"description": "Driver deletion error message"}, "anErrorOccurred": "An error occurred: {error}", "@anErrorOccurred": {"description": "Generic error message", "placeholders": {"error": {"type": "String"}}}, "driverSettingsTitle": "Driver Settings", "@driverSettingsTitle": {"description": "Driver settings screen title"}, "carSettingsTitle": "Car Settings", "@carSettingsTitle": {"description": "Car settings screen title"}, "cargoSettingsTitle": "Cargo Settings", "@cargoSettingsTitle": {"description": "Cargo settings screen title"}, "destinationSettingsTitle": "Destination Settings", "@destinationSettingsTitle": {"description": "Destination settings screen title"}, "searchCars": "Search Cars", "@searchCars": {"description": "Search cars hint text"}, "searchCarsHint": "Enter car code, manufacturer, or model", "@searchCarsHint": {"description": "Search cars hint text"}, "noCarsFound": "No cars found", "@noCarsFound": {"description": "No cars found message"}, "noMatchingCarsFound": "No matching cars found", "@noMatchingCarsFound": {"description": "No matching cars found message"}, "failedToLoadCarsWithCode": "Failed to load cars: {statusCode}", "@failedToLoadCarsWithCode": {"description": "Failed to load cars error message with status code", "placeholders": {"statusCode": {"type": "String"}}}, "carDeletedSuccessfully": "Car deleted successfully", "@carDeletedSuccessfully": {"description": "Car deleted successfully message"}, "failedToDeleteCar": "Failed to delete car", "@failedToDeleteCar": {"description": "Failed to delete car error message"}, "confirmDeleteCar": "Are you sure you want to delete car {carCode}?", "@confirmDeleteCar": {"description": "Confirm delete car message", "placeholders": {"carCode": {"type": "String"}}}, "searchCargos": "Search cargos", "@searchCargos": {"description": "Search cargos placeholder text"}, "searchCargosHint": "Enter cargo code or name", "@searchCargosHint": {"description": "Search cargos hint text"}, "noCargosFound": "No cargos found", "@noCargosFound": {"description": "No cargos found message"}, "noMatchingCargosFound": "No matching cargos found", "@noMatchingCargosFound": {"description": "No matching cargos found message"}, "failedToLoadCargos": "Failed to load cargos: {statusCode}", "@failedToLoadCargos": {"description": "Failed to load cargos error message", "placeholders": {"statusCode": {"type": "String"}}}, "noCargoDataFound": "No cargo data found", "@noCargoDataFound": {"description": "No cargo data found message"}, "cargoDeletedSuccessfully": "Cargo deleted successfully", "@cargoDeletedSuccessfully": {"description": "Cargo deleted successfully message"}, "failedToDeleteCargo": "Failed to delete cargo", "@failedToDeleteCargo": {"description": "Failed to delete cargo error message"}, "confirmDeleteCargo": "Are you sure you want to delete cargo {cargoCode}?", "@confirmDeleteCargo": {"description": "Confirm delete cargo message", "placeholders": {"cargoCode": {"type": "String"}}}, "cargoCode": "Cargo Code", "@cargoCode": {"description": "Cargo code label"}, "cargoName": "Cargo Name", "@cargoName": {"description": "Cargo name label"}, "searchDestinations": "Search destinations", "@searchDestinations": {"description": "Search destinations placeholder text"}, "searchDestinationsHint": "Enter destination code, name, address, or initial", "@searchDestinationsHint": {"description": "Search destinations hint text"}, "noDestinationsFound": "No destinations found", "@noDestinationsFound": {"description": "No destinations found message"}, "noMatchingDestinationsFound": "No matching destinations found", "@noMatchingDestinationsFound": {"description": "No matching destinations found message"}, "failedToLoadDestinations": "Failed to load destinations: {statusCode}", "@failedToLoadDestinations": {"description": "Failed to load destinations error message", "placeholders": {"statusCode": {"type": "String"}}}, "destinationDeletedSuccessfully": "Destination deleted successfully", "@destinationDeletedSuccessfully": {"description": "Destination deleted successfully message"}, "failedToDeleteDestination": "Failed to delete destination", "@failedToDeleteDestination": {"description": "Failed to delete destination error message"}, "confirmDeleteDestination": "Are you sure you want to delete destination {destinationCode}?", "@confirmDeleteDestination": {"description": "Confirm delete destination message", "placeholders": {"destinationCode": {"type": "String"}}}, "destinationCode": "Destination Code", "@destinationCode": {"description": "Destination code label"}, "active": "ACTIVE", "@active": {"description": "Active status value"}, "inactive": "INACTIVE", "@inactive": {"description": "Inactive status value"}, "tripApprovalsTitle": "Trip Approvals", "@tripApprovalsTitle": {"description": "Trip approvals screen title"}, "noTripsForApproval": "No trips for approval", "@noTripsForApproval": {"description": "No trips for approval message"}, "failedToFetchTrips": "Failed to fetch trips", "@failedToFetchTrips": {"description": "Failed to fetch trips error message"}, "requestedBy": "Requested by", "@requestedBy": {"description": "Requested by label"}, "requestDate": "Request Date", "@requestDate": {"description": "Request date label"}, "tripDate": "Trip Date", "@tripDate": {"description": "Trip date label"}, "purpose": "Purpose", "@purpose": {"description": "Purpose label"}, "approve": "Approve", "@approve": {"description": "Approve button text"}, "reject": "Reject", "@reject": {"description": "Reject button text"}, "viewChanges": "View Changes", "@viewChanges": {"description": "Tooltip for view changes button"}, "details": "Details", "@details": {"description": "Details button text"}, "tripApprovedSuccessfully": "Trip approved successfully", "@tripApprovedSuccessfully": {"description": "Trip approved successfully message"}, "tripRejectedSuccessfully": "Trip rejected successfully", "@tripRejectedSuccessfully": {"description": "Success message when a trip is rejected"}, "failedToUpdateTrip": "Failed to update trip", "@failedToUpdateTrip": {"description": "Failed to update trip error message"}, "addDriver": "Add Driver", "@addDriver": {"description": "Add driver screen title and button text"}, "searchDrivers": "Search drivers...", "@searchDrivers": {"description": "Search drivers placeholder text"}, "noDriversFound": "No drivers found", "@noDriversFound": {"description": "No drivers found message"}, "rateTripTitle": "Rate Trip", "@rateTripTitle": {"description": "Rate trip screen title"}, "howWouldYouRateTrip": "How would you rate your trip?", "@howWouldYouRateTrip": {"description": "Rating question text"}, "additionalComments": "Additional Comments", "@additionalComments": {"description": "Additional comments section title"}, "submitRating": "Submit Rating", "@submitRating": {"description": "Submit rating button text"}, "errorSubmittingRating": "Error submitting rating", "@errorSubmittingRating": {"description": "Error message when rating submission fails"}, "tripChangeApproval": "Trip Change Approval", "@tripChangeApproval": {"description": "Trip Change Approval menu item"}, "confirmApproval": "Confirm Approval", "@confirmApproval": {"description": "Confirm approval dialog title"}, "confirmRejection": "Confirm Rejection", "@confirmRejection": {"description": "Confirm rejection dialog title"}, "areYouSureApproveTrip": "Are you sure you want to approve trip {tripCode}? This will change the status to REQUEST.", "@areYouSureApproveTrip": {"description": "Trip approval confirmation message", "placeholders": {"tripCode": {"type": "String"}}}, "areYouSureApproveChanges": "Are you sure you want to approve the changes to trip {tripCode}? This will change the status to REQUEST.", "@areYouSureApproveChanges": {"description": "<PERSON> changes approval confirmation message", "placeholders": {"tripCode": {"type": "String"}}}, "areYouSureApproveCancellation": "Are you sure you want to approve the cancellation of trip {tripCode}? This will change the status to DELETED.", "@areYouSureApproveCancellation": {"description": "Trip cancellation approval confirmation message", "placeholders": {"tripCode": {"type": "String"}}}, "areYouSureRejectTrip": "Are you sure you want to reject trip {tripCode}?", "@areYouSureRejectTrip": {"description": "Trip rejection confirmation message", "placeholders": {"tripCode": {"type": "String"}}}, "areYouSureRejectChanges": "Are you sure you want to reject the changes to trip {tripCode}? The trip will be restored to its original state before the edits were made.", "@areYouSureRejectChanges": {"description": "<PERSON> changes rejection confirmation message", "placeholders": {"tripCode": {"type": "String"}}}, "areYouSureRejectCancellation": "Are you sure you want to reject the cancellation of trip {tripCode}? The trip will remain active.", "@areYouSureRejectCancellation": {"description": "Trip cancellation rejection confirmation message", "placeholders": {"tripCode": {"type": "String"}}}, "pleaseProvideReason": "Please provide a reason for rejection:", "@pleaseProvideReason": {"description": "Prompt for rejection reason"}, "enterRejectionReason": "Enter reason for rejecting the trip", "@enterRejectionReason": {"description": "Hint text for rejection reason field"}, "reasonIsRequired": "Reason is required", "@reasonIsRequired": {"description": "Validation message for required reason field"}, "requestor": "Requestor", "@requestor": {"description": "Requestor column header"}, "tripApprovedAndStatusChanged": "Trip approved and status changed to REQUEST", "@tripApprovedAndStatusChanged": {"description": "Trip approval success message"}, "failedToApproveTrip": "Failed to approve trip", "@failedToApproveTrip": {"description": "Trip approval error message"}, "failedToRejectTrip": "Failed to reject trip", "@failedToRejectTrip": {"description": "Trip rejection error message"}, "failedToApproveChanges": "Failed to approve trip changes", "@failedToApproveChanges": {"description": "Trip changes approval error message"}, "failedToApproveCancellation": "Failed to approve trip cancellation", "@failedToApproveCancellation": {"description": "Trip cancellation approval error message"}, "failedToRejectChanges": "Failed to reject trip changes", "@failedToRejectChanges": {"description": "<PERSON> changes rejection error message"}, "failedToRejectCancellation": "Failed to reject trip cancellation", "@failedToRejectCancellation": {"description": "Trip cancellation rejection error message"}, "tripChangesApprovedSuccessfully": "Trip changes approved successfully. Status changed to REQUEST.", "@tripChangesApprovedSuccessfully": {"description": "Success message when trip changes are approved"}, "tripCancellationApprovedSuccessfully": "Trip cancellation approved successfully. Status changed to DELETED.", "@tripCancellationApprovedSuccessfully": {"description": "Success message when trip cancellation is approved"}, "tripChangesRejectedSuccessfully": "Trip changes rejected successfully. Status changed to REQUEST.", "@tripChangesRejectedSuccessfully": {"description": "Success message when trip changes are rejected"}, "tripCancellationRejectedSuccessfully": "Trip cancellation rejected successfully. Status changed to REQUEST.", "@tripCancellationRejectedSuccessfully": {"description": "Success message when trip cancellation is rejected"}, "approveTrip": "Approve Trip", "@approveTrip": {"description": "Tooltip for approve trip button"}, "approveChanges": "Approve Changes", "@approveChanges": {"description": "Tooltip for approve changes button"}, "approveCancellation": "Approve Cancellation", "@approveCancellation": {"description": "Tooltip for approve cancellation button"}, "rejectTrip": "Reject Trip", "@rejectTrip": {"description": "Tooltip for reject trip button"}, "rejectChanges": "Reject Changes", "@rejectChanges": {"description": "Tooltip for reject changes button"}, "rejectCancellation": "Reject Cancellation", "@rejectCancellation": {"description": "Tooltip for reject cancellation button"}, "noTripsFound": "No trips found", "@noTripsFound": {"description": "Message when no trips are found"}, "noTripsFoundForDate": "No trips found for date: {date}", "@noTripsFoundForDate": {"description": "Message when no trips are found for a specific date", "placeholders": {"date": {"type": "String", "description": "The date for which no trips were found"}}}, "tripMonitoringTitle": "Trip Monitoring", "@tripMonitoringTitle": {"description": "Trip monitoring screen title"}, "trackDrivers": "Track Drivers", "@trackDrivers": {"description": "Track drivers button text"}, "allRequests": "All Requests", "@allRequests": {"description": "All requests button text"}, "refreshData": "Refresh Data", "@refreshData": {"description": "Refresh data button text"}, "swipeToChangeDate": "Swipe to change date", "@swipeToChangeDate": {"description": "Instruction text for mobile date navigation"}, "errorRefreshingData": "Error refreshing data", "@errorRefreshingData": {"description": "Error message when data refresh fails"}, "searchDriversHint": "Enter driver code, initial, or name", "@searchDriversHint": {"description": "Search drivers hint text"}, "noMatchingDriversFound": "No matching drivers found", "@noMatchingDriversFound": {"description": "No matching drivers found message"}, "driverDeletedSuccessfully": "Driver deleted successfully", "@driverDeletedSuccessfully": {"description": "Driver deleted successfully message"}, "failedToLoadDrivers": "Failed to load drivers: {statusCode}", "@failedToLoadDrivers": {"description": "Failed to load drivers error message", "placeholders": {"statusCode": {"type": "String"}}}, "driverCodeLabel": "Driver Code", "@driverCodeLabel": {"description": "Driver code label"}, "carLabel": "Car:", "@carLabel": {"description": "Car label in trip card"}, "saveChanges": "Save Changes", "@saveChanges": {"description": "Save button text"}, "waitingTrip": "Waiting Trip", "@waitingTrip": {"description": "Waiting trip checkbox label"}, "fromDestination": "From Destination", "@fromDestination": {"description": "From destination field label"}, "toDestination": "To Destination", "@toDestination": {"description": "To destination field label"}, "notesOptional": "Notes (optional)", "@notesOptional": {"description": "Notes field label"}, "selectPassengers": "Select Passengers", "@selectPassengers": {"description": "Select passengers field label"}, "selectCargos": "Select Cargos", "@selectCargos": {"description": "Select cargos field label"}, "trip": "Trip", "@trip": {"description": "Trip section header"}, "cargos": "Cargos", "@cargos": {"description": "Cargos section header"}, "searchForDestinations": "Search for destinations", "@searchForDestinations": {"description": "Search placeholder for destinations"}, "customFromDestination": "Custom From Destination", "@customFromDestination": {"description": "Custom from destination field label"}, "customToDestination": "Custom To Destination", "@customToDestination": {"description": "Custom to destination field label"}, "enterCustomLocation": "Enter custom location", "@enterCustomLocation": {"description": "Hint text for custom location fields"}, "addAdditionalNotes": "Add any additional notes for this trip", "@addAdditionalNotes": {"description": "Hint text for notes field"}, "searchForPassengers": "Search for passengers", "@searchForPassengers": {"description": "Search placeholder for passengers"}, "chooseOneOrMorePassengers": "Choose one or more passengers", "@chooseOneOrMorePassengers": {"description": "Hint text for passenger selection"}, "searchForCargos": "Search for cargos", "@searchForCargos": {"description": "Search placeholder for cargos"}, "chooseOneOrMoreCargos": "Choose one or more cargos", "@chooseOneOrMoreCargos": {"description": "Hint text for cargo selection"}, "rateTrips": "Rate Trips", "@rateTrips": {"description": "Rate trips navigation label"}, "myTrips": "My Trips", "@myTrips": {"description": "My trips navigation label"}, "checkIn": "Check-In", "@checkIn": {"description": "Check-in navigation label"}, "notifications": "Notifications", "@notifications": {"description": "Notifications navigation label"}, "admin": "Admin", "@admin": {"description": "Admin navigation label"}, "switchRole": "Switch Role", "@switchRole": {"description": "Switch role button text"}, "currentRole": "Current Role", "@currentRole": {"description": "Current role label"}, "confirmRoleSwitch": "Confirm Role Switch", "@confirmRoleSwitch": {"description": "Role switch confirmation dialog title"}, "switchRoleMessage": "Are you sure you want to switch to {role}? This will change your interface and available functions.", "@switchRoleMessage": {"description": "Role switch confirmation message"}, "roleSwitchedSuccessfully": "Successfully switched to {role}", "@roleSwitchedSuccessfully": {"description": "Role switch success message"}, "now": "Now", "@now": {"description": "Current time label"}, "currentTime": "Current Time", "@currentTime": {"description": "Current time label"}, "rating": "Rating", "@rating": {"description": "Rating column header"}, "cargo": "Cargo", "@cargo": {"description": "Cargo section label"}, "clearStatusFilter": "Clear Status Filter", "@clearStatusFilter": {"description": "Clear status filter button"}, "specificDate": "Specific Date", "@specificDate": {"description": "Specific date filter label"}, "year": "Year", "@year": {"description": "Year filter label"}, "month": "Month", "@month": {"description": "Month filter label"}, "currentFilter": "Current Filter", "@currentFilter": {"description": "Current filter label for date filter summary"}, "selectYear": "Select Year", "@selectYear": {"description": "Select year dialog title"}, "selectMonth": "Select Month", "@selectMonth": {"description": "Select month dialog title"}, "searchTrips": "Search trips (code, requestor, locations...)", "@searchTrips": {"description": "Search trips placeholder text"}, "odometerReading": "Odometer Reading", "@odometerReading": {"description": "Odometer reading label"}, "searchTripsLong": "Search trips (code, requestor, locations...)", "@searchTripsLong": {"description": "Extended search placeholder for trips"}, "itemsPerPage": "Items per page", "@itemsPerPage": {"description": "Dropdown hint for pagination"}, "exportToExcel": "Export to Excel", "@exportToExcel": {"description": "Export to Excel button tooltip"}, "exporting": "Exporting...", "@exporting": {"description": "Exporting status text"}, "exportingToExcel": "Exporting to Excel...", "@exportingToExcel": {"description": "Exporting to Excel loading text"}, "noTripsToExport": "No trips to export", "@noTripsToExport": {"description": "Message when no trips available for export"}, "tripHistoryExported": "Trip history exported as {fileName}", "@tripHistoryExported": {"description": "Success message for trip history export", "placeholders": {"fileName": {"type": "String"}}}, "tripHistoryExportedTo": "Trip history exported to: {filePath}", "@tripHistoryExportedTo": {"description": "Success message for trip history export with file path", "placeholders": {"filePath": {"type": "String"}}}, "exportFailed": "Export failed: {error}", "@exportFailed": {"description": "Error message for export failure", "placeholders": {"error": {"type": "String"}}}, "noTripHistoryFound": "No trip history found", "@noTripHistoryFound": {"description": "Message when no trip history exists"}, "noMatchingTripsFound": "No matching trips found", "@noMatchingTripsFound": {"description": "Message when no trips match search criteria"}, "superAdmin": "Super Admin", "@superAdmin": {"description": "Super Admin role label"}, "tripManager": "Trip Manager", "@tripManager": {"description": "Trip Manager role label"}, "user": "User", "@user": {"description": "Default user name when name is not available"}, "locationTracking": "Location Tracking", "@locationTracking": {"description": "Location Tracking menu item"}, "tripRating": "<PERSON>ing", "@tripRating": {"description": "Trip Rating menu item"}, "driverAvailabilityManagement": "Driver Availability Management", "@driverAvailabilityManagement": {"description": "Driver availability management screen title"}, "carAvailabilityManagement": "Car Availability Management", "@carAvailabilityManagement": {"description": "Car availability management screen title"}, "driverCheckInManagement": "Driver Check-In Management", "@driverCheckInManagement": {"description": "Driver Check-In Management menu item"}, "carOdometerHistory": "Car Odometer History", "@carOdometerHistory": {"description": "Car Odometer History menu item"}, "driverCheckIn": "Driver Check-In", "@driverCheckIn": {"description": "Driver check-in screen title"}, "myTripHistory": "My Trip History", "@myTripHistory": {"description": "My Trip History menu item"}, "selectADriver": "Select a driver", "@selectADriver": {"description": "Driver selection dropdown hint"}, "assign": "Assign", "@assign": {"description": "Assign button text"}, "viewReason": "View Reason", "@viewReason": {"description": "Button text to view rejection reason"}, "close": "Close", "@close": {"description": "Close button text"}, "allTripRequests": "All Trip Requests", "@allTripRequests": {"description": "All trip requests title"}, "rejectionReason": "Rejection Reason", "@rejectionReason": {"description": "Rejection reason field label"}, "tripChanges": "Trip Changes", "@tripChanges": {"description": "<PERSON> changes screen title"}, "confirmCancellation": "Confirm Cancellation", "@confirmCancellation": {"description": "Confirm cancellation dialog title"}, "tripApprovedMessage": "Trip approved and status changed to REQUEST", "@tripApprovedMessage": {"description": "Message shown when trip is approved"}, "pleaseSelectRating": "Please select a rating", "@pleaseSelectRating": {"description": "Validation message for rating selection"}, "ratingSubmittedSuccessfully": "Rating submitted successfully", "@ratingSubmittedSuccessfully": {"description": "Success message for rating submission"}, "failedToLoadTripDetails": "Failed to load trip details", "@failedToLoadTripDetails": {"description": "Error message when trip details fail to load"}, "notRated": "Not rated", "@notRated": {"description": "Text shown when trip is not rated"}, "previous": "Previous", "@previous": {"description": "Previous button text in pagination"}, "next": "Next", "@next": {"description": "Next button text in pagination"}, "noTripDataAvailable": "No trip data available", "@noTripDataAvailable": {"description": "Message when no trip data is available"}, "pleaseSelectBothDestinations": "Please select both destinations", "@pleaseSelectBothDestinations": {"description": "Validation message for destination selection"}, "destinationsCannotBeSame": "From and To destinations cannot be the same", "@destinationsCannotBeSame": {"description": "Validation message for same destinations"}, "customDestinationsCannotBeSame": "From and To custom destinations cannot be the same", "@customDestinationsCannotBeSame": {"description": "Validation message for same custom destinations"}, "pleaseEnterWaitingDuration": "Please enter waiting duration", "@pleaseEnterWaitingDuration": {"description": "Validation message for waiting duration"}, "pleaseEnterCustomFromDestination": "Please enter custom from destination", "@pleaseEnterCustomFromDestination": {"description": "Validation message for custom from destination"}, "pleaseEnterCustomToDestination": "Please enter custom to destination", "@pleaseEnterCustomToDestination": {"description": "Validation message for custom to destination"}, "failedToLoadAvailableCars": "Failed to load available cars", "@failedToLoadAvailableCars": {"description": "Error message when cars fail to load"}, "requestTrips": "Request Trips", "@requestTrips": {"description": "Request trips button text"}, "otherTripStatuses": "Other Trip Statuses", "@otherTripStatuses": {"description": "Other trip statuses section title"}, "multipleTrips": "Multiple Trips", "@multipleTrips": {"description": "Multiple trips section title"}, "tripDuration": "Trip Duration:", "@tripDuration": {"description": "Trip duration label"}, "noNotesProvided": "No notes provided for this trip.", "@noNotesProvided": {"description": "Message when no notes are available for a trip"}, "noNotifications": "No notifications", "@noNotifications": {"description": "Message when there are no notifications"}, "authenticationFailed": "Authentication failed", "@authenticationFailed": {"description": "Generic authentication failure message"}, "noTripDataAvailableRequestor": "No trip data available", "@noTripDataAvailableRequestor": {"description": "Message when no trip data is available in requestor dashboard"}, "myTripRequests": "My Trip Requests", "@myTripRequests": {"description": "Title for trip requests section"}, "editDriver": "Edit Driver", "@editDriver": {"description": "Edit driver screen title"}, "failedToLoadCars": "Failed to load available cars", "@failedToLoadCars": {"description": "Error message when cars fail to load"}, "failedToUpdateDriver": "Failed to update driver", "@failedToUpdateDriver": {"description": "Error message when driver update fails"}, "name": "Name", "@name": {"description": "Name field label"}, "nameWillBeUppercase": "Name will be converted to uppercase", "@nameWillBeUppercase": {"description": "Helper text for name field"}, "nameIsRequired": "Name is required", "@nameIsRequired": {"description": "Validation message for required name field"}, "initialHelperText": "Must be exactly 3 characters (will be converted to uppercase)", "@initialHelperText": {"description": "Helper text for initial field"}, "initialIsRequired": "Initial is required", "@initialIsRequired": {"description": "Validation message for required initial field"}, "initialMustBe3Characters": "Initial must be exactly 3 characters", "@initialMustBe3Characters": {"description": "Validation message for initial field length"}, "loadingCars": "Loading cars...", "@loadingCars": {"description": "Loading text for cars dropdown"}, "selectACar": "Select a car", "@selectACar": {"description": "Placeholder text for car selection"}, "searchForCar": "Search for a car", "@searchForCar": {"description": "Search hint text in car dropdown"}, "pleaseSelectCar": "Please select a car", "@pleaseSelectCar": {"description": "Validation message for car selection"}, "newPasswordOptional": "New Password (optional)", "@newPasswordOptional": {"description": "Password field label"}, "leaveEmptyToKeepPassword": "Leave empty to keep current password", "@leaveEmptyToKeepPassword": {"description": "Helper text for password field"}, "fromDestinationRequired": "From destination is required", "@fromDestinationRequired": {"description": "Validation message for from destination"}, "toDestinationRequired": "To destination is required", "@toDestinationRequired": {"description": "Validation message for to destination"}, "dateIsRequired": "Date is required", "@dateIsRequired": {"description": "Validation message for required date"}, "timeIsRequired": "Time is required", "@timeIsRequired": {"description": "Validation message for required time"}, "pleaseEnterOdometerReading": "Please enter odometer reading", "@pleaseEnterOdometerReading": {"description": "Validation message for odometer field"}, "odometerReadingRequired": "Odometer reading is required", "@odometerReadingRequired": {"description": "Validation message when odometer reading is empty"}, "pleaseEnterValidNumber": "Please enter a valid number", "@pleaseEnterValidNumber": {"description": "Validation message for invalid number input"}, "allDrivers": "All Drivers", "@allDrivers": {"description": "Dropdown option for all drivers"}, "validationStatus": "Validation Status", "@validationStatus": {"description": "Validation status filter label"}, "validated": "Validated", "@validated": {"description": "Validation status: validated"}, "notValidated": "Not Validated", "@notValidated": {"description": "Validation status: not validated"}, "checkInValidatedSuccessfully": "Check-in validated successfully", "@checkInValidatedSuccessfully": {"description": "Success message for check-in validation"}, "validationRemoved": "Validation removed", "@validationRemoved": {"description": "Message when validation is removed"}, "reset": "Reset", "@reset": {"description": "Reset button text"}, "checkInDetails": "Check-In Details", "@checkInDetails": {"description": "Section title for check-in details"}, "checkOutDetails": "Check-Out Details", "@checkOutDetails": {"description": "Check-out details section title"}, "rateTrip": "Rate Trip", "@rateTrip": {"description": "Rate trip button text"}, "rateThisTrip": "Rate This Trip", "@rateThisTrip": {"description": "Rate this trip button text"}, "cannotAdjustDurationNoTripId": "Cannot adjust duration for this trip - no trip ID", "@cannotAdjustDurationNoTripId": {"description": "Error message when trip ID is missing for duration adjustment"}, "cannotAdjustDuration": "Cannot adjust duration for this trip", "@cannotAdjustDuration": {"description": "Generic error message for duration adjustment"}, "failedToUpdateDuration": "Failed to update duration: {statusCode}", "@failedToUpdateDuration": {"description": "Error message when duration update fails", "placeholders": {"statusCode": {"type": "String"}}}, "updatedDurationForTrip": "Updated duration for trip {tripCode} to {minutes} minutes", "@updatedDurationForTrip": {"description": "Success message when trip duration is updated", "placeholders": {"tripCode": {"type": "String"}, "minutes": {"type": "String"}}}, "rateYourTrips": "Rate Your Trips", "@rateYourTrips": {"description": "Rate your trips screen title"}, "tripCreatedSuccessfully": "Trip ({tripCode}) created successfully", "@tripCreatedSuccessfully": {"description": "Success message when a trip is created", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}}}, "roundTripCreatedSuccessfully": "Round trip created successfully: Outbound ({outboundCode}), Return ({returnCode})", "@roundTripCreatedSuccessfully": {"description": "Success message when a round trip is created", "placeholders": {"outboundCode": {"type": "String", "description": "The outbound trip code"}, "returnCode": {"type": "String", "description": "The return trip code"}}}, "waitingTripCreatedSuccessfully": "Waiting trip created successfully: ({tripCode})", "@waitingTripCreatedSuccessfully": {"description": "Success message when a waiting trip is created", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}}}, "tripUpdatedSuccessfully": "Trip ({tripCode}) updated successfully", "@tripUpdatedSuccessfully": {"description": "Success message when a trip is updated", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}}}, "loadingTripDetails": "Loading trip details...", "@loadingTripDetails": {"description": "Loading message for trip details"}, "tripDataIsLoading": "Trip data is loading...", "@tripDataIsLoading": {"description": "Loading message for trip data"}, "loadingAvailableDrivers": "Loading available drivers...", "@loadingAvailableDrivers": {"description": "Loading message for available drivers"}, "checkinValidatedSuccessfully": "Check-in validated successfully", "@checkinValidatedSuccessfully": {"description": "Success message for check-in validation"}, "backToBaseTripCreatedSuccessfully": "Back to Base trip created successfully", "@backToBaseTripCreatedSuccessfully": {"description": "Success message when Back to Base trip is created"}, "failedToUpdateCarOdometer": "Failed to update car odometer", "@failedToUpdateCarOdometer": {"description": "Error message for car odometer update failure"}, "errorLoadingData": "Error loading data", "@errorLoadingData": {"description": "Generic error message when data fails to load"}, "errorWithDetails": "Error: {details}", "@errorWithDetails": {"description": "Generic error message with details placeholder", "placeholders": {"details": {"type": "String", "description": "The error details"}}}, "tripCompletedSuccessfully": "Trip completed successfully", "@tripCompletedSuccessfully": {"description": "Success message when trip is completed"}, "failedToCompleteTrip": "Failed to complete the trip", "@failedToCompleteTrip": {"description": "Error message when trip completion fails"}, "errorCompletingTrip": "Error completing trip: {error}", "@errorCompletingTrip": {"description": "Error message when trip completion throws exception", "placeholders": {"error": {"type": "String", "description": "The error details"}}}, "authenticationFailedInvalidCode": "Authentication failed: Invalid or expired authentication code", "@authenticationFailedInvalidCode": {"description": "Authentication failure message for invalid/expired code"}, "authenticationFailedWithError": "Authentication failed: {error}", "@authenticationFailedWithError": {"description": "Authentication failure message with specific error", "placeholders": {"error": {"type": "String", "description": "The specific error details"}}}, "invalidCredentials": "Invalid username or password", "@invalidCredentials": {"description": "Error message for invalid login credentials"}, "areYouSureCancelTrip": "Are you sure you want to cancel trip {tripCode}? This will submit a cancellation request for review.", "@areYouSureCancelTrip": {"description": "Trip cancellation confirmation message", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}}}, "requestCancellation": "Request Cancellation", "@requestCancellation": {"description": "Request cancellation button text"}, "tripCancellationRequestSubmitted": "Trip cancellation request submitted for review", "@tripCancellationRequestSubmitted": {"description": "Success message for trip cancellation request"}, "failedToSubmitCancellationRequest": "Failed to submit trip cancellation request", "@failedToSubmitCancellationRequest": {"description": "Error message for trip cancellation request failure"}, "update": "Update", "@update": {"description": "Update button text"}, "assignDriverOrOnlineTaxi": "Assign Driver or Online Taxi to Trip {tripCode}", "@assignDriverOrOnlineTaxi": {"description": "Dialog title for assigning driver", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}}}, "deleteTripConfirmation": "Delete Trip {tripCode}?", "@deleteTripConfirmation": {"description": "Confirmation dialog for deleting trip", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}}}, "retry": "Retry", "@retry": {"description": "Retry button text"}, "driversCount": "Drivers: {count}", "@driversCount": {"description": "Display number of drivers", "placeholders": {"count": {"type": "int", "description": "Number of drivers"}}}, "inactiveDriversInfo": "Gray = Inactive > 10 min", "@inactiveDriversInfo": {"description": "Information about inactive drivers display"}, "updateFrequencyInfo": "Updates every 5 min or 500m", "@updateFrequencyInfo": {"description": "Information about location update frequency"}, "errorFetchingDrivers": "Error fetching drivers: {error}", "@errorFetchingDrivers": {"description": "Error message when drivers cannot be fetched", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "unknown": "Unknown", "@unknown": {"description": "Generic unknown value text"}, "unknownDriver": "Unknown Driver", "@unknownDriver": {"description": "Default driver name when name is not available"}, "centeredOnDriver": "Centered on driver {driver<PERSON><PERSON>}", "@centeredOnDriver": {"description": "Message when map is centered on a driver", "placeholders": {"driverName": {"type": "String", "description": "The name of the driver"}}}, "driverTrips": "Driver Trips ({count})", "@driverTrips": {"description": "Number of trips for a driver", "placeholders": {"count": {"type": "int", "description": "The number of trips"}}}, "lastUpdated": "Last Updated", "@lastUpdated": {"description": "Label for last updated time"}, "tripNumber": "Trip {current} of {total}", "@tripNumber": {"description": "Trip number display", "placeholders": {"current": {"type": "String", "description": "The current trip number"}, "total": {"type": "String", "description": "The total number of trips"}}}, "completeTrip": "Complete Trip", "@completeTrip": {"description": "Complete trip button text"}, "allStatuses": "All Statuses", "@allStatuses": {"description": "All statuses option in dropdown"}, "clearFilters": "Clear Filters", "@clearFilters": {"description": "Clear filters button text"}, "noTripsMatchFilters": "No trips match the current filters", "@noTripsMatchFilters": {"description": "Message when no trips match filters"}, "confirmTrip": "Confirm Trip", "@confirmTrip": {"description": "<PERSON><PERSON> text to confirm a trip"}, "startTrip": "Start Trip", "@startTrip": {"description": "<PERSON><PERSON> text to start a trip"}, "finishTrip": "Finish Trip", "@finishTrip": {"description": "Finish trip button text"}, "updateOdometerFor": "Update Odometer for {carCode}", "@updateOdometerFor": {"description": "Title for odometer update dialog", "placeholders": {"carCode": {"type": "String", "description": "The car code"}}}, "firstTripOfDay": "First trip of the day", "@firstTripOfDay": {"description": "Text indicating this is the first trip of the day"}, "enterNewOdometerReading": "Enter new odometer reading", "@enterNewOdometerReading": {"description": "Hint text for odometer reading input field"}, "odometerReadingHelper": "Must be greater than or equal to current reading", "@odometerReadingHelper": {"description": "Helper text for odometer reading validation"}, "newReadingMustBeAtLeast": "New reading must be at least {minimum}", "@newReadingMustBeAtLeast": {"description": "Validation message for odometer reading below minimum", "placeholders": {"minimum": {"type": "String", "description": "The minimum required reading"}}}, "addCar": "Add Car", "@addCar": {"description": "Add car screen title and button text"}, "editCar": "Edit Car", "@editCar": {"description": "Edit car screen title"}, "failedToGenerateCarCode": "Failed to generate car code", "@failedToGenerateCarCode": {"description": "Error message when car code generation fails"}, "pleaseEnterManufacturer": "Please enter manufacturer", "@pleaseEnterManufacturer": {"description": "Validation message for manufacturer field"}, "pleaseEnterModelName": "Please enter model name", "@pleaseEnterModelName": {"description": "Validation message for model name field"}, "pleaseEnterCarType": "Please enter car type", "@pleaseEnterCarType": {"description": "Validation message for car type field"}, "pleaseEnterPlateNumber": "Please enter plate number", "@pleaseEnterPlateNumber": {"description": "Validation message for plate number field"}, "pleaseEnterColor": "Please enter color", "@pleaseEnterColor": {"description": "Validation message for color field"}, "failedToAddCar": "Failed to add car", "@failedToAddCar": {"description": "Error message when car addition fails"}, "addCargo": "Add Cargo", "@addCargo": {"description": "Add cargo screen title and button text"}, "editCargo": "<PERSON>", "@editCargo": {"description": "Edit cargo screen title"}, "failedToGenerateCargoCode": "Failed to generate cargo code", "@failedToGenerateCargoCode": {"description": "Error message when cargo code generation fails"}, "pleaseEnterCargoName": "Please enter cargo name", "@pleaseEnterCargoName": {"description": "Validation message for cargo name field"}, "failedToAddCargo": "Failed to add cargo", "@failedToAddCargo": {"description": "Error message when cargo addition fails"}, "submit": "Submit", "@submit": {"description": "Submit button text"}, "adding": "Adding...", "@adding": {"description": "Loading state text for adding operation"}, "addDestination": "Add Destination", "@addDestination": {"description": "Add destination screen title and button text"}, "editDestination": "Edit Destination", "@editDestination": {"description": "Edit destination screen title"}, "failedToGenerateDestinationCode": "Failed to generate destination code", "@failedToGenerateDestinationCode": {"description": "Error message when destination code generation fails"}, "pleaseEnterDestinationName": "Please enter destination name", "@pleaseEnterDestinationName": {"description": "Validation message for destination name field"}, "pleaseEnterAddress": "Please enter address", "@pleaseEnterAddress": {"description": "Validation message for address field"}, "pleaseEnterInitial": "Please enter initial", "@pleaseEnterInitial": {"description": "Validation message for initial field"}, "failedToAddDestination": "Failed to add destination", "@failedToAddDestination": {"description": "Error message when destination addition fails"}, "destinationName": "Destination Name", "@destinationName": {"description": "Destination name field label"}, "durationHours": "Duration (Hours)", "@durationHours": {"description": "Duration hours field label"}, "durationMinutes": "Duration (Minutes)", "@durationMinutes": {"description": "Duration minutes field label"}, "coordinatesLatLong": "Coordinates (Latitude, Longitude)", "@coordinatesLatLong": {"description": "Coordinates field label with description"}, "coordinatesFormat": "Format: lat,long (e.g., 12.3456,78.9012)", "@coordinatesFormat": {"description": "Coordinates format hint text"}, "pleaseEnterCoordinates": "Please enter coordinates", "@pleaseEnterCoordinates": {"description": "Validation message for coordinates field"}, "invalidHours": "Invalid hours", "@invalidHours": {"description": "Validation message for invalid hours"}, "minutesMustBe0To59": "Minutes must be 0-59", "@minutesMustBe0To59": {"description": "Validation message for minutes range"}, "mustBe3Characters": "Must be exactly 3 characters (will be converted to uppercase)", "@mustBe3Characters": {"description": "Helper text for initial field"}, "initialRequired": "Initial is required", "@initialRequired": {"description": "Validation message for required initial field"}, "failedToGenerateDriverCode": "Failed to generate driver code", "@failedToGenerateDriverCode": {"description": "Error message when driver code generation fails"}, "pleaseEnterName": "Please enter name", "@pleaseEnterName": {"description": "Validation message for name field"}, "pleaseConfirmPassword": "Please confirm password", "@pleaseConfirmPassword": {"description": "Validation message for password confirmation"}, "generatingCode": "Generating code...", "@generatingCode": {"description": "Loading text for code generation"}, "nameUppercaseHelper": "Name will be converted to uppercase", "@nameUppercaseHelper": {"description": "Helper text for name field"}, "searchForACar": "Search for a car", "@searchForACar": {"description": "Search hint for car dropdown"}, "pleaseSelectACar": "Please select a car", "@pleaseSelectACar": {"description": "Validation message for car selection"}, "confirmPassword": "Confirm Password", "@confirmPassword": {"description": "Confirm password field label"}, "passwordMinLength": "Password must be at least 6 characters", "@passwordMinLength": {"description": "Password minimum length validation message"}, "failedToAddDriver": "Failed to add driver", "@failedToAddDriver": {"description": "Error message when driver addition fails"}, "updateCar": "Update Car", "@updateCar": {"description": "Update car button text"}, "updateCargo": "Update Cargo", "@updateCargo": {"description": "Update cargo button text"}, "updating": "Updating...", "@updating": {"description": "Updating button text when in progress"}, "updateDestination": "Update Destination", "@updateDestination": {"description": "Update destination button text"}, "failedToUpdateCar": "Failed to update car", "@failedToUpdateCar": {"description": "Error message when car update fails"}, "failedToUpdateCargo": "Failed to update cargo", "@failedToUpdateCargo": {"description": "Error message when cargo update fails"}, "failedToUpdateDestination": "Failed to update destination", "@failedToUpdateDestination": {"description": "Error message when destination update fails"}, "modelName": "Model Name", "@modelName": {"description": "Model name field label"}, "onMobileDateRangeLimited": "On mobile, date range is limited to 9 days maximum", "@onMobileDateRangeLimited": {"description": "Mobile date range limitation message"}, "unavailableDemoReason": "Unavailable: Demo reason", "@unavailableDemoReason": {"description": "Demo unavailable reason"}, "updatedDriverAvailability": "Updated {driverName}'s availability for {date}", "@updatedDriverAvailability": {"description": "Success message for availability update", "placeholders": {"driverName": {"type": "String"}, "date": {"type": "String"}}}, "editAvailabilityFor": "Edit Availability for {driverName}", "@editAvailabilityFor": {"description": "Dialog title for editing availability for a specific driver", "placeholders": {"driverName": {"type": "String"}}}, "tripNotes": "Trip Notes:", "@tripNotes": {"description": "Trip notes section header"}, "assignTo": "Assign to:", "@assignTo": {"description": "Assign to label"}, "fetchingTrips": "Fetching trips...", "@fetchingTrips": {"description": "Loading message when fetching trips"}, "noTripsWithStatusRequest": "No trips with status REQUEST found.", "@noTripsWithStatusRequest": {"description": "Message when no trips with REQUEST status are found"}, "noTripsWithStatusDriverRejected": "No trips with status DRIVER REJECTED found.", "@noTripsWithStatusDriverRejected": {"description": "Message when no trips with DRIVER REJECTED status are found"}, "assigningDriver": "Assigning driver...", "@assigningDriver": {"description": "Message shown when assigning a driver to a trip"}, "deletingTrip": "Deleting trip...", "@deletingTrip": {"description": "Progress message shown while deleting trip"}, "deleteAction": "Delete", "@deleteAction": {"description": "Delete action button text"}, "assignAction": "Assign", "@assignAction": {"description": "Assign action button text"}, "selectDriverFirst": "Select a driver first", "@selectDriverFirst": {"description": "Hint text when no driver is selected"}, "selectCar": "Select a car", "@selectCar": {"description": "Hint text for car selection"}, "completedStatus": "COMPLETED", "@completedStatus": {"description": "Completed trip status"}, "completedDuration": "COMPLETED Duration", "@completedDuration": {"description": "Completed trip duration label"}, "tripInProgress": "TRIP IN PROGRESS", "@tripInProgress": {"description": "Trip in progress status"}, "tripInProgressDuration": "TRIP IN PROGRESS Duration", "@tripInProgressDuration": {"description": "Trip in progress duration label"}, "waitingForRating": "WAITING FOR RATING", "@waitingForRating": {"description": "Waiting for rating status"}, "waitingForRatingDuration": "WAITING FOR RATING Duration", "@waitingForRatingDuration": {"description": "Waiting for rating duration label"}, "assignToDriverDuration": "ASSIGN TO DRIVER Duration", "@assignToDriverDuration": {"description": "Assign to driver duration label"}, "foundRequestTrips": "Found {count} request trips:", "@foundRequestTrips": {"description": "Number of request trips found", "placeholders": {"count": {"type": "int"}}}, "noRequestTripsFound": "No trips with status REQUEST found.", "@noRequestTripsFound": {"description": "Message when no request trips are found"}, "foundDriverRejectedTrips": "Found {count} driver rejected trips:", "@foundDriverRejectedTrips": {"description": "Number of driver rejected trips found", "placeholders": {"count": {"type": "int"}}}, "noDriverRejectedTripsFound": "No trips with status DRIVER REJECTED found.", "@noDriverRejectedTripsFound": {"description": "Message when no driver rejected trips are found"}, "availabilityUpdatedFor": "Availability updated for {driverName}", "@availabilityUpdatedFor": {"description": "Success message when availability is updated", "placeholders": {"driverName": {"type": "String"}}}, "sick": "Sick", "@sick": {"description": "Sick status option"}, "breakStatus": "Break", "@breakStatus": {"description": "Break status option"}, "onBreak": "On Break", "@onBreak": {"description": "On break status text"}, "noReason": "No Reason", "@noReason": {"description": "No reason option"}, "noReasonProvided": "No reason provided", "@noReasonProvided": {"description": "Default text when no reason is provided"}, "setStatus": "Set Status", "@setStatus": {"description": "Set status button text"}, "startDate": "Start Date", "@startDate": {"description": "Start date label"}, "endDate": "End Date", "@endDate": {"description": "End date label"}, "thisWeek": "This Week", "@thisWeek": {"description": "This week button text"}, "nextWeek": "Next Week", "@nextWeek": {"description": "Next week button text"}, "apply": "Apply", "@apply": {"description": "Apply button text"}, "scrollDates": "Scroll Dates", "@scrollDates": {"description": "Scroll dates instruction text"}, "selectDateRange": "Select Date Range", "@selectDateRange": {"description": "Date range picker dialog title"}, "editAvailability": "Edit Availability", "@editAvailability": {"description": "Edit availability dialog title"}, "maintenance": "Maintenance", "@maintenance": {"description": "Maintenance status"}, "broken": "Broken", "@broken": {"description": "Broken status"}, "borrowed": "Borrowed", "@borrowed": {"description": "Borrowed status"}, "dateRange": "Date Range", "@dateRange": {"description": "Date range label"}, "statusLegend": "Status Legend", "@statusLegend": {"description": "Status legend title"}, "plate": "Plate", "@plate": {"description": "Plate number prefix"}, "tripLabel": "Trip:", "@tripLabel": {"description": "Trip label with colon"}, "driverLabel": "Driver: ", "@driverLabel": {"description": "Label for driver"}, "notesLabel": "Notes", "@notesLabel": {"description": "Notes label in trip details"}, "dateTime": "Date/Time", "@dateTime": {"description": "Date/Time column header"}, "pageXofY": "Page {current} of {total}", "@pageXofY": {"description": "Pagination text showing current page of total pages", "placeholders": {"current": {"type": "int", "description": "Current page number"}, "total": {"type": "int", "description": "Total number of pages"}}}, "singleCar": "Single Car", "@singleCar": {"description": "Single car view option"}, "allCars": "All Cars", "@allCars": {"description": "All cars view option"}, "selectDate": "Select Date", "@selectDate": {"description": "Date picker placeholder text"}, "searchByDriverTripNotes": "Search by driver, trip, notes...", "@searchByDriverTripNotes": {"description": "Search field hint text"}, "noOdometerHistoryRecordsFound": "No odometer history records found", "@noOdometerHistoryRecordsFound": {"description": "Message when no odometer history records are found"}, "dateColon": "Date:", "@dateColon": {"description": "Date label with colon"}, "startDateColon": "Start Date:", "@startDateColon": {"description": "Start date label with colon"}, "endDateColon": "End Date:", "@endDateColon": {"description": "End date label with colon"}, "driverInformation": "Driver Information", "@driverInformation": {"description": "Driver information section title"}, "checkInInformation": "Check-In Information", "@checkInInformation": {"description": "Check-in information section title"}, "completeCheckIn": "Complete Check-In", "@completeCheckIn": {"description": "Complete check-in button text"}, "loggingOut": "Logging out...", "@loggingOut": {"description": "Logging out progress message"}, "clickButtonCompleteCheckIn": "Click the button below to complete your check-in.", "@clickButtonCompleteCheckIn": {"description": "Check-in instruction text"}, "driverCheckOut": "Driver Check-Out", "@driverCheckOut": {"description": "Driver check-out screen title"}, "confirmCheckOut": "Confirm Check-Out", "@confirmCheckOut": {"description": "Confirm check-out dialog title"}, "completeCheckOut": "Complete Check-Out", "@completeCheckOut": {"description": "Complete check-out button text"}, "checkOutSuccessfulRedirecting": "Check-out successful! Redirecting to check-in...", "@checkOutSuccessfulRedirecting": {"description": "Check-out success message"}, "afterCheckOutRedirectMessage": "After checking out, you will be redirected to the check-in screen. Do you want to proceed?", "@afterCheckOutRedirectMessage": {"description": "Check-out confirmation dialog message"}, "checkOutInformation": "Check-Out Information", "@checkOutInformation": {"description": "Check-out information section title"}, "clickButtonCompleteCheckOut": "Click the button below to complete your check-out.", "@clickButtonCompleteCheckOut": {"description": "Check-out instruction text"}, "filterTrips": "Filter <PERSON>s", "@filterTrips": {"description": "Filter trips section title"}, "searchByTripCode": "Search by trip code, location, requestor, rating, comments, etc.", "@searchByTripCode": {"description": "Search field hint text"}, "clearAllFilters": "Clear All Filters", "@clearAllFilters": {"description": "Clear all filters button text"}, "comments": "Comments", "@comments": {"description": "Comments column header"}, "checkInTime": "Check-In Time", "@checkInTime": {"description": "Check-in time label"}, "driverCodeColon": "Driver Code:", "@driverCodeColon": {"description": "Driver code label with colon"}, "checkInTimeColon": "Check-In Time:", "@checkInTimeColon": {"description": "Check-in time label with colon"}, "proceed": "Proceed", "@proceed": {"description": "Proceed button text"}, "tripColon": "Trip:", "@tripColon": {"description": "Trip label with colon"}, "fromColon": "From:", "@fromColon": {"description": "From label with colon"}, "toColon": "To:", "@toColon": {"description": "To label with colon"}, "driverColon": "Driver:", "@driverColon": {"description": "Driver label with colon"}, "atTime": "at", "@atTime": {"description": "Preposition for time display"}, "view": "View", "@view": {"description": "View button text"}, "viewRejection": "View Rejection", "@viewRejection": {"description": "View rejection reason button text"}, "filterByDateLabel": "Filter by Date", "@filterByDateLabel": {"description": "Date filter field label"}, "searchLabel": "Search", "@searchLabel": {"description": "Search field label"}, "unassigned": "Unassigned", "@unassigned": {"description": "Driver unassigned status"}, "rejectionReasonTitle": "Rejection Reason", "@rejectionReasonTitle": {"description": "Rejection reason dialog title"}, "tripDetailsTitle": "Trip {tripCode} Details", "@tripDetailsTitle": {"description": "Title for trip details dialog", "placeholders": {"tripCode": {"type": "String", "description": "Trip code identifier"}}}, "finishTripConfirmTitle": "Finish Trip Confirmation", "@finishTripConfirmTitle": {"description": "Finish trip confirmation dialog title"}, "finishTripConfirmMessage": "Are you sure you want to mark this trip as finished?", "@finishTripConfirmMessage": {"description": "Finish trip confirmation message"}, "markAsFinished": "<PERSON> as Finished", "@markAsFinished": {"description": "Mark as finished button text"}, "tripRejectedMessage": "This trip was rejected for the following reason:", "@tripRejectedMessage": {"description": "Message shown in rejection reason dialog"}, "tripDetailsColon": "Trip Details:", "@tripDetailsColon": {"description": "Trip details dialog title prefix"}, "fromLabel": "From:", "@fromLabel": {"description": "From destination label in trip card"}, "toLabel": "To:", "@toLabel": {"description": "To destination label in trip card"}, "dateLabel": "Date:", "@dateLabel": {"description": "Date label in trip card"}, "timeLabel": "Time", "@timeLabel": {"description": "Time label in trip details"}, "statusLabel": "Status:", "@statusLabel": {"description": "Label for tracking status"}, "driverIdLabel": "Driver ID", "@driverIdLabel": {"description": "Driver ID label"}, "ratingLabel": "Rating", "@ratingLabel": {"description": "Rating label in trip details"}, "passengersLabel": "Passengers", "@passengersLabel": {"description": "Passengers section label"}, "cargoLabel": "Cargo", "@cargoLabel": {"description": "Cargo section label"}, "noneLabel": "None", "@noneLabel": {"description": "None value label"}, "tripCodeHeader": "Trip Code", "@tripCodeHeader": {"description": "Trip code column header"}, "fromHeader": "From", "@fromHeader": {"description": "From destination column header"}, "toHeader": "To", "@toHeader": {"description": "To destination column header"}, "dateHeader": "Date", "@dateHeader": {"description": "Date column header"}, "timeHeader": "Time", "@timeHeader": {"description": "Time column header"}, "statusHeader": "Status", "@statusHeader": {"description": "Status column header"}, "driverHeader": "Driver", "@driverHeader": {"description": "Driver column header"}, "actionsHeader": "Actions", "@actionsHeader": {"description": "Actions column header"}, "viewRejectionReason": "View Rejection Reason", "@viewRejectionReason": {"description": "View rejection reason button text"}, "tripMarkedAsCompleted": "Trip has been marked as completed", "@tripMarkedAsCompleted": {"description": "Success message when trip is completed"}, "tripDataRefreshedSuccessfully": "Trip data refreshed successfully", "@tripDataRefreshedSuccessfully": {"description": "Success message for trip data refresh"}, "roundTrip": "Round Trip", "@roundTrip": {"description": "Round trip checkbox label"}, "waiting": "Waiting", "@waiting": {"description": "Waiting trip checkbox label"}, "outboundJourney": "Outbound Journey", "@outboundJourney": {"description": "Outbound journey section title"}, "returnJourney": "Return Journey", "@returnJourney": {"description": "Return journey section title"}, "waitingTime": "Waiting Time", "@waitingTime": {"description": "Waiting time section title"}, "returnDate": "Return Date", "@returnDate": {"description": "Return date field label"}, "returnTime": "Return Time", "@returnTime": {"description": "Return time field label"}, "submitTrip": "Submit Trip", "@submitTrip": {"description": "Submit trip button text"}, "customFrom": "Custom From", "@customFrom": {"description": "Custom from destination field label"}, "customTo": "Custom To", "@customTo": {"description": "Custom to destination field label"}, "others": "Others", "@others": {"description": "Others option in destination dropdown"}, "returnDateCannotBeEarlier": "Return date cannot be earlier than outbound date", "@returnDateCannotBeEarlier": {"description": "Validation message for return date"}, "outboundDateCannotBeLater": "Outbound date cannot be later than return date", "@outboundDateCannotBeLater": {"description": "Validation message for outbound date"}, "returnDateAutomaticallyAdjusted": "Return date was automatically adjusted to match outbound date", "@returnDateAutomaticallyAdjusted": {"description": "Info message when return date is adjusted"}, "returnTimeAutomaticallyAdjusted": "Return time was automatically adjusted to be after outbound time", "@returnTimeAutomaticallyAdjusted": {"description": "Info message when return time is adjusted"}, "returnDateTimeAutomaticallyAdjusted": "Return date and time were automatically adjusted to be after outbound date and time", "@returnDateTimeAutomaticallyAdjusted": {"description": "Info message when both return date and time are adjusted"}, "returnTimeMustBeAfterOutbound": "Return time must be after outbound time on the same day", "@returnTimeMustBeAfterOutbound": {"description": "Validation message for return time"}, "outboundTimeMustBeBeforeReturn": "Outbound time must be before return time on the same day", "@outboundTimeMustBeBeforeReturn": {"description": "Validation message for outbound time"}, "outboundDateTimeMustBeBeforeReturn": "Outbound date and time must be before return date and time", "@outboundDateTimeMustBeBeforeReturn": {"description": "Validation message for outbound datetime"}, "returnDateTimeMustBeAfterOutbound": "Return date and time must be after outbound date and time", "@returnDateTimeMustBeAfterOutbound": {"description": "Validation message for return datetime"}, "returnTimeMustBeAfterOutboundWarning": "Warning: Return date and time must be after outbound date and time. Please adjust your selection.", "@returnTimeMustBeAfterOutboundWarning": {"description": "Warning message for invalid return datetime"}, "outboundTimeMustBeBeforeReturnOnSameDay": "Outbound time must be before return time on the same day", "@outboundTimeMustBeBeforeReturnOnSameDay": {"description": "Validation message for outbound time on same day"}, "returnFromAndToCannotBeSame": "Return From and To destinations cannot be the same", "@returnFromAndToCannotBeSame": {"description": "Validation message for return destinations"}, "returnFromAndToCustomCannotBeSame": "Return From and To custom destinations cannot be the same", "@returnFromAndToCustomCannotBeSame": {"description": "Validation message for return custom destinations"}, "pleaseEnterCustomReturnTo": "Please enter custom return to destination", "@pleaseEnterCustomReturnTo": {"description": "Validation message for custom return to destination"}, "pleaseSelectOutboundDateTime": "Please select outbound date and time", "@pleaseSelectOutboundDateTime": {"description": "Validation message for outbound date and time"}, "pleaseSelectReturnDateTime": "Please select return date and time", "@pleaseSelectReturnDateTime": {"description": "Validation message for return date and time"}, "pleaseSelectDateTime": "Please select date and time", "@pleaseSelectDateTime": {"description": "Validation message for date and time"}, "invalidDateTimeFormat": "Invalid date or time format", "@invalidDateTimeFormat": {"description": "Validation message for invalid date/time format"}, "pleaseEnterCustomFrom": "Please enter custom from", "@pleaseEnterCustomFrom": {"description": "Validation message for custom from destination"}, "pleaseEnterCustomTo": "Please enter custom to", "@pleaseEnterCustomTo": {"description": "Validation message for custom to destination"}, "enterNotesOptional": "Enter any notes about the trip (optional)", "@enterNotesOptional": {"description": "Hint text for notes field"}, "photo": "Photo:", "@photo": {"description": "Photo section label"}, "pleaseProvideRejectionReason": "Please provide a reason for rejecting this trip:", "@pleaseProvideRejectionReason": {"description": "Text asking user to provide rejection reason"}, "takePhoto": "Take Photo", "@takePhoto": {"description": "Take photo button text"}, "addNotesPhotoOptional": "Add notes and/or photo (optional):", "@addNotesPhotoOptional": {"description": "Instructions for adding notes and photo"}, "shareYourExperienceOptional": "Share your experience (optional)", "@shareYourExperienceOptional": {"description": "Hint text for rating comment field"}, "confirmTripCompletion": "Confirm Trip Completion", "@confirmTripCompletion": {"description": "Confirm trip completion dialog title"}, "areYouSureMarkTripCompleted": "Are you sure you want to mark this trip as completed?", "@areYouSureMarkTripCompleted": {"description": "Confirm trip completion message"}, "timeColon": "Time:", "@timeColon": {"description": "Time label with colon"}, "notesColon": "Notes:", "@notesColon": {"description": "Notes label with colon"}, "tripRequestsByStatus": "Trip Requests by Status", "@tripRequestsByStatus": {"description": "Dashboard section title for trip status overview"}, "newTripRequests": "New trip requests", "@newTripRequests": {"description": "Description for REQUEST status"}, "tripEditRequestsPendingApproval": "Trip edit requests pending approval", "@tripEditRequestsPendingApproval": {"description": "Description for EDIT ON REVIEW status"}, "tripCancellationRequestsPendingApproval": "Trip cancellation requests pending approval", "@tripCancellationRequestsPendingApproval": {"description": "Description for CANCEL ON REVIEW status"}, "tripsReadyToBeAssignedToDrivers": "Trips ready to be assigned to drivers", "@tripsReadyToBeAssignedToDrivers": {"description": "Description for ASSIGN TO DRIVER status"}, "tripsAssignedToOnlineTaxi": "Trips assigned to online taxi", "@tripsAssignedToOnlineTaxi": {"description": "Description for ASSIGN TO ONLINE TAXI status"}, "tripsRejectedByDrivers": "Trips rejected by drivers", "@tripsRejectedByDrivers": {"description": "Description for DRIVER REJECTED status"}, "tripsConfirmedByDrivers": "Trips confirmed by drivers", "@tripsConfirmedByDrivers": {"description": "Description for DRIVER CONFIRMATION status"}, "tripsCurrentlyInProgress": "Trips currently in progress", "@tripsCurrentlyInProgress": {"description": "Description for TRIP IN PROGRESS status"}, "completedTripsWaitingForRating": "Completed trips waiting for rating", "@completedTripsWaitingForRating": {"description": "Description for WAITING FOR RATING status"}, "assignDriverStatusExplanation": "Assigning a driver will change the trip status to \"ASSIGN TO DRIVER\". Selecting \"ONLINE\" will change the status to \"ASSIGN TO ONLINE TAXI\".", "@assignDriverStatusExplanation": {"description": "Explanation shown in assign driver dialog about status changes"}, "tripDateLabel": "Trip Date: {date} ({formattedDate})", "@tripDateLabel": {"description": "Label showing trip date information", "placeholders": {"date": {"type": "String", "description": "Original trip date"}, "formattedDate": {"type": "String", "description": "Formatted API date"}}}, "lookingUpTripId": "Looking up trip ID for {tripCode}...", "@lookingUpTripId": {"description": "Message shown while looking up trip ID", "placeholders": {"tripCode": {"type": "String", "description": "Trip code being looked up"}}}, "cannotAssignDriverMissingId": "Cannot assign driver: Trip ID is missing for trip code {tripCode}", "@cannotAssignDriverMissingId": {"description": "Error message when trip ID is missing", "placeholders": {"tripCode": {"type": "String", "description": "Trip code with missing ID"}}}, "currentDateTime": "Current DateTime: {dateTime}", "@currentDateTime": {"description": "Label showing current date and time", "placeholders": {"dateTime": {"type": "String", "description": "Current date and time string"}}}, "deleteTripWarning": "This will change the trip status to DELETED. This action cannot be undone.\n\nAre you sure you want to continue?", "@deleteTripWarning": {"description": "Warning message shown before deleting a trip"}, "errorDeletingTrip": "Error deleting trip: {error}", "@errorDeletingTrip": {"description": "Error message when trip deletion fails", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "statusDriverRejected": "Status: DRIVER REJECTED", "@statusDriverRejected": {"description": "Label showing driver rejected status"}, "tripRejectedWithReason": "This trip was rejected by the driver for the following reason:", "@tripRejectedWithReason": {"description": "Message shown when trip was rejected with a reason"}, "tripRejectedNoReason": "This trip was rejected by the driver, but no reason was provided.", "@tripRejectedNoReason": {"description": "Message shown when trip was rejected without a reason"}, "tripCodeLabel": "Trip Code: {tripCode}", "@tripCodeLabel": {"description": "Label showing trip code", "placeholders": {"tripCode": {"type": "String", "description": "Trip code identifier"}}}, "tripDurationLegend": "Trip Duration", "@tripDurationLegend": {"description": "Legend label for trip duration in schedule table"}, "noTripRequestsForDate": "No trip requests for this date", "@noTripRequestsForDate": {"description": "Message shown when no trip requests exist for selected date"}, "selectedDateLabel": "Selected date: {date}", "@selectedDateLabel": {"description": "Label showing currently selected date", "placeholders": {"date": {"type": "String", "description": "Formatted selected date"}}}, "timePeriodsTitle": "Time Periods:", "@timePeriodsTitle": {"description": "Title for time periods legend"}, "nightPeriod": "Night (00:00-06:00)", "@nightPeriod": {"description": "Label for night time period"}, "morningPeriod": "Morning (06:00-12:00)", "@morningPeriod": {"description": "Label for morning time period"}, "afternoonPeriod": "Afternoon (12:00-18:00)", "@afternoonPeriod": {"description": "Label for afternoon time period"}, "eveningPeriod": "Evening (18:00-24:00)", "@eveningPeriod": {"description": "Label for evening time period"}, "noCarsAvailableMessage": "No cars are available for this date. All cars may be marked as UNAVAILABLE.", "@noCarsAvailableMessage": {"description": "Message when no cars are available for a date"}, "noDriversAvailableMessage": "No drivers are available for this date. All drivers may be marked as UNAVAILABLE.", "@noDriversAvailableMessage": {"description": "Message when no drivers are available for a date"}, "driverRejectedTripsTitle": "DRIVER REJECTED Trips", "@driverRejectedTripsTitle": {"description": "Title for driver rejected trips section"}, "navigateTimeSlots": "Navigate time slots:", "@navigateTimeSlots": {"description": "Label for time slot navigation"}, "legend": "Legend:", "@legend": {"description": "Legend label"}, "assignToDriverWithDriver": "ASSIGN TO DRIVER (with driver)", "@assignToDriverWithDriver": {"description": "Legend label for trips assigned to driver with driver info"}, "assignToDriverWithoutDriver": "ASSIGN TO DRIVER (without driver)", "@assignToDriverWithoutDriver": {"description": "Legend label for trips assigned to driver without driver info"}, "assignToDriverWithoutDriverDuration": "ASSIGN TO DRIVER (without driver) Duration", "@assignToDriverWithoutDriverDuration": {"description": "Legend label for trip duration without driver info"}, "refreshTripRequestsTooltip": "Refresh trip requests", "@refreshTripRequestsTooltip": {"description": "Tooltip for refresh trip requests button"}, "refreshDriverRejectedTripsTooltip": "Refresh driver rejected trips", "@refreshDriverRejectedTripsTooltip": {"description": "Tooltip for refresh driver rejected trips button"}, "driverRejectedTripsForDate": "Driver Rejected Trips for {date}", "@driverRejectedTripsForDate": {"description": "Title for driver rejected trips section with date", "placeholders": {"date": {"type": "String", "description": "The formatted date"}}}, "noDriverRejectedTripsForDate": "No driver rejected trips for this date", "@noDriverRejectedTripsForDate": {"description": "Message when no driver rejected trips are found for a specific date"}, "errorFetchingOnlineTaxiTrips": "Error fetching online taxi trips list: {error}", "@errorFetchingOnlineTaxiTrips": {"description": "Error message when fetching online taxi trips fails", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "errorFetchingTrips": "Error fetching trips: {error}", "@errorFetchingTrips": {"description": "Error message when fetching trips fails", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "errorFetchingDriverRejectedTrips": "Error fetching driver rejected trips: {error}", "@errorFetchingDriverRejectedTrips": {"description": "Error message when fetching driver rejected trips fails", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "errorFetchingTripsWithStatus": "Error fetching trips: {status1} / {status2}", "@errorFetchingTripsWithStatus": {"description": "Error message when fetching trips fails with status codes", "placeholders": {"status1": {"type": "String", "description": "First status code"}, "status2": {"type": "String", "description": "Second status code"}}}, "errorFetchingTripsWithStatusCodes": "Error fetching trips: {statusCodes}", "@errorFetchingTripsWithStatusCodes": {"description": "Error message when fetching trips fails with status codes", "placeholders": {"statusCodes": {"type": "String", "description": "Status codes from the response"}}}, "tripStatusChangedToOnlineTaxi": "Trip {tripCode} status changed to ASSIGN TO ONLINE TAXI.", "@tripStatusChangedToOnlineTaxi": {"description": "Message shown when a trip status is changed to online taxi", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}}}, "errorAssigningDriver": "Error assigning driver: {error}", "@errorAssigningDriver": {"description": "Error message when assigning a driver fails", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "tripHasBeenDeleted": "Trip {tripCode} has been deleted.", "@tripHasBeenDeleted": {"description": "Message shown when a trip has been deleted", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}}}, "tripRequestsForDate": "Trip Requests for {date}", "@tripRequestsForDate": {"description": "Header for trip requests section showing specific date", "placeholders": {"date": {"type": "String", "description": "Formatted date"}}}, "requestCount": "{count} {count, plural, =1{request} other{requests}}", "@requestCount": {"description": "Count of trip requests with proper singular/plural form", "placeholders": {"count": {"type": "int", "description": "Number of requests"}}}, "tripCount": "{count} {count, plural, =1{trip} other{trips}}", "@tripCount": {"description": "Count of trips with proper singular/plural form", "placeholders": {"count": {"type": "int", "description": "Number of trips"}}}, "tripsCount": "{count} {count, plural, =1{<PERSON>} other{Trips}}", "@tripsCount": {"description": "Count of trips with proper singular/plural form", "placeholders": {"count": {"type": "int", "description": "Number of trips"}}}, "onlineLabel": "Online: ", "@onlineLabel": {"description": "Label for online driver"}, "durationLabel": "Duration:", "@durationLabel": {"description": "Duration label in trip card"}, "taxiText": "TAXI", "@taxiText": {"description": "Text displayed for online taxi"}, "noCarAssigned": "No Car Assigned", "@noCarAssigned": {"description": "Text displayed when no car is assigned"}, "statusDetailLabel": "Status", "@statusDetailLabel": {"description": "Status label in trip details"}, "startTimeLabel": "Start Time:", "@startTimeLabel": {"description": "Start time label in trip card"}, "endTimeLabel": "End Time:", "@endTimeLabel": {"description": "End time label in trip card"}, "durationDetailLabel": "Duration", "@durationDetailLabel": {"description": "Duration label in trip details"}, "driverDetailLabel": "Driver", "@driverDetailLabel": {"description": "Driver label in trip details"}, "driverCodeDetailLabel": "Driver Code", "@driverCodeDetailLabel": {"description": "Driver code label in trip details"}, "fromDetailLabel": "From", "@fromDetailLabel": {"description": "From destination label in trip details"}, "toDetailLabel": "To", "@toDetailLabel": {"description": "To destination label in trip details"}, "additionalInformation": "Additional Information", "@additionalInformation": {"description": "Section title for additional information"}, "tripIdLabel": "Trip ID", "@tripIdLabel": {"description": "Trip ID label in trip details"}, "notAssigned": "Not assigned", "@notAssigned": {"description": "Text displayed when trip ID is not assigned"}, "carInformation": "Car Information", "@carInformation": {"description": "Section title for car information"}, "carIdLabel": "Car ID", "@carIdLabel": {"description": "Car ID label in trip details"}, "carCodeDetailLabel": "Car Code", "@carCodeDetailLabel": {"description": "Car code label in trip details"}, "plateNumberLabel": "Plate Number", "@plateNumberLabel": {"description": "Plate number label in trip details"}, "passengersSection": "Passengers:", "@passengersSection": {"description": "Passengers section title with colon"}, "cargoSection": "Cargo:", "@cargoSection": {"description": "Cargo section title with colon"}, "returnToRequest": "Return to REQUEST", "@returnToRequest": {"description": "Button text to return trip to REQUEST status"}, "deleteTrip": "Delete", "@deleteTrip": {"description": "Delete trip button text"}, "deleteTripTitle": "Delete Trip {tripCode}?", "@deleteTripTitle": {"description": "Delete trip confirmation dialog title", "placeholders": {"tripCode": {"type": "String", "description": "Trip code identifier"}}}, "deleteTripWarningMessage": "This will change the trip status to DELETED. This action cannot be undone.\\n\\nAre you sure you want to continue?", "@deleteTripWarningMessage": {"description": "Delete trip confirmation dialog content"}, "minutesUnit": "minutes", "@minutesUnit": {"description": "Minutes time unit"}, "onlineTaxiText": "ONLINE TAXI", "@onlineTaxiText": {"description": "Text displayed for online taxi service"}, "noCar": "No Car", "@noCar": {"description": "Text displayed when no car is assigned"}, "noPlate": "No Plate", "@noPlate": {"description": "Text displayed when no plate number is available"}, "updatingTripDuration": "Updating trip duration...", "@updatingTripDuration": {"description": "Message shown while updating trip duration"}, "errorUpdatingDuration": "Error updating duration: {error}", "@errorUpdatingDuration": {"description": "Error message when updating duration fails", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "updatingTripStatus": "Updating trip status...", "@updatingTripStatus": {"description": "Message shown while updating trip status"}, "tripStatusChangedToRequest": "Trip {tripCode} status changed to REQUEST.", "@tripStatusChangedToRequest": {"description": "Message shown when trip status changed to REQUEST", "placeholders": {"tripCode": {"type": "String", "description": "Trip code identifier"}}}, "refreshingAllData": "Refreshing all data...", "@refreshingAllData": {"description": "Message shown while refreshing data"}, "errorGeneric": "Error: {error}", "@errorGeneric": {"description": "Generic error message", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "deletingTripMessage": "Deleting trip...", "@deletingTripMessage": {"description": "Message shown while deleting trip"}, "errorDeletingTripMessage": "Error deleting trip: {error}", "@errorDeletingTripMessage": {"description": "Error message when deleting trip fails", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "errorDriverCodeMissing": "Error: Driver code is missing. Please try again.", "@errorDriverCodeMissing": {"description": "Error message when driver code is missing"}, "backToBase": "Back to Base", "@backToBase": {"description": "Back to Base button label"}, "checkOut": "Check Out", "@checkOut": {"description": "Check Out button label"}, "assignToDriverCount": "Assign To Driver: {count}", "@assignToDriverCount": {"description": "Count of trips assigned to driver", "placeholders": {"count": {"type": "int", "description": "Number of trips assigned to driver"}}}, "selectStatus": "Select Status", "@selectStatus": {"description": "Status dropdown placeholder text"}, "tripCodeNumber": "Trip #{tripCode}", "@tripCodeNumber": {"description": "Trip number display format", "placeholders": {"tripCode": {"type": "String", "description": "Trip code"}}}, "durationInMinutes": "{minutes} minutes", "@durationInMinutes": {"description": "Duration in minutes format", "placeholders": {"minutes": {"type": "int", "description": "Duration in minutes"}}}, "requestorLabel": "Requestor:", "@requestorLabel": {"description": "Requestor label in trip card"}, "rejectionLabel": "Rejection:", "@rejectionLabel": {"description": "Rejection reason label in trip card"}, "locationTrackingActive": "Location tracking is active (driver has active trips)", "@locationTrackingActive": {"description": "Tooltip for active location tracking"}, "trackingReady": "Tracking ready (driver has active trips)", "@trackingReady": {"description": "Tooltip for tracking ready state"}, "trackingDisabledNoActiveTrips": "Tracking disabled (no active trips)", "@trackingDisabledNoActiveTrips": {"description": "Tooltip when tracking is disabled due to no active trips"}, "automaticTrackingDisabled": "Automatic tracking is disabled", "@automaticTrackingDisabled": {"description": "Tooltip when automatic tracking is disabled"}, "failedToCreateBackToBaseTrip": "Failed to create Back to Base trip: {statusCode}", "@failedToCreateBackToBaseTrip": {"description": "Error message when Back to Base trip creation fails", "placeholders": {"statusCode": {"type": "int", "description": "HTTP status code"}}}, "errorCreatingBackToBaseTrip": "Error creating Back to Base trip: {error}", "@errorCreatingBackToBaseTrip": {"description": "Error message when Back to Base trip creation throws an exception", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "locationTrackingStatus": "Location Tracking Status", "@locationTrackingStatus": {"description": "Title of the location tracking status dialog"}, "autoTrackingLabel": "Auto Tracking:", "@autoTrackingLabel": {"description": "Label for auto tracking status"}, "enabled": "ENABLED", "@enabled": {"description": "Enabled status value"}, "disabled": "DISABLED", "@disabled": {"description": "Disabled status value"}, "activeTripsLabel": "Active Trips:", "@activeTripsLabel": {"description": "Label for active trips status"}, "currentTimeLabel": "Current Time:", "@currentTimeLabel": {"description": "Label for current time display"}, "todayLabel": "Today:", "@todayLabel": {"description": "Label for today's date display"}, "locationLabel": "Location:", "@locationLabel": {"description": "Label for location coordinates"}, "lastUpdateLabel": "Last Update:", "@lastUpdateLabel": {"description": "Label for last update time"}, "secondsAgo": "{seconds} seconds ago", "@secondsAgo": {"description": "Time format for seconds ago", "placeholders": {"seconds": {"type": "int", "description": "Number of seconds"}}}, "minutesAgo": "{minutes} minutes ago", "@minutesAgo": {"description": "Time format for minutes ago", "placeholders": {"minutes": {"type": "int", "description": "Number of minutes"}}}, "trackingExplanation": "Your location is tracked only when you have active trips (TRIP IN PROGRESS or BACK TO BASE):\n• Tracking is automatically enabled when you have active trips\n• Tracking is automatically disabled when you have no active trips\n• Location updates are sent every minute when tracking is active", "@trackingExplanation": {"description": "Explanation text about how location tracking works"}, "tripCodeColumn": "Trip Code", "@tripCodeColumn": {"description": "Trip code column header"}, "requestorColumn": "Requestor", "@requestorColumn": {"description": "Requestor column header"}, "fromColumn": "From", "@fromColumn": {"description": "From destination column header"}, "toColumn": "To", "@toColumn": {"description": "To destination column header"}, "dateColumn": "Date", "@dateColumn": {"description": "Date column header"}, "timeColumn": "Time", "@timeColumn": {"description": "Time column header"}, "driverColumn": "Driver", "@driverColumn": {"description": "Driver column header"}, "statusColumn": "Status", "@statusColumn": {"description": "Status column header"}, "ratingColumn": "Rating", "@ratingColumn": {"description": "Rating column header"}, "pageOf": "Page {current} of {total}", "@pageOf": {"description": "Page indicator in pagination", "placeholders": {"current": {"type": "int", "description": "Current page number"}, "total": {"type": "int", "description": "Total number of pages"}}}, "itemsText": "items", "@itemsText": {"description": "Text for items in items per page dropdown"}, "tripAssignedToOnlineTaxiTitle": "Trip Assigned to Online Taxi", "@tripAssignedToOnlineTaxiTitle": {"description": "Title for notification when trip is assigned to online taxi"}, "tripAssignedToOnlineTaxiMessage": "Your trip {tripCode} from {fromDestination} to {toDestination} has been assigned to an online taxi service.", "@tripAssignedToOnlineTaxiMessage": {"description": "Message for notification when trip is assigned to online taxi", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}, "fromDestination": {"type": "String", "description": "The departure destination"}, "toDestination": {"type": "String", "description": "The arrival destination"}}}, "tripAssignedToOnlineTaxiSuccess": "Trip {tripCode} assigned to online taxi and notification sent.", "@tripAssignedToOnlineTaxiSuccess": {"description": "Success message when trip is assigned to online taxi and notification is sent", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}}}, "notificationTripAssigned": "Trip Assigned: {tripCode}", "@notificationTripAssigned": {"description": "Notification title when a trip is assigned", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}}}, "notificationNewTripAssignment": "New Trip Assignment: {tripCode}", "@notificationNewTripAssignment": {"description": "Notification title for driver when assigned to a trip", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}}}, "notificationTripApproved": "Trip Approved: {tripCode}", "@notificationTripApproved": {"description": "Notification title when a trip is approved", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}}}, "notificationTripRejected": "<PERSON> Rejected: {tripCode}", "@notificationTripRejected": {"description": "Notification title when a trip is rejected", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}}}, "notificationTripCompleted": "Trip Completed: {tripCode}", "@notificationTripCompleted": {"description": "Notification title when a trip is completed", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}}}, "notificationTripStatusUpdate": "Trip Status Update: {tripCode}", "@notificationTripStatusUpdate": {"description": "Notification title when trip status changes", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}}}, "notificationTripRated": "Trip Rated: {tripCode}", "@notificationTripRated": {"description": "Notification title when a trip is rated", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}}}, "notificationTripReturnedToRequest": "Trip Returned to Request: {tripCode}", "@notificationTripReturnedToRequest": {"description": "Notification title when trip is returned to request status", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}}}, "notificationTripDeleted": "Trip Deleted: {tripCode}", "@notificationTripDeleted": {"description": "Notification title when a trip is deleted", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}}}, "notificationBackToBaseTripCompleted": "Back to Base Trip Completed: {tripCode}", "@notificationBackToBaseTripCompleted": {"description": "Notification title when back to base trip is completed", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}}}, "notificationTripAssignedToOnlineTaxi": "Trip Assigned to Online Taxi: {tripCode}", "@notificationTripAssignedToOnlineTaxi": {"description": "Notification title when trip is assigned to online taxi", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}}}, "notificationOnlineTaxiTripCompleted": "Online Taxi Trip Completed: {tripCode}", "@notificationOnlineTaxiTripCompleted": {"description": "Notification title when online taxi trip is completed", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}}}, "notificationBackToBaseTripCreated": "Back to Base Trip Created: {tripCode}", "@notificationBackToBaseTripCreated": {"description": "Notification title when back to base trip is created", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}}}, "notificationTripChangesApproved": "Trip Changes Approved: {tripCode}", "@notificationTripChangesApproved": {"description": "Notification title when trip edit changes are approved", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}}}, "notificationNewTripRequest": "New Trip Request: {tripCode}", "@notificationNewTripRequest": {"placeholders": {"tripCode": {"type": "String"}}}, "notificationTripEditRequest": "Trip Edit Request: {tripCode}", "@notificationTripEditRequest": {"description": "Notification title when a trip edit is requested", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}}}, "notificationTripCancellationRequest": "Trip Cancellation Request: {tripCode}", "@notificationTripCancellationRequest": {"description": "Notification title when a trip cancellation is requested", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}}}, "notificationMessageTripAssignedToDriver": "Your trip {tripCode} has been assigned to a driver and is scheduled for {date} at {time}.", "@notificationMessageTripAssignedToDriver": {"description": "Notification message when trip is assigned to driver for requestor", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}, "date": {"type": "String", "description": "The trip date"}, "time": {"type": "String", "description": "The trip time"}}}, "notificationMessageDriverAssignedToTrip": "You have been assigned to trip {tripCode} from {fromDestination} to {toDestination} on {date} at {time}.", "@notificationMessageDriverAssignedToTrip": {"description": "Notification message when driver is assigned to trip", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}, "fromDestination": {"type": "String", "description": "The departure destination"}, "toDestination": {"type": "String", "description": "The arrival destination"}, "date": {"type": "String", "description": "The trip date"}, "time": {"type": "String", "description": "The trip time"}}}, "notificationMessageTripApproved": "Your trip {tripCode} from {fromDestination} to {toDestination} has been approved.", "@notificationMessageTripApproved": {"description": "Notification message when trip is approved", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}, "fromDestination": {"type": "String", "description": "The departure destination"}, "toDestination": {"type": "String", "description": "The arrival destination"}}}, "notificationMessageTripRejected": "Your trip {tripCode} has been rejected. Reason: {rejectionReason}.", "@notificationMessageTripRejected": {"description": "Notification message when trip is rejected", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}, "rejectionReason": {"type": "String", "description": "The rejection reason"}}}, "notificationMessageTripRejectedNoReason": "Your trip {tripCode} has been rejected. Reason: No reason provided.", "@notificationMessageTripRejectedNoReason": {"description": "Notification message when trip is rejected without reason", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}}}, "notificationMessageTripStatusChanged": "Your trip {tripCode} status has changed from {oldStatus} to {newStatus}.", "@notificationMessageTripStatusChanged": {"description": "Notification message when trip status changes for requestor", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}, "oldStatus": {"type": "String", "description": "The old status"}, "newStatus": {"type": "String", "description": "The new status"}}}, "notificationMessageDriverTripStatusChanged": "Trip {tripCode} status has changed from {oldStatus} to {newStatus}.", "@notificationMessageDriverTripStatusChanged": {"description": "Notification message when trip status changes for driver", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}, "oldStatus": {"type": "String", "description": "The old status"}, "newStatus": {"type": "String", "description": "The new status"}}}, "notificationMessageTripRated": "Your trip {tripCode} has been rated {rating} stars by the passenger. {ratingMessage}", "@notificationMessageTripRated": {"description": "Notification message when trip is rated", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}, "rating": {"type": "String", "description": "The rating stars"}, "ratingMessage": {"type": "String", "description": "The rating message"}}}, "notificationMessageTripRatedNoMessage": "Your trip {tripCode} has been rated {rating} stars by the passenger.", "@notificationMessageTripRatedNoMessage": {"description": "Notification message when trip is rated without message", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}, "rating": {"type": "String", "description": "The rating stars"}}}, "notificationMessageTripReturnedToRequestor": "Your trip {tripCode} from {fromDestination} to {toDestination} has been returned to REQUEST status by a trip manager.", "@notificationMessageTripReturnedToRequestor": {"description": "Notification message when trip is returned to request for requestor", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}, "fromDestination": {"type": "String", "description": "The departure destination"}, "toDestination": {"type": "String", "description": "The arrival destination"}}}, "notificationMessageTripReturnedToDriver": "Trip {tripCode} from {fromDestination} to {toDestination} has been returned to REQUEST status by a trip manager.", "@notificationMessageTripReturnedToDriver": {"description": "Notification message when trip is returned to request for driver", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}, "fromDestination": {"type": "String", "description": "The departure destination"}, "toDestination": {"type": "String", "description": "The arrival destination"}}}, "notificationMessageTripDeletedRequestor": "Your trip {tripCode} from {fromDestination} to {toDestination} has been deleted by a trip manager.", "@notificationMessageTripDeletedRequestor": {"description": "Notification message when trip is deleted for requestor", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}, "fromDestination": {"type": "String", "description": "The departure destination"}, "toDestination": {"type": "String", "description": "The arrival destination"}}}, "notificationMessageTripDeletedDriver": "Trip {tripCode} from {fromDestination} to {toDestination} has been deleted by a trip manager.", "@notificationMessageTripDeletedDriver": {"description": "Notification message when trip is deleted for driver", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}, "fromDestination": {"type": "String", "description": "The departure destination"}, "toDestination": {"type": "String", "description": "The arrival destination"}}}, "notificationMessageTripCompletedRequestor": "Your trip {tripCode} has been completed successfully. Thank you for using our service!", "@notificationMessageTripCompletedRequestor": {"description": "Notification message when trip is completed for requestor", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}}}, "notificationMessageTripCompletedManager": "Trip {tripCode} has been marked as completed by the driver.", "@notificationMessageTripCompletedManager": {"description": "Notification message when trip is completed for managers", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}}}, "notificationMessageBackToBaseTripCompletedRequestor": "Your back to base trip {tripCode} has been completed successfully.", "@notificationMessageBackToBaseTripCompletedRequestor": {"description": "Notification message when back to base trip is completed for requestor", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}}}, "notificationMessageBackToBaseTripCompletedManager": "Back to base trip {tripCode} has been completed.", "@notificationMessageBackToBaseTripCompletedManager": {"description": "Notification message when back to base trip is completed for managers", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}}}, "notificationMessageTripAssignedToOnlineTaxi": "Your trip {tripCode} from {fromDestination} to {toDestination} has been assigned to an online taxi service.", "@notificationMessageTripAssignedToOnlineTaxi": {"description": "Notification message when trip is assigned to online taxi", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}, "fromDestination": {"type": "String", "description": "The departure destination"}, "toDestination": {"type": "String", "description": "The arrival destination"}}}, "notificationMessageOnlineTaxiTripCompletedRequestor": "Your online taxi trip {tripCode} has been successfully completed. Thank you for using our service!", "@notificationMessageOnlineTaxiTripCompletedRequestor": {"description": "Notification message when online taxi trip is completed for requestor", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}}}, "notificationMessageOnlineTaxiTripCompletedManager": "Online taxi trip {tripCode} has been marked as completed by the requestor.", "@notificationMessageOnlineTaxiTripCompletedManager": {"description": "Notification message when online taxi trip is completed for managers", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}}}, "notificationMessageBackToBaseTripCreated": "{driver<PERSON><PERSON>} has created a \"Back to Base\" trip {tripCode} from {fromDestination} to {toDestination} on {date} at {time}.", "@notificationMessageBackToBaseTripCreated": {"description": "Notification message when back to base trip is created", "placeholders": {"driverName": {"type": "String", "description": "The driver name"}, "tripCode": {"type": "String", "description": "The trip code"}, "fromDestination": {"type": "String", "description": "The departure destination"}, "toDestination": {"type": "String", "description": "The arrival destination"}, "date": {"type": "String", "description": "The trip date"}, "time": {"type": "String", "description": "The trip time"}}}, "notificationMessageTripChangesApproved": "Your changes to trip {tripCode} from {fromDestination} to {toDestination} have been approved. The trip is now back to REQUEST status.", "@notificationMessageTripChangesApproved": {"description": "Notification message when trip edit changes are approved", "placeholders": {"tripCode": {"type": "String", "description": "The trip code"}, "fromDestination": {"type": "String", "description": "The departure destination"}, "toDestination": {"type": "String", "description": "The arrival destination"}}}, "notificationMessageNewTripRequest": "{requestor<PERSON><PERSON>} has requested a new trip {tripCode} from {fromDestination} to {toDestination} on {date} at {time}.", "@notificationMessageNewTripRequest": {"description": "Notification message for new trip request to managers", "placeholders": {"requestorName": {"type": "String", "description": "The requestor name"}, "tripCode": {"type": "String", "description": "The trip code"}, "fromDestination": {"type": "String", "description": "The departure destination"}, "toDestination": {"type": "String", "description": "The arrival destination"}, "date": {"type": "String", "description": "The trip date"}, "time": {"type": "String", "description": "The trip time"}}}, "notificationMessageTripEditRequest": "{userN<PERSON>} has requested changes to trip {tripCode} from {fromDestination} to {toDestination} on {date} at {time}.", "@notificationMessageTripEditRequest": {"description": "Notification message when a trip edit is requested", "placeholders": {"userName": {"type": "String", "description": "The user who requested the edit"}, "tripCode": {"type": "String", "description": "The trip code"}, "fromDestination": {"type": "String", "description": "The trip origin"}, "toDestination": {"type": "String", "description": "The trip destination"}, "date": {"type": "String", "description": "The trip date"}, "time": {"type": "String", "description": "The trip time"}}}, "notificationMessageTripCancellationRequest": "{user<PERSON><PERSON>} has requested to cancel trip {tripCode} from {fromDestination} to {toDestination} on {date} at {time}.", "@notificationMessageTripCancellationRequest": {"description": "Notification message when a trip cancellation is requested", "placeholders": {"userName": {"type": "String", "description": "The user who requested the cancellation"}, "tripCode": {"type": "String", "description": "The trip code"}, "fromDestination": {"type": "String", "description": "The trip origin"}, "toDestination": {"type": "String", "description": "The trip destination"}, "date": {"type": "String", "description": "The trip date"}, "time": {"type": "String", "description": "The trip time"}}}, "notificationTripStarted": "Trip Started: {tripCode}", "@notificationTripStarted": {"placeholders": {"tripCode": {"type": "String"}}}, "notificationDriverConfirmedTrip": "Driver Confirmed Trip: {tripCode}", "@notificationDriverConfirmedTrip": {"placeholders": {"tripCode": {"type": "String"}}}, "notificationMessageTripStarted": "Trip {tripCode} has been started by the driver.", "@notificationMessageTripStarted": {"placeholders": {"tripCode": {"type": "String"}}}, "notificationMessageDriverConfirmedTrip": "{driver<PERSON><PERSON>} has confirmed trip {tripCode}.", "@notificationMessageDriverConfirmedTrip": {"placeholders": {"driverName": {"type": "String"}, "tripCode": {"type": "String"}}}, "notificationDriverRejectedTrip": "Driver Rejected Trip: {tripCode}", "@notificationDriverRejectedTrip": {"placeholders": {"tripCode": {"type": "String"}}}, "notificationMessageDriverRejectedTrip": "{driver<PERSON><PERSON>} has rejected trip {tripCode}. Reason: {rejectionReason}.", "@notificationMessageDriverRejectedTrip": {"placeholders": {"driverName": {"type": "String"}, "tripCode": {"type": "String"}, "rejectionReason": {"type": "String"}}}, "notificationMessageDriverRejectedTripGeneric": "The driver has rejected trip {tripCode}. Reason: {rejectionReason}.", "@notificationMessageDriverRejectedTripGeneric": {"placeholders": {"tripCode": {"type": "String"}, "rejectionReason": {"type": "String"}}}, "notificationTripChangesRejected": "Trip Changes Rejected: {tripCode}", "@notificationTripChangesRejected": {"placeholders": {"tripCode": {"type": "String"}}}, "notificationMessageTripChangesRejected": "The requested changes to trip {tripCode} have been rejected. Reason: {rejectionReason}.", "@notificationMessageTripChangesRejected": {"placeholders": {"tripCode": {"type": "String"}, "rejectionReason": {"type": "String"}}}, "notificationTripCancellationRejected": "Trip Cancellation Rejected: {tripCode}", "@notificationTripCancellationRejected": {"placeholders": {"tripCode": {"type": "String"}}}, "notificationMessageTripCancellationRejected": "The cancellation request for trip {tripCode} has been rejected. Reason: {rejectionReason}.", "@notificationMessageTripCancellationRejected": {"placeholders": {"tripCode": {"type": "String"}, "rejectionReason": {"type": "String"}}}}