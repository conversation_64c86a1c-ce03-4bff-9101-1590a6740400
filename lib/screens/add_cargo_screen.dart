import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../generated/l10n/app_localizations.dart';
import '../services/api_service.dart';

class AddCargoScreen extends StatefulWidget {
  const AddCargoScreen({super.key});

  @override
  AddCargoScreenState createState() => AddCargoScreenState();
}

// Custom formatter to convert text to uppercase
class UpperCaseTextFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    return TextEditingValue(
      text: newValue.text.toUpperCase(),
      selection: newValue.selection,
    );
  }
}

class AddCargoScreenState extends State<AddCargoScreen> {
  final _formKey = GlobalKey<FormState>();
  final _cargoNameController = TextEditingController();
  final _cargoCodeController = TextEditingController();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _fetchNextCargoCode();
  }

  @override
  void dispose() {
    _cargoNameController.dispose();
    _cargoCodeController.dispose();
    super.dispose();
  }

  Future<void> _fetchNextCargoCode() async {
    try {
      final response = await ApiService.get('cargos/next-code');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (mounted) {
          setState(() {
            // Get the cargo code from the API response
            final cargoCode = data['cargoCode'];

            // If the API returns a sequenceNumber, use it to format the code
            if (data.containsKey('sequenceNumber')) {
              final sequenceNumber = data['sequenceNumber'];
              _cargoCodeController.text = 'CRG${sequenceNumber.toString().padLeft(4, '0')}';
            } else {
              // Fallback to the provided code with validation
              if (!cargoCode.startsWith('CRG')) {
                _cargoCodeController.text = 'CRG${cargoCode.replaceAll(RegExp(r'^[A-Za-z]+'), '')}';
              } else {
                _cargoCodeController.text = cargoCode;
              }
            }
          });
        }
      } else {        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.of(context).failedToGenerateCargoCode),
              backgroundColor: Colors.red,
            ),
          );
        }
      }    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${AppLocalizations.of(context).anErrorOccurred}: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final response = await ApiService.post(
        'cargos',
        {
          'cargo_code': _cargoCodeController.text,
          'cargo_name': _cargoNameController.text.toUpperCase(),
        },
      );

      if (response.statusCode == 201) {
        if (mounted) {
          Navigator.pop(context, true);
        }
      } else {
        final errorData = jsonDecode(response.body);        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(errorData['error'] ?? AppLocalizations.of(context).failedToAddCargo),
              backgroundColor: Colors.red,
            ),
          );
        }
      }    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${AppLocalizations.of(context).anErrorOccurred}: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(      appBar: AppBar(
        title: Text(
          AppLocalizations.of(context).addCargo,
          style: const TextStyle(color: Colors.white), // Making text white
        ),
        backgroundColor: const Color(0xFF0D47A1),
        iconTheme: const IconThemeData(color: Colors.white), // Making icons white
        foregroundColor: Colors.white, // This ensures all content is white
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [                      TextFormField(
                        controller: _cargoCodeController,
                        decoration: InputDecoration(
                          labelText: AppLocalizations.of(context).cargoCode,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          filled: true,
                          fillColor: Colors.grey[200],
                        ),
                        enabled: false,
                      ),
                      const SizedBox(height: 16),                      TextFormField(
                        controller: _cargoNameController,
                        decoration: InputDecoration(
                          labelText: AppLocalizations.of(context).cargoName,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        textCapitalization: TextCapitalization.characters,
                        inputFormatters: [
                          UpperCaseTextFormatter(),
                        ],
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return AppLocalizations.of(context).pleaseEnterCargoName;
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton(
                        onPressed: _submitForm,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF0D47A1), // Updated to match
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),                        child: Text(
                          _isLoading ? AppLocalizations.of(context).adding : AppLocalizations.of(context).submit,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white, // Ensuring text is white
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
    );
  }
}





