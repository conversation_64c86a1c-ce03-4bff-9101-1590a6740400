import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../generated/l10n/app_localizations.dart';
import '../main.dart';  // For MyAppState
import '../services/api_service.dart';

// Helper function to create a label with a red asterisk for required fields
Widget requiredLabel(String label) {
  return Row(
    mainAxisSize: MainAxisSize.min,
    children: [
      Text(label),
      const Text(
        ' *',
        style: TextStyle(
          color: Colors.red,
          fontWeight: FontWeight.bold,
        ),
      ),
    ],
  );
}

// Helper function to get required label text with asterisk
String requiredLabelText(String label) {
  return '$label *';
}

class AddTripScreen extends StatefulWidget {
  const AddTripScreen({super.key});

  @override
  State<AddTripScreen> createState() => _AddTripScreenState();
}

class _AddTripScreenState extends State<AddTripScreen> {
  final TextEditingController _dateController = TextEditingController();
  final TextEditingController _timeController = TextEditingController();
  final TextEditingController _returnDateController = TextEditingController();
  final TextEditingController _returnTimeController = TextEditingController();
  final TextEditingController _requestorNameController = TextEditingController();
  final TextEditingController _customFromDestinationController = TextEditingController();
  final TextEditingController _customToDestinationController = TextEditingController();
  final TextEditingController _customReturnFromDestinationController = TextEditingController();
  final TextEditingController _customReturnToDestinationController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();
  DateTime? _selectedDate;
  DateTime? _selectedReturnDate;
  TimeOfDay? _selectedTime;
  TimeOfDay? _selectedReturnTime;
  bool _isLoading = false;
  bool _isRoundTrip = false;
  bool _isWaiting = false;
  bool _isFromOthers = false;
  bool _isToOthers = false;
  bool _isReturnToOthers = false;
  final TextEditingController _waitingHoursController = TextEditingController();
  final TextEditingController _waitingMinutesController = TextEditingController();
  List<Map<String, String>> _destinations = [];
  String? _selectedFromDestination;
  String? _selectedToDestination;
  String? _selectedReturnFromDestination;
  String? _selectedReturnToDestination;
  List<Map<String, String>> _users = [];
  List<Map<String, String>> _cargos = [];
  List<String> _selectedPassengers = [];  // Store selected user IDs
  List<String> _selectedCargos = [];      // Store selected cargo codes

  @override
  void initState() {
    super.initState();
    _fetchDestinations();
    _fetchUsers();
    _fetchCargos();
    _fetchRequestorName();

    // Add listener to custom to destination controller to update return from destination
    _customToDestinationController.addListener(_updateReturnFromDestination);
  }

  // Update return from destination when custom to destination changes
  void _updateReturnFromDestination() {
    if (_isRoundTrip && _isToOthers && _selectedReturnFromDestination == _selectedToDestination) {
      _customReturnFromDestinationController.text = _customToDestinationController.text;
    }
  }

  Future<void> _fetchDestinations() async {
    try {
      final response = await ApiService.get('destinations');

      if (response.statusCode == 200) {
        final data = ApiService.parseResponse(response);
        setState(() {
          _destinations = (data['destinations'] as List).map((dest) => {
            'code': dest['destinationCode'].toString(),
            'name': dest['destination'].toString(),
          }).toList();

          // Make sure 'others' is not already in the list
          if (_destinations.any((dest) => dest['code'] == 'others')) {
            _destinations.removeWhere((dest) => dest['code'] == 'others');
          }          // Add 'others' option
          _destinations.add({
            'code': 'others',
            'name': AppLocalizations.of(context).others
          });
        });
      }
    } catch (e) {
      print('Error fetching destinations: $e');
    }
  }



  Future<void> _fetchRequestorName() async {
    try {
      final appState = Provider.of<MyAppState>(context, listen: false);
      final currentUser = appState.currentUser;

      if (currentUser == null) {
        return;
      }

      final response = await ApiService.get('users/${currentUser.userId}');

      if (response.statusCode == 200) {
        final userData = ApiService.parseResponse(response);
        setState(() {
          _requestorNameController.text = userData['name'] ?? 'Unknown';
        });
      } else {
        print('Error fetching user data: ${response.statusCode}');
      }
    } catch (e) {
      print('Error fetching requestor name: $e');
    }
  }
  Future<void> _fetchUsers() async {
    try {
      // Fetch users with Requestor role
      final requestorResponse = await ApiService.get('users?role=Requestor');
      
      // Fetch users with Trip Manager role
      final tripManagerResponse = await ApiService.get('users?role=Trip Manager');

      final List<Map<String, String>> allUsers = [];

      // Process Requestor users
      if (requestorResponse.statusCode == 200) {
        final requestorData = ApiService.parseResponse(requestorResponse);
        final requestorUsers = (requestorData['users'] as List).map((user) => {
          'id': user['user_id'].toString(),
          'name': user['name'].toString(),
        }).toList();
        allUsers.addAll(requestorUsers);
      }

      // Process Trip Manager users
      if (tripManagerResponse.statusCode == 200) {
        final tripManagerData = ApiService.parseResponse(tripManagerResponse);
        final tripManagerUsers = (tripManagerData['users'] as List).map((user) => {
          'id': user['user_id'].toString(),
          'name': user['name'].toString(),
        }).toList();
        allUsers.addAll(tripManagerUsers);
      }

      // Remove duplicates based on user ID and sort by name
      final Map<String, Map<String, String>> uniqueUsers = {};
      for (final user in allUsers) {
        uniqueUsers[user['id']!] = user;
      }

      setState(() {
        _users = uniqueUsers.values.toList()
          ..sort((a, b) => a['name']!.compareTo(b['name']!));
      });
    } catch (e) {
      print('Error fetching users: $e');
    }
  }

  Future<void> _fetchCargos() async {
    try {
      final response = await ApiService.get('cargos');

      if (response.statusCode == 200) {
        final data = ApiService.parseResponse(response);
        setState(() {
          _cargos = (data['cargos'] as List).map((cargo) => {
            'code': cargo['cargoCode'].toString(),
            'name': cargo['cargoName'].toString(),
          }).toList();
        });
      }
    } catch (e) {
      print('Error fetching cargos: $e');
    }
  }

  Future<void> _selectDate(BuildContext context, {required bool isReturn}) async {
    // Store context reference before async gap
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    // For return date, ensure firstDate is at least the outbound date
    DateTime firstDate = DateTime.now();

    if (isReturn && _selectedDate != null) {
      // Use outbound date as minimum for return date
      firstDate = _selectedDate!;
    }

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isReturn ? (_selectedReturnDate ?? firstDate) : (_selectedDate ?? firstDate),
      firstDate: firstDate,
      lastDate: DateTime(2101),
    );

    // Check if widget is still mounted after async operation
    if (!mounted) return;

    if (picked != null) {
      setState(() {
        if (isReturn) {
          // For round trips, validate that the return date is not before the outbound date
          if (_selectedDate != null && picked.isBefore(_selectedDate!)) {            scaffoldMessenger.showSnackBar(
              SnackBar(
                content: Text(AppLocalizations.of(context).returnDateCannotBeEarlier),
                backgroundColor: Colors.red,
              ),
            );
            return;
          }

          _selectedReturnDate = picked;
          _returnDateController.text = DateFormat('E, dd-MMM-yy').format(picked);

          // If return date is the same as outbound date, ensure return time is after outbound time
          if (_selectedDate != null &&
              picked.year == _selectedDate!.year &&
              picked.month == _selectedDate!.month &&
              picked.day == _selectedDate!.day &&
              _selectedTime != null) {
            // If return time is earlier than or equal to outbound time, reset it
            if (_selectedReturnTime != null) {
              final returnMinutes = _selectedReturnTime!.hour * 60 + _selectedReturnTime!.minute;
              final outboundMinutes = _selectedTime!.hour * 60 + _selectedTime!.minute;

              if (returnMinutes <= outboundMinutes) {
                // Set return time to outbound time + 1 hour
                final newTime = TimeOfDay(
                  hour: (_selectedTime!.hour + 1) % 24,
                  minute: _selectedTime!.minute
                );
                _selectedReturnTime = newTime;

                // Format the time for display
                final now = DateTime.now();
                final dateTime = DateTime(
                  now.year,
                  now.month,
                  now.day,
                  newTime.hour,
                  newTime.minute,
                );
                _returnTimeController.text = DateFormat('HH:mm').format(dateTime);
              }
            }
          }
        } else {
          // If return date is already selected, validate that outbound date is not after return date
          if (_isRoundTrip && _selectedReturnDate != null && picked.isAfter(_selectedReturnDate!)) {
            scaffoldMessenger.showSnackBar(
              const SnackBar(
                content: Text('Outbound date cannot be later than return date'),
                backgroundColor: Colors.red,
              ),
            );
            return;
          }

          _selectedDate = picked;
          _dateController.text = DateFormat('E, dd-MMM-yy').format(picked);

          // If waiting trip is active, recalculate the return date and time
          if (_isWaiting) {
            _calculateReturnDateTime();
          }
          // If round trip is active and return date is before the new outbound date, update it
          else if (_isRoundTrip && _selectedReturnDate != null) {
            if (_selectedReturnDate!.isBefore(picked)) {
              _selectedReturnDate = picked;
              _returnDateController.text = DateFormat('E, dd-MMM-yy').format(picked);

              // Notify the user that return date was adjusted
              scaffoldMessenger.showSnackBar(
                const SnackBar(
                  content: Text('Return date was automatically adjusted to match outbound date'),
                  backgroundColor: Colors.orange,
                ),
              );

              // Also ensure return time is after outbound time
              if (_selectedTime != null && _selectedReturnTime != null) {
                final returnMinutes = _selectedReturnTime!.hour * 60 + _selectedReturnTime!.minute;
                final outboundMinutes = _selectedTime!.hour * 60 + _selectedTime!.minute;

                if (returnMinutes <= outboundMinutes) {
                  // Set return time to outbound time + 1 hour
                  final newTime = TimeOfDay(
                    hour: (_selectedTime!.hour + 1) % 24,
                    minute: _selectedTime!.minute
                  );
                  _selectedReturnTime = newTime;

                  // Format the time for display
                  final now = DateTime.now();
                  final dateTime = DateTime(
                    now.year,
                    now.month,
                    now.day,
                    newTime.hour,
                    newTime.minute,
                  );
                  _returnTimeController.text = DateFormat('HH:mm').format(dateTime);

                  // Notify the user that return time was adjusted
                  scaffoldMessenger.showSnackBar(
                    const SnackBar(
                      content: Text('Return time was automatically adjusted to be after outbound time'),
                      backgroundColor: Colors.orange,
                    ),
                  );
                }
              }
            }
          }
          // If return date/time was selected first, validate the complete datetime
          else if (_isRoundTrip && _selectedReturnDate != null && _selectedReturnTime != null) {
            // Create complete datetime objects to compare
            final DateTime returnDateTime = DateTime(
              _selectedReturnDate!.year,
              _selectedReturnDate!.month,
              _selectedReturnDate!.day,
              _selectedReturnTime!.hour,
              _selectedReturnTime!.minute,
            );

            // We need to check if the outbound time is also selected
            if (_selectedTime != null) {
              final DateTime outboundDateTime = DateTime(
                picked.year,
                picked.month,
                picked.day,
                _selectedTime!.hour,
                _selectedTime!.minute,
              );

              // If outbound datetime is after or equal to return datetime, adjust return datetime
              if (outboundDateTime.isAfter(returnDateTime) || outboundDateTime.isAtSameMomentAs(returnDateTime)) {
                // Set return date to outbound date
                _selectedReturnDate = picked;
                _returnDateController.text = DateFormat('E, dd-MMM-yy').format(picked);

                // Set return time to outbound time + 1 hour
                final newTime = TimeOfDay(
                  hour: (_selectedTime!.hour + 1) % 24,
                  minute: _selectedTime!.minute
                );
                _selectedReturnTime = newTime;

                // Format the time for display
                final now = DateTime.now();
                final dateTime = DateTime(
                  now.year,
                  now.month,
                  now.day,
                  newTime.hour,
                  newTime.minute,
                );
                _returnTimeController.text = DateFormat('HH:mm').format(dateTime);

                // Notify the user that return date/time was adjusted
                scaffoldMessenger.showSnackBar(
                  const SnackBar(
                    content: Text('Return date and time were automatically adjusted to be after outbound date and time'),
                    backgroundColor: Colors.orange,
                  ),
                );
              }
            }
          }
        }
      });
    }
  }

  Future<void> _selectTime(BuildContext context, {required bool isReturn}) async {
    // Store context reference before async gap
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    // For return time on the same day, ensure it's after outbound time
    TimeOfDay initialTime;

    if (isReturn && _selectedTime != null &&
        _selectedDate != null && _selectedReturnDate != null &&
        _selectedDate!.year == _selectedReturnDate!.year &&
        _selectedDate!.month == _selectedReturnDate!.month &&
        _selectedDate!.day == _selectedReturnDate!.day) {
      // If selecting return time on the same day as outbound,
      // set initial time to at least 1 hour after outbound time
      initialTime = TimeOfDay(
        hour: (_selectedTime!.hour + 1) % 24,
        minute: _selectedTime!.minute
      );
    } else {
      initialTime = isReturn
          ? (_selectedReturnTime ?? TimeOfDay.now())
          : (_selectedTime ?? TimeOfDay.now());
    }

    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: initialTime,
      builder: (BuildContext context, Widget? child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: true),
          child: child!,
        );
      },
    );

    // Check if widget is still mounted after async operation
    if (!mounted) return;

    if (picked != null) {
      setState(() {
        if (isReturn) {
          // Check if return time is valid (after outbound time if same day)
          bool isValid = true;

          if (_selectedDate != null && _selectedReturnDate != null && _selectedTime != null) {
            // If dates are the same, ensure return time is after outbound time
            if (_selectedDate!.year == _selectedReturnDate!.year &&
                _selectedDate!.month == _selectedReturnDate!.month &&
                _selectedDate!.day == _selectedReturnDate!.day) {

              final returnMinutes = picked.hour * 60 + picked.minute;
              final outboundMinutes = _selectedTime!.hour * 60 + _selectedTime!.minute;

              if (returnMinutes <= outboundMinutes) {
                isValid = false;
                scaffoldMessenger.showSnackBar(
                  const SnackBar(
                    content: Text('Return time must be after outbound time on the same day'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            }
            // If return date is before outbound date (which shouldn't happen due to our date validation)
            else if (_selectedReturnDate!.isBefore(_selectedDate!)) {
              isValid = false;
              scaffoldMessenger.showSnackBar(
                const SnackBar(
                  content: Text('Return date cannot be before outbound date'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }

          if (isValid) {
            _selectedReturnTime = picked;
            // Convert to 24-hour format
            final now = DateTime.now();
            final dateTime = DateTime(
              now.year,
              now.month,
              now.day,
              picked.hour,
              picked.minute,
            );
            _returnTimeController.text = DateFormat('HH:mm').format(dateTime);

            // If outbound date/time is already set, validate the complete datetime
            if (_isRoundTrip && _selectedDate != null && _selectedTime != null) {
              final DateTime outboundDateTime = DateTime(
                _selectedDate!.year,
                _selectedDate!.month,
                _selectedDate!.day,
                _selectedTime!.hour,
                _selectedTime!.minute,
              );

              final DateTime returnDateTime = DateTime(
                _selectedReturnDate!.year,
                _selectedReturnDate!.month,
                _selectedReturnDate!.day,
                picked.hour,
                picked.minute,
              );

              // If return datetime is before or equal to outbound datetime, show a warning
              if (returnDateTime.isBefore(outboundDateTime) || returnDateTime.isAtSameMomentAs(outboundDateTime)) {
                scaffoldMessenger.showSnackBar(
                  const SnackBar(
                    content: Text('Warning: Return date and time must be after outbound date and time. Please adjust your selection.'),
                    backgroundColor: Colors.orange,
                    duration: Duration(seconds: 5),
                  ),
                );
              }
            }
          }
        } else {
          // Check if outbound time is valid (before return time if same day and return time is already set)
          bool isValid = true;

          if (_isRoundTrip && _selectedDate != null && _selectedReturnDate != null && _selectedReturnTime != null) {
            // If dates are the same, ensure outbound time is before return time
            if (_selectedDate!.year == _selectedReturnDate!.year &&
                _selectedDate!.month == _selectedReturnDate!.month &&
                _selectedDate!.day == _selectedReturnDate!.day) {

              final outboundMinutes = picked.hour * 60 + picked.minute;
              final returnMinutes = _selectedReturnTime!.hour * 60 + _selectedReturnTime!.minute;

              if (outboundMinutes >= returnMinutes) {
                isValid = false;
                scaffoldMessenger.showSnackBar(
                  const SnackBar(
                    content: Text('Outbound time must be before return time on the same day'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            }
            // If outbound date is after return date (which shouldn't happen due to our date validation)
            else if (_selectedDate!.isAfter(_selectedReturnDate!)) {
              isValid = false;
              scaffoldMessenger.showSnackBar(
                const SnackBar(
                  content: Text('Outbound date cannot be after return date'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }

          if (isValid) {
            _selectedTime = picked;
            // Convert to 24-hour format
            final now = DateTime.now();
            final dateTime = DateTime(
              now.year,
              now.month,
              now.day,
              picked.hour,
              picked.minute,
            );
            _timeController.text = DateFormat('HH:mm').format(dateTime);

            // If waiting trip is active, recalculate the return date and time
            if (_isWaiting) {
              _calculateReturnDateTime();
            }
            // If round trip is active and dates are the same, check if return time needs updating
            else if (_isRoundTrip && _selectedReturnTime != null &&
                _selectedDate != null && _selectedReturnDate != null) {

              // Create complete datetime objects to compare
              final DateTime outboundDateTime = DateTime(
                _selectedDate!.year,
                _selectedDate!.month,
                _selectedDate!.day,
                picked.hour,
                picked.minute,
              );

              final DateTime returnDateTime = DateTime(
                _selectedReturnDate!.year,
                _selectedReturnDate!.month,
                _selectedReturnDate!.day,
                _selectedReturnTime!.hour,
                _selectedReturnTime!.minute,
              );

              // If outbound datetime is after or equal to return datetime, adjust return datetime
              if (outboundDateTime.isAfter(returnDateTime) || outboundDateTime.isAtSameMomentAs(returnDateTime)) {
                // If dates are different, set return date to outbound date
                if (_selectedDate!.year != _selectedReturnDate!.year ||
                    _selectedDate!.month != _selectedReturnDate!.month ||
                    _selectedDate!.day != _selectedReturnDate!.day) {
                  _selectedReturnDate = _selectedDate;
                  _returnDateController.text = DateFormat('E, dd-MMM-yy').format(_selectedDate!);
                }

                // Set return time to outbound time + 1 hour
                final newTime = TimeOfDay(
                  hour: (picked.hour + 1) % 24,
                  minute: picked.minute
                );
                _selectedReturnTime = newTime;

                // Format the time for display
                final returnDateTime = DateTime(
                  now.year,
                  now.month,
                  now.day,
                  newTime.hour,
                  newTime.minute,
                );
                _returnTimeController.text = DateFormat('HH:mm').format(returnDateTime);

                // Notify the user that return date/time was adjusted
                scaffoldMessenger.showSnackBar(
                  const SnackBar(
                    content: Text('Return date and time were automatically adjusted to be after outbound date and time'),
                    backgroundColor: Colors.orange,
                  ),
                );
              }
            }
          }
        }
      });
    }
  }

  void _calculateReturnDateTime() {
    if (_dateController.text.isEmpty || _timeController.text.isEmpty) return;

    try {
      // Parse the outbound date and time
      final DateTime outboundDate = DateFormat('E, dd-MMM-yy').parse(_dateController.text);
      final TimeOfDay outboundTime = TimeOfDay.fromDateTime(
        DateFormat('HH:mm').parse(_timeController.text)
      );

      // Create a DateTime object combining date and time
      final DateTime outboundDateTime = DateTime(
        outboundDate.year,
        outboundDate.month,
        outboundDate.day,
        outboundTime.hour,
        outboundTime.minute,
      );

      // Add waiting hours and minutes
      final int waitingHours = int.tryParse(_waitingHoursController.text) ?? 0;
      final int waitingMinutes = int.tryParse(_waitingMinutesController.text) ?? 0;
      final DateTime returnDateTime = outboundDateTime.add(
        Duration(hours: waitingHours, minutes: waitingMinutes)
      );

      // Update return date and time controllers
      setState(() {
        _selectedReturnDate = returnDateTime;
        _returnDateController.text = DateFormat('E, dd-MMM-yy').format(returnDateTime);
        _selectedReturnTime = TimeOfDay.fromDateTime(returnDateTime);
        _returnTimeController.text = DateFormat('HH:mm').format(returnDateTime);
      });
    } catch (e) {
      print('Error calculating return date/time: $e');
    }
  }

  bool _validateForm() {
    // Store context reference for SnackBar messages
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    if (_selectedFromDestination == null || _selectedToDestination == null) {
      scaffoldMessenger.showSnackBar(        SnackBar(
          content: Text(AppLocalizations.of(context).pleaseSelectBothDestinations),
          backgroundColor: Colors.red,
        ),
      );
      return false;
    }

    // Validate that from and to destinations are not the same
    if (_selectedFromDestination == _selectedToDestination && _selectedFromDestination != 'others') {      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context).destinationsCannotBeSame),
          backgroundColor: Colors.red,
        ),
      );
      return false;
    }

    // For custom destinations (when 'others' is selected), check if the custom text is the same
    if (_isFromOthers && _isToOthers &&
        _customFromDestinationController.text.trim().isNotEmpty &&
        _customToDestinationController.text.trim().isNotEmpty &&
        _customFromDestinationController.text.trim() == _customToDestinationController.text.trim()) {      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context).customDestinationsCannotBeSame),
          backgroundColor: Colors.red,
        ),
      );
      return false;
    }

    if (_isFromOthers && _customFromDestinationController.text.trim().isEmpty) {
      scaffoldMessenger.showSnackBar(        SnackBar(
          content: Text(AppLocalizations.of(context).pleaseEnterCustomFrom),
          backgroundColor: Colors.red,
        ),
      );
      return false;
    }

    if (_isToOthers && _customToDestinationController.text.trim().isEmpty) {
      scaffoldMessenger.showSnackBar(        SnackBar(
          content: Text(AppLocalizations.of(context).pleaseEnterCustomTo),
          backgroundColor: Colors.red,
        ),
      );
      return false;
    }

    // Validate custom return destination for round trips
    if (_isRoundTrip && _isReturnToOthers && _customReturnToDestinationController.text.trim().isEmpty) {      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context).pleaseEnterCustomReturnTo),
          backgroundColor: Colors.red,
        ),
      );
      return false;
    }

    // Check if both outbound and return dates/times are provided
    final bool hasOutboundDateTime = _dateController.text.isNotEmpty && _timeController.text.isNotEmpty;
    final bool hasReturnDateTime = _returnDateController.text.isNotEmpty && _returnTimeController.text.isNotEmpty;

    // For round trips or waiting trips, both outbound and return dates/times are required
    if (_isRoundTrip || _isWaiting) {
      if (!hasOutboundDateTime) {
        scaffoldMessenger.showSnackBar(          SnackBar(
            content: Text(AppLocalizations.of(context).pleaseSelectOutboundDateTime),
            backgroundColor: Colors.red,
          ),
        );
        return false;
      }

      if (!hasReturnDateTime) {        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).pleaseSelectReturnDateTime),
            backgroundColor: Colors.red,
          ),
        );
        return false;
      }
    } else {
      // For one-way trips, only outbound date/time is required
      if (!hasOutboundDateTime) {        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).pleaseSelectDateTime),
            backgroundColor: Colors.red,
          ),
        );
        return false;
      }
    }

    // For round trips, validate that return From and To destinations are not the same
    if (_isRoundTrip && !_isWaiting) {
      final String returnFrom = _selectedReturnFromDestination ?? _selectedToDestination ?? '';
      final String returnTo = _selectedReturnToDestination ?? _selectedFromDestination ?? '';

      if (returnFrom == returnTo && returnFrom != 'others') {        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).returnFromAndToCannotBeSame),
            backgroundColor: Colors.red,
          ),
        );
        return false;
      }

      // For custom destinations (when 'others' is selected), check if the custom text is the same
      if (returnFrom == 'others' && returnTo == 'others' &&
          _customReturnFromDestinationController.text.trim().isNotEmpty &&
          _customReturnToDestinationController.text.trim().isNotEmpty &&
          _customReturnFromDestinationController.text.trim() == _customReturnToDestinationController.text.trim()) {        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).returnFromAndToCustomCannotBeSame),
            backgroundColor: Colors.red,
          ),
        );
        return false;
      }
    }

    // Validate that return date/time is after outbound date/time for round trips and waiting trips
    if (_isRoundTrip || _isWaiting) {
      try {
        // Parse outbound date and time
        final DateTime outboundDate = DateFormat('E, dd-MMM-yy').parse(_dateController.text);
        final TimeOfDay outboundTime = TimeOfDay.fromDateTime(
          DateFormat('HH:mm').parse(_timeController.text)
        );

        // Create outbound DateTime
        final DateTime outboundDateTime = DateTime(
          outboundDate.year,
          outboundDate.month,
          outboundDate.day,
          outboundTime.hour,
          outboundTime.minute,
        );

        // Parse return date and time
        final DateTime returnDate = DateFormat('E, dd-MMM-yy').parse(_returnDateController.text);
        final TimeOfDay returnTime = TimeOfDay.fromDateTime(
          DateFormat('HH:mm').parse(_returnTimeController.text)
        );

        // Create return DateTime
        final DateTime returnDateTime = DateTime(
          returnDate.year,
          returnDate.month,
          returnDate.day,
          returnTime.hour,
          returnTime.minute,
        );

        // Check if outbound date is after return date
        if (outboundDate.isAfter(returnDate)) {
          scaffoldMessenger.showSnackBar(
            const SnackBar(
              content: Text('Outbound date cannot be later than return date'),
              backgroundColor: Colors.red,
            ),
          );
          return false;
        }

        // Check if dates are the same but outbound time is after or equal to return time
        if (outboundDate.year == returnDate.year &&
            outboundDate.month == returnDate.month &&
            outboundDate.day == returnDate.day) {

          final outboundMinutes = outboundTime.hour * 60 + outboundTime.minute;
          final returnMinutes = returnTime.hour * 60 + returnTime.minute;

          if (outboundMinutes >= returnMinutes) {
            scaffoldMessenger.showSnackBar(
              const SnackBar(
                content: Text('Outbound time must be before return time on the same day'),
                backgroundColor: Colors.red,
              ),
            );
            return false;
          }
        }

        // Final check: ensure outbound datetime is before return datetime
        if (outboundDateTime.isAfter(returnDateTime) || outboundDateTime.isAtSameMomentAs(returnDateTime)) {
          scaffoldMessenger.showSnackBar(
            const SnackBar(
              content: Text('Outbound date and time must be before return date and time'),
              backgroundColor: Colors.red,
            ),
          );
          return false;
        }

        // Additional check: ensure the return datetime is after the outbound datetime
        // This is a redundant check but ensures we catch any edge cases
        if (returnDateTime.isBefore(outboundDateTime) || returnDateTime.isAtSameMomentAs(outboundDateTime)) {
          scaffoldMessenger.showSnackBar(
            const SnackBar(
              content: Text('Return date and time must be after outbound date and time'),
              backgroundColor: Colors.red,
            ),
          );
          return false;
        }
      } catch (e) {
        print('Error validating dates: $e');
        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text('Invalid date or time format'),
            backgroundColor: Colors.red,
          ),
        );
        return false;
      }
    }

    // For waiting trips, validate that waiting duration is provided
    if (_isWaiting) {
      if (_waitingHoursController.text.isEmpty && _waitingMinutesController.text.isEmpty) {        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).pleaseEnterWaitingDuration),
            backgroundColor: Colors.red,
          ),
        );
        return false;
      }
    }

    return true;
  }

  Future<void> _submitTrip() async {
    if (!_validateForm()) return;

    // Store context reference before async gap
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final navigator = Navigator.of(context);

    setState(() => _isLoading = true);

    try {
      final appState = Provider.of<MyAppState>(context, listen: false);
      final currentUser = appState.currentUser;      if (currentUser == null) {
        throw Exception(AppLocalizations.of(context).youMustBeLoggedIn);
      }

      // Convert outbound date to ISO format
      final DateTime parsedOutboundDate = DateFormat('E, dd-MMM-yy').parse(_dateController.text);
      final String isoOutboundDate = DateFormat('yyyy-MM-dd').format(parsedOutboundDate.toLocal());

      // Use the selected passengers and cargos directly
      final List<String> validPassengers = _selectedPassengers;
      final List<String> validCargos = _selectedCargos;

      // Submit trip
      final requestBody = {
        'from_destination': _selectedFromDestination,
        'to_destination': _selectedToDestination,
        'date': isoOutboundDate,
        'time': _timeController.text,
        'status': 'REQUEST',
        'requestor_id': currentUser.userId,
        'passengers': validPassengers,
        'cargos': validCargos,
        'trip_type': _isRoundTrip ? 'Round Trip - Outbound' : (_isWaiting ? 'Waiting' : 'One Way'),
        'is_waiting': _isWaiting,
        'notes': _notesController.text.trim(),
      };      // For waiting trips, add return time, date, and total_duration
      if (_isWaiting) {
        requestBody['return_time'] = _returnTimeController.text;

        // Add return date for multi-day waiting periods
        if (_selectedReturnDate != null) {
          final String isoReturnDate = DateFormat('yyyy-MM-dd').format(_selectedReturnDate!.toLocal());
          requestBody['return_date'] = isoReturnDate;
          
          // Calculate and add total_duration as an ISO timestamp
          // This represents the end time of the waiting period
          final DateTime endDateTime = DateTime(
            _selectedReturnDate!.year,
            _selectedReturnDate!.month,
            _selectedReturnDate!.day,
            _selectedReturnTime!.hour,
            _selectedReturnTime!.minute,
          );
          requestBody['total_duration'] = endDateTime.toIso8601String();
        }
      }

      // Add custom destinations if 'Others' is selected
      if (_isFromOthers) {
        requestBody['custom_from'] = _customFromDestinationController.text.trim();
      }

      if (_isToOthers) {
        requestBody['custom_to'] = _customToDestinationController.text.trim();
      }

      final response = await ApiService.post('trips', requestBody);

      // Check if widget is still mounted after first API call
      if (!mounted) return;

      if (response.statusCode != 201) {
        final errorData = ApiService.parseResponse(response);
        throw Exception(errorData['error'] ?? 'Failed to create trip');
      }

      // Variables for round trip
      String? returnTripCode;

      // If this is a round trip (not waiting), submit a second trip
      if (_isRoundTrip && !_isWaiting) {
        // Convert return date to ISO format
        final DateTime parsedReturnDate = DateFormat('E, dd-MMM-yy').parse(_returnDateController.text);
        final String isoReturnDate = DateFormat('yyyy-MM-dd').format(parsedReturnDate.toLocal());

        final returnRequestBody = {
          'from_destination': _selectedReturnFromDestination ?? _selectedToDestination,
          'to_destination': _selectedReturnToDestination ?? _selectedFromDestination,
          'date': isoReturnDate,
          'time': _returnTimeController.text,
          'status': 'REQUEST',
          'requestor_id': currentUser.userId,
          'passengers': validPassengers,
          'cargos': validCargos,
          'trip_type': 'Round Trip - Return',
          'is_waiting': false,
          'notes': _notesController.text.trim(),
        };

        // Add custom destinations for return trip if needed

        // IMPORTANT: For the return journey, the FROM destination is the outbound journey's TO destination
        if (_isToOthers) {
          // If outbound TO was "others", use that custom text for return FROM
          returnRequestBody['custom_from'] = _customToDestinationController.text.trim();
          print('Setting return custom_from to: ${_customToDestinationController.text.trim()}');
        }

        // IMPORTANT: For the return journey, the TO destination is the outbound journey's FROM destination
        if (_isFromOthers) {
          // If outbound FROM was "others", use that custom text for return TO
          returnRequestBody['custom_to'] = _customFromDestinationController.text.trim();
          print('Setting return custom_to to: ${_customFromDestinationController.text.trim()}');
        }

        // Override with specific return custom values if they exist
        if (_isReturnToOthers && _customReturnToDestinationController.text.trim().isNotEmpty) {
          returnRequestBody['custom_to'] = _customReturnToDestinationController.text.trim();
          print('Overriding return custom_to with: ${_customReturnToDestinationController.text.trim()}');
        }

        final returnResponse = await ApiService.post('trips', returnRequestBody);

        // Check if widget is still mounted after second API call
        if (!mounted) return;

        if (returnResponse.statusCode != 201) {
          final errorData = ApiService.parseResponse(returnResponse);
          throw Exception(errorData['error'] ?? 'Failed to create return trip');
        }

        // Get the return trip code from the response
        final returnResponseData = ApiService.parseResponse(returnResponse);
        returnTripCode = returnResponseData['trip_code'];
      }

      // Parse the response to get the trip code
      final responseData = ApiService.parseResponse(response);
      final tripCode = responseData['trip_code'];

      // Create the success message
      String successMessage;      if (_isRoundTrip && !_isWaiting) {
        // For round trip, include both trip codes
        successMessage = AppLocalizations.of(context).roundTripCreatedSuccessfully(tripCode, returnTripCode ?? '');
      } else if (_isWaiting) {
        // For waiting trip, just include the trip code
        successMessage = AppLocalizations.of(context).waitingTripCreatedSuccessfully(tripCode);
      } else {
        // For one-way trip, just include the trip code
        successMessage = AppLocalizations.of(context).tripCreatedSuccessfully(tripCode);
      }

      if (mounted) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(successMessage),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 5), // Make it last for 5 seconds
          ),
        );

        // Return to request_trip_screen with refresh flag
        navigator.pop(true);
      }

    } catch (e) {
      print('Error submitting trip: $e');
      if (mounted) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  String _getSelectedPassengerNames() {
    final selectedUsers = _users.where((user) => _selectedPassengers.contains(user['id'])).toList();
    if (selectedUsers.length <= 2) {
      return selectedUsers.map((user) => user['name']).join(', ');
    } else {
      return '${selectedUsers.first['name']} and ${selectedUsers.length - 1} others';
    }
  }

  String _getSelectedCargoNames() {
    final selectedCargos = _cargos.where((cargo) => _selectedCargos.contains(cargo['code'])).toList();
    if (selectedCargos.length <= 2) {
      return selectedCargos.map((cargo) => cargo['name']).join(', ');
    } else {
      return '${selectedCargos.first['name']} and ${selectedCargos.length - 1} others';
    }
  }

  void _showPassengerSelectionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setModalState) {
            return AlertDialog(
              title: Text(AppLocalizations.of(context).selectPassengers),
              content: SizedBox(
                width: double.maxFinite,
                height: 400,
                child: Column(
                  children: [
                    // Search field
                    TextField(
                      decoration: InputDecoration(
                        hintText: AppLocalizations.of(context).searchForPassengers,
                        prefixIcon: const Icon(Icons.search),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      onChanged: (value) {
                        // Filter users based on search
                        setModalState(() {});
                      },
                    ),
                    const SizedBox(height: 16),
                    // User list
                    Expanded(
                      child: ListView.builder(
                        itemCount: _users.length,
                        itemBuilder: (context, index) {
                          final user = _users[index];
                          final isSelected = _selectedPassengers.contains(user['id']);
                          return CheckboxListTile(
                            value: isSelected,
                            title: Text(user['name']!),
                            onChanged: (bool? checked) {
                              if (checked == true) {
                                if (!_selectedPassengers.contains(user['id']!)) {
                                  setState(() {
                                    _selectedPassengers.add(user['id']!);
                                  });
                                  setModalState(() {});
                                }
                              } else {
                                setState(() {
                                  _selectedPassengers.remove(user['id']!);
                                });
                                setModalState(() {});
                              }
                            },
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Done'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _showCargoSelectionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setModalState) {
            return AlertDialog(
              title: Text(AppLocalizations.of(context).selectCargos),
              content: SizedBox(
                width: double.maxFinite,
                height: 400,
                child: Column(
                  children: [
                    // Search field
                    TextField(
                      decoration: InputDecoration(
                        hintText: AppLocalizations.of(context).searchForCargos,
                        prefixIcon: const Icon(Icons.search),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      onChanged: (value) {
                        // Filter cargos based on search
                        setModalState(() {});
                      },
                    ),
                    const SizedBox(height: 16),
                    // Cargo list
                    Expanded(
                      child: ListView.builder(
                        itemCount: _cargos.length,
                        itemBuilder: (context, index) {
                          final cargo = _cargos[index];
                          final isSelected = _selectedCargos.contains(cargo['code']);
                          return CheckboxListTile(
                            value: isSelected,
                            title: Text(cargo['name']!),
                            onChanged: (bool? checked) {
                              if (checked == true) {
                                if (!_selectedCargos.contains(cargo['code']!)) {
                                  setState(() {
                                    _selectedCargos.add(cargo['code']!);
                                  });
                                  setModalState(() {});
                                }
                              } else {
                                setState(() {
                                  _selectedCargos.remove(cargo['code']!);
                                });
                                setModalState(() {});
                              }
                            },
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Done'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(      appBar: AppBar(
        title: Text(AppLocalizations.of(context).addTrip, style: const TextStyle(color: Colors.white)),
        backgroundColor: const Color(0xFF0D47A1),
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [                  TextField(
                    controller: _requestorNameController,
                    readOnly: true,
                    decoration: InputDecoration(
                      labelText: AppLocalizations.of(context).requestorName,
                      border: const OutlineInputBorder(),
                      filled: true,
                      fillColor: const Color(0xFFF5F5F5),
                    ),
                  ),
                  const SizedBox(height: 16),
                  // Add Round Trip and Waiting options
                  Row(
                    children: [
                      Expanded(                        child: CheckboxListTile(
                          title: Text(AppLocalizations.of(context).roundTrip),
                          value: _isRoundTrip,
                          onChanged: _isWaiting ? null : (bool? value) {
                            setState(() {
                              _isRoundTrip = value ?? false;
                              if (!_isRoundTrip) {
                                _returnDateController.clear();
                                _returnTimeController.clear();
                                _selectedReturnDate = null;
                                _selectedReturnTime = null;
                                _selectedReturnFromDestination = null;
                                _selectedReturnToDestination = null;
                                _customReturnFromDestinationController.clear();
                                _customReturnToDestinationController.clear();
                              } else {
                                // Set return from destination to outbound to destination
                                _selectedReturnFromDestination = _selectedToDestination;
                                // Set return to destination to outbound from destination
                                _selectedReturnToDestination = _selectedFromDestination;

                                // If outbound 'to' is 'others', copy the custom text to return 'from'
                                if (_selectedToDestination == 'others') {
                                  _customReturnFromDestinationController.text = _customToDestinationController.text;
                                  print('Copied custom TO to return FROM: ${_customToDestinationController.text}');
                                }

                                // If outbound 'from' is 'others', copy the custom text to return 'to'
                                if (_selectedFromDestination == 'others') {
                                  _customReturnToDestinationController.text = _customFromDestinationController.text;
                                  print('Copied custom FROM to return TO: ${_customFromDestinationController.text}');
                                }
                              }
                            });
                          },
                          controlAffinity: ListTileControlAffinity.leading,
                        ),
                      ),
                      Expanded(                        child: CheckboxListTile(
                          title: Text(AppLocalizations.of(context).waiting),
                          value: _isWaiting,
                          onChanged: _isRoundTrip ? null : (bool? value) {
                            setState(() {
                              _isWaiting = value ?? false;
                              if (!_isWaiting) {
                                _waitingHoursController.clear();
                                _waitingMinutesController.clear();
                                _selectedReturnFromDestination = null;
                                _selectedReturnToDestination = null;
                              } else {
                                _selectedReturnFromDestination = _selectedToDestination;

                                // If 'others' is selected, also copy the custom text
                                if (_selectedToDestination == 'others') {
                                  _customReturnFromDestinationController.text = _customToDestinationController.text;
                                }
                              }
                            });
                          },
                          controlAffinity: ListTileControlAffinity.leading,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  // Outbound Journey Section
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [                        Text(
                          AppLocalizations.of(context).outboundJourney,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        // From and To destinations in a row
                        Row(
                          children: [
                            Expanded(                              child: DropdownSearch<Map<String, String>>(
                                selectedItem: _selectedFromDestination != null
                                  ? _destinations.firstWhere(
                                      (dest) => dest['code'] == _selectedFromDestination,
                                      orElse: () => {'code': '', 'name': ''},
                                    )
                                  : null,
                                popupProps: PopupProps.menu(
                                  showSearchBox: true,
                                  searchFieldProps: TextFieldProps(
                                    decoration: InputDecoration(
                                      hintText: AppLocalizations.of(context).searchForDestinations,
                                      prefixIcon: const Icon(Icons.search),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                    ),
                                  ),
                                  itemBuilder: (context, dest, isSelected) {
                                    return ListTile(
                                      selected: isSelected,
                                      title: Text(dest['name']!),
                                    );
                                  },
                                ),                                dropdownDecoratorProps: DropDownDecoratorProps(
                                  dropdownSearchDecoration: InputDecoration(
                                    labelText: requiredLabelText(AppLocalizations.of(context).from),
                                    hintText: AppLocalizations.of(context).selectOrigin,
                                    border: const OutlineInputBorder(),
                                  ),
                                ),
                                itemAsString: (Map<String, String>? dest) => dest?['name'] ?? '',
                                items: _destinations,
                                onChanged: (value) {
                                  if (value != null) {
                                    setState(() {
                                      _selectedFromDestination = value['code'];
                                      _isFromOthers = value['code'] == 'others';
                                      if (!_isFromOthers) {
                                        _customFromDestinationController.clear();
                                      }
                                    });
                                  }
                                },
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(                              child: DropdownSearch<Map<String, String>>(
                                selectedItem: _selectedToDestination != null
                                  ? _destinations.firstWhere(
                                      (dest) => dest['code'] == _selectedToDestination,
                                      orElse: () => {'code': '', 'name': ''},
                                    )
                                  : null,
                                popupProps: PopupProps.menu(
                                  showSearchBox: true,
                                  searchFieldProps: TextFieldProps(
                                    decoration: InputDecoration(
                                      hintText: AppLocalizations.of(context).searchForDestinations,
                                      prefixIcon: const Icon(Icons.search),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                    ),
                                  ),
                                  itemBuilder: (context, dest, isSelected) {
                                    return ListTile(
                                      selected: isSelected,
                                      title: Text(dest['name']!),
                                    );
                                  },
                                ),                                dropdownDecoratorProps: DropDownDecoratorProps(
                                  dropdownSearchDecoration: InputDecoration(
                                    labelText: requiredLabelText(AppLocalizations.of(context).to),
                                    hintText: AppLocalizations.of(context).selectDestination,
                                    border: const OutlineInputBorder(),
                                  ),
                                ),
                                itemAsString: (Map<String, String>? dest) => dest?['name'] ?? '',
                                items: _destinations,
                                onChanged: (value) {
                                  if (value != null) {
                                    setState(() {
                                      _selectedToDestination = value['code'];
                                      _isToOthers = value['code'] == 'others';
                                      if (_isRoundTrip) {
                                        _selectedReturnFromDestination = value['code']; // Automatically set return "From" destination

                                        // If 'others' is selected, also copy the custom text
                                        if (value['code'] == 'others') {
                                          _customReturnFromDestinationController.text = _customToDestinationController.text;
                                          print('Set return from custom destination to: ${_customToDestinationController.text}');
                                        }
                                      }
                                      if (!_isToOthers) {
                                        _customToDestinationController.clear();
                                      }
                                    });
                                  }
                                },
                              ),
                            ),
                          ],
                        ),

                        // Custom From and To in a row (if needed)
                        if (_isFromOthers || _isToOthers) ...[
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              if (_isFromOthers)
                                Expanded(
                                  child: TextField(
                                    controller: _customFromDestinationController,                                    decoration: InputDecoration(
                                      label: requiredLabel(AppLocalizations.of(context).customFrom),
                                      border: const OutlineInputBorder(),
                                      hintText: AppLocalizations.of(context).enterCustomLocation,
                                    ),
                                  ),
                                ),
                              if (_isFromOthers && _isToOthers)
                                const SizedBox(width: 16),
                              if (_isToOthers)
                                Expanded(
                                  child: TextField(
                                    controller: _customToDestinationController,                                    decoration: InputDecoration(
                                      label: requiredLabel(AppLocalizations.of(context).customTo),
                                      border: const OutlineInputBorder(),
                                      hintText: AppLocalizations.of(context).enterCustomLocation,
                                    ),
                                    onChanged: (value) {
                                      // Update return from destination if round trip is active
                                      if (_isRoundTrip && _selectedReturnFromDestination == _selectedToDestination) {
                                        setState(() {
                                          _customReturnFromDestinationController.text = value;
                                          print('Updated return from custom destination to: $value');
                                        });
                                      }
                                    },
                                  ),
                                ),
                            ],
                          ),
                        ],

                        const SizedBox(height: 16),

                        // Date and Time in a row
                        Row(
                          children: [
                            Expanded(
                              child: TextField(
                                controller: _dateController,
                                readOnly: true,
                                decoration: InputDecoration(
                                  label: requiredLabel(AppLocalizations.of(context).date),
                                  border: const OutlineInputBorder(),
                                  suffixIcon: const Icon(Icons.calendar_today),
                                ),
                                onTap: () => _selectDate(context, isReturn: false),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: TextField(
                                controller: _timeController,
                                readOnly: true,
                                decoration: InputDecoration(
                                  label: requiredLabel(AppLocalizations.of(context).time),
                                  border: const OutlineInputBorder(),
                                  suffixIcon: const Icon(Icons.access_time),
                                ),
                                onTap: () => _selectTime(context, isReturn: false),
                              ),
                            ),
                          ],
                        ),                        const SizedBox(height: 16),
                        // Notes field
                        TextField(
                          controller: _notesController,
                          decoration: InputDecoration(
                            labelText: AppLocalizations.of(context).notesOptional,
                            border: const OutlineInputBorder(),
                            hintText: AppLocalizations.of(context).addAdditionalNotes,
                          ),
                          maxLines: 3,
                        ),
                      ],
                    ),
                  ),
                  // Return Journey Section (for Round Trip) or Waiting Section
                  if (_isRoundTrip || _isWaiting) ...[
                    const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [                          Text(
                            _isRoundTrip ? AppLocalizations.of(context).returnJourney : AppLocalizations.of(context).waitingTime,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          if (_isWaiting) ...[
                            Row(
                              children: [
                                Expanded(
                                  child: TextField(
                                    controller: _waitingHoursController,                                    decoration: InputDecoration(
                                      label: requiredLabel(AppLocalizations.of(context).hours),
                                      border: const OutlineInputBorder(),
                                      suffixText: 'hrs',
                                    ),
                                    keyboardType: TextInputType.number,
                                    inputFormatters: [
                                      FilteringTextInputFormatter.digitsOnly,
                                    ],
                                    onChanged: (_) => _calculateReturnDateTime(),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: TextField(
                                    controller: _waitingMinutesController,
                                    decoration: InputDecoration(
                                      label: requiredLabel(AppLocalizations.of(context).minutes),
                                      border: const OutlineInputBorder(),
                                      suffixText: 'min',
                                    ),
                                    keyboardType: TextInputType.number,
                                    inputFormatters: [
                                      FilteringTextInputFormatter.digitsOnly,
                                      FilteringTextInputFormatter.allow(RegExp(r'^([0-5]?[0-9])$')),
                                    ],
                                    onChanged: (_) => _calculateReturnDateTime(),
                                  ),
                                ),
                              ],
                            ),
                            // From and To destination inputs are removed for waiting trips

                          ],
                          if (_isRoundTrip) ...[
                            const SizedBox(height: 16),
                            // Return From and To in a row
                            Row(
                              children: [
                                Expanded(                                  child: DropdownSearch<Map<String, String>>(
                                    selectedItem: (_selectedReturnFromDestination ?? _selectedToDestination) != null
                                      ? _destinations.firstWhere(
                                          (dest) => dest['code'] == (_selectedReturnFromDestination ?? _selectedToDestination),
                                          orElse: () => {'code': '', 'name': ''},
                                        )
                                      : null,
                                    popupProps: PopupProps.menu(
                                      showSearchBox: true,
                                      searchFieldProps: TextFieldProps(
                                        decoration: InputDecoration(
                                          hintText: AppLocalizations.of(context).searchForDestinations,
                                          prefixIcon: const Icon(Icons.search),
                                          border: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(10),
                                          ),
                                        ),
                                      ),
                                      itemBuilder: (context, dest, isSelected) {
                                        return ListTile(
                                          selected: isSelected,
                                          title: Text(dest['name']!),
                                        );
                                      },
                                    ),                                    dropdownDecoratorProps: DropDownDecoratorProps(
                                      dropdownSearchDecoration: InputDecoration(
                                        labelText: requiredLabelText(AppLocalizations.of(context).from),
                                        hintText: AppLocalizations.of(context).selectOrigin,
                                        border: const OutlineInputBorder(),
                                        filled: true,
                                        fillColor: const Color(0xFFF5F5F5),
                                      ),
                                    ),
                                    itemAsString: (Map<String, String>? dest) => dest?['name'] ?? '',
                                    items: _destinations,
                                    enabled: false, // Disabled for round trip
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(                                  child: DropdownSearch<Map<String, String>>(
                                    selectedItem: _selectedReturnToDestination != null
                                      ? _destinations.firstWhere(
                                          (dest) => dest['code'] == _selectedReturnToDestination,
                                          orElse: () => {'code': '', 'name': ''},
                                        )
                                      : null,
                                    popupProps: PopupProps.menu(
                                      showSearchBox: true,
                                      searchFieldProps: TextFieldProps(
                                        decoration: InputDecoration(
                                          hintText: AppLocalizations.of(context).searchForDestinations,
                                          prefixIcon: const Icon(Icons.search),
                                          border: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(10),
                                          ),
                                        ),
                                      ),
                                      itemBuilder: (context, dest, isSelected) {
                                        return ListTile(
                                          selected: isSelected,
                                          title: Text(dest['name']!),
                                        );
                                      },
                                    ),                                    dropdownDecoratorProps: DropDownDecoratorProps(
                                      dropdownSearchDecoration: InputDecoration(
                                        labelText: requiredLabelText(AppLocalizations.of(context).to),
                                        hintText: AppLocalizations.of(context).selectDestination,
                                        border: const OutlineInputBorder(),
                                      ),
                                    ),
                                    itemAsString: (Map<String, String>? dest) => dest?['name'] ?? '',
                                    items: _destinations,
                                    onChanged: (value) {
                                      if (value != null) {
                                        setState(() {
                                          _selectedReturnToDestination = value['code'];
                                          _isReturnToOthers = value['code'] == 'others';
                                          if (!_isReturnToOthers) {
                                            _customReturnToDestinationController.clear();
                                          } else if (value['code'] == 'others' && _selectedFromDestination == _selectedReturnToDestination) {
                                            // If return to is 'others' and it matches the original from destination
                                            // and the original from was also 'others', copy the custom text
                                            if (_isFromOthers) {
                                              _customReturnToDestinationController.text = _customFromDestinationController.text;
                                            }
                                          }
                                        });
                                      }
                                    },
                                  ),
                                ),
                              ],
                            ),

                            // Custom Return From and To in a row (if needed)
                            if (_isToOthers || _isReturnToOthers) ...[
                              const SizedBox(height: 16),
                              Row(
                                children: [
                                  if (_isToOthers)
                                    Expanded(
                                      child: TextField(
                                        controller: _customReturnFromDestinationController,
                                        readOnly: true, // Make it read-only since it's automatically set
                                        decoration: InputDecoration(
                                          label: Text(AppLocalizations.of(context).customFrom),
                                          border: const OutlineInputBorder(),
                                          filled: true,
                                          fillColor: const Color(0xFFF5F5F5),
                                        ),
                                      ),
                                    ),
                                  if (_isToOthers && _isReturnToOthers)
                                    const SizedBox(width: 16),
                                  if (_isReturnToOthers)
                                    Expanded(
                                      child: TextField(
                                        controller: _customReturnToDestinationController,                                        decoration: InputDecoration(
                                          label: requiredLabel(AppLocalizations.of(context).customTo),
                                          border: const OutlineInputBorder(),
                                          hintText: AppLocalizations.of(context).enterCustomLocation,
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ],
                          ],
                          if (_isRoundTrip || _isWaiting) ...[
                            const SizedBox(height: 16),
                            // Return Date and Time in a row
                            Row(
                              children: [
                                Expanded(
                                  child: TextField(
                                    controller: _returnDateController,
                                    readOnly: true,
                                    enabled: !_isWaiting,
                                    decoration: InputDecoration(
                                      label: requiredLabel(AppLocalizations.of(context).returnDate),
                                      border: const OutlineInputBorder(),
                                      suffixIcon: const Icon(Icons.calendar_today),
                                      filled: true,
                                      fillColor: const Color(0xFFF5F5F5),
                                    ),
                                    onTap: _isWaiting ? null : () => _selectDate(context, isReturn: true),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: TextField(
                                    controller: _returnTimeController,
                                    readOnly: true,
                                    enabled: !_isWaiting,
                                    decoration: InputDecoration(
                                      label: requiredLabel(AppLocalizations.of(context).returnTime),
                                      border: const OutlineInputBorder(),
                                      suffixIcon: const Icon(Icons.access_time),
                                      filled: true,
                                      fillColor: const Color(0xFFF5F5F5),
                                    ),
                                    onTap: _isWaiting ? null : () => _selectTime(context, isReturn: true),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ],
                      ),
                    ),
                  ],                  // Passengers Section (Full Width)
                  const SizedBox(height: 16),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(                          AppLocalizations.of(context).passengers,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        // Custom Multi-Select for Passengers
                        GestureDetector(
                          onTap: () => _showPassengerSelectionDialog(context),
                          child: Container(
                            width: double.infinity,
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    _selectedPassengers.isEmpty
                                        ? AppLocalizations.of(context).chooseOneOrMorePassengers
                                        : _getSelectedPassengerNames(),
                                    style: TextStyle(
                                      color: _selectedPassengers.isEmpty ? Colors.grey[600] : Colors.black,
                                    ),
                                  ),
                                ),
                                const Icon(Icons.arrow_drop_down),                              ],
                            ),
                          ),
                        ),
                      ],
                    ),                  ),

                  // Cargos Section (Full Width)
                  const SizedBox(height: 16),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          AppLocalizations.of(context).cargos,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),                        const SizedBox(height: 8),
                        // Custom Multi-Select for Cargos
                        GestureDetector(
                          onTap: () => _showCargoSelectionDialog(context),
                          child: Container(
                            width: double.infinity,
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    _selectedCargos.isEmpty
                                        ? AppLocalizations.of(context).chooseOneOrMoreCargos
                                        : _getSelectedCargoNames(),
                                    style: TextStyle(
                                      color: _selectedCargos.isEmpty ? Colors.grey[600] : Colors.black,
                                    ),
                                  ),
                                ),
                                const Icon(Icons.arrow_drop_down),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Submit Button
                  const SizedBox(height: 24),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF0D47A1),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    onPressed: _isLoading ? null : _submitTrip,
                    child: _isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )                        : Text(
                            AppLocalizations.of(context).submitTrip,
                            style: TextStyle(fontSize: 16, color: Colors.white),
                          ),
                  ),
                ],
              ),
            ),
    );
  }

  @override
  void dispose() {
    // Remove listener before disposing
    _customToDestinationController.removeListener(_updateReturnFromDestination);

    _dateController.dispose();
    _timeController.dispose();
    _returnDateController.dispose();
    _returnTimeController.dispose();
    _requestorNameController.dispose();
    _customFromDestinationController.dispose();
    _customToDestinationController.dispose();
    _customReturnFromDestinationController.dispose();
    _customReturnToDestinationController.dispose();
    _waitingHoursController.dispose();
    _waitingMinutesController.dispose();
    _notesController.dispose();
    super.dispose();
  }
}














