import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../generated/l10n/app_localizations.dart';
import '../main.dart';

class LanguageSelectionScreen extends StatefulWidget {
  const LanguageSelectionScreen({super.key});

  @override
  State<LanguageSelectionScreen> createState() => _LanguageSelectionScreenState();
}

class _LanguageSelectionScreenState extends State<LanguageSelectionScreen> {
  Locale? _selectedLocale;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadCurrentLocale();
  }
  Future<void> _loadCurrentLocale() async {
    final localeNotifier = Provider.of<LocaleNotifier>(context, listen: false);
    setState(() {
      _selectedLocale = localeNotifier.locale;
      _isLoading = false;
    });
  }

  String _getLanguageName(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'English';
      case 'id':
        return 'Bahasa Indonesia';
      default:
        return languageCode.toUpperCase();
    }
  }

  Future<void> _saveLanguageAndRestart(Locale locale) async {
    setState(() => _isLoading = true);
    
    try {
      // Save the selected locale using LocaleNotifier
      final localeNotifier = Provider.of<LocaleNotifier>(context, listen: false);
      await localeNotifier.setLocale(locale);
      
      // Show success message
      if (mounted) {
        final l10n = AppLocalizations.of(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(l10n.languageChanged),
            backgroundColor: Colors.green,
          ),
        );
        
        // Navigate back to settings
        Navigator.of(context).pop(true); // Return true to indicate language was changed
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error changing language: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
    
    if (mounted) {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.selectLanguage),
        backgroundColor: const Color(0xFF0D47A1),
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    l10n.chooseLanguage,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 20),                  Expanded(
                    child: ListView.builder(
                      itemCount: AppLocalizations.supportedLocales.length,
                      itemBuilder: (context, index) {
                        final locale = AppLocalizations.supportedLocales.elementAt(index);
                        final languageName = _getLanguageName(locale.languageCode);
                        final isSelected = _selectedLocale?.languageCode == locale.languageCode;
                        
                        return Card(
                          margin: const EdgeInsets.symmetric(vertical: 4),
                          child: ListTile(
                            title: Text(
                              languageName,
                              style: TextStyle(
                                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                              ),
                            ),
                            subtitle: Text('Code: ${locale.languageCode}'),
                            leading: Radio<String>(
                              value: locale.languageCode,
                              groupValue: _selectedLocale?.languageCode,
                              onChanged: (value) {
                                if (value != null) {
                                  final newLocale = Locale(value);
                                  setState(() => _selectedLocale = newLocale);
                                }
                              },
                              activeColor: const Color(0xFF0D47A1),
                            ),
                            trailing: isSelected
                                ? const Icon(
                                    Icons.check_circle,
                                    color: Color(0xFF0D47A1),
                                  )
                                : null,
                            onTap: () {
                              final newLocale = Locale(locale.languageCode);
                              setState(() => _selectedLocale = newLocale);
                            },
                          ),
                        );
                      },
                    ),
                  ),
                  const SizedBox(height: 20),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _selectedLocale != null
                          ? () => _saveLanguageAndRestart(_selectedLocale!)
                          : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF0D47A1),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: Text(
                        l10n.saveLanguage,
                        style: const TextStyle(fontSize: 16),
                      ),
                    ),
                  ),
                  const SizedBox(height: 10),
                  Text(
                    l10n.languageChangeNote,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ),
            ),
    );
  }
}



