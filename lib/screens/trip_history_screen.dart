import 'dart:convert';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../generated/l10n/app_localizations.dart';
import '../models/passenger.dart';
import '../models/trip.dart';
import '../models/trip_cargo.dart';
import '../services/api_service.dart';
import '../services/excel_export_service.dart';
import '../sidebar.dart';
// Import web download utility only for web
import '../utils/web_download_util.dart' if (dart.library.io) '../utils/web_download_util_stub.dart';
import '../widgets/common_app_bar.dart';
import '../widgets/mobile_bottom_nav.dart';

class TripHistoryScreen extends StatefulWidget {
  const TripHistoryScreen({super.key});

  @override
  State<TripHistoryScreen> createState() => _TripHistoryScreenState();
}

class _TripHistoryScreenState extends State<TripHistoryScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  // Current index for bottom navigation
  int _currentIndex = 3; // Set to 3 for History tab
  List<Trip> _trips = [];
  List<Trip> _filteredTrips = [];
  bool _isLoading = false;  String _errorMessage = '';
  String _universalSearchQuery = '';
  DateTime? _selectedDate;
  String? _selectedStatus;
  final TextEditingController _dateController = TextEditingController();
  final TextEditingController _universalSearchController = TextEditingController();

  // Enhanced date filtering variables
  String _dateFilterType = 'specific'; // 'specific', 'daily', 'monthly', 'yearly'
  int? _selectedYear;
  int? _selectedMonth;
  DateTime? _selectedSpecificDate;
  final TextEditingController _yearController = TextEditingController();
  final TextEditingController _monthController = TextEditingController();
  final TextEditingController _specificDateController = TextEditingController();

  // Cache for driver names to avoid repeated API calls
  final Map<String, String> _driverNameCache = {};
  // Pagination variables
  int _currentPage = 1;
  int _itemsPerPage = 10; // Default to 10 items per page
  int _totalPages = 1;

  // Options for items per page
  final List<int> _itemsPerPageOptions = [5, 10, 25, 50, 100];

  // Export flag to prevent multiple simultaneous exports
  bool _isExporting = false;

  // List of all possible trip statuses
  final List<String> _statuses = [
    'REQUEST',
    'EDIT ON REVIEW',
    'CANCEL ON REVIEW',
    'DRIVER REJECTED',
    'ASSIGN TO DRIVER',
    'ASSIGN TO ONLINE TAXI',
    'DRIVER CONFIRMATION',
    'TRIP IN PROGRESS',
    'WAITING FOR RATING',
    'COMPLETED',
    'DELETED'
  ];

  @override
  void initState() {
    super.initState();
    _fetchTrips();
  }
  @override
  void dispose() {
    _dateController.dispose();
    _universalSearchController.dispose();
    _yearController.dispose();
    _monthController.dispose();
    _specificDateController.dispose();
    super.dispose();
  }



  Future<void> _fetchTrips() async {
    if (!mounted) return;

    // Clear the driver name cache to ensure we get fresh data
    _driverNameCache.clear();

    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      // Fetch all trips without any status filter
      // This ensures we get COMPLETED, DELETED, and WAITING FOR RATING trips
      final response = await ApiService.get('trips');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['trips'] != null) {
          final List<dynamic> tripsJson = data['trips'];

          // Parse all trips
          final List<Trip> allTrips = tripsJson.map((json) => Trip.fromJson(json)).toList();

          // Use all trips without filtering by status
          final allTripsList = allTrips.toList();

          // Sort trips by date (newest first)
          allTripsList.sort((a, b) {
            // Parse the dates for comparison
            final aDate = DateFormat('yyyy-MM-dd').parse(a.originalDate);
            final bDate = DateFormat('yyyy-MM-dd').parse(b.originalDate);

            // Sort by date (descending)
            final dateComparison = bDate.compareTo(aDate);
            if (dateComparison != 0) {
              return dateComparison;
            }

            // If dates are the same, sort by time
            // Use a helper function to safely parse time strings
            final DateTime aTime = _safeParseTime(a.time);
            final DateTime bTime = _safeParseTime(b.time);
            return bTime.compareTo(aTime);
          });

          // Pre-load driver names for all trips to ensure search works properly
          for (final trip in allTripsList) {
            if (trip.driverName != null && trip.driverName != 'Unassigned') {
              _driverNameCache[trip.tripCode] = trip.driverName!;
            } else {
              // Start fetching driver names in the background
              _fetchDriverName(trip);
            }
          }

          if (mounted) {
            setState(() {
              _trips = allTripsList;
              _applyFilters();
              _isLoading = false;
            });
          }
        } else {
          if (mounted) {
            setState(() {
              _trips = [];
              _filteredTrips = [];
              _isLoading = false;
            });
          }
        }
      } else {
        if (mounted) {
          setState(() {
            _errorMessage = 'Failed to load trips: ${response.statusCode}';
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Error: $e';
          _isLoading = false;
        });
      }
    }
  }
  void _applyFilters() {
    setState(() {
      _filteredTrips = _trips.where((trip) {
        // Apply enhanced date filter based on selected type
        bool matchesDate = true;
        if (_dateFilterType == 'specific' && _selectedSpecificDate != null) {
          final tripDate = DateFormat('yyyy-MM-dd').parse(trip.originalDate);
          final selectedDate = DateFormat('yyyy-MM-dd').format(_selectedSpecificDate!);
          matchesDate = DateFormat('yyyy-MM-dd').format(tripDate) == selectedDate;
        } else if (_dateFilterType == 'daily' && _selectedSpecificDate != null) {
          final tripDate = DateFormat('yyyy-MM-dd').parse(trip.originalDate);
          final selectedDate = DateFormat('yyyy-MM-dd').format(_selectedSpecificDate!);
          matchesDate = DateFormat('yyyy-MM-dd').format(tripDate) == selectedDate;
        } else if (_dateFilterType == 'monthly' && _selectedYear != null && _selectedMonth != null) {
          final tripDate = DateFormat('yyyy-MM-dd').parse(trip.originalDate);
          matchesDate = tripDate.year == _selectedYear && tripDate.month == _selectedMonth;
        } else if (_dateFilterType == 'yearly' && _selectedYear != null) {
          final tripDate = DateFormat('yyyy-MM-dd').parse(trip.originalDate);
          matchesDate = tripDate.year == _selectedYear;
        }
        // Legacy support for the old _selectedDate approach
        else if (_selectedDate != null) {
          final tripDate = DateFormat('yyyy-MM-dd').parse(trip.originalDate);
          final selectedDate = DateFormat('yyyy-MM-dd').format(_selectedDate!);
          matchesDate = DateFormat('yyyy-MM-dd').format(tripDate) == selectedDate;
        }

        // Apply universal search filter
        bool matchesSearch = true;
        if (_universalSearchQuery.isNotEmpty) {
          final query = _universalSearchQuery.toLowerCase();

          // Get the driver name from cache if available
          String driverNameToSearch = '';
          if (_driverNameCache.containsKey(trip.tripCode)) {
            driverNameToSearch = _driverNameCache[trip.tripCode]!.toLowerCase();
          } else if (trip.driverName != null) {
            driverNameToSearch = trip.driverName!.toLowerCase();
          }

          matchesSearch = trip.tripCode.toLowerCase().contains(query) ||
                          trip.requestorName.toLowerCase().contains(query) ||
                          trip.fromDestination.toLowerCase().contains(query) ||
                          trip.toDestination.toLowerCase().contains(query) ||
                          driverNameToSearch.contains(query);
        }

        // Apply status filter
        bool matchesStatus = true;
        if (_selectedStatus != null) {
          matchesStatus = trip.status == _selectedStatus;
        }

        return matchesDate && matchesSearch && matchesStatus;
      }).toList();

      // Reset to first page when filters change
      _currentPage = 1;

      // Calculate total pages
      _totalPages = (_filteredTrips.length / _itemsPerPage).ceil();    if (_totalPages < 1) _totalPages = 1;
    });
  }

  void _clearStatusFilter() {
    setState(() {
      _selectedStatus = null;
      _applyFilters();
    });
  }

  // Get paginated trips for the current page
  List<Trip> _getPaginatedTrips() {
    if (_filteredTrips.isEmpty) return [];

    final int startIndex = (_currentPage - 1) * _itemsPerPage;
    final int endIndex = startIndex + _itemsPerPage > _filteredTrips.length
        ? _filteredTrips.length
        : startIndex + _itemsPerPage;

    if (startIndex >= _filteredTrips.length) return [];

    return _filteredTrips.sublist(startIndex, endIndex);
  }

  // Helper method to safely parse time strings in various formats
  DateTime _safeParseTime(String timeStr) {
    // Create a base DateTime to use for parsing (just using today's date)
    final baseDate = DateTime.now();

    try {
      // Try HH:mm:ss format first
      return DateFormat('HH:mm:ss').parse(timeStr);
    } catch (e) {
      try {
        // Then try HH:mm format
        return DateFormat('HH:mm').parse(timeStr);
      } catch (e) {
        try {
          // Try h:mm a format (12-hour with AM/PM)
          return DateFormat('h:mm a').parse(timeStr);
        } catch (e) {
          // If all parsing fails, return a default time
          print('Failed to parse time: $timeStr');
          return DateTime(baseDate.year, baseDate.month, baseDate.day, 0, 0);
        }
      }
    }
  }

  // Helper method to get color based on trip status
  MaterialColor _getStatusColor(String status) {
    switch (status) {
      case 'COMPLETED':
        return Colors.green;
      case 'WAITING FOR RATING':
        return Colors.blue;
      case 'DELETED':
        return Colors.red;
      case 'REQUEST':
        return Colors.amber;
      case 'EDIT ON REVIEW':
        return Colors.orange;
      case 'CANCEL ON REVIEW':
        return Colors.deepOrange;
      case 'DRIVER REJECTED':
        return Colors.red;
      case 'ASSIGN TO DRIVER':
        return Colors.lightGreen;
      case 'ASSIGN TO ONLINE TAXI':
        return Colors.deepPurple;
      case 'DRIVER CONFIRMATION':
        return Colors.indigo;
      case 'TRIP IN PROGRESS':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }

  // Get driver name for a trip
  String getDriverName(Trip trip) {
    // Check if we already have a cached value for this trip
    if (_driverNameCache.containsKey(trip.tripCode)) {
      return _driverNameCache[trip.tripCode]!;
    }

    // If the trip already has a driver name that's not "Unassigned", use it and cache it
    if (trip.driverName != null && trip.driverName != 'Unassigned') {
      _driverNameCache[trip.tripCode] = trip.driverName!;
      return trip.driverName!;
    }

    // If we don't have a cached value, start fetching it
    _fetchDriverName(trip);

    // Return a placeholder while we're fetching
    return AppLocalizations.of(context).loading;
  }

  // Fetch driver name from the API and update the cache
  Future<void> _fetchDriverName(Trip trip) async {
    // Check if widget is still mounted before making API call
    if (!mounted) return;

    try {
      // Fetch the driver information from the API
      final response = await ApiService.get('trips/code/${trip.tripCode}');

      // Check if widget is still mounted after async operation
      if (!mounted) return;

      String driverName = 'Unassigned';

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['driver_name'] != null && data['driver_name'] != 'Unassigned') {
          driverName = data['driver_name'];
        }
      }

      // Update the cache and trigger a rebuild
      if (mounted) {
        setState(() {
          _driverNameCache[trip.tripCode] = driverName;
          // Re-apply filters if there's a search query that might match driver names
          if (_universalSearchQuery.isNotEmpty) {
            _applyFilters();
          }
        });
      }
    } catch (e) {
      print('Error getting driver name for trip ${trip.tripCode}: $e');
      // Update the cache with the error state
      if (mounted) {
        setState(() {
          _driverNameCache[trip.tripCode] = 'Unassigned';
        });
      }
    }
  }  // Enhanced date selection methods
  Future<void> _selectSpecificDate(BuildContext context) async {
    if (!mounted) return;

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedSpecificDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );

    if (!mounted) return;    if (picked != null && picked != _selectedSpecificDate) {
      setState(() {
        _selectedSpecificDate = picked;
        _specificDateController.text = DateFormat('yyyy-MM-dd').format(picked);
        
        // Clear year and month when selecting specific date
        _selectedYear = null;
        _yearController.clear();
        _selectedMonth = null;
        _monthController.clear();
        
        _dateFilterType = 'specific';
        _applyFilters();
      });
    }
  }
  Future<void> _selectYear(BuildContext context) async {
    if (!mounted) return;

    final int currentYear = DateTime.now().year;
    final int initialYear = _selectedYear ?? currentYear;

    showDialog(
      context: context,
      builder: (BuildContext context) {        return AlertDialog(
          title: Text(AppLocalizations.of(context).selectYear),
          content: SizedBox(
            width: double.maxFinite,
            height: 300,
            child: YearPicker(
              firstDate: DateTime(2020),
              lastDate: DateTime(2030),
              selectedDate: DateTime(initialYear),
              onChanged: (DateTime dateTime) {
                Navigator.pop(context);                if (mounted) {
                  setState(() {
                    _selectedYear = dateTime.year;
                    _yearController.text = dateTime.year.toString();
                    
                    // Clear specific date when selecting year (for monthly/yearly reports)
                    _selectedSpecificDate = null;
                    _specificDateController.clear();
                    
                    // If no month is selected and no specific date is selected, default to yearly
                    if (_selectedMonth == null && _selectedSpecificDate == null) {
                      _dateFilterType = 'yearly';
                    }
                    // If month is already selected, keep it as monthly
                    else if (_selectedMonth != null) {
                      _dateFilterType = 'monthly';
                    }
                    
                    _applyFilters();
                  });
                }
              },
            ),
          ),
        );
      },
    );
  }

  Future<void> _selectMonth(BuildContext context) async {
    if (!mounted) return;

    final int currentYear = DateTime.now().year;
    final int selectedYear = _selectedYear ?? currentYear;

    showDialog(
      context: context,
      builder: (BuildContext context) {        return AlertDialog(
          title: Text(AppLocalizations.of(context).selectMonth),
          content: SizedBox(
            width: double.maxFinite,
            height: 400,
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                childAspectRatio: 2,
              ),
              itemCount: 12,
              itemBuilder: (context, index) {
                final month = index + 1;
                final monthName = DateFormat('MMMM').format(DateTime(selectedYear, month));
                final isSelected = _selectedMonth == month;

                return Padding(
                  padding: const EdgeInsets.all(4.0),
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);                      if (mounted) {
                        setState(() {
                          _selectedMonth = month;
                          _monthController.text = monthName;
                          
                          // Clear specific date when selecting month (for monthly reports)
                          _selectedSpecificDate = null;
                          _specificDateController.clear();
                          
                          _dateFilterType = 'monthly';
                          _applyFilters();
                        });
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: isSelected ? const Color(0xFF0D47A1) : null,
                      foregroundColor: isSelected ? Colors.white : null,
                    ),
                    child: Text(
                      monthName,
                      style: const TextStyle(fontSize: 12),
                    ),
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  String _getDateFilterDisplayText() {
    switch (_dateFilterType) {
      case 'yearly':
        return _selectedYear != null ? _selectedYear.toString() : '';
      case 'monthly':
        if (_selectedYear != null && _selectedMonth != null) {
          return DateFormat('MMMM yyyy').format(DateTime(_selectedYear!, _selectedMonth!));
        }
        return '';
      case 'daily':
      case 'specific':
        return _selectedSpecificDate != null 
            ? DateFormat('yyyy-MM-dd').format(_selectedSpecificDate!) 
            : '';
      default:
        return '';
    }
  }
  Future<void> _exportToExcel() async {
    print('Export function called, _isExporting: $_isExporting');
    
    if (!mounted) return;

    // Prevent multiple simultaneous exports
    if (_isExporting) {
      print('Export already in progress, returning early');
      return;
    }    if (_filteredTrips.isEmpty) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).noTripsToExport),
            backgroundColor: Colors.orange,
          ),
        );
      }
      return;
    }

    setState(() {
      _isExporting = true;
    });

    try {
      // Show loading dialog
      if (mounted) {        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return AlertDialog(
              content: Row(
                children: [
                  const CircularProgressIndicator(),
                  const SizedBox(width: 20),
                  Text(AppLocalizations.of(context).exportingToExcel),
                ],
              ),
            );
          },
        );
      }

      if (kIsWeb) {
        print('Generating Excel file for web download');
        // For web, generate file and trigger download
        final bytes = ExcelExportService.getExcelBytes(_filteredTrips);
        if (bytes != null) {
          final timestamp = DateFormat('yyyy-MM-dd_HH-mm-ss').format(DateTime.now());
          final fileName = 'trip_history_$timestamp.xlsx';
          
          print('Calling WebDownloadUtil.downloadExcel with filename: $fileName');
          // Use web download utility
          WebDownloadUtil.downloadExcel(bytes, fileName);
            if (mounted) {
            Navigator.of(context).pop(); // Close loading dialog
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(AppLocalizations.of(context).tripHistoryExported(fileName)),
                backgroundColor: Colors.green,
              ),
            );
          }
        }
      } else {
        // For mobile/desktop
        final filePath = await ExcelExportService.exportTripsToExcel(_filteredTrips);
          if (mounted) {
          Navigator.of(context).pop(); // Close loading dialog
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.of(context).tripHistoryExportedTo(filePath)),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 5),
            ),
          );
        }
      }
    } catch (e) {
      print('Export error: $e');
      if (mounted) {        Navigator.of(context).pop(); // Close loading dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).exportFailed(e.toString())),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      // Reset the export flag
      print('Resetting _isExporting flag');
      if (mounted) {
        setState(() {
          _isExporting = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Check if we're on a small screen (mobile)
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600 && !kIsWeb;

    return Scaffold(
      key: _scaffoldKey,      appBar: CommonAppBar(
        title: AppLocalizations.of(context).tripHistory,
        onRefresh: _fetchTrips,
        showMenuIcon: true,
        additionalActions: [
          IconButton(
            icon: _isExporting 
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.file_download),
            tooltip: _isExporting ? AppLocalizations.of(context).exporting : AppLocalizations.of(context).exportToExcel,
            onPressed: _isExporting ? null : _exportToExcel,
          ),
        ],
      ),
      drawer: const SidebarNavigation(),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: isSmallScreen                // Mobile layout - each filter on its own row
                ? Column(
                    children: [
                      // Universal Search - Full width for mobile
                      TextField(
                        controller: _universalSearchController,
                        decoration: InputDecoration(
                          labelText: AppLocalizations.of(context).searchTripsLong,
                          prefixIcon: const Icon(Icons.search),
                          suffixIcon: _universalSearchQuery.isNotEmpty
                              ? IconButton(
                                  icon: const Icon(Icons.clear),
                                  onPressed: () {
                                    setState(() {
                                      _universalSearchController.clear();
                                      _universalSearchQuery = '';
                                      _applyFilters();
                                    });
                                  },
                                )
                              : null,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        onChanged: (value) {
                          setState(() {
                            _universalSearchQuery = value;
                            _applyFilters();
                          });
                        },
                      ),                      const SizedBox(height: 8),
                        // Date Filter Row - Mobile (Single Line)
                      Row(
                        children: [
                          // Specific Date Selector
                          Expanded(
                            child: TextField(
                              controller: _specificDateController,
                              readOnly: true,
                              enabled: _dateFilterType != 'monthly' && _dateFilterType != 'yearly',
                              decoration: InputDecoration(
                                labelText: AppLocalizations.of(context).specificDate,
                                prefixIcon: const Icon(Icons.calendar_today),
                                suffixIcon: _selectedSpecificDate != null
                                    ? IconButton(
                                        icon: const Icon(Icons.clear),
                                        onPressed: () {
                                          setState(() {
                                            _selectedSpecificDate = null;
                                            _specificDateController.clear();
                                            if (_dateFilterType == 'specific') {
                                              _applyFilters();
                                            }
                                          });
                                        },
                                      )
                                    : null,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                fillColor: _dateFilterType == 'specific' ? Colors.blue.shade50 : null,
                                filled: _dateFilterType == 'specific',
                              ),
                              onTap: (_dateFilterType != 'monthly' && _dateFilterType != 'yearly') ? () {
                                setState(() {
                                  _dateFilterType = 'specific';
                                });
                                _selectSpecificDate(context);
                              } : null,
                            ),
                          ),
                          const SizedBox(width: 8),
                          // Year Selector
                          Expanded(
                            child: TextField(
                              controller: _yearController,
                              readOnly: true,
                              decoration: InputDecoration(
                                labelText: AppLocalizations.of(context).year,
                                prefixIcon: const Icon(Icons.date_range),
                                suffixIcon: _selectedYear != null
                                    ? IconButton(
                                        icon: const Icon(Icons.clear),
                                        onPressed: () {
                                          setState(() {
                                            _selectedYear = null;
                                            _yearController.clear();
                                            _selectedMonth = null;
                                            _monthController.clear();
                                            // Reset filter type to allow specific date selection
                                            _dateFilterType = 'specific';
                                            _applyFilters();
                                          });
                                        },
                                      )
                                    : null,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                fillColor: (_dateFilterType == 'monthly' || _dateFilterType == 'yearly') ? Colors.blue.shade50 : null,
                                filled: (_dateFilterType == 'monthly' || _dateFilterType == 'yearly'),
                              ),
                              onTap: () => _selectYear(context),
                            ),
                          ),
                          const SizedBox(width: 8),
                          // Month Selector
                          Expanded(
                            child: TextField(
                              controller: _monthController,
                              readOnly: true,
                              decoration: InputDecoration(
                                labelText: AppLocalizations.of(context).month,
                                prefixIcon: const Icon(Icons.calendar_month),
                                enabled: _selectedYear != null,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                fillColor: _dateFilterType == 'monthly' ? Colors.blue.shade50 : null,
                                filled: _dateFilterType == 'monthly',
                              ),
                              onTap: _selectedYear != null ? () {
                                setState(() {
                                  _dateFilterType = 'monthly';
                                });
                                _selectMonth(context);
                              } : null,
                            ),
                          ),
                        ],
                      ),
                      
                      // Display current filter summary
                      if (_getDateFilterDisplayText().isNotEmpty) ...[
                        const SizedBox(height: 8),
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.blue.shade50,
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: Text(
                            '${AppLocalizations.of(context).currentFilter}: ${_getDateFilterDisplayText()}',
                            style: const TextStyle(
                              fontWeight: FontWeight.w500,
                              color: Colors.blue,
                            ),
                          ),
                        ),
                      ],
                      const SizedBox(height: 8),// Filter by Status - Full width for mobile
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(horizontal: 12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<String>(
                            isExpanded: true,
                            hint: Text(AppLocalizations.of(context).filterByStatus),
                            value: _selectedStatus,
                            icon: const Icon(Icons.arrow_drop_down),
                            iconSize: 24,
                            elevation: 16,
                            style: const TextStyle(color: Colors.black),
                            onChanged: (String? newValue) {
                              setState(() {
                                _selectedStatus = newValue;
                                _applyFilters();
                              });
                            },
                            items: _statuses
                                .map<DropdownMenuItem<String>>((String value) {
                              return DropdownMenuItem<String>(
                                value: value,
                                child: Text(value),
                              );
                            }).toList(),
                          ),
                        ),
                      ),

                      // Clear Status Filter button
                      if (_selectedStatus != null)
                        Padding(
                          padding: const EdgeInsets.only(top: 8.0),
                          child: SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              onPressed: _clearStatusFilter,
                              icon: const Icon(Icons.clear),                              label: Text(AppLocalizations.of(context).clearStatusFilter),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.red.shade100,
                                foregroundColor: Colors.red.shade800,
                              ),
                            ),
                          ),
                        ),
                    ],
                  )
                // Desktop layout - column-based filters
                : Column(
                    children: [                      // Universal Search
                      TextField(
                        controller: _universalSearchController,
                        decoration: InputDecoration(
                          labelText: AppLocalizations.of(context).searchTripsLong,
                          prefixIcon: const Icon(Icons.search),
                          suffixIcon: _universalSearchQuery.isNotEmpty
                              ? IconButton(
                                  icon: const Icon(Icons.clear),
                                  onPressed: () {
                                    setState(() {
                                      _universalSearchController.clear();
                                      _universalSearchQuery = '';
                                      _applyFilters();
                                    });
                                  },
                                )
                              : null,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        onChanged: (value) {
                          setState(() {
                            _universalSearchQuery = value;
                            _applyFilters();
                          });
                        },
                      ),                      const SizedBox(height: 12),                      // Date Filter Row - Desktop (Single Line)
                      Row(
                        children: [
                          // Specific Date Selector
                          Expanded(
                            child: TextField(
                              controller: _specificDateController,
                              readOnly: true,
                              enabled: _dateFilterType != 'monthly' && _dateFilterType != 'yearly',
                              decoration: InputDecoration(
                                labelText: AppLocalizations.of(context).specificDate,
                                prefixIcon: const Icon(Icons.calendar_today),
                                suffixIcon: _selectedSpecificDate != null
                                    ? IconButton(
                                        icon: const Icon(Icons.clear),
                                        onPressed: () {
                                          setState(() {
                                            _selectedSpecificDate = null;
                                            _specificDateController.clear();
                                            if (_dateFilterType == 'specific') {
                                              _applyFilters();
                                            }
                                          });
                                        },
                                      )
                                    : null,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                fillColor: _dateFilterType == 'specific' ? Colors.blue.shade50 : null,
                                filled: _dateFilterType == 'specific',
                              ),
                              onTap: (_dateFilterType != 'monthly' && _dateFilterType != 'yearly') ? () {
                                setState(() {
                                  _dateFilterType = 'specific';
                                });
                                _selectSpecificDate(context);
                              } : null,
                            ),
                          ),
                          const SizedBox(width: 16),
                          // Year Selector
                          Expanded(
                            child: TextField(
                              controller: _yearController,
                              readOnly: true,
                              decoration: InputDecoration(
                                labelText: AppLocalizations.of(context).year,
                                prefixIcon: const Icon(Icons.date_range),
                                suffixIcon: _selectedYear != null
                                    ? IconButton(
                                        icon: const Icon(Icons.clear),
                                        onPressed: () {
                                          setState(() {
                                            _selectedYear = null;
                                            _yearController.clear();
                                            _selectedMonth = null;
                                            _monthController.clear();
                                            // Reset filter type to allow specific date selection
                                            _dateFilterType = 'specific';
                                            _applyFilters();
                                          });
                                        },
                                      )
                                    : null,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                fillColor: (_dateFilterType == 'monthly' || _dateFilterType == 'yearly') ? Colors.blue.shade50 : null,
                                filled: (_dateFilterType == 'monthly' || _dateFilterType == 'yearly'),
                              ),
                              onTap: () => _selectYear(context),
                            ),
                          ),
                          const SizedBox(width: 16),
                          // Month Selector
                          Expanded(
                            child: TextField(
                              controller: _monthController,
                              readOnly: true,
                              decoration: InputDecoration(
                                labelText: AppLocalizations.of(context).month,
                                prefixIcon: const Icon(Icons.calendar_month),
                                enabled: _selectedYear != null,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                fillColor: _dateFilterType == 'monthly' ? Colors.blue.shade50 : null,
                                filled: _dateFilterType == 'monthly',
                              ),
                              onTap: _selectedYear != null ? () {
                                setState(() {
                                  _dateFilterType = 'monthly';
                                });
                                _selectMonth(context);
                              } : null,
                            ),
                          ),
                        ],
                      ),
                      
                      // Display current filter summary
                      if (_getDateFilterDisplayText().isNotEmpty) ...[
                        const SizedBox(height: 12),
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.blue.shade50,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.filter_alt, color: Colors.blue.shade700, size: 20),
                              const SizedBox(width: 8),
                              Text(
                                '${AppLocalizations.of(context).currentFilter}: ${_getDateFilterDisplayText()}',
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  color: Colors.blue.shade700,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                      const SizedBox(height: 12),

                      // Filter by Status
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(horizontal: 12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(10),
                        ),                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<String>(
                            isExpanded: true,
                            hint: Text(AppLocalizations.of(context).filterByStatus),
                            value: _selectedStatus,
                            icon: const Icon(Icons.arrow_drop_down),
                            iconSize: 24,
                            elevation: 16,
                            style: const TextStyle(color: Colors.black),
                            onChanged: (String? newValue) {
                              setState(() {
                                _selectedStatus = newValue;
                                _applyFilters();
                              });
                            },
                            items: _statuses
                                .map<DropdownMenuItem<String>>((String value) {
                              return DropdownMenuItem<String>(
                                value: value,
                                child: Text(value),
                              );
                            }).toList(),
                          ),
                        ),
                      ),

                      // Clear Status Filter button
                      if (_selectedStatus != null)
                        Padding(
                          padding: const EdgeInsets.only(top: 8.0),
                          child: SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              onPressed: _clearStatusFilter,
                              icon: const Icon(Icons.clear),                              label: Text(AppLocalizations.of(context).clearStatusFilter),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.red.shade100,
                                foregroundColor: Colors.red.shade800,
                              ),
                            ),
                          ),
                        ),
              ],
            ),
          ),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _errorMessage.isNotEmpty
                    ? Center(child: Text(_errorMessage))
                    : _filteredTrips.isEmpty                        ? Center(
                            child: Text(
                              _universalSearchQuery.isEmpty && _selectedDate == null
                                  ? AppLocalizations.of(context).noTripHistoryFound
                                  : AppLocalizations.of(context).noMatchingTripsFound,
                            ),
                          )
                        : isSmallScreen
                            // Mobile layout - vertical list of cards
                            ? RefreshIndicator(
                                onRefresh: () async {
                                  await _fetchTrips();
                                },
                                child: ListView.builder(
                                  padding: const EdgeInsets.all(8.0),
                                  // For mobile view, we'll show all items without pagination
                                  itemCount: _filteredTrips.length,
                                  itemBuilder: (context, index) {
                                    return _buildTripCard(_filteredTrips[index]);
                                  },
                                ),
                              )
                            // Desktop layout - data table with pagination
                            : Column(
                                children: [
                                  Expanded(
                                    child: SingleChildScrollView(
                                      child: Padding(
                                        padding: const EdgeInsets.all(16.0),
                                        child: Theme(
                                          data: Theme.of(context).copyWith(
                                            dataTableTheme: DataTableThemeData(
                                              columnSpacing: 16,
                                              horizontalMargin: 16,
                                              headingTextStyle: const TextStyle(
                                                fontWeight: FontWeight.bold,
                                                color: Color(0xFF0D47A1),
                                              ),
                                            ),
                                          ),
                                          child: DataTable(
                                            showCheckboxColumn: false,
                                            headingRowColor: WidgetStateProperty.all(
                                              const Color(0xFF0D47A1).withAlpha(25),
                                            ),                                            columns: [
                                              DataColumn(label: Text(AppLocalizations.of(context).tripCodeColumn)),
                                              DataColumn(label: Text(AppLocalizations.of(context).requestorColumn)),
                                              DataColumn(label: Text(AppLocalizations.of(context).fromColumn)),
                                              DataColumn(label: Text(AppLocalizations.of(context).toColumn)),
                                              DataColumn(label: Text(AppLocalizations.of(context).dateColumn)),
                                              DataColumn(label: Text(AppLocalizations.of(context).timeColumn)),
                                              DataColumn(label: Text(AppLocalizations.of(context).driverColumn)),
                                              DataColumn(label: Text(AppLocalizations.of(context).statusColumn)),
                                              DataColumn(label: Text(AppLocalizations.of(context).ratingColumn)),
                                            ],
                                            rows: _getPaginatedTrips().map((trip) {
                                              return DataRow(
                                                onSelectChanged: (_) => _showTripDetails(trip),
                                                cells: [
                                                  DataCell(Text(trip.tripCode)),
                                                  DataCell(Text(trip.requestorName)),
                                                  DataCell(Text(trip.fromDestination)),
                                                  DataCell(Text(trip.toDestination)),
                                                  DataCell(Text(trip.date)),
                                                  DataCell(Text(trip.time)),
                                                  DataCell(Text(getDriverName(trip))),
                                                  DataCell(
                                                    Container(
                                                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                                      decoration: BoxDecoration(
                                                        color: _getStatusColor(trip.status).withAlpha(50),
                                                        borderRadius: BorderRadius.circular(4),
                                                      ),
                                                      child: Text(
                                                        trip.status,
                                                        style: TextStyle(
                                                          color: _getStatusColor(trip.status).shade800,
                                                          fontWeight: FontWeight.bold,
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  DataCell(
                                                    trip.rating != null
                                                        ? Row(
                                                            children: List.generate(
                                                              trip.rating!,
                                                              (index) => const Icon(
                                                                Icons.star,
                                                                color: Colors.amber,
                                                                size: 18,
                                                              ),
                                                            ),
                                                          )
                                                        : Text(AppLocalizations.of(context).notRated),
                                                  ),
                                                ],
                                              );
                                            }).toList(),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  // Pagination controls
                                  Padding(
                                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        // Items per page dropdown
                                        Container(
                                          padding: const EdgeInsets.symmetric(horizontal: 12),
                                          decoration: BoxDecoration(
                                            border: Border.all(color: Colors.grey),
                                            borderRadius: BorderRadius.circular(10),
                                          ),                                          child: DropdownButtonHideUnderline(
                                            child: DropdownButton<int>(
                                              value: _itemsPerPage,
                                              hint: Text(AppLocalizations.of(context).itemsPerPage),
                                              onChanged: (int? newValue) {
                                                if (newValue != null) {
                                                  setState(() {
                                                    _itemsPerPage = newValue;
                                                    // Recalculate total pages
                                                    _totalPages = (_filteredTrips.length / _itemsPerPage).ceil();
                                                    if (_totalPages < 1) _totalPages = 1;
                                                    // Reset to first page when changing items per page
                                                    _currentPage = 1;
                                                  });
                                                }
                                              },
                                              items: _itemsPerPageOptions.map<DropdownMenuItem<int>>((int value) {
                                                return DropdownMenuItem<int>(
                                                  value: value,
                                                  child: Text('$value ${AppLocalizations.of(context).itemsText}'),
                                                );
                                              }).toList(),
                                            ),
                                          ),
                                        ),
                                        const SizedBox(width: 16),
                                        ElevatedButton(
                                          onPressed: _currentPage > 1
                                              ? () {
                                                  setState(() {
                                                    _currentPage--;
                                                  });
                                                }
                                              : null,
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: const Color(0xFF0D47A1),
                                            foregroundColor: Colors.white,
                                            disabledBackgroundColor: Colors.grey.shade300,
                                          ),
                                          child: Text(AppLocalizations.of(context).previous),
                                        ),
                                        Padding(
                                          padding: const EdgeInsets.symmetric(horizontal: 16.0),                                          child: Text(
                                            AppLocalizations.of(context).pageOf(_currentPage, _totalPages),
                                            style: const TextStyle(fontWeight: FontWeight.bold),
                                          ),
                                        ),
                                        ElevatedButton(
                                          onPressed: _currentPage < _totalPages
                                              ? () {
                                                  setState(() {
                                                    _currentPage++;
                                                  });
                                                }
                                              : null,
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: const Color(0xFF0D47A1),
                                            foregroundColor: Colors.white,
                                            disabledBackgroundColor: Colors.grey.shade300,
                                          ),
                                          child: Text(AppLocalizations.of(context).next),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
          ),
        ],
      ),
      // Only show bottom navigation on mobile
      bottomNavigationBar: isSmallScreen
          ? MobileBottomNav(
              currentIndex: _currentIndex,
              onTap: _onBottomNavTap,
            )
          : null,
    );
  }
  void _showTripDetails(Trip trip) async {
    // Check if widget is still mounted
    if (!mounted) return;

    // Fetch trip details with passengers and cargos data
    Trip updatedTrip = trip;
    try {
      final response = await ApiService.get('trips/code/${trip.tripCode}');
      if (response.statusCode == 200) {
        final data = ApiService.parseResponse(response);
        
        // Parse passengers from API response
        List<Passenger>? passengers;
        if (data['passengers'] != null) {
          passengers = (data['passengers'] as List)
              .map((passengerJson) => Passenger.fromJson(passengerJson))
              .toList();
        }

        // Parse cargos from API response
        List<TripCargo>? cargos;
        if (data['cargos'] != null) {
          cargos = (data['cargos'] as List)
              .map((cargoJson) => TripCargo.fromJson(cargoJson))
              .toList();
        }

        // Create updated trip with passengers and cargos
        updatedTrip = Trip(
          tripId: trip.tripId,
          tripCode: trip.tripCode,
          fromDestination: trip.fromDestination,
          toDestination: trip.toDestination,
          date: trip.date,
          originalDate: trip.originalDate,
          time: trip.time,
          status: trip.status,
          requestorId: trip.requestorId,
          requestorName: trip.requestorName,
          driverId: trip.driverId,
          driverCode: trip.driverCode,
          driverName: trip.driverName,
          rating: trip.rating,
          comments: trip.comments,
          rejectionReason: trip.rejectionReason,
          completionNotes: trip.completionNotes,
          completionImagePath: trip.completionImagePath,
          totalDuration: trip.totalDuration,          notes: trip.notes,
          isWaiting: trip.isWaiting,
          passengers: passengers,
          cargos: cargos,
        );
      }
    } catch (e) {
      print('Error fetching trip details: $e');
      // Continue with original trip data if fetch fails
    }

    // Check if widget is still mounted before showing dialog
    if (!mounted) return;

    // Check if we're on a small screen (mobile)
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600 && !kIsWeb;

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 0,
          backgroundColor: Colors.transparent,
          // For mobile, make the dialog take up more screen space
          insetPadding: isSmallScreen
              ? const EdgeInsets.symmetric(horizontal: 16, vertical: 24)
              : const EdgeInsets.symmetric(horizontal: 40, vertical: 24),
          child: Container(
            width: isSmallScreen ? double.infinity : null,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(25),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [                    Text(
                      'Trip Details: ${updatedTrip.tripCode}',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF0D47A1),
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.of(dialogContext).pop(),
                    ),
                  ],
                ),                const Divider(),
                const SizedBox(height: 8),
                _buildDetailRow('Status', updatedTrip.status, _getStatusColor(updatedTrip.status)),
                _buildDetailRow('From', updatedTrip.fromDestination),
                _buildDetailRow('To', updatedTrip.toDestination),
                _buildDetailRow('Date', updatedTrip.date),
                _buildDetailRow('Time', updatedTrip.time),
                _buildDetailRow('Requestor', updatedTrip.requestorName),
                _buildDetailRow('Driver', getDriverName(updatedTrip)),
                if (updatedTrip.rating != null)
                  _buildRatingRow('Rating', updatedTrip.rating!),
                if (updatedTrip.comments != null && updatedTrip.comments!.isNotEmpty)
                  _buildDetailRow('Comments', updatedTrip.comments!),                // Completion Notes Section
                if (updatedTrip.completionNotes != null && updatedTrip.completionNotes!.isNotEmpty)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 16),
                      const Text(
                        'Completion Notes:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade100,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey.shade300),
                        ),
                        child: Text(updatedTrip.completionNotes!),
                      ),
                    ],
                  ),

                // Completion Image Section
                if (updatedTrip.completionImagePath != null && updatedTrip.completionImagePath!.isNotEmpty)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 16),
                      const Text(
                        'Completion Image:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      GestureDetector(
                        onTap: () {
                          // Show the completion image in a full-screen dialog
                          _showCompletionImageDialog(context, updatedTrip.completionImagePath!);
                        },
                        child: Container(
                          height: 200,
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: Colors.grey.shade200,
                            borderRadius: BorderRadius.circular(8),
                          ),                          child: _isBase64Image(updatedTrip.completionImagePath!)
                              ? (kIsWeb
                                  ? Image.network(
                                      updatedTrip.completionImagePath!,
                                      fit: BoxFit.contain,
                                      errorBuilder: (imgContext, error, stackTrace) {
                                        return Center(
                                          child: Column(
                                            mainAxisAlignment: MainAxisAlignment.center,
                                            children: [
                                              const Icon(Icons.broken_image, size: 40, color: Colors.grey),
                                              const SizedBox(height: 8),
                                              Text('Failed to load image: ${error.toString()}'),
                                            ],
                                          ),
                                        );
                                      },
                                    )
                                  : Image.memory(
                                      base64Decode(updatedTrip.completionImagePath!.split(',').last),
                                      fit: BoxFit.contain,
                                      errorBuilder: (imgContext, error, stackTrace) {
                                        return Center(
                                          child: Column(
                                            mainAxisAlignment: MainAxisAlignment.center,
                                            children: [
                                              const Icon(Icons.broken_image, size: 40, color: Colors.grey),
                                              const SizedBox(height: 8),
                                              Text('Failed to load image: ${error.toString()}'),
                                            ],
                                          ),
                                        );
                                      },
                                    ))
                              : (kIsWeb
                                  ? Image.network(
                                      ApiService.getImageUrl(updatedTrip.completionImagePath!.replaceFirst('./', '')),
                                      fit: BoxFit.contain,
                                      errorBuilder: (imgContext, error, stackTrace) {
                                        return Center(
                                          child: Column(
                                            mainAxisAlignment: MainAxisAlignment.center,
                                            children: [
                                              const Icon(Icons.broken_image, size: 40, color: Colors.grey),
                                              const SizedBox(height: 8),
                                              Text('Failed to load image: ${error.toString()}'),
                                              const SizedBox(height: 4),
                                              Text('Path: ${updatedTrip.completionImagePath}', style: const TextStyle(fontSize: 12)),
                                            ],
                                          ),
                                        );
                                      },
                                    )
                                  : Image.network(
                                      ApiService.getImageUrl(updatedTrip.completionImagePath!.replaceFirst('./', '')),
                                      fit: BoxFit.contain,
                                      errorBuilder: (imgContext, error, stackTrace) {
                                        return Center(
                                          child: Column(
                                            mainAxisAlignment: MainAxisAlignment.center,
                                            children: [
                                              const Icon(Icons.broken_image, size: 40, color: Colors.grey),
                                              const SizedBox(height: 8),
                                              Text('Failed to load image: ${error.toString()}'),
                                              const SizedBox(height: 4),
                                              Text('Path: ${updatedTrip.completionImagePath}', style: const TextStyle(fontSize: 12)),
                                            ],
                                          ),
                                        );
                                      },
                                    )),
                        ),
                      ),
                    ],
                  ),

                // Passengers Section
                if (updatedTrip.passengers != null && updatedTrip.passengers!.isNotEmpty)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 16),
                      const Text(
                        'Passengers:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.blue.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.blue.shade200),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: updatedTrip.passengers!.map((passenger) => Padding(
                            padding: const EdgeInsets.only(bottom: 4),
                            child: Text(
                              '• ${passenger.name}',
                              style: TextStyle(color: Colors.blue.shade800),
                            ),
                          )).toList(),
                        ),
                      ),
                    ],
                  ),

                // Cargo Section
                if (updatedTrip.cargos != null && updatedTrip.cargos!.isNotEmpty)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 16),
                      const Text(
                        'Cargo:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.orange.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.orange.shade200),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: updatedTrip.cargos!.map((cargo) => Padding(
                            padding: const EdgeInsets.only(bottom: 4),
                            child: Text(
                              '• ${cargo.name}',
                              style: TextStyle(color: Colors.orange.shade800),
                            ),
                          )).toList(),
                        ),
                      ),
                    ],
                  ),
              ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Show completion image dialog for viewing photos
  void _showCompletionImageDialog(BuildContext context, String imagePath) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          child: Container(
            constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                AppBar(
                  title: const Text('Trip Completion Photo'),
                  automaticallyImplyLeading: false,
                  actions: [
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ],
                ),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(16),                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: _isBase64Image(imagePath)
                          ? (kIsWeb
                              ? Image.network(
                                  imagePath,
                                  fit: BoxFit.contain,
                                  errorBuilder: (context, error, stackTrace) {
                                    return const Center(
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          Icon(Icons.broken_image, size: 64),
                                          Text('Failed to load image'),
                                        ],
                                      ),
                                    );
                                  },
                                )
                              : Image.memory(
                                  base64Decode(imagePath.split(',').last),
                                  fit: BoxFit.contain,
                                  errorBuilder: (context, error, stackTrace) {
                                    return const Center(
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          Icon(Icons.broken_image, size: 64),
                                          Text('Failed to load image'),
                                        ],
                                      ),
                                    );
                                  },
                                ))
                          : Image.network(
                              ApiService.getImageUrl(imagePath.replaceFirst('./', '')),
                              fit: BoxFit.contain,
                              errorBuilder: (context, error, stackTrace) {
                                return const Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(Icons.broken_image, size: 64),
                                      Text('Failed to load image'),
                                    ],
                                  ),
                                );
                              },                            ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildDetailRow(String label, String value, [MaterialColor? color]) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: color != null
                ? Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: color.withAlpha(50),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      value,
                      style: TextStyle(
                        color: color.shade800,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  )
                : Text(value),
          ),
        ],
      ),
    );
  }

  Widget _buildRatingRow(String label, int rating) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Row(
              children: List.generate(
                rating,
                (index) => const Icon(
                  Icons.star,
                  color: Colors.amber,
                  size: 18,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build a mobile-friendly trip card
  Widget _buildTripCard(Trip trip) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: InkWell(
        onTap: () => _showTripDetails(trip),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Trip code and status row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Trip: ${trip.tripCode}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: Color(0xFF0D47A1),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getStatusColor(trip.status).withAlpha(50),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      trip.status,
                      style: TextStyle(
                        color: _getStatusColor(trip.status).shade800,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              // From and To destinations
              Row(
                children: [
                  const Icon(Icons.location_on_outlined, size: 16, color: Colors.grey),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      'From: ${trip.fromDestination}',
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  const Icon(Icons.location_on, size: 16, color: Colors.grey),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      'To: ${trip.toDestination}',
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              // Date, time and requestor
              Row(
                children: [
                  const Icon(Icons.calendar_today, size: 16, color: Colors.grey),
                  const SizedBox(width: 4),
                  Text(
                    '${trip.date} at ${trip.time}',
                    style: const TextStyle(fontSize: 14),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  const Icon(Icons.person_outline, size: 16, color: Colors.grey),
                  const SizedBox(width: 4),
                  Text(
                    'Requestor: ${trip.requestorName}',
                    style: const TextStyle(fontSize: 14),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  const Icon(Icons.drive_eta_outlined, size: 16, color: Colors.grey),
                  const SizedBox(width: 4),
                  Text(
                    'Driver: ${getDriverName(trip)}',
                    style: const TextStyle(fontSize: 14),
                  ),
                ],
              ),              // Rating if available
              if (trip.rating != null) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Text('Rating: ', style: TextStyle(fontSize: 14)),
                    ...List.generate(
                      trip.rating!,
                      (index) => const Icon(
                        Icons.star,                        color: Colors.amber,
                        size: 16,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  // Handle bottom navigation tap
  void _onBottomNavTap(int index) {
    // Check if widget is still mounted before navigation
    if (!mounted) return;

    // Store navigator reference before potential async gap
    final navigator = Navigator.of(context);

    if (index == _currentIndex) return;

    switch (index) {
      case 0: // Home
        navigator.pushReplacementNamed('/requestor-dashboard');
        return;
      case 1: // Add Trip
        navigator.pushReplacementNamed('/add-trip');
        return;
      case 2: // My Trips
        navigator.pushReplacementNamed('/request-trip');
        return;
      case 3: // History
        // Already on this screen
        return;    }
  }

  // Helper method to check if image data is base64 encoded
  bool _isBase64Image(String imagePath) {
    return imagePath.startsWith('data:image/');
  }
}