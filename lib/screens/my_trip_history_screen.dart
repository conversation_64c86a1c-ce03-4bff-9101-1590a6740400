import 'dart:convert';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../generated/l10n/app_localizations.dart';
import '../main.dart';
import '../models/passenger.dart';
import '../models/trip.dart';
import '../models/trip_cargo.dart';
import '../services/api_service.dart';
import '../sidebar.dart';
import '../widgets/common_app_bar.dart';
import '../widgets/mobile_bottom_nav.dart';

class MyTripHistoryScreen extends StatefulWidget {
  const MyTripHistoryScreen({super.key});

  @override
  State<MyTripHistoryScreen> createState() => _MyTripHistoryScreenState();
}

class _MyTripHistoryScreenState extends State<MyTripHistoryScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  List<Trip> _trips = [];
  List<Trip> _filteredTrips = [];
  bool _isLoading = false;
  String _errorMessage = '';
  String _searchQuery = '';
  DateTime? _selectedDate;
  String? _selectedStatus;
  final TextEditingController _dateController = TextEditingController();

  // Cache for driver names to avoid repeated API calls
  final Map<String, String> _driverNameCache = {};

  // Pagination variables
  int _currentPage = 1;
  int _itemsPerPage = 10; // Default to 10 items per page
  int _totalPages = 1;

  // Options for items per page
  final List<int> _itemsPerPageOptions = [5, 10, 25, 50, 100];

  // Current index for bottom navigation
  int _currentIndex = 3; // Set to 3 for History tab

  // List of all possible trip statuses
  final List<String> _statuses = [
    'REQUEST',
    'EDIT ON REVIEW',
    'CANCEL ON REVIEW',
    'DRIVER REJECTED',
    'ASSIGN TO DRIVER',
    'ASSIGN TO ONLINE TAXI',
    'DRIVER CONFIRMATION',
    'TRIP IN PROGRESS',
    'WAITING FOR RATING',
    'COMPLETED',
    'DELETED'
  ];

  @override
  void initState() {
    super.initState();
    _fetchTrips();
  }



  @override
  void dispose() {
    _dateController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
        _dateController.text = DateFormat('E, dd-MMM-yy').format(picked);
        // Filter trips by selected date
        _filteredTrips = _trips.where((trip) {
          try {
            final tripDate = DateFormat('E, dd-MMM-yy').parse(trip.date);
            return tripDate.year == picked.year &&
                   tripDate.month == picked.month &&
                   tripDate.day == picked.day;
          } catch (e) {
            print('Error parsing date: ${trip.date}');
            return false;
          }
        }).toList();
      });
    }
  }

  void _clearDateFilter() {
    setState(() {
      _selectedDate = null;
      _dateController.clear();
      _applyFilters();
    });
  }

  // Handle bottom navigation tap
  void _onBottomNavTap(int index) {
    if (index == _currentIndex) {
      // Refresh if tapping the current tab
      _fetchTrips();
      return;
    }

    setState(() {
      _currentIndex = index;
    });

    // Navigate based on the selected index
    switch (index) {
      case 0: // Dashboard
        Navigator.pushReplacementNamed(context, '/requestor-dashboard');
      case 1: // Request Trip
        Navigator.pushReplacementNamed(context, '/request-trip');
      case 2: // Rate Trips
        Navigator.pushReplacementNamed(context, '/trip-rating');
      case 3: // History - already here
        break;
    }
  }

  // Build a status chip with appropriate color
  Widget _buildStatusChip(String status) {
    Color chipColor;
    Color textColor = Colors.white;

    switch (status) {
      case 'PENDING':
        chipColor = Colors.orange;
      case 'APPROVED':
        chipColor = Colors.green;
      case 'REJECTED':
        chipColor = Colors.red;
      case 'COMPLETED':
        chipColor = Colors.blue;
      case 'CANCELLED':
        chipColor = Colors.grey;
      case 'IN PROGRESS':
        chipColor = Colors.purple;
      case 'WAITING FOR RATING':
        chipColor = Colors.amber;
      default:
        chipColor = Colors.grey;
        textColor = Colors.black;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: chipColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        status,
        style: TextStyle(
          color: textColor,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  // Build a mobile-friendly trip card
  Widget _buildTripCard(Trip trip) {
    return InkWell(
      onTap: () => _showTripDetails(trip.tripCode),
      child: Card(
        elevation: 2,
        margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Trip code and status row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Trip: ${trip.tripCode}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: Color(0xFF0D47A1),
                    ),
                  ),
                  _buildStatusChip(trip.status),
                ],
              ),
              const Divider(),
              // Date and time
              Row(
                children: [
                  const Icon(Icons.calendar_today, color: Colors.blue, size: 18),
                  const SizedBox(width: 8),
                  Text(
                    '${trip.date} at ${trip.time}',
                    style: TextStyle(
                      color: Colors.grey[700],
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              // From and To
              Row(
                children: [
                  const Icon(Icons.location_on, color: Colors.red, size: 18),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'From: ${trip.fromDestination}',
                      style: const TextStyle(fontSize: 14),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  const Icon(Icons.location_on, color: Colors.green, size: 18),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'To: ${trip.toDestination}',
                      style: const TextStyle(fontSize: 14),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              // Driver
              Row(
                children: [
                  const Icon(Icons.person, color: Colors.blue, size: 18),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Driver: ${getDriverName(trip)}',
                      style: const TextStyle(fontSize: 14),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              // Passengers section
              if (trip.passengers?.isNotEmpty == true) ...[
                Row(
                  children: [
                    const Icon(Icons.groups, color: Colors.purple, size: 18),
                    const SizedBox(width: 8),
                    Text(
                      'Passengers: ${trip.passengers!.length}',
                      style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                ...trip.passengers!.take(3).map((passenger) => Padding(
                  padding: const EdgeInsets.only(left: 26, bottom: 2),
                  child: Text(
                    '• ${passenger.name}',
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    overflow: TextOverflow.ellipsis,
                  ),
                )),
                if (trip.passengers!.length > 3)
                  Padding(
                    padding: const EdgeInsets.only(left: 26),
                    child: Text(
                      '• ... and ${trip.passengers!.length - 3} more',
                      style: TextStyle(fontSize: 12, color: Colors.grey[600], fontStyle: FontStyle.italic),
                    ),
                  ),
                const SizedBox(height: 4),
              ],
              // Cargo section
              if (trip.cargos?.isNotEmpty == true) ...[
                Row(
                  children: [
                    const Icon(Icons.inventory, color: Colors.orange, size: 18),
                    const SizedBox(width: 8),
                    Text(
                      'Cargo: ${trip.cargos!.length} item${trip.cargos!.length > 1 ? 's' : ''}',
                      style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                ...trip.cargos!.take(3).map((cargo) => Padding(
                  padding: const EdgeInsets.only(left: 26, bottom: 2),
                  child: Text(
                    '• ${cargo.name}${cargo.code != null ? ' (${cargo.code})' : ''}',
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    overflow: TextOverflow.ellipsis,
                  ),
                )),
                if (trip.cargos!.length > 3)
                  Padding(
                    padding: const EdgeInsets.only(left: 26),
                    child: Text(
                      '• ... and ${trip.cargos!.length - 3} more',
                      style: TextStyle(fontSize: 12, color: Colors.grey[600], fontStyle: FontStyle.italic),
                    ),
                  ),
              ],
              // Show tap hint
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    'Tap for details',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[500],
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 12,
                    color: Colors.grey[500],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
  Future<void> _showTripDetails(String tripCode) async {
    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {          return AlertDialog(
            content: Row(
              children: [
                const CircularProgressIndicator(),
                const SizedBox(width: 20),
                Text(AppLocalizations.of(context).loadingTripDetails),
              ],
            ),
          );
        },
      );      // Fetch trip details
      final response = await ApiService.get('trips/code/$tripCode');
      
      // Close loading dialog
      if (mounted) {
        Navigator.of(context).pop();
      }if (response.statusCode == 200) {
        final data = ApiService.parseResponse(response);
        
        // Parse passengers from API response
        List<Passenger>? passengers;
        if (data['passengers'] != null) {
          passengers = (data['passengers'] as List)
              .map((passengerJson) => Passenger.fromJson(passengerJson))
              .toList();
        }

        // Parse cargos from API response
        List<TripCargo>? cargos;
        if (data['cargos'] != null) {
          cargos = (data['cargos'] as List)
              .map((cargoJson) => TripCargo.fromJson(cargoJson))
              .toList();
        }

        // Create trip with complete data including passengers and cargos
        final trip = Trip.fromJson(data);
        final updatedTrip = Trip(
          tripId: trip.tripId,
          tripCode: trip.tripCode,
          fromDestination: trip.fromDestination,
          toDestination: trip.toDestination,
          date: trip.date,
          originalDate: trip.originalDate,
          time: trip.time,
          status: trip.status,
          requestorId: trip.requestorId,
          requestorName: trip.requestorName,
          driverId: trip.driverId,
          driverCode: trip.driverCode,
          driverName: trip.driverName,
          rating: trip.rating,
          comments: trip.comments,
          rejectionReason: trip.rejectionReason,
          completionNotes: trip.completionNotes,
          completionImagePath: trip.completionImagePath,
          totalDuration: trip.totalDuration,          notes: trip.notes,
          isWaiting: trip.isWaiting,
          passengers: passengers,
          cargos: cargos,
        );        // Show trip details dialog
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Text('Trip Details - ${updatedTrip.tripCode}'),
              content: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Basic trip info
                    _buildDetailRow('From', updatedTrip.fromDestination),
                    _buildDetailRow('To', updatedTrip.toDestination),
                    _buildDetailRow('Date', updatedTrip.date),
                    _buildDetailRow('Time', updatedTrip.time),
                    _buildDetailRow('Driver', getDriverName(updatedTrip)),
                    _buildDetailRow('Status', updatedTrip.status),
                    
                    const SizedBox(height: 16),
                    
                    // Passengers section
                    if (updatedTrip.passengers?.isNotEmpty == true) ...[
                      const Text(
                        'Passengers:',
                        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                      ),
                      const SizedBox(height: 8),
                      ...updatedTrip.passengers!.map((passenger) => Padding(
                        padding: const EdgeInsets.only(left: 16, bottom: 4),
                        child: Row(
                          children: [
                            const Icon(Icons.person, size: 16, color: Colors.blue),
                            const SizedBox(width: 8),
                            Expanded(child: Text(passenger.name)),
                          ],
                        ),
                      )),
                      const SizedBox(height: 12),
                    ] else ...[
                      const Text('No passengers for this trip.'),
                      const SizedBox(height: 12),
                    ],
                    
                    // Cargo section
                    if (updatedTrip.cargos?.isNotEmpty == true) ...[
                      const Text(
                        'Cargo:',
                        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                      ),
                      const SizedBox(height: 8),
                      ...updatedTrip.cargos!.map((cargo) => Padding(
                        padding: const EdgeInsets.only(left: 16, bottom: 4),
                        child: Row(
                          children: [
                            const Icon(Icons.inventory, size: 16, color: Colors.orange),
                            const SizedBox(width: 8),
                            Expanded(child: Text(cargo.name)),
                          ],
                        ),
                      )),
                    ] else ...[
                      const Text('No cargo for this trip.'),
                    ],
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(AppLocalizations.of(context).close),
                ),
              ],
            );
          },
        );      } else {
        // Show error dialog
        try {
          final data = ApiService.parseResponse(response);
          showDialog(
            context: context,
            builder: (BuildContext context) {              return AlertDialog(
                title: Text(AppLocalizations.of(context).error),
                content: Text(data['message'] ?? AppLocalizations.of(context).failedToLoadTripDetails),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('OK'),
                  ),
                ],
              );
            },
          );
        } catch (e) {
          showDialog(
            context: context,
            builder: (BuildContext context) {              return AlertDialog(
                title: Text(AppLocalizations.of(context).error),
                content: Text(AppLocalizations.of(context).failedToLoadTripDetails),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('OK'),
                  ),
                ],
              );
            },
          );
        }
      }
    } catch (e) {
      // Close loading dialog if still open
      Navigator.of(context).pop();
      
      // Show error dialog
      showDialog(
        context: context,
        builder: (BuildContext context) {          return AlertDialog(
            title: Text(AppLocalizations.of(context).error),
            content: Text('${AppLocalizations.of(context).failedToLoadTripDetails}: $e'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          );
        },
      );
    }
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _clearStatusFilter() {
    setState(() {
      _selectedStatus = null;
      _applyFilters();
    });
  }

  // Get paginated trips for the current page
  List<Trip> _getPaginatedTrips() {
    if (_filteredTrips.isEmpty) return [];

    final int startIndex = (_currentPage - 1) * _itemsPerPage;
    final int endIndex = startIndex + _itemsPerPage > _filteredTrips.length
        ? _filteredTrips.length
        : startIndex + _itemsPerPage;

    if (startIndex >= _filteredTrips.length) return [];

    return _filteredTrips.sublist(startIndex, endIndex);
  }

  void _applyFilters() {
    setState(() {
      _filteredTrips = _trips.where((trip) {
        // Apply date filter if selected
        bool matchesDate = true;
        if (_selectedDate != null) {
          try {
            final tripDate = DateFormat('E, dd-MMM-yy').parse(trip.date);
            matchesDate = tripDate.year == _selectedDate!.year &&
                         tripDate.month == _selectedDate!.month &&
                         tripDate.day == _selectedDate!.day;
          } catch (e) {
            print('Error parsing date: ${trip.date}');
            matchesDate = false;
          }
        }

        // Apply search filter
        bool matchesSearch = true;
        if (_searchQuery.isNotEmpty) {
          final searchText = _searchQuery.toLowerCase();

          // Get the driver name from cache if available
          String driverNameToSearch = '';
          if (_driverNameCache.containsKey(trip.tripCode)) {
            driverNameToSearch = _driverNameCache[trip.tripCode]!.toLowerCase();
          } else if (trip.driverName != null) {
            driverNameToSearch = trip.driverName!.toLowerCase();
          }

          matchesSearch = trip.tripCode.toLowerCase().contains(searchText) ||
                         trip.fromDestination.toLowerCase().contains(searchText) ||
                         trip.toDestination.toLowerCase().contains(searchText) ||
                         trip.status.toLowerCase().contains(searchText) ||
                         trip.date.toLowerCase().contains(searchText) ||
                         trip.time.toLowerCase().contains(searchText) ||
                         driverNameToSearch.contains(searchText);
        }

        // Apply status filter
        bool matchesStatus = true;
        if (_selectedStatus != null) {
          matchesStatus = trip.status == _selectedStatus;
        }

        return matchesDate && matchesSearch && matchesStatus;
      }).toList();

      // Reset to first page when filters change
      _currentPage = 1;

      // Calculate total pages
      _totalPages = (_filteredTrips.length / _itemsPerPage).ceil();
      if (_totalPages < 1) _totalPages = 1;
    });
  }

  Future<void> _fetchTrips() async {
    // Clear the driver name cache to ensure we get fresh data
    _driverNameCache.clear();

    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      // Get the current user from the provider
      final appState = Provider.of<MyAppState>(context, listen: false);
      final currentUser = appState.currentUser;      if (currentUser == null) {
        setState(() {
          _errorMessage = AppLocalizations.of(context).youMustBeLoggedIn;
          _isLoading = false;
        });
        return;
      }

      // Debug: Print the current user info
      print('Current user: ${currentUser.name} (${currentUser.userId})');

      // Fetch all trips without any status filter
      // This ensures we get DELETED trips which might be filtered out by default
      final response = await ApiService.get('trips');

      if (response.statusCode == 200) {
        try {
          final Map<String, dynamic> data = jsonDecode(response.body);

          // Debug: Print raw trip data from server
          print('Raw trip data from server:');
          for (var tripJson in data['trips']) {
            print('Trip ${tripJson['trip_code']}: status=${tripJson['status']}');
          }

          // Parse all trips
          final allTrips = (data['trips'] as List)
              .map((json) => Trip.fromJson(json))
              .toList();

          // Filter trips to only show those requested by the current user
          // AND with status "COMPLETED" or "DELETED"
          final userTrips = allTrips.where((trip) {
            // First check if this trip belongs to the current user
            bool belongsToCurrentUser = false;
            if (trip.requestorId != null) {
              belongsToCurrentUser = trip.requestorId == currentUser.userId;
            } else {
              belongsToCurrentUser = trip.requestorName.toLowerCase() == currentUser.name.toLowerCase();
            }

            // Debug: Print trip ownership and status
            if (belongsToCurrentUser) {
              print('Trip ${trip.tripCode}: belongs to current user, status=${trip.status}');
            }

            // Include all trips that belong to the current user, regardless of status
            return belongsToCurrentUser;
          }).toList();

          // Debug: Print filtered trips
          print('Filtered trips:');
          for (var trip in userTrips) {
            print('Trip ${trip.tripCode}: status=${trip.status}');
          }

          // Sort trips by date (newest first)
          userTrips.sort((a, b) {
            try {
              final dateA = DateFormat('E, dd-MMM-yy').parse(a.date);
              final dateB = DateFormat('E, dd-MMM-yy').parse(b.date);
              return dateB.compareTo(dateA); // Newest first
            } catch (e) {
              return 0;
            }
          });

          // Debug: Print sorted trips
          print('Sorted trips:');
          for (var trip in userTrips) {
            print('Trip ${trip.tripCode}: date=${trip.date}, status=${trip.status}');
          }

          // Pre-load driver names for all trips to ensure search works properly
          for (final trip in userTrips) {
            if (trip.driverName != null && trip.driverName != 'Unassigned') {
              _driverNameCache[trip.tripCode] = trip.driverName!;
            } else {
              // Start fetching driver names in the background
              _fetchDriverName(trip);
            }
          }

          setState(() {
            _trips = userTrips;
            _applyFilters();
          });
        } catch (e) {
          print('JSON decode error: $e');
          setState(() {
            _errorMessage = 'Invalid response format. Server might be down.';
          });
        }
      } else {
        print('Non-200 response: ${response.statusCode}');
        setState(() {
          _errorMessage = 'Server error: ${response.statusCode}';
        });
      }
    } catch (e) {
      print('Network error: $e');
      setState(() {
        _errorMessage = 'Connection error. Is the server running?';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _filterTrips(String query) {
    setState(() {
      _searchQuery = query;
      _applyFilters();
    });
  }

  // Get appropriate status color
  Color _getStatusColor(String status) {
    switch (status) {
      case 'COMPLETED':
        return Colors.green;
      case 'WAITING FOR RATING':
        return Colors.blue;
      case 'DELETED':
        return Colors.red;
      case 'REQUEST':
        return Colors.amber;
      case 'EDIT ON REVIEW':
        return Colors.orange;
      case 'CANCEL ON REVIEW':
        return Colors.deepOrange;
      case 'DRIVER REJECTED':
        return Colors.red;
      case 'ASSIGN TO DRIVER':
        return Colors.lightGreen;
      case 'ASSIGN TO ONLINE TAXI':
        return Colors.deepPurple;
      case 'DRIVER CONFIRMATION':
        return Colors.indigo;
      case 'TRIP IN PROGRESS':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }

  // Get driver name for a trip
  String getDriverName(Trip trip) {
    // Check if we already have a cached value for this trip
    if (_driverNameCache.containsKey(trip.tripCode)) {
      return _driverNameCache[trip.tripCode]!;
    }

    // If the trip already has a driver name that's not "Unassigned", use it and cache it
    if (trip.driverName != null && trip.driverName != 'Unassigned') {
      _driverNameCache[trip.tripCode] = trip.driverName!;
      return trip.driverName!;
    }

    // If we don't have a cached value, start fetching it
    _fetchDriverName(trip);

    // Return a placeholder while we're fetching
    return AppLocalizations.of(context).loading;
  }

  // Fetch driver name from the API and update the cache
  Future<void> _fetchDriverName(Trip trip) async {
    try {
      // Fetch the driver information from the API
      final response = await ApiService.get('trips/code/${trip.tripCode}');

      String driverName = 'Unassigned';

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['driver_name'] != null && data['driver_name'] != 'Unassigned') {
          driverName = data['driver_name'];
        }
      }

      // Update the cache and trigger a rebuild
      if (mounted) {
        setState(() {
          _driverNameCache[trip.tripCode] = driverName;

          // Re-apply filters if there's a search query that might match driver names
          if (_searchQuery.isNotEmpty) {
            _applyFilters();
          }
        });
      }
    } catch (e) {
      print('Error getting driver name for trip ${trip.tripCode}: $e');
      // Update the cache with the error state
      if (mounted) {
        setState(() {
          _driverNameCache[trip.tripCode] = 'Unassigned';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Check if we're on a small screen (mobile)
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600 && !kIsWeb;

    return Scaffold(
      key: _scaffoldKey,
      drawer: const SidebarNavigation(),
      appBar: CommonAppBar(
        title: 'My Trip History',
        onRefresh: _fetchTrips,
        showMenuIcon: true,
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      flex: 2,                      child: TextField(
                        onChanged: _filterTrips,
                        decoration: InputDecoration(
                          labelText: AppLocalizations.of(context).search,
                          prefixIcon: const Icon(Icons.search),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      flex: 1,                      child: TextField(
                        controller: _dateController,
                        readOnly: true,
                        decoration: InputDecoration(
                          labelText: AppLocalizations.of(context).filterByDate,
                          prefixIcon: const Icon(Icons.calendar_today),
                          suffixIcon: _selectedDate != null
                              ? IconButton(
                                  icon: const Icon(Icons.clear),
                                  onPressed: _clearDateFilter,
                                )
                              : null,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        onTap: () => _selectDate(context),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      flex: 1,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<String>(
                            isExpanded: true,
                            hint: Text(AppLocalizations.of(context).filterByStatus),
                            value: _selectedStatus,
                            icon: const Icon(Icons.arrow_drop_down),
                            iconSize: 24,
                            elevation: 16,
                            style: const TextStyle(color: Colors.black),
                            onChanged: (String? newValue) {
                              setState(() {
                                _selectedStatus = newValue;
                                _applyFilters();
                              });
                            },
                            items: _statuses
                                .map<DropdownMenuItem<String>>((String value) {
                              return DropdownMenuItem<String>(
                                value: value,
                                child: Text(value),
                              );
                            }).toList(),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    if (_selectedStatus != null)
                      ElevatedButton.icon(
                        onPressed: _clearStatusFilter,
                        icon: const Icon(Icons.clear),
                        label: Text(AppLocalizations.of(context).clearStatusFilter),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red.shade100,
                          foregroundColor: Colors.red.shade800,
                        ),
                      ),
                    const Spacer(flex: 2),
                  ],
                ),
              ],
            ),
          ),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _errorMessage.isNotEmpty
                    ? Center(child: Text(_errorMessage))
                    : _filteredTrips.isEmpty
                        ? Center(
                            child: Text(
                              _searchQuery.isEmpty && _selectedDate == null
                                  ? 'No trip history found'
                                  : 'No matching trips found',
                            ),
                          )
                        : isSmallScreen
                            // Mobile layout - vertical list of cards
                            ? RefreshIndicator(
                                onRefresh: () async {
                                  await _fetchTrips();
                                },
                                child: ListView.builder(
                                  padding: const EdgeInsets.all(8.0),
                                  // For mobile view, we'll show all items without pagination
                                  itemCount: _filteredTrips.length,
                                  itemBuilder: (context, index) {
                                    return _buildTripCard(_filteredTrips[index]);
                                  },
                                ),
                              )
                            // Desktop layout - data table with pagination
                            : Column(
                                children: [
                                  Expanded(
                                    child: SingleChildScrollView(
                                      child: Padding(
                                        padding: const EdgeInsets.all(16.0),
                                        child: Theme(
                                          data: Theme.of(context).copyWith(
                                            dataTableTheme: DataTableThemeData(
                                              columnSpacing: 16,
                                              horizontalMargin: 16,
                                              headingTextStyle: const TextStyle(
                                                fontWeight: FontWeight.bold,
                                                color: Color(0xFF0D47A1),
                                              ),
                                            ),
                                          ),
                                          child: DataTable(
                                            headingRowColor: WidgetStateProperty.all(
                                              const Color(0xFF0D47A1).withAlpha(25),
                                            ),                                            columns: [
                                              DataColumn(label: Text(AppLocalizations.of(context).tripCode)),
                                              DataColumn(label: Text(AppLocalizations.of(context).from)),
                                              DataColumn(label: Text(AppLocalizations.of(context).to)),
                                              DataColumn(label: Text(AppLocalizations.of(context).date)),
                                              DataColumn(label: Text(AppLocalizations.of(context).time)),
                                              DataColumn(label: Text(AppLocalizations.of(context).driver)),
                                              DataColumn(label: Text(AppLocalizations.of(context).passengers)),
                                              DataColumn(label: Text(AppLocalizations.of(context).cargo)),
                                              DataColumn(label: Text(AppLocalizations.of(context).status)),
                                              DataColumn(label: Text(AppLocalizations.of(context).rating)),
                                            ],rows: _getPaginatedTrips().map((trip) {
                                              return DataRow(
                                                onSelectChanged: (bool? selected) {
                                                  if (selected == true) {
                                                    _showTripDetails(trip.tripCode);
                                                  }
                                                },
                                                cells: [
                                                  DataCell(Text(trip.tripCode)),
                                                  DataCell(Text(trip.fromDestination)),
                                                  DataCell(Text(trip.toDestination)),
                                                  DataCell(Text(trip.date)),
                                                  DataCell(Text(trip.time)),
                                                  DataCell(Text(getDriverName(trip))),                                                  DataCell(
                                                    Tooltip(
                                                      message: trip.passengers?.map((p) => '${p.name} (ID: ${p.id})').join('\n') ?? 'No passengers',
                                                      child: Text(
                                                        trip.passengers?.isNotEmpty == true 
                                                          ? '${trip.passengers!.length} passenger${trip.passengers!.length > 1 ? 's' : ''}'
                                                          : 'No passengers',
                                                        overflow: TextOverflow.ellipsis,
                                                      ),
                                                    ),
                                                  ),
                                                  DataCell(
                                                    Tooltip(
                                                      message: trip.cargos?.map((c) => '${c.name}${c.code != null ? ' (${c.code})' : ''}').join('\n') ?? 'No cargo',
                                                      child: Text(
                                                        trip.cargos?.isNotEmpty == true 
                                                          ? '${trip.cargos!.length} item${trip.cargos!.length > 1 ? 's' : ''}'
                                                          : 'No cargo',
                                                        overflow: TextOverflow.ellipsis,
                                                      ),
                                                    ),
                                                  ),
                                                  DataCell(
                                                    Container(
                                                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                                      decoration: BoxDecoration(
                                                        color: _getStatusColor(trip.status).withAlpha(50),
                                                        borderRadius: BorderRadius.circular(4),
                                                        border: Border.all(
                                                          color: _getStatusColor(trip.status),
                                                          width: 1,
                                                        ),
                                                      ),
                                                      child: Text(
                                                        trip.status,
                                                        style: TextStyle(
                                                          color: _getStatusColor(trip.status),
                                                          fontWeight: FontWeight.bold,
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  DataCell(
                                                    trip.rating != null
                                                        ? Row(
                                                            children: List.generate(
                                                              5,
                                                              (index) => Icon(
                                                                index < trip.rating! ? Icons.star : Icons.star_border,
                                                                color: index < trip.rating! ? Colors.amber : Colors.grey,
                                                                size: 16,
                                                              ),
                                                            ),
                                                          )
                                                        : Text(AppLocalizations.of(context).notRated, style: const TextStyle(fontStyle: FontStyle.italic, color: Colors.grey)),
                                                  ),
                                                ],
                                              );
                                            }).toList(),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  // Pagination controls
                                  Padding(
                                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        // Items per page dropdown
                                        Container(
                                          padding: const EdgeInsets.symmetric(horizontal: 12),
                                          decoration: BoxDecoration(
                                            border: Border.all(color: Colors.grey),
                                            borderRadius: BorderRadius.circular(10),
                                          ),
                                          child: DropdownButtonHideUnderline(
                                            child: DropdownButton<int>(
                                              value: _itemsPerPage,
                                              hint: Text(AppLocalizations.of(context).itemsPerPage),
                                              onChanged: (int? newValue) {
                                                if (newValue != null) {
                                                  setState(() {
                                                    _itemsPerPage = newValue;
                                                    // Recalculate total pages
                                                    _totalPages = (_filteredTrips.length / _itemsPerPage).ceil();
                                                    if (_totalPages < 1) _totalPages = 1;
                                                    // Reset to first page when changing items per page
                                                    _currentPage = 1;
                                                  });
                                                }
                                              },
                                              items: _itemsPerPageOptions.map<DropdownMenuItem<int>>((int value) {
                                                return DropdownMenuItem<int>(
                                                  value: value,
                                                  child: Text('$value items'),
                                                );
                                              }).toList(),
                                            ),
                                          ),
                                        ),
                                        const SizedBox(width: 16),
                                        ElevatedButton(
                                          onPressed: _currentPage > 1
                                              ? () {
                                                  setState(() {
                                                    _currentPage--;
                                                  });
                                                }
                                              : null,
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: const Color(0xFF0D47A1),
                                            foregroundColor: Colors.white,
                                            disabledBackgroundColor: Colors.grey.shade300,
                                          ),
                                          child: Text(AppLocalizations.of(context).previous),
                                        ),
                                        Padding(
                                          padding: const EdgeInsets.symmetric(horizontal: 16.0),
                                          child: Text(
                                            'Page $_currentPage of $_totalPages',
                                            style: const TextStyle(fontWeight: FontWeight.bold),
                                          ),
                                        ),
                                        ElevatedButton(
                                          onPressed: _currentPage < _totalPages
                                              ? () {
                                                  setState(() {
                                                    _currentPage++;
                                                  });
                                                }
                                              : null,
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: const Color(0xFF0D47A1),
                                            foregroundColor: Colors.white,
                                            disabledBackgroundColor: Colors.grey.shade300,
                                          ),
                                          child: Text(AppLocalizations.of(context).next),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),

          ),
        ],
      ),
      // Only show bottom navigation on mobile
      bottomNavigationBar: isSmallScreen
          ? MobileBottomNav(
              currentIndex: _currentIndex,
              onTap: _onBottomNavTap,
            )
          : null,
    );
  }
}



