import 'dart:convert';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../generated/l10n/app_localizations.dart';
import '../main.dart';
import '../models/passenger.dart';
import '../models/trip.dart';
import '../models/trip_cargo.dart';
import '../services/api_service.dart';
import '../sidebar.dart';
import '../utils/auto_refresh.dart';
import '../widgets/common_app_bar.dart';
import '../widgets/mobile_bottom_nav.dart';
import 'add_trip_screen.dart';
import 'edit_trip_screen.dart';
import 'rating_screen.dart';

class RequestTripScreen extends StatefulWidget {
  const RequestTripScreen({super.key});

  @override
  State<RequestTripScreen> createState() => _RequestTripScreenState();
}

class _RequestTripScreenState extends State<RequestTripScreen> with AutoRefreshMixin {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  List<Trip> _trips = [];
  List<Trip> _filteredTrips = [];
  bool _isLoading = false;

  // Current index for bottom navigation
  int _currentIndex = 1; // Set to 1 for Request Trip tab
  String _errorMessage = '';
  String _searchQuery = '';
  DateTime? _selectedDate;
  final TextEditingController _dateController = TextEditingController();
  final TextEditingController _timeController = TextEditingController();

  // Cache for driver names to avoid repeated API calls
  final Map<String, String> _driverNameCache = {};

  @override
  void initState() {
    super.initState();
    _fetchTrips();

    // Initialize auto-refresh functionality
    initAutoRefresh();
  }
  // Implement the refreshData method required by AutoRefreshMixin
  @override
  Future<void> refreshData({bool showSnackbar = true}) async {
    // Clear the driver name cache to ensure we get fresh data
    _driverNameCache.clear();

    await _fetchTrips();

    if (showSnackbar && mounted) {
      showRefreshSnackbar(AppLocalizations.of(context).tripDataRefreshedSuccessfully);
    }
  }

  @override
  void dispose() {
    _dateController.dispose();
    _timeController.dispose();

    // Clean up auto-refresh timers
    disposeAutoRefresh();

    super.dispose();
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
        _dateController.text = DateFormat('E, dd-MMM-yy').format(picked);
        // Filter trips by selected date
        _filteredTrips = _trips.where((trip) {
          try {
            final tripDate = DateFormat('E, dd-MMM-yy').parse(trip.date);
            return tripDate.year == picked.year &&
                   tripDate.month == picked.month &&
                   tripDate.day == picked.day;
          } catch (e) {
            // If date parsing fails, exclude from results
            return false;
          }
        }).toList();
      });
    }
  }

  void _clearDateFilter() {
    setState(() {
      _selectedDate = null;
      _dateController.clear();
      _filteredTrips = _trips;
      _filterTrips(_searchQuery); // Apply any existing text search
    });
  }

  Future<void> _fetchTrips() async {
    // Clear the driver name cache to ensure we get fresh data
    _driverNameCache.clear();

    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      // Get the current user from the provider
      final appState = Provider.of<MyAppState>(context, listen: false);
      final currentUser = appState.currentUser;      if (currentUser == null) {
        setState(() {
          _errorMessage = AppLocalizations.of(context).youMustBeLoggedIn;
          _isLoading = false;
        });
        return;
      }

      // Fetch all trips
      final response = await ApiService.get('trips');

      // Process the response
      if (response.statusCode == 200) {
        try {
          final Map<String, dynamic> data = jsonDecode(response.body);

          // Debug: Print the raw response data
          print('Raw trips response: ${response.body}');

          // Process trip data from server

          // Parse all trips
          final allTrips = (data['trips'] as List)
              .map((json) => Trip.fromJson(json))
              .toList();

          // Filter trips by the current user

          // Filter trips to only show those requested by the current user
          // AND exclude trips with status "WAITING FOR RATING", "COMPLETED", and "DELETED"
          // (WAITING FOR RATING trips are shown in Trip Rating screen, COMPLETED and DELETED in Trip History)
          final userTrips = allTrips.where((trip) {
            // First check if this trip belongs to the current user
            bool belongsToCurrentUser = false;

            // First try to match by requestor_id (most reliable)
            if (trip.requestorId != null) {
              belongsToCurrentUser = trip.requestorId == currentUser.userId;
            } else if (trip.requestorName != 'Unknown') {
              // Fall back to matching by name if ID is not available
              belongsToCurrentUser = trip.requestorName.toLowerCase() == currentUser.name.toLowerCase();
            }

            // Only include trips that belong to the current user AND are NOT in excluded statuses
            return belongsToCurrentUser &&
                   trip.status != 'WAITING FOR RATING' &&
                   trip.status != 'COMPLETED' &&
                   trip.status != 'DELETED';
          }).toList();

          // Debug: Print all trips with their driver IDs
          print('User trips:');
          for (final trip in userTrips) {
            print('Trip ${trip.tripCode}: driverId=${trip.driverId}, driverCode=${trip.driverCode}, driverName=${trip.driverName}');
          }

          // Update trips with driver information
          final List<Trip> updatedTrips = [];

          // Fetch driver information for each trip directly
          for (final trip in userTrips) {
            print('Processing trip ${trip.tripCode}');

            try {
              // Fetch trip details with driver information
              final tripResponse = await ApiService.get('trips/code/${trip.tripCode}');

              if (tripResponse.statusCode == 200) {
                final tripData = jsonDecode(tripResponse.body);
                print('Trip data for ${trip.tripCode}: ${tripData.toString()}');

                // Check if driver information is available
                // Create a new trip with the driver information from the API
                final updatedTrip = Trip(
                  tripId: trip.tripId,
                  tripCode: trip.tripCode,
                  fromDestination: trip.fromDestination,
                  toDestination: trip.toDestination,
                  date: trip.date,
                  originalDate: trip.originalDate,
                  time: trip.time,
                  status: trip.status,
                  requestorId: trip.requestorId,
                  requestorName: trip.requestorName,
                  driverId: tripData['driver_id'],
                  driverCode: tripData['driver_code'],
                  driverName: tripData['driver_name'] ?? AppLocalizations.of(context).unassigned,
                  rating: trip.rating,
                  comments: trip.comments,
                  rejectionReason: trip.rejectionReason,
                  completionNotes: trip.completionNotes,                  completionImagePath: trip.completionImagePath,
                  totalDuration: trip.totalDuration,
                  isWaiting: trip.isWaiting,
                );

                print('Updated trip ${updatedTrip.tripCode} with driver: ${updatedTrip.driverName}');
                print('Driver data in trip: driverId=${updatedTrip.driverId}, driverCode=${updatedTrip.driverCode}, driverName=${updatedTrip.driverName}');
                updatedTrips.add(updatedTrip);
              } else {
                // Failed to fetch trip details
                print('Failed to fetch trip details for ${trip.tripCode}: ${tripResponse.statusCode}');
                updatedTrips.add(trip);
              }
            } catch (e) {
              // Error fetching trip details
              print('Error fetching trip details for ${trip.tripCode}: $e');
              updatedTrips.add(trip);
            }
          }

          // Debug: Print all updated trips before setting state
          print('Final updated trips:');
          for (final trip in updatedTrips) {
            print('Trip ${trip.tripCode}: driverId=${trip.driverId}, driverCode=${trip.driverCode}, driverName=${trip.driverName}');
          }

          // Set the filtered trips
          setState(() {
            _trips = updatedTrips;
            _filteredTrips = updatedTrips;
          });

          // Debug: Print all trips after setting state
          print('Trips after setting state:');
          for (final trip in _filteredTrips) {
            print('Trip ${trip.tripCode}: driverId=${trip.driverId}, driverCode=${trip.driverCode}, driverName=${trip.driverName}');
          }
        } catch (e) {
          print('Error processing trips: $e');
          setState(() {
            _errorMessage = 'Invalid response format. Server might be down.';
          });
        }
      } else {
        setState(() {
          _errorMessage = 'Server error: ${response.statusCode}';
        });
      }
    } catch (e) {
      print('Error fetching trips: $e');
      setState(() {
        _errorMessage = 'Connection error. Is the server running?';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _filterTrips(String query) {
    setState(() {
      _searchQuery = query;
      _filteredTrips = _trips.where((trip) {
        // Simple text search across all text fields
        final searchText = query.toLowerCase();
        return trip.tripCode.toLowerCase().contains(searchText) ||
               trip.fromDestination.toLowerCase().contains(searchText) ||
               trip.toDestination.toLowerCase().contains(searchText) ||
               trip.status.toLowerCase().contains(searchText) ||
               trip.date.toLowerCase().contains(searchText) ||
               trip.time.toLowerCase().contains(searchText) ||
               (trip.driverName?.toLowerCase().contains(searchText) ?? false);
      }).toList();
    });
  }

  Future<void> _deleteTrip(Trip trip) async {
    try {      // Instead of deleting, update the trip status to 'CANCEL ON REVIEW'
      final response = await ApiService.put(
        'trips/code/${trip.tripCode}',
        {'status': 'CANCEL ON REVIEW'},
      );

      // Process the response

      if (response.statusCode == 200) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(            SnackBar(
              content: Text(AppLocalizations.of(context).tripCancellationRequestSubmitted),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 3),
            ),
          );
          _fetchTrips(); // Refresh the list
        }
      } else {
        if (mounted) {
          // Handle error response

          Map<String, dynamic> errorData;
          try {
            errorData = jsonDecode(response.body);
          } catch (e) {
            print('Error parsing response body: $e');
            errorData = {'error': 'Failed to parse error response'};
          }

          ScaffoldMessenger.of(context).showSnackBar(            SnackBar(
              content: Text(errorData['error'] ?? AppLocalizations.of(context).failedToSubmitCancellationRequest),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {      // Log the exception details
      print('Exception in _deleteTrip: $e');
      print('Stack trace: ${StackTrace.current}');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).anErrorOccurred(e.toString())),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showDeleteConfirmation(Trip trip) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(AppLocalizations.of(context).confirmCancellation),          content: Text(AppLocalizations.of(context).areYouSureCancelTrip(trip.tripCode)),
          actions: [
            TextButton(
              child: Text(AppLocalizations.of(context).cancel),
              onPressed: () => Navigator.of(context).pop(),
            ),
            TextButton(
              child: Text(
                AppLocalizations.of(context).requestCancellation,
                style: const TextStyle(color: Colors.red),
              ),
              onPressed: () {
                Navigator.of(context).pop();
                _deleteTrip(trip);
              },
            ),
          ],
        );
      },
    );
  }

  // Handle bottom navigation tap
  void _onBottomNavTap(int index) {
    if (index == _currentIndex) {
      // Refresh if tapping the current tab
      refreshData();
      return;
    }

    setState(() {
      _currentIndex = index;
    });

    // Navigate based on the selected index
    switch (index) {
      case 0: // Dashboard
        Navigator.pushReplacementNamed(context, '/requestor-dashboard');
      case 1: // Request Trip - already here
        break;
      case 2: // Rate Trips
        Navigator.pushReplacementNamed(context, '/trip-rating');
      case 3: // History
        Navigator.pushReplacementNamed(context, '/trip-history');
    }
  }

  // Build a status chip with appropriate color
  Widget _buildStatusChip(String status) {
    Color chipColor;
    Color textColor = Colors.white;    switch (status) {
      case 'PENDING':
        chipColor = Colors.orange;
      case 'APPROVED':
        chipColor = Colors.green;
      case 'REJECTED':
        chipColor = Colors.red;
      case 'DRIVER REJECTED':
        chipColor = Colors.red.shade700;
      case 'COMPLETED':
        chipColor = Colors.blue;
      case 'CANCELLED':
        chipColor = Colors.grey;
      case 'IN PROGRESS':
        chipColor = Colors.purple;
      case 'WAITING FOR RATING':
        chipColor = Colors.amber;
      case 'ASSIGN TO ONLINE TAXI':
        chipColor = Colors.teal;
      default:
        chipColor = Colors.grey;
        textColor = Colors.black;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: chipColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        status,
        style: TextStyle(
          color: textColor,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  // Build action buttons for a trip
  List<Widget> _buildActionButtons(Trip trip, [bool isMobile = false]) {
    final List<Widget> buttons = [];
    final double iconSize = isMobile ? 18.0 : 20.0;
    final double fontSize = isMobile ? 12.0 : 14.0;

    // Build action buttons based on trip status

    // View details button
    buttons.add(
      Padding(
        padding: EdgeInsets.only(right: isMobile ? 4.0 : 8.0),
        child: ElevatedButton.icon(
          icon: Icon(Icons.visibility, size: iconSize),
          label: Text(AppLocalizations.of(context).view, style: TextStyle(fontSize: fontSize)),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue.shade100,
            foregroundColor: Colors.blue.shade900,
            padding: EdgeInsets.symmetric(
              horizontal: isMobile ? 8.0 : 12.0,
              vertical: isMobile ? 4.0 : 8.0,
            ),
          ),          onPressed: () => _showTripDetails(trip),
        ),
      ),
    );

    // View Rejection Reason button if available and status is REQUEST
    if (trip.rejectionReason != null && trip.status == 'REQUEST') {
      buttons.add(
        Padding(
          padding: EdgeInsets.only(right: isMobile ? 4.0 : 8.0),
          child: ElevatedButton.icon(
            icon: Icon(Icons.info_outline, size: iconSize, color: Colors.red),
            label: Text(AppLocalizations.of(context).viewRejection, style: TextStyle(fontSize: fontSize)),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red.shade50,
              foregroundColor: Colors.red.shade900,
              padding: EdgeInsets.symmetric(
                horizontal: isMobile ? 8.0 : 12.0,
                vertical: isMobile ? 4.0 : 8.0,
              ),
            ),
            onPressed: () => _showRejectionReasonDialog(trip),
          ),
        ),
      );
    }

    // Edit button for REQUEST status trips
    if (trip.status == 'REQUEST') {
      buttons.add(
        Padding(
          padding: EdgeInsets.only(right: isMobile ? 4.0 : 8.0),
          child: ElevatedButton.icon(
            icon: Icon(Icons.edit, size: iconSize),
            label: Text(AppLocalizations.of(context).edit, style: TextStyle(fontSize: fontSize)),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green.shade100,
              foregroundColor: Colors.green.shade900,
              padding: EdgeInsets.symmetric(
                horizontal: isMobile ? 8.0 : 12.0,
                vertical: isMobile ? 4.0 : 8.0,
              ),
            ),
            onPressed: () async {
              // Navigate to edit trip screen to match web version
              final result = await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => EditTripScreen(trip: trip),
                ),
              );
              if (result == true) {
                _fetchTrips();
              }
            },
          ),
        ),
      );

      // Cancel button for pending trips
      buttons.add(
        ElevatedButton.icon(
          icon: Icon(Icons.cancel, size: iconSize),
          label: Text(AppLocalizations.of(context).cancel, style: TextStyle(fontSize: fontSize)),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.red.shade100,
            foregroundColor: Colors.red.shade900,
            padding: EdgeInsets.symmetric(
              horizontal: isMobile ? 8.0 : 12.0,
              vertical: isMobile ? 4.0 : 8.0,
            ),
          ),
          onPressed: () {
            // Show delete confirmation dialog to match web version
            _showDeleteConfirmation(trip);
          },
        ),
      );
    }

    // Finish Trip button for ASSIGN TO ONLINE TAXI status
    if (trip.status == 'ASSIGN TO ONLINE TAXI') {
      buttons.add(
        Padding(
          padding: EdgeInsets.only(right: isMobile ? 4.0 : 8.0),
          child: ElevatedButton.icon(
            icon: Icon(Icons.check_circle, size: iconSize),
            label: Text(AppLocalizations.of(context).finishTrip, style: TextStyle(fontSize: fontSize)),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green.shade100,
              foregroundColor: Colors.green.shade900,
              padding: EdgeInsets.symmetric(
                horizontal: isMobile ? 8.0 : 12.0,
                vertical: isMobile ? 4.0 : 8.0,
              ),
            ),
            onPressed: () => _showFinishTripConfirmation(trip),
          ),
        ),
      );
    }

    return buttons;
  }

  // Helper method to build detail rows for the trip details dialog
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  // Build a mobile-friendly trip card
  Widget _buildTripCard(Trip trip) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Trip code and status row
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [                Text(
                  '${AppLocalizations.of(context).tripColon} ${trip.tripCode}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Color(0xFF0D47A1),
                  ),
                ),
                _buildStatusChip(trip.status),
              ],
            ),
            const Divider(),
            // Date and time
            Row(
              children: [
                const Icon(Icons.calendar_today, color: Colors.blue, size: 18),
                const SizedBox(width: 8),
                Text(
                  '${trip.date} at ${trip.time}',
                  style: TextStyle(
                    color: Colors.grey[700],
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            // From and To
            Row(
              children: [
                const Icon(Icons.location_on, color: Colors.red, size: 18),
                const SizedBox(width: 8),
                Expanded(
                  child:                  Text(
                    '${AppLocalizations.of(context).fromColon} ${trip.fromDestination}',
                    style: const TextStyle(fontSize: 14),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                const Icon(Icons.location_on, color: Colors.green, size: 18),
                const SizedBox(width: 8),
                Expanded(
                  child:                  Text(
                    '${AppLocalizations.of(context).toColon} ${trip.toDestination}',
                    style: const TextStyle(fontSize: 14),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                const Icon(Icons.person, color: Colors.blue, size: 18),
                const SizedBox(width: 8),
                Expanded(
                  child:                  Text(
                    '${AppLocalizations.of(context).driverColon} ${getDriverName(trip)}',
                    style: const TextStyle(fontSize: 14),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: _buildActionButtons(trip, true),
            ),
          ],
        ),
      ),
    );
  }

  // Build responsive search and filter UI based on screen size
  Widget _buildResponsiveSearchFilters() {
    // Check if we're on a small screen (mobile portrait)
    final bool isSmallScreen = MediaQuery.of(context).size.width < 600;

    if (isSmallScreen && !kIsWeb) {
      // Use a Column layout for mobile portrait
      return Column(        children: [
          // Search field
          TextField(
            onChanged: _filterTrips,
            decoration: InputDecoration(
              labelText: AppLocalizations.of(context).searchLabel,
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          ),
          const SizedBox(height: 16),          // Date filter field
          TextField(
            controller: _dateController,
            readOnly: true,
            decoration: InputDecoration(
              labelText: AppLocalizations.of(context).filterByDateLabel,
              prefixIcon: const Icon(Icons.calendar_today),
              suffixIcon: _selectedDate != null
                ? IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: _clearDateFilter,
                  )
                : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            onTap: () => _selectDate(context),
          ),
        ],
      );
    } else {
      // Use a Row layout for web or landscape
      return Row(
        children: [
          // Search field
          Expanded(
            flex: 2,
            child: TextField(
              onChanged: _filterTrips,
              decoration: InputDecoration(
              labelText: AppLocalizations.of(context).searchLabel,
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          // Date filter field
          Expanded(
            flex: 1,
            child: TextField(
              controller: _dateController,
              readOnly: true,
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context).filterByDateLabel,
                prefixIcon: const Icon(Icons.calendar_today),
                suffixIcon: _selectedDate != null
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: _clearDateFilter,
                    )
                  : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              onTap: () => _selectDate(context),
            ),
          ),
        ],
      );
    }
  }
  // Get driver name for a trip
  String getDriverName(Trip trip) {
    // For ASSIGN TO ONLINE TAXI status, always return "Unassigned"
    if (trip.status == 'ASSIGN TO ONLINE TAXI') {
      return AppLocalizations.of(context).unassigned;
    }

    // Check if we already have a cached value for this trip
    if (_driverNameCache.containsKey(trip.tripCode)) {
      return _driverNameCache[trip.tripCode]!;
    }

    // If the trip already has a driver name that's not "Unassigned", use it and cache it
    if (trip.driverName != null && trip.driverName != AppLocalizations.of(context).unassigned) {
      _driverNameCache[trip.tripCode] = trip.driverName!;
      return trip.driverName!;
    }

    // If we don't have a cached value, start fetching it
    _fetchDriverName(trip);

    // Return a placeholder while we're fetching
    return AppLocalizations.of(context).loading;
  }
  // Fetch driver name from the API and update the cache
  Future<void> _fetchDriverName(Trip trip) async {
    // For ASSIGN TO ONLINE TAXI status, always set "Unassigned" in the cache
    if (trip.status == 'ASSIGN TO ONLINE TAXI') {
      if (mounted) {
        setState(() {
          _driverNameCache[trip.tripCode] = AppLocalizations.of(context).unassigned;
        });
      }
      return;
    }

    try {
      // Fetch the driver information from the API
      final response = await ApiService.get('trips/code/${trip.tripCode}');

      String driverName = AppLocalizations.of(context).unassigned;

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['driver_name'] != null && data['driver_name'] != AppLocalizations.of(context).unassigned) {
          driverName = data['driver_name'];
        }
      }

      // Update the cache and trigger a rebuild
      if (mounted) {
        setState(() {
          _driverNameCache[trip.tripCode] = driverName;
        });
      }    } catch (e) {
      print('Error getting driver name for trip ${trip.tripCode}: $e');
      // Update the cache with the error state
      if (mounted) {
        setState(() {
          _driverNameCache[trip.tripCode] = AppLocalizations.of(context).unassigned;
        });
      }
    }
  }

  void _showRejectionReasonDialog(Trip trip) {
    print('_showRejectionReasonDialog called for trip ${trip.tripCode}');
    print('Rejection reason: ${trip.rejectionReason}');

    showDialog(
      context: context,
      builder: (BuildContext context) {        return AlertDialog(
          title: Text(AppLocalizations.of(context).rejectionReason),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Trip Code: ${trip.tripCode}',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),              const SizedBox(height: 16),
              Text(
                AppLocalizations.of(context).tripRejectedMessage,
                style: const TextStyle(fontStyle: FontStyle.italic),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade300),
                ),                child: Text(
                  trip.rejectionReason ?? AppLocalizations.of(context).noReasonProvided,
                  style: const TextStyle(fontSize: 16),
                ),
              ),
            ],
          ),          actions: [
            TextButton(
              child: Text(AppLocalizations.of(context).close),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ],
        );
      },    );
  }
  // Show trip details with passengers and cargo information
  Future<void> _showTripDetails(Trip trip) async {
    try {
      // Fetch complete trip details with passengers and cargos
      final response = await ApiService.get('trips/code/${trip.tripCode}');
      if (response.statusCode == 200) {        final data = ApiService.parseResponse(response);
        
        // Parse the updated trip data
        final Trip updatedTrip = Trip.fromJson(data);
        
        // Parse passengers and cargos from the response
        List<Passenger>? passengers;
        if (data['passengers'] != null) {
          passengers = (data['passengers'] as List)
              .map((passengerJson) => Passenger.fromJson(passengerJson))
              .toList();
        }
        
        List<TripCargo>? cargos;
        if (data['cargos'] != null) {
          cargos = (data['cargos'] as List)
              .map((cargoJson) => TripCargo.fromJson(cargoJson))
              .toList();
        }
        
        if (!mounted) return;
          showDialog(
          context: context,
          builder: (context) => AlertDialog(            title: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Trip Details',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  updatedTrip.tripCode,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF0D47A1),
                  ),
                ),
              ],
            ),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [                  _buildDetailRow(AppLocalizations.of(context).fromHeader, updatedTrip.fromDestination),
                  _buildDetailRow(AppLocalizations.of(context).toHeader, updatedTrip.toDestination),
                  _buildDetailRow(AppLocalizations.of(context).dateHeader, updatedTrip.date),
                  _buildDetailRow(AppLocalizations.of(context).timeHeader, updatedTrip.time),
                  _buildDetailRow(AppLocalizations.of(context).statusHeader, updatedTrip.status),
                  _buildDetailRow(AppLocalizations.of(context).driverHeader, updatedTrip.status == 'ASSIGN TO ONLINE TAXI' ? AppLocalizations.of(context).unassigned : getDriverName(updatedTrip)),
                  _buildDetailRow(AppLocalizations.of(context).driverIdLabel, updatedTrip.status == 'ASSIGN TO ONLINE TAXI' ? AppLocalizations.of(context).noneLabel : (updatedTrip.driverId?.toString() ?? AppLocalizations.of(context).noneLabel)),
                  _buildDetailRow(AppLocalizations.of(context).driverCodeLabel, updatedTrip.status == 'ASSIGN TO ONLINE TAXI' ? AppLocalizations.of(context).noneLabel : (updatedTrip.driverCode ?? AppLocalizations.of(context).noneLabel)),
                  _buildDetailRow(AppLocalizations.of(context).rating, updatedTrip.rating != null ? '${updatedTrip.rating}/5' : AppLocalizations.of(context).notRated),                  if (updatedTrip.notes != null && updatedTrip.notes!.isNotEmpty)
                    _buildDetailRow(AppLocalizations.of(context).notesLabel, updatedTrip.notes!),
                  
                  // Passengers section
                  if (passengers != null && passengers.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.blue.shade200),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.person, color: Colors.blue.shade700, size: 18),
                              const SizedBox(width: 8),                              Text(
                                AppLocalizations.of(context).passengers,
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.blue.shade700,
                                  fontSize: 16,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          ...passengers.map((passenger) => Padding(
                            padding: const EdgeInsets.only(left: 16, bottom: 4),
                            child: Row(
                              children: [
                                Text('• ', style: TextStyle(color: Colors.blue.shade700)),
                                Expanded(child: Text(passenger.name)),
                              ],
                            ),
                          )),
                        ],
                      ),
                    ),
                  ],
                  
                  // Cargo section
                  if (cargos != null && cargos.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.orange.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.orange.shade200),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.inventory_2, color: Colors.orange.shade700, size: 18),
                              const SizedBox(width: 8),                              Text(
                                AppLocalizations.of(context).cargo,
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.orange.shade700,
                                  fontSize: 16,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          ...cargos.map((cargo) => Padding(
                            padding: const EdgeInsets.only(left: 16, bottom: 4),
                            child: Row(
                              children: [
                                Text('• ', style: TextStyle(color: Colors.orange.shade700)),
                                Expanded(child: Text(cargo.name)),
                              ],
                            ),
                          )),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(AppLocalizations.of(context).close),
              ),
            ],
          ),
        );
      } else {
        throw Exception('Failed to fetch trip details');
      }
    } catch (e) {
      // If API call fails, show dialog with original trip data (fallback)
      if (!mounted) return;
        showDialog(
        context: context,
        builder: (context) => AlertDialog(          title: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Trip Details',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                trip.tripCode,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF0D47A1),
                ),
              ),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [                _buildDetailRow(AppLocalizations.of(context).fromHeader, trip.fromDestination),
                _buildDetailRow(AppLocalizations.of(context).toHeader, trip.toDestination),
                _buildDetailRow(AppLocalizations.of(context).dateHeader, trip.date),
                _buildDetailRow(AppLocalizations.of(context).timeHeader, trip.time),
                _buildDetailRow(AppLocalizations.of(context).statusHeader, trip.status),
                _buildDetailRow(AppLocalizations.of(context).driverHeader, trip.status == 'ASSIGN TO ONLINE TAXI' ? AppLocalizations.of(context).unassigned : getDriverName(trip)),
                _buildDetailRow(AppLocalizations.of(context).driverIdLabel, trip.status == 'ASSIGN TO ONLINE TAXI' ? AppLocalizations.of(context).noneLabel : (trip.driverId?.toString() ?? AppLocalizations.of(context).noneLabel)),
                _buildDetailRow(AppLocalizations.of(context).driverCodeLabel, trip.status == 'ASSIGN TO ONLINE TAXI' ? AppLocalizations.of(context).noneLabel : (trip.driverCode ?? AppLocalizations.of(context).noneLabel)),
                _buildDetailRow(AppLocalizations.of(context).rating, trip.rating != null ? '${trip.rating}/5' : AppLocalizations.of(context).notRated),                if (trip.notes != null && trip.notes!.isNotEmpty)
                  _buildDetailRow(AppLocalizations.of(context).notesLabel, trip.notes!),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(AppLocalizations.of(context).close),
            ),
          ],
        ),
      );
    }
  }

  // Show confirmation dialog before finishing a trip
  void _showFinishTripConfirmation(Trip trip) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(AppLocalizations.of(context).confirmTripCompletion),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Trip Code: ${trip.tripCode}',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),              Text(
                AppLocalizations.of(context).areYouSureMarkTripCompleted,
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,                  children: [
                    Text('${AppLocalizations.of(context).fromColon} ${trip.fromDestination}'),
                    Text('${AppLocalizations.of(context).toColon} ${trip.toDestination}'),
                    Text('${AppLocalizations.of(context).dateColon} ${trip.date}'),
                    Text('${AppLocalizations.of(context).timeColon} ${trip.time}'),
                    if (trip.notes != null && trip.notes!.isNotEmpty)
                      Text('${AppLocalizations.of(context).notesColon} ${trip.notes}'),
                  ],
                ),
              ),
            ],
          ),
          actions: [            TextButton(
              child: Text(AppLocalizations.of(context).cancel),
              onPressed: () => Navigator.of(context).pop(),
            ),TextButton(
              child: Text(
                AppLocalizations.of(context).completeTrip,
                style: const TextStyle(color: Colors.green),
              ),
              onPressed: () {
                Navigator.of(context).pop();
                _finishTrip(trip);
              },
            ),
          ],
        );
      },
    );
  }

  // Update trip status to COMPLETED
  Future<void> _finishTrip(Trip trip) async {
    try {      // Update the trip status to 'COMPLETED'
      final response = await ApiService.put(
        'trips/code/${trip.tripCode}',
        {'status': 'COMPLETED'},
      );// Process the response
      if (response.statusCode == 200) {
        // Trip status updated successfully - notification to trip managers handled automatically by backend
        print('Trip ${trip.tripCode} status updated to COMPLETED successfully');
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.of(context).tripMarkedAsCompleted),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 3),
            ),
          );
          _fetchTrips(); // Refresh the list
        }
      } else {
        if (mounted) {
          // Handle error response
          Map<String, dynamic> errorData;
          try {
            errorData = jsonDecode(response.body);
          } catch (e) {
            print('Error parsing response body: $e');
            errorData = {'error': 'Failed to parse error response'};
          }

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(errorData['error'] ?? AppLocalizations.of(context).failedToCompleteTrip),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // Log the exception details      print('Exception in _finishTrip: $e');
      print('Stack trace: ${StackTrace.current}');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).anErrorOccurred(e.toString())),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Check if we're on a small screen (mobile)
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600 && !kIsWeb;

    return Scaffold(
      key: _scaffoldKey,
      drawer: const SidebarNavigation(),      appBar: CommonAppBar(
        title: AppLocalizations.of(context).requestTrip,
        onRefresh: () {
          setState(() {
            lastRefreshTime = DateTime.now();
          });
          refreshData();
        },
        showMenuIcon: true,
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: _buildResponsiveSearchFilters(),
          ),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _errorMessage.isNotEmpty
                    ? Center(child: Text(_errorMessage))
                    : _filteredTrips.isEmpty
                        ? Center(
                            child: Text(
                              _searchQuery.isEmpty && _selectedDate == null
                                  ? AppLocalizations.of(context).noTripsFound
                                  : AppLocalizations.of(context).noMatchingTripsFound,
                            ),
                          )
                        : isSmallScreen
                            // Mobile layout - vertical list of cards
                            ? RefreshIndicator(
                                onRefresh: () async {
                                  await _fetchTrips();
                                },
                                child: ListView.builder(
                                  padding: const EdgeInsets.all(8.0),
                                  itemCount: _filteredTrips.length,
                                  itemBuilder: (context, index) {
                                    return _buildTripCard(_filteredTrips[index]);
                                  },
                                ),
                              )
                            // Desktop layout - data table
                            : SingleChildScrollView(
                                child: Padding(
                                  padding: const EdgeInsets.all(16.0),
                                  child: Theme(
                                    data: Theme.of(context).copyWith(
                                      dataTableTheme: DataTableThemeData(
                                        columnSpacing: 16,
                                        horizontalMargin: 16,
                                        headingTextStyle: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                          color: Color(0xFF0D47A1),
                                        ),
                                      ),
                                    ),
                                    child: DataTable(
                                      headingRowColor: WidgetStateProperty.all(
                                        const Color(0xFF0D47A1).withAlpha(25),
                                      ),                                  columns: [
                                    DataColumn(label: Text(AppLocalizations.of(context).tripCodeHeader)),
                                    DataColumn(label: Text(AppLocalizations.of(context).fromHeader)),
                                    DataColumn(label: Text(AppLocalizations.of(context).toHeader)),
                                    DataColumn(label: Text(AppLocalizations.of(context).dateHeader)),
                                    DataColumn(label: Text(AppLocalizations.of(context).timeHeader)),
                                    DataColumn(label: Text(AppLocalizations.of(context).statusHeader)),
                                    DataColumn(label: Text(AppLocalizations.of(context).driverHeader)),
                                    DataColumn(label: Text(AppLocalizations.of(context).actionsHeader)),
                                  ],
                                  rows: _filteredTrips.map((trip) {
                                    return DataRow(
                                      cells: [
                                        DataCell(Text(trip.tripCode)),
                                        DataCell(Text(trip.fromDestination)),
                                        DataCell(Text(trip.toDestination)),
                                        DataCell(Text(trip.date)),
                                        DataCell(Text(trip.time)),
                                        DataCell(Text(trip.status)),
                                        DataCell(Text(getDriverName(trip))),
                                        DataCell(
                                          // Show different action buttons based on trip status
                                          trip.status == 'DELETED' || trip.status == 'CANCEL ON REVIEW'
                                              ? const SizedBox.shrink() // Empty widget instead of 'No actions available' text
                                              : trip.status == 'WAITING FOR RATING'
                                                  ? Row(
                                                      mainAxisSize: MainAxisSize.min,
                                                      children: [
                                                        ElevatedButton.icon(
                                                          icon: const Icon(Icons.star, color: Colors.amber),
                                                          label: Text(AppLocalizations.of(context).rateTrip),
                                                          style: ElevatedButton.styleFrom(
                                                            backgroundColor: Colors.purple.shade100,
                                                            foregroundColor: Colors.purple.shade900,
                                                          ),
                                                          onPressed: () async {
                                                            final result = await Navigator.push(
                                                              context,
                                                              MaterialPageRoute(
                                                                builder: (context) => RatingScreen(trip: trip),
                                                              ),
                                                            );
                                                            if (result == true) {
                                                              _fetchTrips();
                                                            }
                                                          },
                                                        ),                                                        // Show rejection reason button if available and status is REQUEST
                                                        if (trip.rejectionReason != null && trip.status == 'REQUEST') ...[
                                                          const SizedBox(width: 8),
                                                          ElevatedButton.icon(
                                                            icon: const Icon(Icons.info_outline, color: Colors.red),
                                                            label: Text(AppLocalizations.of(context).viewRejectionReason),
                                                            style: ElevatedButton.styleFrom(
                                                              backgroundColor: Colors.red.shade50,
                                                              foregroundColor: Colors.red.shade900,
                                                            ),
                                                            onPressed: () => _showRejectionReasonDialog(trip),
                                                          ),
                                                        ],
                                                      ],
                                                    )
                                                  : trip.status == 'ASSIGN TO ONLINE TAXI'
                                                    ? Row(
                                                        mainAxisSize: MainAxisSize.min,
                                                        children: [
                                                          ElevatedButton.icon(
                                                            icon: const Icon(Icons.check_circle, color: Colors.green),
                                                            label: Text(AppLocalizations.of(context).finishTrip),
                                                            style: ElevatedButton.styleFrom(
                                                              backgroundColor: Colors.green.shade100,
                                                              foregroundColor: Colors.green.shade900,
                                                            ),
                                                            onPressed: () => _showFinishTripConfirmation(trip),
                                                          ),
                                                        ],
                                                      )
                                                    : Row(
                                                        mainAxisSize: MainAxisSize.min,
                                                        children: [                                                          // Show rejection reason button if available and status is REQUEST
                                                          if (trip.rejectionReason != null && trip.status == 'REQUEST')
                                                            ElevatedButton.icon(
                                                              icon: const Icon(Icons.info_outline, color: Colors.red),
                                                              label: Text(AppLocalizations.of(context).viewRejectionReason),
                                                              style: ElevatedButton.styleFrom(
                                                                backgroundColor: Colors.red.shade50,
                                                                foregroundColor: Colors.red.shade900,
                                                              ),
                                                              onPressed: () {
                                                                print('Showing rejection reason dialog for trip ${trip.tripCode}');
                                                                print('Rejection reason: ${trip.rejectionReason}');
                                                                _showRejectionReasonDialog(trip);
                                                              },
                                                            ),

                                                          // Add spacing if we have both rejection reason and edit/delete buttons
                                                          if (trip.rejectionReason != null && trip.status == 'REQUEST')
                                                            const SizedBox(width: 8),

                                                          // Standard edit/delete buttons - only show for REQUEST status
                                                          if (trip.status == 'REQUEST') ...[
                                                            IconButton(
                                                              icon: const Icon(Icons.edit),
                                                              color: const Color(0xFF0D47A1),
                                                              onPressed: () async {
                                                                final result = await Navigator.push(
                                                                  context,
                                                                  MaterialPageRoute(
                                                                    builder: (context) => EditTripScreen(trip: trip),
                                                                  ),
                                                                );
                                                                if (result == true) {
                                                                  _fetchTrips();
                                                                }
                                                              },
                                                            ),
                                                            IconButton(
                                                              icon: const Icon(Icons.delete),
                                                              color: Colors.red,
                                                              onPressed: () => _showDeleteConfirmation(trip),
                                                            ),
                                                          ],
                                                        ],
                                                      ),
                                        ),
                                      ],
                                    );
                                  }).toList(),
                                ),
                              ),
                            ),
                          ),
          ),
        ],
      ),
      // Only show bottom navigation on mobile
      bottomNavigationBar: isSmallScreen
          ? MobileBottomNav(
              currentIndex: _currentIndex,
              onTap: _onBottomNavTap,
            )
          : null,
      floatingActionButton: FloatingActionButton(
        backgroundColor: const Color(0xFF0D47A1),
        child: const Icon(Icons.add, color: Colors.white),
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const AddTripScreen(),
            ),
          );
          if (result == true) {
            _fetchTrips(); // Refresh the list if a trip was added
          }
        },
      ),
    );
  }
}







