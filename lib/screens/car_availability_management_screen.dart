import 'package:flutter/foundation.dart' show kIsWeb, defaultTargetPlatform, TargetPlatform;
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../generated/l10n/app_localizations.dart';
import '../models/car.dart';
import '../models/car_availability.dart';
import '../services/api_service.dart';
import '../sidebar.dart';
import '../widgets/common_app_bar.dart';
import '../widgets/mobile_bottom_nav.dart';

class CarAvailabilityManagementScreen extends StatefulWidget {
  const CarAvailabilityManagementScreen({super.key});

  @override
  State<CarAvailabilityManagementScreen> createState() => _CarAvailabilityManagementScreenState();
}

class _CarAvailabilityManagementScreenState extends State<CarAvailabilityManagementScreen> {
  List<Car> _cars = [];
  List<CarAvailability> _availabilities = [];
  bool _isLoading = true;
  String _errorMessage = '';
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  // Current index for bottom navigation on mobile
  int _currentIndex = 3; // Settings tab

  // Date range for the table - ensure local dates
  late DateTime _startDate;
  late DateTime _endDate;

  // Initialize date range in initState to ensure proper handling of weekend dates

  // List of dates to display in the table
  List<DateTime> _dateRange = [];

  // Scroll controller for horizontal scrolling
  final ScrollController _horizontalScrollController = ScrollController();

  // Current slider value (0.0 to 1.0) for horizontal scrolling
  double _horizontalScrollValue = 0.0;

  // Available status options
  final List<String> _statusOptions = [
    'AVAILABLE',
    'UNAVAILABLE',
  ];

  @override
  void initState() {
    super.initState();

    // Initialize date range with proper handling of weekend dates
    _initializeDateRange();

    _generateDateRange();
    _fetchData();

    // Add listener to update slider value when scrolling manually
    _horizontalScrollController.addListener(_updateSliderFromScroll);
  }

  // Initialize date range with proper handling of weekend dates
  void _initializeDateRange() {
    // Get the current date
    final DateTime now = DateTime.now().toLocal();

    // Check if the current date is a weekend
    final bool isWeekend = now.weekday == DateTime.saturday || now.weekday == DateTime.sunday;

    // Set the start date
    _startDate = now;

    // Set the end date (default to 1 week)
    _endDate = now.add(const Duration(days: 6));

    // Debug print to help diagnose the issue
    print('Initializing date range: start=${DateFormat('yyyy-MM-dd').format(_startDate)} (${_startDate.weekday}), isWeekend=$isWeekend');
  }
  // Check if an availability entry is explicitly set in the database
  bool _isExplicitlySet(String carCode, DateTime date) {
    try {
      // Ensure we're working with local date
      final DateTime localDate = date.toLocal();

      // Find the car by car code
      final Car car = _cars.firstWhere((c) => c.carCode == carCode);
      final int carId = car.carId ?? 0;

      // Check if we have an explicit availability entry in the database
      // Try multiple matching strategies to ensure we find the record
      CarAvailability? existingAvailability;
      
      // Strategy 1: Match by carId and exact date
      try {
        existingAvailability = _availabilities.firstWhere(
          (availability) {
            // Convert availability date to local to ensure proper comparison
            final DateTime availDate = availability.date.toLocal();

            return availability.carId == carId &&
              availDate.year == localDate.year &&
              availDate.month == localDate.month &&
              availDate.day == localDate.day;
          },
        );
      } catch (e) {
        // Strategy 2: Match by carCode and exact date (fallback)
        try {
          existingAvailability = _availabilities.firstWhere(
            (availability) {
              // Convert availability date to local to ensure proper comparison
              final DateTime availDate = availability.date.toLocal();

              return availability.carCode == carCode &&
                availDate.year == localDate.year &&
                availDate.month == localDate.month &&
                availDate.day == localDate.day;
            },
          );
        } catch (e2) {
          // No existing availability found
          existingAvailability = null;
        }
      }

      // If we found an existing availability, it's explicitly set
      return existingAvailability != null;
    } catch (e) {
      print('Error checking if availability is explicitly set: $e');
      return false;
    }
  }

  @override
  void dispose() {
    _horizontalScrollController.dispose();
    super.dispose();
  }

  // Update slider value based on scroll position
  void _updateSliderFromScroll() {
    if (_horizontalScrollController.hasClients) {
      final currentScroll = _horizontalScrollController.position.pixels;
      final maxScroll = _horizontalScrollController.position.maxScrollExtent;

      if (maxScroll > 0) {
        setState(() {
          _horizontalScrollValue = currentScroll / maxScroll;
        });
      }
    }
  }

  // Generate the date range for the table
  void _generateDateRange() {
    _dateRange = [];
    // Ensure we're using local dates
    final DateTime currentDate = _startDate.toLocal();
    final DateTime endDate = _endDate.toLocal();

    DateTime dateIterator = currentDate;
    while (!dateIterator.isAfter(endDate)) {
      _dateRange.add(dateIterator);
      dateIterator = dateIterator.add(const Duration(days: 1));
    }
  }

  // Show a message about mobile date range limitation
  void _showMobileDateRangeMessage({bool isError = false, String? customMessage}) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(        SnackBar(
          content: Text(customMessage ?? AppLocalizations.of(context).onMobileDateRangeLimited),
          duration: const Duration(seconds: 3),
          backgroundColor: isError ? Colors.red : Colors.orange,
        ),
      );
    }
  }

  // Handle bottom navigation
  void _onBottomNavTap(int index) {
    if (index == _currentIndex) return;

    switch (index) {
      case 0: // Dashboard
        Navigator.pushReplacementNamed(context, '/trip-manager-dashboard');
      case 1: // Monitoring
        Navigator.pushReplacementNamed(context, '/trip-monitoring');
      case 2: // Approvals
        Navigator.pushReplacementNamed(context, '/trip-approval');
      case 3: // Settings - current screen
        // Already here
    }
  }

  Future<void> _fetchData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      // Fetch cars
      await _fetchCars();      // Fetch availabilities
      await _fetchAvailabilities();

      // Debug print to see what we have after fetching availabilities
      print('After _fetchAvailabilities: _availabilities has ${_availabilities.length} records');
      if (_availabilities.isNotEmpty) {
        print('Sample records:');
        for (var avail in _availabilities.take(3)) {
          print('  - Car ${avail.carCode} (ID: ${avail.carId}) on ${DateFormat('yyyy-MM-dd').format(avail.date)}: ${avail.status}');
        }
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {      if (mounted) {
        setState(() {
          _errorMessage = '${AppLocalizations.of(context).error}: $e';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _fetchCars() async {
    try {
      final response = await ApiService.get('cars');

      if (response.statusCode == 200) {
        final data = ApiService.parseResponse(response);
        if (mounted) {
          setState(() {
            _cars = List<Car>.from(
              data['cars'].map((car) => Car.fromJson(car))
            );
          });
        }
      } else {
        throw Exception('Failed to load cars');
      }
    } catch (e) {
      rethrow;
    }
  }

  Future<void> _fetchAvailabilities() async {
    try {
      // Format dates for API request - ensure local dates
      final String formattedStartDate = DateFormat('yyyy-MM-dd').format(_startDate.toLocal());
      final String formattedEndDate = DateFormat('yyyy-MM-dd').format(_endDate.toLocal());

      // Try to fetch from API endpoint
      try {
        final response = await ApiService.get(
          'car-availabilities?start_date=$formattedStartDate&end_date=$formattedEndDate'
        );

        if (response.statusCode == 200) {
          final data = ApiService.parseResponse(response);

          // Debug print to see what data is coming from the API
          print('API returned ${data['availabilities'].length} availability records');          if (mounted) {
            // Convert the API data to CarAvailability objects
            final List<CarAvailability> apiAvailabilities = List<CarAvailability>.from(
              data['availabilities'].map((avail) => CarAvailability.fromJson(avail))
            );

            // Debug print to see what data was converted
            print('Converted ${apiAvailabilities.length} availability records from API:');
            for (var avail in apiAvailabilities.take(5)) { // Show first 5 records
              print('  - Car ${avail.carCode} (ID: ${avail.carId}) on ${DateFormat('yyyy-MM-dd').format(avail.date)}: ${avail.status}');
            }

            // Update the state with the API data
            setState(() {
              _availabilities = apiAvailabilities;
            });

            print('Updated _availabilities list with ${_availabilities.length} records');
            return; // Exit early if API call was successful
          }
        } else {
          print('API returned status code ${response.statusCode}');
        }
      } catch (e) {
        // If API call fails, fall back to generating sample data
        print('Failed to fetch availabilities from API: $e');
      }

      // Fallback: Generate sample data
      print('Falling back to generating sample data');
      final List<CarAvailability> availabilities = [];

      if (_cars.isNotEmpty) {
        for (var car in _cars) {
          for (var date in _dateRange) {
            // Use default status based on weekday/weekend
            final String defaultStatus = CarAvailability.getDefaultStatusForDate(date);

            // Add notes for UNAVAILABLE status (for demo purposes)
            String? notes;
            if (defaultStatus == 'UNAVAILABLE') {
              notes = 'Unavailable: Demo reason';
            }

            availabilities.add(
              CarAvailability(
                carId: car.carId ?? 0,
                carCode: car.carCode,
                date: date,
                status: defaultStatus,
                notes: notes,
              )
            );
          }
        }
      }

      if (mounted) {
        setState(() {
          _availabilities = availabilities;
        });
      }
    } catch (e) {
      print('Error in _fetchAvailabilities: $e');
      rethrow;
    }
  }

  // Update the date range and refresh data
  void _updateDateRange(DateTime startDate, DateTime endDate) {
    // Check if we're on mobile and enforce 9-day limit
    final bool isSmallScreen = _isMobileDevice();

    if (isSmallScreen) {
      // Calculate the difference in days
      final int daysDifference = endDate.difference(startDate).inDays;

      // If the range exceeds 9 days, adjust the end date
      if (daysDifference > 8) { // 8 days difference = 9 days total (inclusive)
        endDate = startDate.add(const Duration(days: 8)); // 8 days after start = 9 days total

        // Show an error message to the user
        _showMobileDateRangeMessage(isError: true);
      }
    }

    setState(() {
      _startDate = startDate;
      _endDate = endDate;
    });
    _generateDateRange();
    _fetchAvailabilities();
  }  // Get availability for a specific car and date
  CarAvailability? _getAvailability(String carCode, DateTime date) {
    try {
      // Ensure we're working with local date
      final DateTime localDate = date.toLocal();

      // Find the car by car code
      final Car car = _cars.firstWhere((c) => c.carCode == carCode);
      final int carId = car.carId ?? 0;

      // Debug print to help diagnose the issue
      print('Getting availability for carCode: $carCode, carId: $carId, date: ${DateFormat('yyyy-MM-dd').format(localDate)}');
      print('Total availabilities in list: ${_availabilities.length}');

      // First check if we have an explicit availability entry in the database
      // Try multiple matching strategies to ensure we find the record
      CarAvailability? existingAvailability;
      
      // Strategy 1: Match by carId and exact date
      try {
        existingAvailability = _availabilities.firstWhere(
          (availability) {
            // Convert availability date to local to ensure proper comparison
            final DateTime availDate = availability.date.toLocal();
            final bool carIdMatch = availability.carId == carId;
            final bool dateMatch = availDate.year == localDate.year &&
                                  availDate.month == localDate.month &&
                                  availDate.day == localDate.day;

            if (carIdMatch && dateMatch) {
              print('Found match by carId: ${availability.carCode} (${availability.carId}) on ${DateFormat('yyyy-MM-dd').format(availDate)} with status: ${availability.status}');
            }

            return carIdMatch && dateMatch;
          },
        );
      } catch (e) {
        // Strategy 2: Match by carCode and exact date (fallback)
        try {
          existingAvailability = _availabilities.firstWhere(
            (availability) {
              // Convert availability date to local to ensure proper comparison
              final DateTime availDate = availability.date.toLocal();
              final bool carCodeMatch = availability.carCode == carCode;
              final bool dateMatch = availDate.year == localDate.year &&
                                    availDate.month == localDate.month &&
                                    availDate.day == localDate.day;

              if (carCodeMatch && dateMatch) {
                print('Found match by carCode: ${availability.carCode} on ${DateFormat('yyyy-MM-dd').format(availDate)} with status: ${availability.status}');
              }

              return carCodeMatch && dateMatch;
            },
          );
        } catch (e2) {
          // No existing availability found
          print('No availability found for carCode: $carCode, date: ${DateFormat('yyyy-MM-dd').format(localDate)}');
          existingAvailability = null;
        }
      }

      // If we found an existing availability, return it
      if (existingAvailability != null) {
        print('Returning existing availability: ${existingAvailability.carCode} - ${existingAvailability.status}');
        return existingAvailability;
      }

      // Otherwise, create a new availability with the correct default status based on weekday/weekend
      final String defaultStatus = CarAvailability.getDefaultStatusForDate(localDate);
      print('Creating new availability with default status: $defaultStatus for $carCode on ${DateFormat('yyyy-MM-dd').format(localDate)}');

      return CarAvailability(
        carId: carId,
        carCode: car.carCode,
        date: localDate,
        status: defaultStatus,
      );
    } catch (e) {
      print('Error in _getAvailability: $e');
      return null;
    }
  }



  // Update availability status
  Future<void> _updateAvailabilityStatus(CarAvailability availability, String newStatus, {String? notes}) async {
    // Ensure we're working with local date
    final DateTime localDate = availability.date.toLocal();

    // Format date for API request
    final String formattedDate = DateFormat('yyyy-MM-dd').format(localDate);

    // Debug print for weekend dates
    final bool isWeekend = localDate.weekday == DateTime.saturday || localDate.weekday == DateTime.sunday;
    if (isWeekend) {
      print('Updating availability for weekend date: $formattedDate with new status: $newStatus');
    }

    try {
      // Send API request to update the status
      final response = await ApiService.put(
        'car-availabilities/${availability.carId}/$formattedDate',
        {
          'status': newStatus,
          'notes': notes ?? availability.notes,
        }
      );

      if (response.statusCode != 200) {
        throw Exception('Failed to update availability: ${response.statusCode}');
      }

      // Update local state
      setState(() {
        // Find and update the availability in the list
        final index = _availabilities.indexWhere(
          (a) => a.carId == availability.carId &&
                a.date.year == availability.date.year &&
                a.date.month == availability.date.month &&
                a.date.day == availability.date.day
        );

        if (index >= 0) {
          // Create a new availability with the updated status
          final updatedAvailability = CarAvailability(
            carId: availability.carId,
            carCode: availability.carCode,
            date: availability.date,
            status: newStatus,
            notes: notes ?? availability.notes,
          );

          // Replace the old availability with the updated one
          _availabilities[index] = updatedAvailability;
        } else {
          // Add a new availability if not found
          _availabilities.add(
            CarAvailability(
              carId: availability.carId,
              carCode: availability.carCode,
              date: availability.date,
              status: newStatus,
              notes: notes,
            )
          );
        }
      });      // Show a success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Updated ${availability.carCode}\'s availability for ${DateFormat('EEE, MMM d').format(availability.date)}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      // Show error message
      if (mounted) {        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${AppLocalizations.of(context).error}: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Show dialog to edit availability
  void _showEditAvailabilityDialog(CarAvailability availability) {
    // Ensure we're working with local date
    final DateTime localDate = availability.date.toLocal();

    // Check if it's a weekend
    final bool isWeekend = localDate.weekday == DateTime.saturday || localDate.weekday == DateTime.sunday;

    // Get the status from the availability object
    String selectedStatus = availability.status;

    // We don't need to check car code or if explicitly set anymore since we don't override weekend status

    // No need to override status for weekend dates anymore
    if (isWeekend) {
      print('Using status for weekend date in dialog: ${DateFormat('yyyy-MM-dd').format(localDate)} - Status: $selectedStatus');
    }

    final TextEditingController notesController = TextEditingController(text: availability.notes);

    // Check if we're on a small screen (mobile)
    final bool isSmallScreen = _isMobileDevice();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setDialogState) {            return AlertDialog(
              title: Text('Edit Availability for ${availability.carCode}'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,                  children: [
                    Text('${AppLocalizations.of(context).dateColon} ${DateFormat('EEEE, MMMM d, yyyy').format(availability.date.toLocal())}'),
                    const SizedBox(height: 16),
                    Text(AppLocalizations.of(context).status),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: _statusOptions.map((status) {
                        return ChoiceChip(
                          label: Text(status),
                          selected: selectedStatus == status,
                          onSelected: (selected) {
                            if (selected) {
                              setDialogState(() {
                                selectedStatus = status;
                              });
                            }
                          },
                          backgroundColor: CarAvailability.getStatusColor(status).withAlpha(75),
                          selectedColor: CarAvailability.getStatusColor(status),
                          labelStyle: TextStyle(
                            color: CarAvailability.getStatusTextColor(status),
                            fontWeight: selectedStatus == status ? FontWeight.bold : FontWeight.normal,
                          ),
                        );
                      }).toList(),
                    ),
                    const SizedBox(height: 24),                    TextField(
                      controller: notesController,
                      decoration: InputDecoration(
                        labelText: AppLocalizations.of(context).notesOptional,
                        border: const OutlineInputBorder(),
                      ),
                      maxLines: 3,
                    ),
                  ],
                ),
              ),
              actions: [
                // Use a Column for mobile layout to prevent overflow
                if (isSmallScreen)
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [                      // Quick action buttons in a scrollable row for mobile
                      SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Row(
                          children: [
                            TextButton.icon(
                              icon: const Icon(Icons.build_circle, size: 16),
                              label: Text(AppLocalizations.of(context).maintenance),
                              style: TextButton.styleFrom(
                                foregroundColor: Colors.red.shade800,
                              ),
                              onPressed: () {
                                Navigator.of(context).pop();
                                _updateAvailabilityStatus(
                                  availability,
                                  'UNAVAILABLE',
                                  notes: 'Maintenance',
                                );
                              },
                            ),
                            TextButton.icon(
                              icon: const Icon(Icons.car_crash, size: 16),
                              label: Text(AppLocalizations.of(context).broken),
                              style: TextButton.styleFrom(
                                foregroundColor: Colors.amber.shade800,
                              ),
                              onPressed: () {
                                Navigator.of(context).pop();
                                _updateAvailabilityStatus(
                                  availability,
                                  'UNAVAILABLE',
                                  notes: 'Broken',
                                );
                              },
                            ),                            TextButton.icon(
                              icon: const Icon(Icons.handshake, size: 16),
                              label: Text(AppLocalizations.of(context).borrowed),
                              style: TextButton.styleFrom(
                                foregroundColor: Colors.blue.shade800,
                              ),
                              onPressed: () {
                                Navigator.of(context).pop();
                                _updateAvailabilityStatus(
                                  availability,
                                  'UNAVAILABLE',
                                  notes: 'Borrowed',
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 8),
                      // Cancel and Save buttons in a row
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,                        children: [
                          TextButton(
                            child: Text(AppLocalizations.of(context).cancel),
                            onPressed: () => Navigator.of(context).pop(),
                          ),
                          TextButton(
                            child: Text(AppLocalizations.of(context).save),
                            onPressed: () {
                              Navigator.of(context).pop();
                              print('Explicitly setting status for date ${DateFormat('yyyy-MM-dd').format(availability.date)} to $selectedStatus');
                              _updateAvailabilityStatus(
                                availability,
                                selectedStatus,
                                notes: notesController.text,
                              );
                            },
                          ),
                        ],
                      ),
                    ],
                  )
                else
                  // Desktop layout - original layout
                  Row(
                    children: [
                      // Quick action buttons for setting UNAVAILABLE with predefined reasons
                      Wrap(
                        spacing: 8,
                        children: [                          TextButton.icon(
                            icon: const Icon(Icons.build_circle, size: 16),
                            label: Text(AppLocalizations.of(context).maintenance),
                            style: TextButton.styleFrom(
                              foregroundColor: Colors.red.shade800,
                            ),
                            onPressed: () {
                              Navigator.of(context).pop();
                              _updateAvailabilityStatus(
                                availability,
                                'UNAVAILABLE',
                                notes: 'Maintenance',
                              );
                            },
                          ),
                          TextButton.icon(
                            icon: const Icon(Icons.car_crash, size: 16),
                            label: Text(AppLocalizations.of(context).broken),
                            style: TextButton.styleFrom(
                              foregroundColor: Colors.amber.shade800,
                            ),
                            onPressed: () {
                              Navigator.of(context).pop();
                              _updateAvailabilityStatus(
                                availability,
                                'UNAVAILABLE',
                                notes: 'Broken',
                              );
                            },
                          ),                          TextButton.icon(
                            icon: const Icon(Icons.handshake, size: 16),
                            label: Text(AppLocalizations.of(context).borrowed),
                            style: TextButton.styleFrom(
                              foregroundColor: Colors.blue.shade800,
                            ),
                            onPressed: () {
                              Navigator.of(context).pop();
                              _updateAvailabilityStatus(
                                availability,
                                'UNAVAILABLE',
                                notes: 'Borrowed',
                              );
                            },
                          ),
                        ],
                      ),                      const Spacer(),
                      TextButton(
                        child: Text(AppLocalizations.of(context).cancel),
                        onPressed: () => Navigator.of(context).pop(),
                      ),
                      TextButton(
                        child: Text(AppLocalizations.of(context).save),
                        onPressed: () {
                          Navigator.of(context).pop();
                          print('Explicitly setting status for date ${DateFormat('yyyy-MM-dd').format(availability.date)} to $selectedStatus');
                          _updateAvailabilityStatus(
                            availability,
                            selectedStatus,
                            notes: notesController.text,
                          );
                        },
                      ),
                    ],
                  ),
              ],
            );
          },
        );
      },
    );
  }

  // Build a cell for the availability table
  Widget _buildAvailabilityCell(String carCode, DateTime date) {
    // Ensure we're working with local date
    final DateTime localDate = date.toLocal();

    // Check if it's a weekend
    final bool isWeekend = localDate.weekday == DateTime.saturday || localDate.weekday == DateTime.sunday;

    // Get the availability with the correct default status
    final availability = _getAvailability(carCode, localDate);

    if (availability != null) {
      // Get the status from the availability object
      final String displayStatus = availability.status;

      // No need to override status for weekend dates anymore - using the default from CarAvailability
      if (isWeekend) {
        print('Using status for weekend date: ${DateFormat('yyyy-MM-dd').format(localDate)} - Status: $displayStatus');
      }

      // Debug print to check the status
      if (isWeekend) {
        print('Building cell for weekend date: ${DateFormat('yyyy-MM-dd').format(localDate)} with status: $displayStatus');
      }

      final Color backgroundColor = CarAvailability.getStatusColor(displayStatus);
      final Color textColor = CarAvailability.getStatusTextColor(displayStatus);

      return InkWell(
        onTap: () => _showEditAvailabilityDialog(availability),
        child: Container(
          width: 120,
          height: 40,
          decoration: BoxDecoration(
            color: backgroundColor,
            border: Border.all(color: Colors.grey.shade300),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                displayStatus,
                style: TextStyle(
                  color: textColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
              ),
              if (availability.notes != null && availability.notes!.isNotEmpty)
                Expanded(
                  child: Tooltip(
                    message: availability.notes!,
                    child: Text(
                      availability.notes!,
                      style: TextStyle(
                        color: textColor,
                        fontSize: 10,
                      ),
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
            ],
          ),
        ),
      );
    } else {
      return InkWell(
        onTap: () {
          // Get the availability with the correct default status
          final availability = _getAvailability(carCode, localDate);
          _showEditAvailabilityDialog(availability!);
        },
        child: Container(
          width: 120,
          height: 40,
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            border: Border.all(color: Colors.grey.shade300),
          ),
          child: const Center(
            child: Text(
              'Set Status',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      );
    }
  }

  // Show floating date range picker dialog
  Future<void> _showDateRangePicker() async {
    // Create controllers for start and end date
    final TextEditingController startDateController = TextEditingController(
      text: DateFormat('MMM d, yyyy').format(_startDate.toLocal()),
    );
    final TextEditingController endDateController = TextEditingController(
      text: DateFormat('MMM d, yyyy').format(_endDate.toLocal()),
    );

    // Local variables to track selected dates
    DateTime startDate = _startDate.toLocal();
    DateTime endDate = _endDate.toLocal();

    // Show custom floating dialog
    final result = await showDialog<DateTimeRange>(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          insetPadding: const EdgeInsets.symmetric(horizontal: 40.0, vertical: 24.0),
          child: StatefulBuilder(
            builder: (BuildContext context, StateSetter setState) {
              return Container(
                width: 400,
                padding: const EdgeInsets.all(16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title with close button
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Select Date Range',
                          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                        ),
                        IconButton(
                          icon: const Icon(Icons.close),
                          onPressed: () => Navigator.of(context).pop(),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),                    // Start date picker
                    Row(
                      children: [
                        Text(AppLocalizations.of(context).startDateColon, style: const TextStyle(fontWeight: FontWeight.bold)),
                        const SizedBox(width: 8),
                        Expanded(
                          child: InkWell(
                            onTap: () async {
                              // Check if we're on mobile to enforce the 9-day limit
                              final bool isSmallScreen = _isMobileDevice();

                              // For mobile, we need to limit the start date selection
                              // to ensure the range doesn't exceed 9 days
                              DateTime firstDate = DateTime(2020);

                              if (isSmallScreen) {
                                // Calculate the earliest possible start date that would result in a 9-day range
                                // This is the end date minus 8 days
                                final DateTime earliestStartDate = endDate.subtract(const Duration(days: 8));

                                // If this date is after our normal firstDate (2020), use it as the firstDate
                                if (earliestStartDate.isAfter(DateTime(2020))) {
                                  firstDate = earliestStartDate;
                                }
                              }

                              final DateTime? picked = await showDatePicker(
                                context: context,
                                initialDate: startDate.isBefore(firstDate) ? firstDate : startDate,
                                firstDate: firstDate,
                                lastDate: DateTime.now().toLocal().add(const Duration(days: 365)),
                                builder: (BuildContext context, Widget? child) {
                                  return Theme(
                                    data: ThemeData.light().copyWith(
                                      colorScheme: const ColorScheme.light(
                                        primary: Color(0xFF0D47A1),
                                        onPrimary: Colors.white,
                                        surface: Colors.white,
                                        onSurface: Colors.black,
                                      ),
                                    ),
                                    child: child!,
                                  );
                                },
                              );

                              if (picked != null) {
                                setState(() {
                                  startDate = picked;
                                  startDateController.text = DateFormat('MMM d, yyyy').format(picked);

                                  // Ensure end date is not before start date
                                  if (endDate.isBefore(startDate)) {
                                    endDate = startDate;
                                    endDateController.text = DateFormat('MMM d, yyyy').format(startDate);
                                  }
                                });

                                // No additional logic needed for non-mobile case
                              }
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey.shade300),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(startDateController.text),
                                  const Icon(Icons.calendar_today, size: 18),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),                    // End date picker
                    Row(
                      children: [
                        Text(AppLocalizations.of(context).endDateColon, style: const TextStyle(fontWeight: FontWeight.bold)),
                        const SizedBox(width: 8),
                        Expanded(
                          child: InkWell(
                            onTap: () async {
                              // Check if we're on mobile to enforce the 9-day limit
                              final bool isSmallScreen = _isMobileDevice();

                              // Calculate the maximum allowed end date
                              final DateTime maxEndDate = isSmallScreen
                                  ? startDate.add(const Duration(days: 8)) // 9 days total (inclusive)
                                  : DateTime.now().toLocal().add(const Duration(days: 365));

                              final DateTime? picked = await showDatePicker(
                                context: context,
                                initialDate: endDate.isBefore(startDate) ? startDate :
                                  (endDate.isAfter(maxEndDate) ? maxEndDate : endDate),
                                firstDate: startDate, // End date must be after start date
                                lastDate: maxEndDate, // Limit based on device type
                                builder: (BuildContext context, Widget? child) {
                                  return Theme(
                                    data: ThemeData.light().copyWith(
                                      colorScheme: const ColorScheme.light(
                                        primary: Color(0xFF0D47A1),
                                        onPrimary: Colors.white,
                                        surface: Colors.white,
                                        onSurface: Colors.black,
                                      ),
                                    ),
                                    child: child!,
                                  );
                                },
                              );

                              // We need to check if the widget is still mounted after the async gap
                              if (!mounted) return;

                              // Show message if on mobile
                              if (isSmallScreen) {
                                _showMobileDateRangeMessage(isError: false);
                              }

                              if (picked != null) {
                                setState(() {
                                  endDate = picked;
                                  endDateController.text = DateFormat('MMM d, yyyy').format(picked);
                                });
                              }
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey.shade300),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(endDateController.text),
                                  const Icon(Icons.calendar_today, size: 18),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // Quick selection buttons
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        ElevatedButton(
                          onPressed: () {
                            // Set to current week
                            final now = DateTime.now().toLocal();
                            final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
                            final endOfWeek = startOfWeek.add(const Duration(days: 6));

                            setState(() {
                              startDate = startOfWeek;
                              endDate = endOfWeek;
                              startDateController.text = DateFormat('MMM d, yyyy').format(startOfWeek);
                              endDateController.text = DateFormat('MMM d, yyyy').format(endOfWeek);
                            });
                          },                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF0D47A1),
                            foregroundColor: Colors.white,
                          ),
                          child: Text(AppLocalizations.of(context).thisWeek),
                        ),
                        ElevatedButton(
                          onPressed: () {
                            // Set to next week
                            final now = DateTime.now().toLocal();
                            final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
                            final startOfNextWeek = startOfWeek.add(const Duration(days: 7));

                            // Check if we're on mobile to enforce the 9-day limit
                            final bool isSmallScreen = _isMobileDevice();

                            // For mobile, limit to 8 days after start (9 days total)
                            final endOfNextWeek = startOfNextWeek.add(Duration(days: isSmallScreen ? 8 : 6));

                            setState(() {
                              startDate = startOfNextWeek;
                              endDate = endOfNextWeek;
                              startDateController.text = DateFormat('MMM d, yyyy').format(startOfNextWeek);
                              endDateController.text = DateFormat('MMM d, yyyy').format(endOfNextWeek);
                            });

                            // Show message if on mobile
                            if (isSmallScreen) {
                              _showMobileDateRangeMessage(isError: false);
                            }
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF0D47A1),
                            foregroundColor: Colors.white,
                          ),
                          child: Text(AppLocalizations.of(context).nextWeek),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),

                    // Action buttons
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,                      children: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: Text(AppLocalizations.of(context).cancel),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: () {
                            // Check if we're on mobile and the range exceeds 9 days
                            final bool isSmallScreen = _isMobileDevice();
                            final int daysDifference = endDate.difference(startDate).inDays;

                            if (isSmallScreen && daysDifference > 8) {
                              // Adjust the end date to be 8 days after start (9 days total)
                              final DateTime adjustedEndDate = startDate.add(const Duration(days: 8));

                              // Show error message
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('On mobile, date range is limited to 9 days maximum'),
                                  backgroundColor: Colors.red,
                                  duration: Duration(seconds: 3),
                                ),
                              );

                              // Update the UI
                              setState(() {
                                endDate = adjustedEndDate;
                                endDateController.text = DateFormat('MMM d, yyyy').format(adjustedEndDate);
                              });

                              // Close dialog with adjusted range
                              Navigator.of(context).pop(
                                DateTimeRange(start: startDate, end: adjustedEndDate),
                              );
                            } else {
                              // Close dialog and return the selected range
                              Navigator.of(context).pop(
                                DateTimeRange(start: startDate, end: endDate),
                              );
                            }
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF0D47A1),
                            foregroundColor: Colors.white,
                          ),
                          child: Text(AppLocalizations.of(context).apply),
                        ),
                      ],
                    ),
                  ],
                ),
              );
            },
          ),
        );
      },
    );

    if (result != null) {
      _updateDateRange(result.start.toLocal(), result.end.toLocal());
    }
  }

  // Search filter for cars on mobile
  String _carSearchQuery = '';
  List<Car> get _filteredCars {
    if (_carSearchQuery.isEmpty) {
      return _cars;
    }
    return _cars.where((car) =>
      car.manufacturer.toLowerCase().contains(_carSearchQuery.toLowerCase()) ||
      car.modelName.toLowerCase().contains(_carSearchQuery.toLowerCase()) ||
      car.carCode.toLowerCase().contains(_carSearchQuery.toLowerCase()) ||
      car.plateNumber.toLowerCase().contains(_carSearchQuery.toLowerCase())
    ).toList();
  }

  // Build mobile layout
  Widget _buildMobileLayout() {
    return Column(
      children: [
        // Mobile date range selector
        Container(
          color: const Color(0xFF0D47A1).withAlpha(13), // 0.05 opacity
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Date range display
              Card(
                elevation: 1,
                child: InkWell(
                  onTap: _showDateRangePicker,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Date Range',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey,
                              ),
                            ),
                            Text(
                              '${DateFormat('MMM d').format(_startDate.toLocal())} - ${DateFormat('MMM d').format(_endDate.toLocal())}',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const Icon(Icons.calendar_today, color: Color(0xFF0D47A1)),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 8),
              // Quick buttons
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        // Set date range to current week
                        final now = DateTime.now().toLocal();
                        final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
                        final endOfWeek = startOfWeek.add(const Duration(days: 6));
                        _updateDateRange(startOfWeek, endOfWeek);
                      },                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF0D47A1),
                        foregroundColor: Colors.white,
                      ),
                      child: Text(AppLocalizations.of(context).thisWeek),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        // Set date range to next week
                        final now = DateTime.now().toLocal();
                        final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
                        final startOfNextWeek = startOfWeek.add(const Duration(days: 7));

                        // Check if we're on mobile to enforce the 9-day limit
                        final bool isSmallScreen = _isMobileDevice();

                        // For mobile, limit to 8 days after start (9 days total)
                        final endOfNextWeek = startOfNextWeek.add(Duration(days: isSmallScreen ? 8 : 6));
                        _updateDateRange(startOfNextWeek, endOfNextWeek);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF0D47A1),
                        foregroundColor: Colors.white,
                      ),
                      child: Text(AppLocalizations.of(context).nextWeek),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        // Search bar for cars
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: TextField(
            onChanged: (value) {
              setState(() {
                _carSearchQuery = value;
              });
            },            decoration: InputDecoration(
              labelText: AppLocalizations.of(context).searchCars,
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              contentPadding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
            ),
          ),
        ),
        // Car availability cards
        Expanded(          child: _filteredCars.isEmpty
              ? Center(child: Text(AppLocalizations.of(context).noCarsFound))
              : RefreshIndicator(
                  onRefresh: _fetchData,
                  child: ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    itemCount: _filteredCars.length,
                    itemBuilder: (context, index) {
                      return _buildMobileAvailabilityCard(_filteredCars[index]);
                    },
                  ),
                ),
        ),        // Legend
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                AppLocalizations.of(context).statusLegend,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 16,
                runSpacing: 8,
                children: _statusOptions.map((status) {
                  return Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        width: 16,
                        height: 16,
                        decoration: BoxDecoration(
                          color: CarAvailability.getStatusColor(status),
                          border: Border.all(color: Colors.grey.shade300),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        status,
                        style: TextStyle(
                          color: CarAvailability.getStatusTextColor(status),
                          fontSize: 12,
                        ),
                      ),
                    ],
                  );
                }).toList(),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Build a mobile-friendly availability card
  Widget _buildMobileAvailabilityCard(Car car) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Car details
            Text(
              '${car.carCode} - ${car.manufacturer} ${car.modelName}',
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),            ),
            Text(
              '${AppLocalizations.of(context).plate}: ${car.plateNumber}',
              style: const TextStyle(fontSize: 14),
            ),
            const Divider(),
            // Availability grid for this car
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3, // 3 days per row
                crossAxisSpacing: 4.0,
                mainAxisSpacing: 4.0,
                childAspectRatio: 1.2,
              ),
              itemCount: _dateRange.length,
              itemBuilder: (context, index) {
                final date = _dateRange[index];
                return _buildMobileAvailabilityCell(car.carCode, date);
              },
            ),
          ],
        ),
      ),
    );
  }

  // Build a mobile-friendly availability cell
  Widget _buildMobileAvailabilityCell(String carCode, DateTime date) {
    // Ensure we're using local date
    final DateTime localDate = date.toLocal();

    final availability = _getAvailability(carCode, localDate);
    final bool isWeekend = localDate.weekday == DateTime.saturday || localDate.weekday == DateTime.sunday;

    // Get the status from the availability object
    String displayStatus = availability != null ? availability.status : '';

    // Check if this is an explicitly set record or a default one
    final bool isExplicitlySet = availability != null ? _isExplicitlySet(carCode, localDate) : false;

    // For weekend dates, ensure the status is UNAVAILABLE ONLY if not explicitly set in the database
    if (isWeekend && availability != null && !isExplicitlySet) {
      displayStatus = 'UNAVAILABLE';
    }

    // Debug print to check the status
    if (isWeekend) {
      print('Building mobile cell for weekend date: ${DateFormat('yyyy-MM-dd').format(localDate)} with status: $displayStatus');
    }

    final Color statusColor = availability != null
        ? CarAvailability.getStatusColor(displayStatus)
        : Colors.grey.shade100;
    final Color textColor = availability != null
        ? CarAvailability.getStatusTextColor(displayStatus)
        : Colors.grey;

    return Card(
      elevation: 1,
      margin: const EdgeInsets.all(2),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(color: Colors.grey.shade300, width: 0.5),
      ),
      child: InkWell(
        onTap: () {
          // Get the availability with the correct default status using local date
          final carAvailability = _getAvailability(carCode, localDate);
          _showEditAvailabilityDialog(carAvailability!);
        },
        borderRadius: BorderRadius.circular(8),
        child: Container(
          decoration: BoxDecoration(
            color: statusColor,
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Date and day
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    DateFormat('d').format(localDate),
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      color: isWeekend ? Colors.red : Colors.black87,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    DateFormat('E').format(localDate),
                    style: TextStyle(
                      fontSize: 12,
                      color: isWeekend ? Colors.red : Colors.black87,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 6),
              // Status
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.white.withAlpha(179), // 0.7 opacity
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: textColor.withAlpha(77)), // 0.3 opacity
                ),
                child: availability != null
                  ? Text(
                      displayStatus, // Use the corrected status
                      style: TextStyle(
                        color: textColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 11,
                      ),
                      textAlign: TextAlign.center,
                    )
                  : const Text(
                      'Set Status',
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: 11,
                      ),
                      textAlign: TextAlign.center,
                    ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper method to determine if we're on a mobile device
  bool _isMobileDevice() {
    return !kIsWeb && (defaultTargetPlatform == TargetPlatform.android || defaultTargetPlatform == TargetPlatform.iOS);
  }

  @override
  Widget build(BuildContext context) {
    // Always use mobile layout on mobile devices, regardless of screen size
    final bool isSmallScreen = _isMobileDevice();    return Scaffold(
      key: _scaffoldKey,
      appBar: CommonAppBar(
        title: AppLocalizations.of(context).carAvailabilityManagement,
        onRefresh: _fetchData,
      ),
      drawer: const SidebarNavigation(),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage.isNotEmpty
              ? Center(child: Text(_errorMessage))
              : isSmallScreen
                  ? _buildMobileLayout()
                  : Column(
                  children: [
                    // Date range selector
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Row(
                        children: [
                          Expanded(
                            child: GestureDetector(
                              onTap: _showDateRangePicker,
                              child: Container(
                                padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.grey.shade300),
                                  borderRadius: BorderRadius.circular(4.0),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      '${DateFormat('MMM d, yyyy').format(_startDate.toLocal())} - ${DateFormat('MMM d, yyyy').format(_endDate.toLocal())}',
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    const Icon(Icons.calendar_today),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          ElevatedButton.icon(
                            onPressed: () {
                              // Set date range to current week
                              final now = DateTime.now().toLocal();
                              final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
                              final endOfWeek = startOfWeek.add(const Duration(days: 6));
                              _updateDateRange(startOfWeek, endOfWeek);
                            },                            icon: const Icon(Icons.view_week),
                            label: Text(AppLocalizations.of(context).thisWeek),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF0D47A1),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                            ),
                          ),
                          const SizedBox(width: 8),
                          ElevatedButton.icon(
                            onPressed: () {
                              // Set date range to next week
                              final now = DateTime.now().toLocal();
                              final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
                              final startOfNextWeek = startOfWeek.add(const Duration(days: 7));

                              // Check if we're on mobile to enforce the 9-day limit
                              final bool isSmallScreen = _isMobileDevice();

                              // For mobile, limit to 8 days after start (9 days total)
                              final endOfNextWeek = startOfNextWeek.add(Duration(days: isSmallScreen ? 8 : 6));
                              _updateDateRange(startOfNextWeek, endOfNextWeek);
                            },
                            icon: const Icon(Icons.next_week),
                            label: Text(AppLocalizations.of(context).nextWeek),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF0D47A1),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Horizontal slider for date navigation
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                      child: Row(
                        children: [
                          const Text('Scroll dates:'),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Slider(
                              value: _horizontalScrollValue,
                              onChanged: (value) {
                                setState(() {
                                  _horizontalScrollValue = value;
                                  // Calculate scroll position based on slider value
                                  if (_horizontalScrollController.hasClients) {
                                    final maxScroll = _horizontalScrollController.position.maxScrollExtent;
                                    _horizontalScrollController.jumpTo(maxScroll * value);
                                  }
                                });
                              },
                              activeColor: const Color(0xFF0D47A1),
                            ),
                          ),
                          IconButton(
                            icon: const Icon(Icons.arrow_left),
                            onPressed: () {
                              if (_horizontalScrollController.hasClients) {
                                final currentScroll = _horizontalScrollController.position.pixels;
                                final targetScroll = (currentScroll - 300).clamp(
                                  0.0,
                                  _horizontalScrollController.position.maxScrollExtent
                                );
                                _horizontalScrollController.animateTo(
                                  targetScroll,
                                  duration: const Duration(milliseconds: 300),
                                  curve: Curves.easeInOut,
                                );
                              }
                            },
                          ),
                          IconButton(
                            icon: const Icon(Icons.arrow_right),
                            onPressed: () {
                              if (_horizontalScrollController.hasClients) {
                                final currentScroll = _horizontalScrollController.position.pixels;
                                final targetScroll = (currentScroll + 300).clamp(
                                  0.0,
                                  _horizontalScrollController.position.maxScrollExtent
                                );
                                _horizontalScrollController.animateTo(
                                  targetScroll,
                                  duration: const Duration(milliseconds: 300),
                                  curve: Curves.easeInOut,
                                );
                              }
                            },
                          ),
                        ],
                      ),
                    ),

                    // Availability table
                    Expanded(
                      child: SingleChildScrollView(
                        scrollDirection: Axis.vertical,
                        child: SingleChildScrollView(
                          controller: _horizontalScrollController,
                          scrollDirection: Axis.horizontal,
                          child: DataTable(
                            headingRowColor: WidgetStateProperty.all(
                              const Color(0xFF0D47A1).withAlpha(25),
                            ),
                            columnSpacing: 8,
                            horizontalMargin: 8,
                            columns: [                              DataColumn(
                                label: SizedBox(
                                  width: 150,
                                  child: Text(
                                    AppLocalizations.of(context).car,
                                    style: const TextStyle(fontWeight: FontWeight.bold),
                                  ),
                                ),
                              ),
                              ..._dateRange.map((date) {
                                // Ensure we're working with local date
                                final DateTime localDate = date.toLocal();

                                return DataColumn(
                                  label: SizedBox(
                                    width: 120,
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Text(
                                          DateFormat('EEE').format(localDate),
                                          style: const TextStyle(fontWeight: FontWeight.bold),
                                        ),
                                        Text(
                                          DateFormat('MMM d').format(localDate),
                                          style: TextStyle(
                                            color: localDate.weekday == DateTime.saturday || localDate.weekday == DateTime.sunday
                                                ? Colors.red
                                                : Colors.black87,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              }),
                            ],
                            rows: _cars.map((car) => DataRow(
                              cells: [
                                DataCell(
                                  SizedBox(
                                    width: 150,
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Text(
                                          '${car.carCode} - ${car.modelName}',
                                          style: const TextStyle(fontWeight: FontWeight.bold),
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        Text(
                                          car.plateNumber,
                                          style: const TextStyle(fontSize: 12),
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                ..._dateRange.map((date) {
                                  // Ensure we're working with local date
                                  final DateTime localDate = date.toLocal();
                                  return DataCell(
                                    _buildAvailabilityCell(car.carCode, localDate),
                                  );
                                }),
                              ],
                            )).toList(),
                          ),
                        ),
                      ),
                    ),

                    // Legend
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,                        children: [
                          Text(
                            AppLocalizations.of(context).statusLegend,
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 8),
                          Wrap(
                            spacing: 16,
                            runSpacing: 8,
                            children: _statusOptions.map((status) {
                              return Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Container(
                                    width: 16,
                                    height: 16,
                                    decoration: BoxDecoration(
                                      color: CarAvailability.getStatusColor(status),
                                      border: Border.all(color: Colors.grey.shade300),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    status,
                                    style: TextStyle(
                                      color: CarAvailability.getStatusTextColor(status),
                                    ),
                                  ),
                                ],
                              );
                            }).toList(),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
      // Only show bottom navigation on mobile
      bottomNavigationBar: isSmallScreen
          ? MobileBottomNav(
              currentIndex: _currentIndex,
              onTap: _onBottomNavTap,
            )
          : null,
    );
  }
}



