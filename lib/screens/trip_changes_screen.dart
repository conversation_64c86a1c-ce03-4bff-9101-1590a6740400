import 'dart:convert';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import '../generated/l10n/app_localizations.dart';
import '../models/trip.dart';
import '../services/api_service.dart';

class TripChangesScreen extends StatefulWidget {
  final Trip trip;

  const TripChangesScreen({super.key, required this.trip});

  @override
  State<TripChangesScreen> createState() => _TripChangesScreenState();
}

class _TripChangesScreenState extends State<TripChangesScreen> {
  bool _isLoading = true;
  String _errorMessage = '';
  Map<String, dynamic>? _currentTrip;
  Map<String, dynamic>? _originalTrip;


  @override
  void initState() {
    super.initState();
    _fetchTripHistory();
  }  Future<void> _fetchTripHistory() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });    try {
      // Add timestamp to prevent caching
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final response = await ApiService.get(
        'trips/${widget.trip.tripCode}/history?t=$timestamp',
      );
      
      print('Trip history response status: ${response.statusCode}');
      print('Trip history response body: ${response.body}');      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        print('Parsed data: $data');
        
        setState(() {
          _currentTrip = data['current'];
          _originalTrip = data['original'];
          _isLoading = false;
        });
        
        print('Current trip: $_currentTrip');
        print('Original trip: $_originalTrip');      } else if (response.statusCode == 304) {
        // 304 Not Modified - this might be due to caching
        // For 304 responses, there's typically no response body
        print('Received 304 Not Modified response - likely caching issue');
        setState(() {
          _errorMessage = 'Data not modified. This trip may not have any recorded changes or the data is cached.';
          _isLoading = false;
        });
      } else {
        setState(() {
          _errorMessage = 'Failed to load trip history: ${response.statusCode}';
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error fetching trip history: $e');
      setState(() {
        _errorMessage = 'Error: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Check if we're on a small screen (mobile portrait)
    final screenWidth = MediaQuery.of(context).size.width;
    final bool isSmallScreen = screenWidth < 600 && !kIsWeb;

    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context).tripChanges, style: const TextStyle(color: Colors.white)),
        backgroundColor: const Color(0xFF0D47A1),
        iconTheme: const IconThemeData(color: Colors.white),
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage.isNotEmpty
              ? Center(child: Text(_errorMessage, style: const TextStyle(color: Colors.red)))
              : _buildComparisonView(isSmallScreen),
    );
  }  Widget _buildComparisonView(bool isSmallScreen) {
    // Check if we have current trip data
    if (_currentTrip == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.warning,
              size: 64,
              color: Colors.orange.shade700,
            ),
            const SizedBox(height: 16),
            Text(
              'No trip data available',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.orange.shade700,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Unable to load current trip information.',
              style: TextStyle(fontSize: 14, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    if (_originalTrip == null) {
      return SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              elevation: 2,
              color: Colors.blue.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    Icon(Icons.info, color: Colors.blue.shade700),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'No Changes Detected',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.blue.shade700,
                            ),
                          ),
                          const SizedBox(height: 4),
                          const Text(
                            'This trip has not been modified since it was created. Current trip details are shown below.',
                            style: TextStyle(fontSize: 14),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            _buildTripCodeSection(),
            const SizedBox(height: 24),
            _buildCurrentTripDetails(),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,        children: [
          Text(
            AppLocalizations.of(context).tripChanges,
            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          _buildTripCodeSection(),
          const SizedBox(height: 24),
          isSmallScreen
              ? _buildMobileComparisonView()
              : _buildComparisonTable(),
        ],
      ),
    );
  }

  Widget _buildTripCodeSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            const Icon(Icons.confirmation_number, color: Color(0xFF0D47A1)),
            const SizedBox(width: 16),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Trip Code',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
                Text(
                  widget.trip.tripCode,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Method to show current trip details when there's no history
  Widget _buildCurrentTripDetails() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Current Trip Details',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildDetailRow('From', _currentTrip!['from_destination'] ?? _currentTrip!['custom_from'] ?? 'N/A'),
            _buildDetailRow('To', _currentTrip!['to_destination'] ?? _currentTrip!['custom_to'] ?? 'N/A'),
            _buildDetailRow('Date', _currentTrip!['date'] ?? 'N/A'),
            _buildDetailRow('Time', _currentTrip!['time'] ?? 'N/A'),
            _buildDetailRow('Waiting Trip', (_currentTrip!['is_waiting'] ?? false) ? 'Yes' : 'No'),
            if (_currentTrip!['is_waiting'] == true)
              _buildDetailRow('Return Time', _currentTrip!['return_time'] ?? 'N/A'),
            _buildDetailRow('Passengers', _formatPassengers(_currentTrip!['passengers'] ?? [])),
            _buildDetailRow('Cargo', _formatCargo(_currentTrip!['cargos'] ?? [])),
            _buildDetailRow('Notes', _currentTrip!['notes'] ?? 'None'),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }

  // Mobile-friendly comparison view using cards instead of a table
  Widget _buildMobileComparisonView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Changes Overview',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),        // From
        _buildComparisonCard(
          'From',
          _originalTrip!['from_destination'] ?? _originalTrip!['custom_from'] ?? 'N/A',
          _currentTrip!['from_destination'] ?? _currentTrip!['custom_from'] ?? 'N/A',
          _originalTrip!['custom_from'] != _currentTrip!['custom_from'] ||
              _originalTrip!['from_destination'] != _currentTrip!['from_destination'],
        ),
        // To
        _buildComparisonCard(
          'To',
          _originalTrip!['to_destination'] ?? _originalTrip!['custom_to'] ?? 'N/A',
          _currentTrip!['to_destination'] ?? _currentTrip!['custom_to'] ?? 'N/A',
          _originalTrip!['custom_to'] != _currentTrip!['custom_to'] ||
              _originalTrip!['to_destination'] != _currentTrip!['to_destination'],
        ),
        // Date
        _buildComparisonCard(
          'Date',
          _originalTrip!['date'] ?? 'N/A',
          _currentTrip!['date'] ?? 'N/A',
          _originalTrip!['date'] != _currentTrip!['date'],
        ),
        // Time
        _buildComparisonCard(
          'Time',
          _originalTrip!['time'] ?? 'N/A',
          _currentTrip!['time'] ?? 'N/A',
          _originalTrip!['time'] != _currentTrip!['time'],
        ),
        // Waiting Trip
        _buildComparisonCard(
          'Waiting Trip',
          (_originalTrip!['is_waiting'] ?? false) ? 'Yes' : 'No',
          (_currentTrip!['is_waiting'] ?? false) ? 'Yes' : 'No',
          _originalTrip!['is_waiting'] != _currentTrip!['is_waiting'],
        ),
        // Return Time (if applicable)
        if (_currentTrip!['is_waiting'] == true || _originalTrip!['is_waiting'] == true)
          _buildComparisonCard(
            'Return Time',
            _originalTrip!['return_time'] ?? 'N/A',
            _currentTrip!['return_time'] ?? 'N/A',
            _originalTrip!['return_time'] != _currentTrip!['return_time'],
          ),
        // Passengers
        _buildComparisonCard(
          'Passengers',
          _formatPassengers(_originalTrip!['passengers'] ?? []),
          _formatPassengers(_currentTrip!['passengers'] ?? []),
          _arePassengersDifferent(_originalTrip!['passengers'] ?? [], _currentTrip!['passengers'] ?? []),
        ),        // Cargo
        _buildComparisonCard(
          'Cargo',
          _formatCargo(_originalTrip!['cargos'] ?? []),
          _formatCargo(_currentTrip!['cargos'] ?? []),
          _areCargosDifferent(_originalTrip!['cargos'] ?? [], _currentTrip!['cargos'] ?? []),
        ),
        // Notes
        _buildComparisonCard(
          'Notes',
          _originalTrip!['notes'] ?? 'None',
          _currentTrip!['notes'] ?? 'None',
          _originalTrip!['notes'] != _currentTrip!['notes'],
        ),
      ],
    );
  }

  // Card widget for mobile comparison view
  Widget _buildComparisonCard(String field, String originalValue, String newValue, bool hasChanged) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12.0),
      elevation: hasChanged ? 2 : 1,
      color: hasChanged ? Colors.yellow.shade50 : null,
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Field name
            Text(
              field,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            const Divider(),
            // Original value
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(
                  width: 100,
                  child: Text(
                    'Original:',
                    style: TextStyle(fontSize: 14, color: Colors.grey),
                  ),
                ),
                Expanded(
                  child: Text(
                    originalValue,
                    style: hasChanged
                        ? const TextStyle(
                            decoration: TextDecoration.lineThrough,
                            color: Colors.red,
                            fontSize: 14,
                          )
                        : const TextStyle(fontSize: 14),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            // New value
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(
                  width: 100,
                  child: Text(
                    'New:',
                    style: TextStyle(fontSize: 14, color: Colors.grey),
                  ),
                ),
                Expanded(
                  child: Text(
                    newValue,
                    style: hasChanged
                        ? const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.green,
                            fontSize: 14,
                          )
                        : const TextStyle(fontSize: 14),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Desktop table view
  Widget _buildComparisonTable() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Changes Overview',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Table(
              border: TableBorder.all(
                color: Colors.grey.shade300,
                width: 1,
              ),
              columnWidths: const {
                0: FlexColumnWidth(1),
                1: FlexColumnWidth(2),
                2: FlexColumnWidth(2),
              },
              children: [
                _buildTableHeader(),                _buildTableRow(
                  'From',
                  _originalTrip!['from_destination'] ?? _originalTrip!['custom_from'] ?? 'N/A',
                  _currentTrip!['from_destination'] ?? _currentTrip!['custom_from'] ?? 'N/A',
                  _originalTrip!['custom_from'] != _currentTrip!['custom_from'] ||
                      _originalTrip!['from_destination'] != _currentTrip!['from_destination'],
                ),
                _buildTableRow(
                  'To',
                  _originalTrip!['to_destination'] ?? _originalTrip!['custom_to'] ?? 'N/A',
                  _currentTrip!['to_destination'] ?? _currentTrip!['custom_to'] ?? 'N/A',
                  _originalTrip!['custom_to'] != _currentTrip!['custom_to'] ||
                      _originalTrip!['to_destination'] != _currentTrip!['to_destination'],
                ),
                _buildTableRow(
                  'Date',
                  _originalTrip!['date'] ?? 'N/A',
                  _currentTrip!['date'] ?? 'N/A',
                  _originalTrip!['date'] != _currentTrip!['date'],
                ),
                _buildTableRow(
                  'Time',
                  _originalTrip!['time'] ?? 'N/A',
                  _currentTrip!['time'] ?? 'N/A',
                  _originalTrip!['time'] != _currentTrip!['time'],
                ),
                _buildTableRow(
                  'Waiting Trip',
                  (_originalTrip!['is_waiting'] ?? false) ? 'Yes' : 'No',
                  (_currentTrip!['is_waiting'] ?? false) ? 'Yes' : 'No',
                  _originalTrip!['is_waiting'] != _currentTrip!['is_waiting'],
                ),
                if (_currentTrip!['is_waiting'] == true || _originalTrip!['is_waiting'] == true)
                  _buildTableRow(
                    'Return Time',
                    _originalTrip!['return_time'] ?? 'N/A',
                    _currentTrip!['return_time'] ?? 'N/A',
                    _originalTrip!['return_time'] != _currentTrip!['return_time'],
                  ),
                _buildTableRow(
                  'Passengers',
                  _formatPassengers(_originalTrip!['passengers'] ?? []),
                  _formatPassengers(_currentTrip!['passengers'] ?? []),
                  _arePassengersDifferent(_originalTrip!['passengers'] ?? [], _currentTrip!['passengers'] ?? []),
                ),
                _buildTableRow(
                  'Cargo',
                  _formatCargo(_originalTrip!['cargos'] ?? []),
                  _formatCargo(_currentTrip!['cargos'] ?? []),
                  _areCargosDifferent(_originalTrip!['cargos'] ?? [], _currentTrip!['cargos'] ?? []),
                ),
                _buildTableRow(
                  'Notes',
                  _originalTrip!['notes'] ?? 'None',
                  _currentTrip!['notes'] ?? 'None',
                  _originalTrip!['notes'] != _currentTrip!['notes'],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  TableRow _buildTableHeader() {
    return TableRow(
      decoration: BoxDecoration(
        color: Colors.grey.shade200,
      ),
      children: const [
        Padding(
          padding: EdgeInsets.all(8.0),
          child: Text(
            'Field',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        Padding(
          padding: EdgeInsets.all(8.0),
          child: Text(
            'Original Value',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        Padding(
          padding: EdgeInsets.all(8.0),
          child: Text(
            'New Value',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
      ],
    );
  }

  TableRow _buildTableRow(String field, String originalValue, String newValue, bool hasChanged) {
    return TableRow(
      decoration: hasChanged
          ? BoxDecoration(
              color: Colors.yellow.shade50,
            )
          : null,
      children: [
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text(field),
        ),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text(
            originalValue,
            style: hasChanged
                ? const TextStyle(
                    decoration: TextDecoration.lineThrough,
                    color: Colors.red,
                  )
                : null,
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text(
            newValue,
            style: hasChanged
                ? const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  )
                : null,
          ),
        ),
      ],
    );
  }

  // Format passengers list to a readable string
  String _formatPassengers(List<dynamic> passengers) {
    if (passengers.isEmpty) {
      return 'None';
    }

    return passengers
        .map((passenger) => passenger['name'] as String)
        .join(', ');
  }  // Format cargo list to a readable string
  String _formatCargo(List<dynamic> cargos) {
    if (cargos.isEmpty) {
      return 'None';
    }

    return cargos
        .map((cargo) => cargo['cargo_name'] as String)
        .join(', ');
  }

  // Check if passengers lists are different
  bool _arePassengersDifferent(List<dynamic> originalPassengers, List<dynamic> newPassengers) {
    if (originalPassengers.length != newPassengers.length) {
      return true;
    }

    // Create sets of passenger IDs for comparison
    final originalIds = originalPassengers
        .map((passenger) => passenger['user_id'].toString())
        .toSet();

    final newIds = newPassengers
        .map((passenger) => passenger['user_id'].toString())
        .toSet();

    // Check if the sets are equal
    return originalIds.length != newIds.length ||
        originalIds.any((id) => !newIds.contains(id));
  }

  // Check if cargo lists are different
  bool _areCargosDifferent(List<dynamic> originalCargos, List<dynamic> newCargos) {
    if (originalCargos.length != newCargos.length) {
      return true;
    }

    // Create sets of cargo IDs for comparison
    final originalIds = originalCargos
        .map((cargo) => cargo['cargo_id'].toString())
        .toSet();

    final newIds = newCargos
        .map((cargo) => cargo['cargo_id'].toString())
        .toSet();

    // Check if the sets are equal
    return originalIds.length != newIds.length ||
        originalIds.any((id) => !newIds.contains(id));
  }
}



