import 'dart:convert';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../generated/l10n/app_localizations.dart';
import '../models/car.dart';
import '../services/api_service.dart';
import '../sidebar.dart';
import '../widgets/common_app_bar.dart';
import '../widgets/mobile_bottom_nav.dart';

class CarOdometerHistoryScreen extends StatefulWidget {
  const CarOdometerHistoryScreen({super.key});

  @override
  State<CarOdometerHistoryScreen> createState() => _CarOdometerHistoryScreenState();
}

class _CarOdometerHistoryScreenState extends State<CarOdometerHistoryScreen> {
  bool _isLoading = true;
  String _errorMessage = '';
  List<Car> _cars = [];
  Car? _selectedCar;
  List<Map<String, dynamic>> _historyRecords = [];
  List<Map<String, dynamic>> _filteredRecords = [];

  // For pagination
  int _rowsPerPage = 10;
  int _currentPage = 0;

  // For filtering
  final TextEditingController _searchController = TextEditingController();
  DateTime? _selectedDate;
  bool _viewAllCars = false;

  // For mobile navigation
  int _currentIndex = 3; // Settings tab

  @override
  void initState() {
    super.initState();
    _fetchCars();

    // Add listener to search controller
    _searchController.addListener(_filterRecords);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  // Fetch all cars
  Future<void> _fetchCars() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final response = await ApiService.get('cars');

      if (response.statusCode == 200) {
        final data = ApiService.parseResponse(response);
        final List<Car> cars = (data['cars'] as List)
            .map((carJson) => Car.fromJson(carJson))
            .toList();

        setState(() {
          _cars = cars;
          _isLoading = false;

          // If there are cars, select the first one by default
          if (_cars.isNotEmpty && _selectedCar == null) {
            _selectedCar = _cars.first;
            _fetchCarOdometerHistory(_selectedCar!.carId!);
          } else {
            _isLoading = false;
          }
        });
      } else {
        setState(() {
          _errorMessage = 'Failed to load cars: ${response.statusCode}';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error: $e';
        _isLoading = false;
      });
    }
  }

  // Fetch odometer history for a specific car
  Future<void> _fetchCarOdometerHistory(int carId) async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
      _viewAllCars = false;
    });

    try {
      // Build query parameters for date filtering
      final Map<String, String> queryParams = {};
      if (_selectedDate != null) {
        queryParams['date'] = DateFormat('yyyy-MM-dd').format(_selectedDate!);
      }

      // Add limit parameter
      queryParams['limit'] = '100';

      // Build the endpoint with query parameters
      String endpoint = 'cars/$carId/odometer-history';
      if (queryParams.isNotEmpty) {
        endpoint += '?${queryParams.entries.map((e) => '${e.key}=${e.value}').join('&')}';
      }
      final response = await ApiService.get(endpoint);

      if (response.statusCode == 200) {
        final data = ApiService.parseResponse(response);

        setState(() {
          _historyRecords = List<Map<String, dynamic>>.from(data['history']);
          _filterRecords();
          _isLoading = false;
        });
      } else {
        setState(() {
          _errorMessage = 'Failed to load odometer history: ${response.statusCode}';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error: $e';
        _isLoading = false;
      });
    }
  }

  // Fetch odometer history for all cars
  Future<void> _fetchAllCarOdometerHistory() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
      _selectedCar = null;
      _viewAllCars = true;
    });

    try {
      // Build query parameters for date filtering
      final Map<String, String> queryParams = {};
      if (_selectedDate != null) {
        queryParams['date'] = DateFormat('yyyy-MM-dd').format(_selectedDate!);
      }

      // Add pagination parameters
      queryParams['limit'] = '100';
      queryParams['offset'] = '0';

      // Build the endpoint with query parameters
      String endpoint = 'cars/odometer-history/all';
      if (queryParams.isNotEmpty) {
        endpoint += '?${queryParams.entries.map((e) => '${e.key}=${e.value}').join('&')}';
      }
      final response = await ApiService.get(endpoint);

      if (response.statusCode == 200) {
        final data = ApiService.parseResponse(response);

        setState(() {
          _historyRecords = List<Map<String, dynamic>>.from(data['history']);
          _filterRecords();
          _isLoading = false;
        });
      } else {
        setState(() {
          _errorMessage = 'Failed to load odometer history: ${response.statusCode}';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error: $e';
        _isLoading = false;
      });
    }
  }

  // Filter records based on search text
  void _filterRecords() {
    final searchText = _searchController.text.toLowerCase();

    setState(() {
      if (searchText.isEmpty) {
        _filteredRecords = List.from(_historyRecords);
      } else {
        _filteredRecords = _historyRecords.where((record) {
          // Search in multiple fields
          return
            (record['car_code']?.toString().toLowerCase() ?? '').contains(searchText) ||
            (record['trip_code']?.toString().toLowerCase() ?? '').contains(searchText) ||
            (record['driver_code']?.toString().toLowerCase() ?? '').contains(searchText) ||
            (record['driver_name']?.toString().toLowerCase() ?? '').contains(searchText) ||
            (record['notes']?.toString().toLowerCase() ?? '').contains(searchText);
        }).toList();
      }

      // Reset pagination when filtering
      _currentPage = 0;
    });
  }

  // Handle car selection change
  void _onCarChanged(Car? newCar) {
    if (newCar != null && newCar != _selectedCar) {
      setState(() {
        _selectedCar = newCar;
        _viewAllCars = false;
      });
      _fetchCarOdometerHistory(newCar.carId!);
    }
  }

  // Handle date selection
  Future<void> _selectDate(BuildContext context) async {
    final initialDate = _selectedDate ?? DateTime.now();

    final newDate = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 1)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Color(0xFF0D47A1),
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (newDate != null) {
      setState(() {
        _selectedDate = newDate;
      });

      if (_viewAllCars) {
        _fetchAllCarOdometerHistory();
      } else if (_selectedCar != null) {
        _fetchCarOdometerHistory(_selectedCar!.carId!);
      }
    }
  }

  // Clear all filters
  void _clearFilters() {
    setState(() {
      _selectedDate = null;
      _searchController.text = '';
    });

    if (_viewAllCars) {
      _fetchAllCarOdometerHistory();
    } else if (_selectedCar != null) {
      _fetchCarOdometerHistory(_selectedCar!.carId!);
    }
  }

  // Toggle between viewing all cars and a specific car
  void _toggleViewMode(bool viewAll) {
    if (viewAll == _viewAllCars) return;

    setState(() {
      _viewAllCars = viewAll;
    });

    if (viewAll) {
      _fetchAllCarOdometerHistory();
    } else if (_selectedCar != null) {
      _fetchCarOdometerHistory(_selectedCar!.carId!);
    } else if (_cars.isNotEmpty) {
      _selectedCar = _cars.first;
      _fetchCarOdometerHistory(_selectedCar!.carId!);
    }
  }

  // Handle bottom navigation
  void _onBottomNavTap(int index) {
    if (index == _currentIndex) return;

    switch (index) {
      case 0: // Dashboard
        Navigator.pushReplacementNamed(context, '/trip-manager-dashboard');
      case 1: // Monitoring
        Navigator.pushReplacementNamed(context, '/trip-monitoring');
      case 2: // Approvals
        Navigator.pushReplacementNamed(context, '/trip-approval');
      case 3: // Settings - current screen
        // Already here
    }
  }

  // Show image dialog for viewing odometer photos
  void _showImageDialog(BuildContext context, String imageData) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          child: Container(
            constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                AppBar(
                  title: const Text('Odometer Photo'),
                  automaticallyImplyLeading: false,
                  actions: [
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ],
                ),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: kIsWeb
                          ? Image.network(
                              imageData,
                              fit: BoxFit.contain,
                              errorBuilder: (context, error, stackTrace) {
                                return const Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(Icons.broken_image, size: 64),
                                      Text('Failed to load image'),
                                    ],
                                  ),
                                );
                              },
                            )
                          : Image.memory(
                              base64Decode(imageData.split(',').last),
                              fit: BoxFit.contain,
                              errorBuilder: (context, error, stackTrace) {
                                return const Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(Icons.broken_image, size: 64),
                                      Text('Failed to load image'),
                                    ],
                                  ),
                                );
                              },
                            ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    // Check if we're on a small screen (mobile)
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600 && !kIsWeb;

    return Scaffold(      appBar: CommonAppBar(
        title: AppLocalizations.of(context).carOdometerHistory,
      ),
      drawer: const SidebarNavigation(),
      body: _buildBody(isSmallScreen),
      bottomNavigationBar: isSmallScreen
          ? MobileBottomNav(
              currentIndex: _currentIndex,
              onTap: _onBottomNavTap,
            )
          : null,
    );
  }

  Widget _buildBody(bool isSmallScreen) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage.isNotEmpty) {
      return Center(child: Text(_errorMessage));
    }

    return Column(
      children: [
        // Filters section
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [              // Title
              Text(
                AppLocalizations.of(context).carOdometerHistory,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF0D47A1),
                ),
              ),
              const SizedBox(height: 16),

              // Filters
              isSmallScreen
                  ? _buildMobileFilters()
                  : _buildDesktopFilters(),
            ],
          ),
        ),

        // Data section
        Expanded(
          child: _filteredRecords.isEmpty
              ? Center(child: Text(AppLocalizations.of(context).noOdometerHistoryRecordsFound))
              : isSmallScreen
                  ? _buildMobileHistoryList()
                  : _buildDesktopHistoryTable(),
        ),
      ],
    );
  }

  Widget _buildMobileFilters() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // View mode toggle
        Row(
          children: [            Expanded(
              child: SegmentedButton<bool>(
                segments: [
                  ButtonSegment<bool>(
                    value: false,
                    label: Text(AppLocalizations.of(context).singleCar),
                    icon: const Icon(Icons.directions_car),
                  ),
                  ButtonSegment<bool>(
                    value: true,
                    label: Text(AppLocalizations.of(context).allCars),
                    icon: const Icon(Icons.directions_car_filled),
                  ),
                ],
                selected: {_viewAllCars},
                onSelectionChanged: (Set<bool> newSelection) {
                  _toggleViewMode(newSelection.first);
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),        // Car dropdown - only show when not viewing all cars
        if (!_viewAllCars)
          ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 300),
            child: DropdownButtonFormField<Car>(
            isExpanded: true,
            decoration: InputDecoration(
              labelText: AppLocalizations.of(context).selectCar,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            value: _selectedCar,
            items: _cars.map((car) {
              return DropdownMenuItem<Car>(
                value: car,
                child: Container(
                  constraints: const BoxConstraints(maxWidth: 250),
                  child: Tooltip(
                    message: car.displayName,
                    child: Text(
                      car.shortDisplayName,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),
                ),
              );
            }).toList(),
            onChanged: _onCarChanged,
          ),
          ),
        if (!_viewAllCars)
          const SizedBox(height: 12),

        // Date picker
        Row(
          children: [
            Expanded(            child: OutlinedButton.icon(
                icon: const Icon(Icons.calendar_today),
                label: Text(
                  _selectedDate != null
                      ? DateFormat('MM/dd/yyyy').format(_selectedDate!)
                      : AppLocalizations.of(context).selectDate,
                ),
                onPressed: () => _selectDate(context),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),        // Search field
        TextField(
          controller: _searchController,
          decoration: InputDecoration(
            labelText: AppLocalizations.of(context).search,
            hintText: AppLocalizations.of(context).searchByDriverTripNotes,
            prefixIcon: const Icon(Icons.search),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            suffixIcon: _searchController.text.isNotEmpty
                ? IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () {
                      _searchController.clear();
                    },
                  )
                : null,
          ),
        ),
        const SizedBox(height: 12),

        // Clear filters button
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            TextButton.icon(
              icon: const Icon(Icons.clear_all),
              label: Text(AppLocalizations.of(context).clearFilters),
              onPressed: _clearFilters,
            ),
            const SizedBox(width: 8),
            TextButton.icon(
              icon: const Icon(Icons.refresh),
              label: Text(AppLocalizations.of(context).refresh),
              onPressed: () {
                if (_viewAllCars) {
                  _fetchAllCarOdometerHistory();
                } else if (_selectedCar != null) {
                  _fetchCarOdometerHistory(_selectedCar!.carId!);
                }
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDesktopFilters() {
    return Row(
      children: [        // View mode toggle
        SegmentedButton<bool>(
          segments: [
            ButtonSegment<bool>(
              value: false,
              label: Text(AppLocalizations.of(context).singleCar),
              icon: const Icon(Icons.directions_car),
            ),
            ButtonSegment<bool>(
              value: true,
              label: Text(AppLocalizations.of(context).allCars),
              icon: const Icon(Icons.directions_car_filled),
            ),
          ],
          selected: {_viewAllCars},
          onSelectionChanged: (Set<bool> newSelection) {
            _toggleViewMode(newSelection.first);
          },
        ),
        const SizedBox(width: 16),        // Car dropdown - only show when not viewing all cars
        if (!_viewAllCars)
          Expanded(
            flex: 2,
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 300),
              child: DropdownButtonFormField<Car>(
              isExpanded: true,
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context).selectCar,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              value: _selectedCar,
              items: _cars.map((car) {
                return DropdownMenuItem<Car>(
                  value: car,
                  child: Container(
                    constraints: const BoxConstraints(maxWidth: 250),
                    child: Tooltip(
                      message: car.displayName,
                      child: Text(
                        car.shortDisplayName,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                  ),
                );
              }).toList(),
              onChanged: _onCarChanged,
            ),
          ),
          ),
        if (!_viewAllCars)
          const SizedBox(width: 16),

        // Date picker
        Expanded(
          flex: 2,
          child: OutlinedButton.icon(
            icon: const Icon(Icons.calendar_today),            label: Text(
              _selectedDate != null
                  ? DateFormat('MM/dd/yyyy').format(_selectedDate!)
                  : AppLocalizations.of(context).selectDate,
            ),
            onPressed: () => _selectDate(context),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
          ),
        ),
        const SizedBox(width: 16),

        // Search field
        Expanded(
          flex: 3,
          child: TextField(            controller: _searchController,
            decoration: InputDecoration(
              labelText: AppLocalizations.of(context).search,
              hintText: AppLocalizations.of(context).searchByDriverTripNotes,
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                      },
                    )
                  : null,
            ),
          ),
        ),
        const SizedBox(width: 16),        // Clear filters button
        TextButton.icon(
          icon: const Icon(Icons.clear_all),
          label: Text(AppLocalizations.of(context).clearFilters),
          onPressed: _clearFilters,
        ),
        const SizedBox(width: 8),

        // Refresh button
        TextButton.icon(
          icon: const Icon(Icons.refresh),
          label: Text(AppLocalizations.of(context).refresh),
          onPressed: () {
            if (_viewAllCars) {
              _fetchAllCarOdometerHistory();
            } else if (_selectedCar != null) {
              _fetchCarOdometerHistory(_selectedCar!.carId!);
            }
          },
        ),
      ],
    );
  }

  Widget _buildMobileHistoryList() {
    return RefreshIndicator(
      onRefresh: () async {
        if (_selectedCar != null) {
          await _fetchCarOdometerHistory(_selectedCar!.carId!);
        }
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(8.0),
        itemCount: _filteredRecords.length,
        itemBuilder: (context, index) {
          final record = _filteredRecords[index];
          final recordedAt = DateTime.parse(record['recorded_at']);
          final formattedDate = DateFormat('MM/dd/yyyy hh:mm a').format(recordedAt);

          return Card(
            margin: const EdgeInsets.only(bottom: 12.0),
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Date and odometer reading
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        formattedDate,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        '${record['odometer_reading']} km',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: Color(0xFF0D47A1),
                        ),
                      ),
                    ],
                  ),
                  const Divider(),                  // Trip and driver info
                  if (record['trip_code'] != null) ...[
                    Text('${AppLocalizations.of(context).tripLabel} ${record['trip_code']}'),
                    const SizedBox(height: 4),
                  ],
                  if (record['driver_name'] != null) ...[
                    Text('${AppLocalizations.of(context).driverLabel} ${record['driver_name']} (${record['driver_code']})'),
                    const SizedBox(height: 4),
                  ],                  // Notes
                  if (record['notes'] != null && record['notes'] != '') ...[
                    const SizedBox(height: 8),
                    Text(
                      AppLocalizations.of(context).notesLabel,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(record['notes']),
                  ],

                  // Image
                  if (record['image_data'] != null && record['image_data'] != '') ...[
                    const SizedBox(height: 8),
                    const Text(
                      'Photo',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    GestureDetector(
                      onTap: () => _showImageDialog(context, record['image_data']),
                      child: Container(
                        height: 80,
                        width: double.infinity,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: kIsWeb
                              ? Image.network(
                                  record['image_data'],
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return const Center(
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          Icon(Icons.broken_image, size: 24),
                                          Text('Failed to load image', style: TextStyle(fontSize: 12)),
                                        ],
                                      ),
                                    );
                                  },
                                )
                              : Image.memory(
                                  base64Decode(record['image_data'].split(',').last),
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return const Center(
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          Icon(Icons.broken_image, size: 24),
                                          Text('Failed to load image', style: TextStyle(fontSize: 12)),
                                        ],
                                      ),
                                    );
                                  },
                                ),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildDesktopHistoryTable() {
    // Calculate the number of pages
    final int rowCount = _filteredRecords.length;
    final int pageCount = (rowCount / _rowsPerPage).ceil();

    // Calculate the range of records to display
    final int startIndex = _currentPage * _rowsPerPage;
    final int endIndex = (startIndex + _rowsPerPage) > rowCount
        ? rowCount
        : startIndex + _rowsPerPage;

    // Get the records for the current page
    final List<Map<String, dynamic>> pageRecords =
        _filteredRecords.sublist(startIndex, endIndex);

    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Theme(
              data: Theme.of(context).copyWith(
                dataTableTheme: DataTableThemeData(
                  columnSpacing: 16,
                  horizontalMargin: 16,
                  headingTextStyle: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF0D47A1),
                  ),
                ),
              ),
              child: DataTable(                columns: [
                  DataColumn(label: Text(AppLocalizations.of(context).dateTime)),
                  DataColumn(label: Text(AppLocalizations.of(context).odometer)),
                  DataColumn(label: Text(AppLocalizations.of(context).trip)),
                  DataColumn(label: Text(AppLocalizations.of(context).driver)),
                  DataColumn(label: Text(AppLocalizations.of(context).notes)),
                  const DataColumn(label: Text('Image')),
                ],
                rows: pageRecords.map((record) {
                  final recordedAt = DateTime.parse(record['recorded_at']);
                  final formattedDate = DateFormat('MM/dd/yyyy hh:mm a').format(recordedAt);

                  return DataRow(
                    cells: [
                      DataCell(Text(formattedDate)),
                      DataCell(Text('${record['odometer_reading']} km')),
                      DataCell(Text(record['trip_code'] ?? '-')),
                      DataCell(Text(record['driver_name'] != null
                          ? '${record['driver_name']} (${record['driver_code']})'
                          : '-')),                      DataCell(
                        record['notes'] != null && record['notes'] != ''
                            ? Tooltip(
                                message: record['notes'],
                                child: Text(
                                  record['notes'],
                                  overflow: TextOverflow.ellipsis,
                                ),
                              )
                            : const Text('-'),
                      ),
                      DataCell(
                        record['image_data'] != null && record['image_data'] != ''
                            ? GestureDetector(
                                onTap: () => _showImageDialog(context, record['image_data']),
                                child: Container(
                                  width: 40,
                                  height: 40,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(4),
                                    border: Border.all(color: Colors.grey),
                                  ),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(4),
                                    child: kIsWeb
                                        ? Image.network(
                                            record['image_data'],
                                            fit: BoxFit.cover,
                                            errorBuilder: (context, error, stackTrace) {
                                              return const Icon(Icons.broken_image, size: 20);
                                            },
                                          )
                                        : Image.memory(
                                            base64Decode(record['image_data'].split(',').last),
                                            fit: BoxFit.cover,
                                            errorBuilder: (context, error, stackTrace) {
                                              return const Icon(Icons.broken_image, size: 20);
                                            },
                                          ),
                                  ),
                                ),
                              )
                            : const Text('-'),
                      ),
                    ],
                  );
                }).toList(),
              ),
            ),
          ),
        ),

        // Pagination controls
        if (pageCount > 1)
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                IconButton(
                  icon: const Icon(Icons.first_page),
                  onPressed: _currentPage > 0
                      ? () => setState(() => _currentPage = 0)
                      : null,
                ),                IconButton(
                  icon: const Icon(Icons.navigate_before),
                  onPressed: _currentPage > 0
                      ? () => setState(() => _currentPage--)
                      : null,
                ),
                const SizedBox(width: 16),
                Text(AppLocalizations.of(context).pageXofY(_currentPage + 1, pageCount)),
                const SizedBox(width: 16),
                IconButton(
                  icon: const Icon(Icons.navigate_next),
                  onPressed: _currentPage < pageCount - 1
                      ? () => setState(() => _currentPage++)
                      : null,
                ),
                IconButton(
                  icon: const Icon(Icons.last_page),
                  onPressed: _currentPage < pageCount - 1
                      ? () => setState(() => _currentPage = pageCount - 1)
                      : null,
                ),
              ],
            ),
          ),
      ],
    );
  }
}



