import 'dart:convert';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import '../generated/l10n/app_localizations.dart';
import '../models/car.dart';
import '../services/api_service.dart';
import '../sidebar.dart';
import '../widgets/common_app_bar.dart';
import 'add_car_screen.dart';
import 'edit_car_screen.dart';

class CarSettingsScreen extends StatefulWidget {
  const CarSettingsScreen({super.key});

  @override
  State<CarSettingsScreen> createState() => CarSettingsScreenState();
}

class CarSettingsScreenState extends State<CarSettingsScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  List<Car> _cars = [];
  List<Car> _filteredCars = [];
  bool _isLoading = false;
  String _errorMessage = '';
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _fetchCars();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterCars(String query) {
    setState(() {
      _searchQuery = query.toLowerCase();
      _filteredCars = _cars.where((car) {
        return car.carCode.toLowerCase().contains(_searchQuery) ||
            car.manufacturer.toLowerCase().contains(_searchQuery) ||
            car.modelName.toLowerCase().contains(_searchQuery);
      }).toList();
    });
  }

  Future<void> _fetchCars() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final response = await ApiService.get('cars');

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = jsonDecode(response.body);
        setState(() {
          _cars = (data['cars'] as List)
              .map((json) => Car.fromJson(json))
              .toList();
          _filteredCars = _cars;
        });      } else {
        setState(() {
          _errorMessage = AppLocalizations.of(context).failedToLoadCarsWithCode(response.statusCode.toString());
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = '${AppLocalizations.of(context).anErrorOccurred}: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _deleteCar(Car car) async {
    // Store context reference before async gap
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    try {
      final response = await ApiService.delete('cars/${car.carCode}');

      // Check if widget is still mounted before updating UI
      if (!mounted) return;      if (response.statusCode == 200) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).carDeletedSuccessfully),
            backgroundColor: Colors.green,
          ),
        );
        _fetchCars();
      } else {
        final errorData = jsonDecode(response.body);
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(errorData['error'] ?? AppLocalizations.of(context).failedToDeleteCar),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      // Check if widget is still mounted before updating UI
      if (!mounted) return;

      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('${AppLocalizations.of(context).anErrorOccurred}: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
  void _showDeleteConfirmation(Car car) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(AppLocalizations.of(context).confirmDelete),
          content: Text(AppLocalizations.of(context).confirmDeleteCar(car.carCode)),
          actions: [
            TextButton(
              child: Text(AppLocalizations.of(context).cancel),
              onPressed: () => Navigator.of(context).pop(),
            ),
            TextButton(
              child: Text(
                AppLocalizations.of(context).delete,
                style: const TextStyle(color: Colors.red),
              ),
              onPressed: () {
                Navigator.of(context).pop();
                _deleteCar(car);
              },
            ),
          ],
        );
      },
    );
  }

  // Build a mobile-friendly card for each car
  Widget _buildCarCard(Car car) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Car code and manufacturer
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: const Color(0xFF0D47A1).withAlpha(25),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    car.carCode,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF0D47A1),
                    ),
                  ),
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(left: 8.0),
                    child: Text(
                      '${car.manufacturer} ${car.modelName}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                      textAlign: TextAlign.end,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            // Car details
            Row(
                  children: [
                    const Icon(Icons.directions_car, size: 16, color: Colors.grey),
                    const SizedBox(width: 4),
                    Text('${AppLocalizations.of(context).type}: ${car.type}'),
                    const Spacer(),
                    const Icon(Icons.palette, size: 16, color: Colors.grey),
                    const SizedBox(width: 4),
                    Text('${AppLocalizations.of(context).color}: ${car.color}'),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    const Icon(Icons.confirmation_number, size: 16, color: Colors.grey),
                    const SizedBox(width: 4),
                    Text('${AppLocalizations.of(context).plateNumber}: ${car.plateNumber}'),
                    const Spacer(),
                    const Icon(Icons.speed, size: 16, color: Colors.grey),
                    const SizedBox(width: 4),
                    Text('${AppLocalizations.of(context).odometer}: ${car.odometer}'),
                  ],
                ),
            const SizedBox(height: 12),
            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [                TextButton.icon(
                  icon: const Icon(Icons.edit, size: 18),
                  label: Text(AppLocalizations.of(context).edit),
                  style: TextButton.styleFrom(
                    foregroundColor: const Color(0xFF0D47A1),
                  ),
                  onPressed: () async {
                    final result = await Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => EditCarScreen(car: car),
                      ),
                    );
                    if (result == true) {
                      _fetchCars();
                    }
                  },
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  icon: const Icon(Icons.delete, size: 18),
                  label: Text(AppLocalizations.of(context).delete),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.red,
                  ),
                  onPressed: () => _showDeleteConfirmation(car),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Check if we're on a small screen (mobile)
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600 && !kIsWeb;

    return Scaffold(
      key: _scaffoldKey,      drawer: const SidebarNavigation(),
      appBar: CommonAppBar(
        title: AppLocalizations.of(context).carSettingsTitle,
        onRefresh: _fetchCars,
        showMenuIcon: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage.isNotEmpty
              ? Center(child: Text(_errorMessage))
              : Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16.0),                      child: TextField(
                        controller: _searchController,
                        decoration: InputDecoration(
                          labelText: AppLocalizations.of(context).searchCars,
                          hintText: AppLocalizations.of(context).searchCarsHint,
                          prefixIcon: const Icon(Icons.search),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          filled: true,
                          fillColor: Colors.white,
                          suffixIcon: _searchQuery.isNotEmpty
                              ? IconButton(
                                  icon: const Icon(Icons.clear),
                                  onPressed: () {
                                    _searchController.clear();
                                    _filterCars('');
                                  },
                                )
                              : null,
                        ),
                        onChanged: _filterCars,
                      ),
                    ),
                    Expanded(                      child: _filteredCars.isEmpty
                          ? Center(
                              child: Text(
                                _searchQuery.isEmpty
                                    ? AppLocalizations.of(context).noCarsFound
                                    : AppLocalizations.of(context).noMatchingCarsFound,
                              ),
                            )
                          : isSmallScreen
                              // Mobile layout - card list
                              ? RefreshIndicator(
                                  onRefresh: () async {
                                    await _fetchCars();
                                  },
                                  child: ListView.builder(
                                    padding: const EdgeInsets.all(8.0),
                                    itemCount: _filteredCars.length,
                                    itemBuilder: (context, index) {
                                      return _buildCarCard(_filteredCars[index]);
                                    },
                                  ),
                                )
                              // Desktop layout - data table with horizontal scroll
                              : SingleChildScrollView(
                                  scrollDirection: Axis.horizontal,
                                  child: SingleChildScrollView(
                                    child: Padding(
                                      padding: const EdgeInsets.all(16.0),
                                      child: Theme(
                                        data: Theme.of(context).copyWith(
                                          dataTableTheme: DataTableThemeData(
                                            columnSpacing: 16,
                                            horizontalMargin: 16,
                                            headingTextStyle: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                              color: Color(0xFF0D47A1),
                                            ),
                                          ),
                                        ),
                                        child: DataTable(
                                          headingRowColor: WidgetStateProperty.all(
                                            const Color(0xFF0D47A1).withAlpha(25),
                                          ),                                          columns: [
                                            DataColumn(label: Text(AppLocalizations.of(context).carCode)),
                                            DataColumn(label: Text(AppLocalizations.of(context).manufacturer)),
                                            DataColumn(label: Text(AppLocalizations.of(context).model)),
                                            DataColumn(label: Text(AppLocalizations.of(context).type)),
                                            DataColumn(label: Text(AppLocalizations.of(context).plateNumber)),
                                            DataColumn(label: Text(AppLocalizations.of(context).odometer)),
                                            DataColumn(label: Text(AppLocalizations.of(context).color)),
                                            DataColumn(label: Text(AppLocalizations.of(context).actions)),
                                          ],
                                          rows: _filteredCars.map((car) {
                                            return DataRow(
                                              cells: [
                                                DataCell(Text(car.carCode)),
                                                DataCell(Text(car.manufacturer)),
                                                DataCell(Text(car.modelName)),
                                                DataCell(Text(car.type)),
                                                DataCell(Text(car.plateNumber)),
                                                DataCell(Text(car.odometer.toString())),
                                                DataCell(Text(car.color)),
                                                DataCell(
                                                  Row(
                                                    mainAxisSize: MainAxisSize.min,
                                                    children: [
                                                      IconButton(
                                                        icon: const Icon(Icons.edit),
                                                        onPressed: () async {
                                                          final result = await Navigator.push(
                                                            context,
                                                            MaterialPageRoute(
                                                              builder: (context) => EditCarScreen(car: car),
                                                            ),
                                                          );
                                                          if (result == true) {
                                                            _fetchCars();
                                                          }
                                                        },
                                                      ),
                                                      IconButton(
                                                        icon: const Icon(Icons.delete),
                                                        color: Colors.red,
                                                        onPressed: () => _showDeleteConfirmation(car),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            );
                                          }).toList(),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                    ),
                  ],
                ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: const Color(0xFF0D47A1),
        child: const Icon(Icons.add),
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => AddCarScreen()),
          );
          if (result == true) {
            _fetchCars();
          }
        },
      ),
    );
  }
}






