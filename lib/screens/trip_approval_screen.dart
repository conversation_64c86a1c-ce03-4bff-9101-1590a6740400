import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import '../generated/l10n/app_localizations.dart';
import '../models/trip.dart';
import '../services/api_service.dart';
import '../sidebar.dart';
import '../widgets/common_app_bar.dart';
import '../widgets/mobile_bottom_nav.dart';
import 'edit_trip_screen.dart';
import 'trip_changes_screen.dart';

class TripApprovalScreen extends StatefulWidget {
  const TripApprovalScreen({super.key});

  @override
  State<TripApprovalScreen> createState() => _TripApprovalScreenState();
}

class _TripApprovalScreenState extends State<TripApprovalScreen> {
  List<Trip> _trips = [];
  List<Trip> _filteredTrips = [];
  bool _isLoading = false;
  String _errorMessage = '';


  @override
  void initState() {
    super.initState();
    _fetchTrips();
  }



  Future<void> _fetchTrips() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      // Get current date in YYYY-MM-DD format
      final now = DateTime.now();
      final formattedDate = '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';

      print('Fetching trips for approval with date: $formattedDate');
      // Fetch trips with status EDIT ON REVIEW or CANCEL ON REVIEW (without date filter)
      final response = await ApiService.get(
        'trips/approval?status=EDIT%20ON%20REVIEW,CANCEL%20ON%20REVIEW'
      );

      print('Response status code: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = ApiService.parseResponse(response);
        setState(() {
          _trips = (data['trips'] as List)
              .map((json) => Trip.fromJson(json))
              .toList();
          print('Fetched ${_trips.length} trips for approval');
          _filteredTrips = List.from(_trips);
          _isLoading = false;
        });
      } else {
        setState(() {
          _errorMessage = 'Failed to load trips: ${response.statusCode}';
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error fetching trips: $e');
      setState(() {
        _errorMessage = 'Error: $e';
        _isLoading = false;
      });
    }
  }

  void _filterTrips(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredTrips = List.from(_trips);
      } else {
        _filteredTrips = _trips.where((trip) {
          return trip.tripCode.toLowerCase().contains(query.toLowerCase()) ||
              trip.fromDestination.toLowerCase().contains(query.toLowerCase()) ||
              trip.toDestination.toLowerCase().contains(query.toLowerCase()) ||
              trip.status.toLowerCase().contains(query.toLowerCase()) ||
              trip.requestorName.toLowerCase().contains(query.toLowerCase());
        }).toList();
      }
    });
  }
  // Get color based on trip status
  Color _getStatusColor(String status) {
    switch (status.toUpperCase()) {
      case 'EDIT ON REVIEW':
        return Colors.deepOrange;
      case 'CANCEL ON REVIEW':
        return Colors.red;
      default:
        return Colors.grey.shade700;
    }
  }

  Future<void> _approveTrip(Trip trip) async {
    try {
      final response = await ApiService.put(
        'trips/${trip.tripCode}/approve',
        {}
      );      if (response.statusCode == 200) {
        if (mounted) {
          // Show different success messages based on trip status
          String successMessage;
          if (trip.status == 'EDIT ON REVIEW') {
            successMessage = AppLocalizations.of(context).tripChangesApprovedSuccessfully;
          } else if (trip.status == 'CANCEL ON REVIEW') {
            successMessage = AppLocalizations.of(context).tripCancellationApprovedSuccessfully;
          } else {
            // Fallback message
            successMessage = AppLocalizations.of(context).tripApprovedMessage;
          }
          
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(successMessage),
              backgroundColor: Colors.green,
            ),
          );
          _fetchTrips(); // Refresh the list
        }
      } else {
        throw Exception('Failed to approve trip');
      }    } catch (e) {
      if (mounted) {
        // Show different error messages based on trip status
        String errorMessage;
        if (trip.status == 'EDIT ON REVIEW') {
          errorMessage = AppLocalizations.of(context).failedToApproveChanges;
        } else if (trip.status == 'CANCEL ON REVIEW') {
          errorMessage = AppLocalizations.of(context).failedToApproveCancellation;
        } else {
          // Fallback message
          errorMessage = AppLocalizations.of(context).failedToApproveTrip;
        }
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _rejectTrip(Trip trip, String reason) async {
    try {
      final response = await ApiService.put(
        'trips/${trip.tripCode}/reject',
        {'reason': reason}
      );      if (response.statusCode == 200) {
        if (mounted) {
          // Show different success messages based on trip status
          String successMessage;
          if (trip.status == 'EDIT ON REVIEW') {
            successMessage = AppLocalizations.of(context).tripChangesRejectedSuccessfully;
          } else if (trip.status == 'CANCEL ON REVIEW') {
            successMessage = AppLocalizations.of(context).tripCancellationRejectedSuccessfully;
          } else {
            // Fallback message
            successMessage = AppLocalizations.of(context).tripRejectedSuccessfully;
          }

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(successMessage),
              backgroundColor: Colors.green,
            ),
          );
          _fetchTrips(); // Refresh the list
        }
      } else if (response.statusCode == 400) {
        // Handle validation error (missing reason)
        final errorData = ApiService.parseResponse(response);
        throw Exception(errorData['error'] ?? 'Failed to reject trip');      } else {
        throw Exception('Failed to reject trip');
      }    } catch (e) {
      if (mounted) {
        // Show different error messages based on trip status
        String errorMessage;
        if (trip.status == 'EDIT ON REVIEW') {
          errorMessage = AppLocalizations.of(context).failedToRejectChanges;
        } else if (trip.status == 'CANCEL ON REVIEW') {
          errorMessage = AppLocalizations.of(context).failedToRejectCancellation;
        } else {
          // Fallback message
          errorMessage = AppLocalizations.of(context).failedToRejectTrip;
        }
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
  void _showApproveConfirmDialog(Trip trip) {
    // Customize message based on trip status
    final String title = AppLocalizations.of(context).confirmApproval;
    final String message;    if (trip.status == 'EDIT ON REVIEW') {
      message = AppLocalizations.of(context).areYouSureApproveChanges(trip.tripCode);
    } else if (trip.status == 'CANCEL ON REVIEW') {
      message = AppLocalizations.of(context).areYouSureApproveCancellation(trip.tripCode);
    } else {
      // Fallback for any other status
      message = AppLocalizations.of(context).areYouSureApproveTrip(trip.tripCode);
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [            TextButton(
              child: Text(AppLocalizations.of(context).cancel),
              onPressed: () => Navigator.of(context).pop(),
            ),
            TextButton(
              child: Text(
                AppLocalizations.of(context).approveTrip,
                style: const TextStyle(color: Colors.green),
              ),
              onPressed: () {
                Navigator.of(context).pop();
                _approveTrip(trip);
              },
            ),
          ],
        );
      },
    );
  }
  void _showRejectConfirmDialog(Trip trip) {
    // Customize message based on trip status
    final String title = AppLocalizations.of(context).confirmRejection;
    final String message;
    final TextEditingController reasonController = TextEditingController();
    bool isReasonValid = false;    if (trip.status == 'EDIT ON REVIEW') {
      message = AppLocalizations.of(context).areYouSureRejectChanges(trip.tripCode);
    } else if (trip.status == 'CANCEL ON REVIEW') {
      message = AppLocalizations.of(context).areYouSureRejectCancellation(trip.tripCode);
    } else {
      // Fallback for any other status
      message = AppLocalizations.of(context).areYouSureRejectTrip(trip.tripCode);
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text(title),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(message),
                  const SizedBox(height: 16),                  Text(
                    AppLocalizations.of(context).pleaseProvideReason,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: reasonController,                    decoration: InputDecoration(
                      hintText: AppLocalizations.of(context).enterRejectionReason,
                      border: const OutlineInputBorder(),errorText: reasonController.text.trim().isEmpty && !isReasonValid
                          ? AppLocalizations.of(context).reasonIsRequired
                          : null,
                    ),
                    maxLines: 3,
                    onChanged: (value) {
                      setState(() {
                        isReasonValid = value.trim().isNotEmpty;
                      });
                    },
                  ),
                ],
              ),              actions: [
                TextButton(
                  child: Text(AppLocalizations.of(context).cancel),
                  onPressed: () => Navigator.of(context).pop(),
                ),
                TextButton(
                  child: Text(
                    AppLocalizations.of(context).rejectTrip,
                    style: const TextStyle(color: Colors.red),
                  ),
                  onPressed: () {
                    if (reasonController.text.trim().isEmpty) {
                      setState(() {
                        isReasonValid = false;
                      });
                      return;
                    }
                    Navigator.of(context).pop();
                    _rejectTrip(trip, reasonController.text);
                  },
                ),
              ],
            );
          },
        );
      },
    );
  }

  // Current index for bottom navigation on mobile
  int _currentIndex = 2; // Set to 2 for Approvals tab

  void _onBottomNavTap(int index) {
    if (index == _currentIndex) return;

    switch (index) {
      case 0: // Dashboard
        Navigator.pushReplacementNamed(context, '/trip-manager-dashboard');
      case 1: // Monitoring
        Navigator.pushReplacementNamed(context, '/trip-monitoring');
      case 2: // Approvals - current screen
        // Already here
      case 3: // Settings
        Navigator.pushReplacementNamed(context, '/master-settings');
    }
  }

  // Build a card for mobile view
  Widget _buildTripCard(Trip trip) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Trip code and status row
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  trip.tripCode,
                  style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor(trip.status),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    trip.status,
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                ),
              ],
            ),
            const Divider(),
            // Trip details
            _buildDetailRow('Requestor', trip.requestorName),
            _buildDetailRow('From', trip.fromDestination),
            _buildDetailRow('To', trip.toDestination),
            _buildDetailRow('Date', trip.date),
            _buildDetailRow('Time', trip.time),
            const SizedBox(height: 8),
            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [                // View/Edit button - only show for EDIT ON REVIEW
                if (trip.status != 'CANCEL ON REVIEW')
                  ElevatedButton.icon(
                    icon: trip.status == 'EDIT ON REVIEW'
                        ? const Icon(Icons.visibility, size: 18) // View changes
                        : const Icon(Icons.edit, size: 18), // Edit trip
                    label: Text(
                      trip.status == 'EDIT ON REVIEW' ? AppLocalizations.of(context).viewChanges : AppLocalizations.of(context).edit,
                      style: const TextStyle(fontSize: 12),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF0D47A1),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                    ),
                    onPressed: () async {
                      if (trip.status == 'EDIT ON REVIEW') {
                        // For EDIT ON REVIEW, show the changes screen
                        await Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => TripChangesScreen(trip: trip),
                          ),
                        );
                      } else {
                        // For other statuses, show the edit screen
                        final result = await Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => EditTripScreen(trip: trip),
                          ),
                        );
                        if (result == true) {
                          _fetchTrips();
                        }
                      }
                    },
                  ),
                // Approve button
                ElevatedButton.icon(
                  icon: const Icon(Icons.check_circle, size: 18),
                  label: const Text(
                    'Approve',
                    style: TextStyle(fontSize: 12),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                  ),
                  onPressed: () => _showApproveConfirmDialog(trip),
                ),
                // Reject button
                ElevatedButton.icon(
                  icon: const Icon(Icons.cancel, size: 18),
                  label: const Text(
                    'Reject',
                    style: TextStyle(fontSize: 12),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                  ),
                  onPressed: () => _showRejectConfirmDialog(trip),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Helper to build detail rows for mobile cards
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Check if we're on a small screen (mobile portrait)
    final screenWidth = MediaQuery.of(context).size.width;
    final bool isSmallScreen = screenWidth < 600 && !kIsWeb;

    return Scaffold(
      appBar: CommonAppBar(
        title: 'Trip Change Approval',
        onRefresh: _fetchTrips,
      ),
      drawer: const SidebarNavigation(),
      body: Column(
        children: [
          // Search and refresh section
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: isSmallScreen
                ? Column(
                    children: [                      // Search field takes full width on mobile
                      TextField(
                        onChanged: _filterTrips,
                        decoration: InputDecoration(
                          labelText: AppLocalizations.of(context).search,
                          prefixIcon: const Icon(Icons.search),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),
                      // Refresh button takes full width on mobile
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          onPressed: _fetchTrips,
                          icon: const Icon(Icons.refresh),
                          label: Text(AppLocalizations.of(context).refresh),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF0D47A1),
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                          ),
                        ),
                      ),
                    ],
                  )
                : Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: TextField(                          onChanged: _filterTrips,
                          decoration: InputDecoration(
                            labelText: AppLocalizations.of(context).search,
                            prefixIcon: const Icon(Icons.search),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      ElevatedButton.icon(
                        onPressed: _fetchTrips,
                        icon: const Icon(Icons.refresh),
                        label: Text(AppLocalizations.of(context).refresh),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF0D47A1),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                        ),
                      ),
                    ],
                  ),
          ),
          // Trip list - different layouts for mobile and desktop
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _errorMessage.isNotEmpty
                    ? Center(child: Text(_errorMessage, style: const TextStyle(color: Colors.red)))                    : _filteredTrips.isEmpty
                        ? Center(child: Text(AppLocalizations.of(context).noTripsFound))
                        : isSmallScreen
                            // Mobile layout - vertical list of cards
                            ? RefreshIndicator(
                                onRefresh: () async {
                                  await _fetchTrips();
                                },
                                child: ListView.builder(
                                  padding: const EdgeInsets.all(8.0),
                                  itemCount: _filteredTrips.length,
                                  itemBuilder: (context, index) {
                                    return _buildTripCard(_filteredTrips[index]);
                                  },
                                ),
                              )
                            // Desktop layout - data table
                            : SingleChildScrollView(
                                scrollDirection: Axis.vertical,
                                child: SingleChildScrollView(
                                  scrollDirection: Axis.horizontal,
                                  child: Theme(
                                    data: Theme.of(context).copyWith(
                                      dataTableTheme: DataTableThemeData(
                                        headingRowHeight: 56,
                                        dataRowMinHeight: 56,
                                        dataRowMaxHeight: 56,
                                        dividerThickness: 1,
                                        horizontalMargin: 16,
                                        headingTextStyle: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                          color: Color(0xFF0D47A1),
                                        ),
                                      ),
                                    ),
                                    child: DataTable(
                                      headingRowColor: WidgetStateProperty.all(
                                        Color.fromRGBO(13, 71, 161, 0.1),
                                      ),                                      columns: [
                                        DataColumn(label: Text(AppLocalizations.of(context).tripCode)),
                                        DataColumn(label: Text(AppLocalizations.of(context).requestor)),
                                        DataColumn(label: Text(AppLocalizations.of(context).from)),
                                        DataColumn(label: Text(AppLocalizations.of(context).to)),
                                        DataColumn(label: Text(AppLocalizations.of(context).date)),
                                        DataColumn(label: Text(AppLocalizations.of(context).time)),
                                        DataColumn(label: Text(AppLocalizations.of(context).status)),
                                        DataColumn(label: Text(AppLocalizations.of(context).actions)),
                                      ],
                                      rows: _filteredTrips.map((trip) {
                                        return DataRow(
                                          cells: [
                                            DataCell(Text(trip.tripCode)),
                                            DataCell(Text(trip.requestorName)),
                                            DataCell(Text(trip.fromDestination)),
                                            DataCell(Text(trip.toDestination)),
                                            DataCell(Text(trip.date)),
                                            DataCell(Text(trip.time)),
                                            DataCell(
                                              Container(
                                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                                decoration: BoxDecoration(
                                                  color: _getStatusColor(trip.status),
                                                  borderRadius: BorderRadius.circular(4),
                                                ),
                                                child: Text(
                                                  trip.status,
                                                  style: const TextStyle(color: Colors.white),
                                                ),
                                              ),
                                            ),
                                            DataCell(
                                              Row(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [                                                  // View/Edit button - only show for EDIT ON REVIEW
                                                  if (trip.status != 'CANCEL ON REVIEW')
                                                    IconButton(
                                                      icon: trip.status == 'EDIT ON REVIEW'
                                                          ? const Icon(Icons.visibility) // View changes
                                                          : const Icon(Icons.edit), // Edit trip
                                                      tooltip: trip.status == 'EDIT ON REVIEW'
                                                          ? AppLocalizations.of(context).viewChanges
                                                          : AppLocalizations.of(context).editTrip,
                                                      color: const Color(0xFF0D47A1),
                                                      onPressed: () async {
                                                        if (trip.status == 'EDIT ON REVIEW') {
                                                          // For EDIT ON REVIEW, show the changes screen
                                                          await Navigator.push(
                                                            context,
                                                            MaterialPageRoute(
                                                              builder: (context) => TripChangesScreen(trip: trip),
                                                            ),
                                                          );
                                                        } else {
                                                          // For other statuses, show the edit screen
                                                          final result = await Navigator.push(
                                                            context,
                                                            MaterialPageRoute(
                                                              builder: (context) => EditTripScreen(trip: trip),
                                                            ),
                                                          );
                                                          if (result == true) {
                                                            _fetchTrips();
                                                          }
                                                        }
                                                      },
                                                    ),
                                                  // Approve button with different icons based on status
                                                  IconButton(
                                                    icon: const Icon(Icons.check_circle), // Same icon for all approve actions
                                                    tooltip: trip.status == 'CANCEL ON REVIEW'
                                                        ? AppLocalizations.of(context).approveCancellation
                                                        : trip.status == 'EDIT ON REVIEW'
                                                            ? AppLocalizations.of(context).approveChanges
                                                            : AppLocalizations.of(context).approveTrip,
                                                    color: Colors.green,
                                                    onPressed: () => _showApproveConfirmDialog(trip),
                                                  ),
                                                  // Reject button with different icons based on status
                                                  IconButton(
                                                    icon: const Icon(Icons.cancel), // Same icon for all reject actions
                                                    tooltip: trip.status == 'CANCEL ON REVIEW'
                                                        ? AppLocalizations.of(context).rejectCancellation
                                                        : trip.status == 'EDIT ON REVIEW'
                                                            ? AppLocalizations.of(context).rejectChanges
                                                            : AppLocalizations.of(context).rejectTrip,
                                                    color: Colors.red,
                                                    onPressed: () => _showRejectConfirmDialog(trip),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        );
                                      }).toList(),
                                    ),
                                  ),
                                ),
                              ),
          ),
        ],
      ),
      // Only show bottom navigation on mobile
      bottomNavigationBar: isSmallScreen
          ? MobileBottomNav(
              currentIndex: _currentIndex,
              onTap: _onBottomNavTap,
            )
          : null,
    );
  }
}



