import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../generated/l10n/app_localizations.dart';
import '../models/trip.dart';
import '../services/api_service.dart';

// Helper function to create a label with a red asterisk for required fields
Widget requiredLabel(String label) {
  return Row(
    mainAxisSize: MainAxisSize.min,
    children: [
      Text(label),
      const Text(
        ' *',
        style: TextStyle(
          color: Colors.red,
          fontWeight: FontWeight.bold,
        ),
      ),
    ],
  );
}

// Helper function to get required label text with asterisk
String requiredLabelText(String label) {
  return '$label *';
}

class EditTripScreen extends StatefulWidget {
  final Trip trip;

  const EditTripScreen({super.key, required this.trip});

  @override
  State<EditTripScreen> createState() => _EditTripScreenState();
}

class _EditTripScreenState extends State<EditTripScreen> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _dateController;
  late final TextEditingController _timeController;
  late final TextEditingController _customFromDestinationController = TextEditingController();
  late final TextEditingController _customToDestinationController = TextEditingController();
  late final TextEditingController _returnTimeController = TextEditingController();

  // Controllers for waiting time
  late final TextEditingController _waitingHoursController = TextEditingController();
  late final TextEditingController _waitingMinutesController = TextEditingController();
  late final TextEditingController _notesController = TextEditingController();

  bool _isLoading = false;
  bool _isFromOthers = false;
  bool _isToOthers = false;  // These variables are set from API data and used in API requests
  // roundtrip is no longer in use - removed
  bool _isWaiting = false;   // Kept for API compatibility
  DateTime? _selectedDate;
  TimeOfDay? _selectedTime;
  // For waiting trips
  DateTime? _returnDate;
  TimeOfDay? _returnTime;
  List<Map<String, String>> _destinations = [];
  List<Map<String, String>> _users = [];
  List<Map<String, String>> _cargos = [];
  String? _selectedFromDestination;
  String? _selectedToDestination;
  List<String> _selectedPassengers = [];
  List<String> _selectedCargos = [];
  @override
  void initState() {
    super.initState();
    _dateController = TextEditingController(text: widget.trip.date);
    
    // Format initial time to ensure consistent hh:mm format
    try {
      final timeComponents = widget.trip.time.split(':');
      final timeOfDay = TimeOfDay(
        hour: int.parse(timeComponents[0]),
        minute: int.parse(timeComponents[1]),
      );
      final now = DateTime.now();
      final dateTime = DateTime(
        now.year,
        now.month,
        now.day,
        timeOfDay.hour,
        timeOfDay.minute,
      );
      _timeController = TextEditingController(text: DateFormat('HH:mm').format(dateTime));
    } catch (e) {
      print('Error formatting initial time: $e');
      _timeController = TextEditingController(text: widget.trip.time);
    }

    // Initialize with the trip's values
    _selectedFromDestination = widget.trip.fromDestination;
    _selectedToDestination = widget.trip.toDestination;

    // Check if custom destinations are used
    _isFromOthers = _selectedFromDestination == 'others';
    _isToOthers = _selectedToDestination == 'others';

    _fetchDestinations();
    _fetchUsers();
    _fetchCargos();
    _fetchTripDetails();    // Parse initial date and time
    try {
      _selectedDate = DateFormat('E, dd-MMM-yy').parse(widget.trip.date);
      final timeComponents = widget.trip.time.split(':');
      _selectedTime = TimeOfDay(
        hour: int.parse(timeComponents[0]),
        minute: int.parse(timeComponents[1]),
      );
      // Format time to ensure consistent hh:mm format
      final now = DateTime.now();
      final dateTime = DateTime(
        now.year,
        now.month,
        now.day,
        _selectedTime!.hour,
        _selectedTime!.minute,
      );
      _timeController.text = DateFormat('HH:mm').format(dateTime);
    } catch (e) {
      print('Error parsing initial date/time: $e');
    }
  }

  Future<void> _fetchTripDetails() async {
    try {
      setState(() {
        _isLoading = true;
      });      final response = await ApiService.get('trips/${widget.trip.tripCode}');

      if (response.statusCode == 200) {
        final data = ApiService.parseResponse(response);
        
        // Debug: Print all received data to check what's coming from backend
        print('=== BACKEND DATA DEBUG ===');
        print('Full API Response: $data');
        print('Trip Code: ${data['trip_code']}');
        print('Date: ${data['date']}');
        print('Time: ${data['time']}');
        print('From Destination Code: ${data['from_destination_code']}');
        print('To Destination Code: ${data['to_destination_code']}');        print('Custom From: ${data['custom_from']}');
        print('Custom To: ${data['custom_to']}');
        print('Is Waiting: ${data['is_waiting']}');
        print('Return Time: ${data['return_time']}');
        print('Return Date: ${data['return_date']}');
        print('Waiting Hours: ${data['waiting_hours']}');
        print('Waiting Minutes: ${data['waiting_minutes']}');
        print('Passengers: ${data['passengers']}');
        print('Cargos: ${data['cargos']}');
        print('Notes: ${data['notes']}');
        print('========================');if (mounted) {
          setState(() {
            // Parse date and time first to ensure they're available for calculations
            try {
              if (data['date'] != null) {
                _selectedDate = DateTime.parse(data['date']).toLocal();
                _dateController.text = DateFormat('E, dd-MMM-yy').format(_selectedDate!);
              }              if (data['time'] != null) {
                final timeComponents = data['time'].split(':');
                _selectedTime = TimeOfDay(
                  hour: int.parse(timeComponents[0]),
                  minute: int.parse(timeComponents[1]),
                );
                // Format time to ensure consistent hh:mm format
                final now = DateTime.now();
                final dateTime = DateTime(
                  now.year,
                  now.month,
                  now.day,
                  _selectedTime!.hour,
                  _selectedTime!.minute,
                );
                _timeController.text = DateFormat('HH:mm').format(dateTime);
              }
            } catch (e) {
              print('Error parsing date/time from API: $e');
            }

            // Check if custom destinations are used first
            // Custom destinations have empty destination codes and non-empty custom fields
            _isFromOthers = (data['from_destination_code'] == null || 
                           data['from_destination_code'].toString().isEmpty) && 
                          (data['custom_from'] != null && 
                           data['custom_from'].toString().isNotEmpty);
            _isToOthers = (data['to_destination_code'] == null || 
                         data['to_destination_code'].toString().isEmpty) && 
                        (data['custom_to'] != null && 
                         data['custom_to'].toString().isNotEmpty);

            // Set from and to destinations
            if (_isFromOthers) {
              _selectedFromDestination = 'others';
              _customFromDestinationController.text = data['custom_from'];
            } else if (data['from_destination_code'] != null) {
              _selectedFromDestination = data['from_destination_code'];
            }
            
            if (_isToOthers) {
              _selectedToDestination = 'others';
              _customToDestinationController.text = data['custom_to'];
            } else if (data['to_destination_code'] != null) {
              _selectedToDestination = data['to_destination_code'];
            }            // Check if this is a waiting trip
            _isWaiting = data['is_waiting'] ?? false;

            // Set passengers
            if (data['passengers'] != null) {
              _selectedPassengers = (data['passengers'] as List)
                .map((passenger) => passenger['user_id']?.toString() ?? '')
                .where((id) => id.isNotEmpty)
                .toList();
            } else {
              _selectedPassengers = [];
            }

            // Set cargos
            if (data['cargos'] != null) {
              _selectedCargos = (data['cargos'] as List)
                .map((cargo) => cargo['cargo_code']?.toString() ?? '')
                .where((code) => code.isNotEmpty)
                .toList();
            } else {
              _selectedCargos = [];
            }            // Handle waiting trip data
            if (_isWaiting) {
              // First, try to get waiting duration directly from API if available
              if (data['waiting_hours'] != null || data['waiting_minutes'] != null) {
                _waitingHoursController.text = (data['waiting_hours'] ?? 0).toString();
                _waitingMinutesController.text = (data['waiting_minutes'] ?? 0).toString();
              }
              
              // Handle return time and date for API compatibility
              if (data['return_time'] != null) {
                try {
                  final timeComponents = data['return_time'].split(':');
                  _returnTime = TimeOfDay(
                    hour: int.parse(timeComponents[0]),
                    minute: int.parse(timeComponents[1]),
                  );

                  // Set the return time controller text
                  final now = DateTime.now();
                  final dateTime = DateTime(
                    now.year,
                    now.month,
                    now.day,
                    _returnTime!.hour,
                    _returnTime!.minute,
                  );
                  _returnTimeController.text = DateFormat('HH:mm').format(dateTime);

                  // Parse return date if available
                  if (data['return_date'] != null) {
                    try {
                      _returnDate = DateTime.parse(data['return_date']).toLocal();
                    } catch (e) {
                      print('Error parsing return date: $e');
                      _returnDate = _selectedDate;
                    }
                  } else {
                    _returnDate = _selectedDate;
                  }

                  // If we don't have direct waiting duration, calculate it from return time
                  if (_waitingHoursController.text.isEmpty && _waitingMinutesController.text.isEmpty) {
                    // Make sure we have the outbound date and time parsed first
                    if (_selectedDate != null && _selectedTime != null) {
                      final outboundDateTime = DateTime(
                        _selectedDate!.year,
                        _selectedDate!.month,
                        _selectedDate!.day,
                        _selectedTime!.hour,
                        _selectedTime!.minute,
                      );

                      final returnDateTime = DateTime(
                        _returnDate!.year,
                        _returnDate!.month,
                        _returnDate!.day,
                        _returnTime!.hour,
                        _returnTime!.minute,
                      );

                      // Calculate the duration between outbound and return
                      final duration = returnDateTime.difference(outboundDateTime);
                      final hours = duration.inHours;
                      final minutes = duration.inMinutes % 60;

                      // Set the waiting duration controllers
                      _waitingHoursController.text = hours.toString();
                      _waitingMinutesController.text = minutes.toString();
                    }
                  }
                } catch (e) {
                  print('Error parsing return time: $e');
                }
              }
            }
            // Connected trips are no longer in use            // Set notes if available
            if (data['notes'] != null) {
              _notesController.text = data['notes'];
            }

            _isLoading = false;
            
            // Debug: Print what values were actually set in the UI
            print('=== UI VALUES DEBUG ===');
            print('Selected Date: $_selectedDate');
            print('Selected Time: $_selectedTime');
            print('Date Controller: ${_dateController.text}');
            print('Time Controller: ${_timeController.text}');
            print('Selected From Destination: $_selectedFromDestination');
            print('Selected To Destination: $_selectedToDestination');
            print('Is From Others: $_isFromOthers');
            print('Is To Others: $_isToOthers');
            print('Custom From Controller: ${_customFromDestinationController.text}');            print('Custom To Controller: ${_customToDestinationController.text}');
            print('Is Waiting: $_isWaiting');
            print('Waiting Hours Controller: ${_waitingHoursController.text}');
            print('Waiting Minutes Controller: ${_waitingMinutesController.text}');
            print('Return Time: $_returnTime');
            print('Return Date: $_returnDate');
            print('Return Time Controller: ${_returnTimeController.text}');
            print('Selected Passengers: $_selectedPassengers');
            print('Selected Cargos: $_selectedCargos');
            print('Notes Controller: ${_notesController.text}');
            print('=====================');
          });
        }
      }
    } catch (e) {
      print('Error fetching trip details: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading trip details: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _fetchDestinations() async {
    try {
      final response = await ApiService.get('destinations');

      if (response.statusCode == 200) {
        final data = ApiService.parseResponse(response);
        if (mounted) {
          setState(() {
            _destinations = (data['destinations'] as List).map((dest) => {
              'code': dest['destinationCode'].toString(),
              'name': dest['destination'].toString(),
            }).toList();

            // Make sure 'others' is not already in the list
            if (_destinations.any((dest) => dest['code'] == 'others')) {
              _destinations.removeWhere((dest) => dest['code'] == 'others');
            }

            // Add 'others' option
            _destinations.add({
              'code': 'others',
              'name': 'Others'
            });
          });
        }
      }
    } catch (e) {
      print('Error fetching destinations: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading destinations: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _dateController.dispose();
    _timeController.dispose();
    _customFromDestinationController.dispose();
    _customToDestinationController.dispose();
    _returnTimeController.dispose();
    _waitingHoursController.dispose();
    _waitingMinutesController.dispose();
    _notesController.dispose();
    super.dispose();
  }
  Future<void> _fetchUsers() async {
    try {
      // Fetch users with Requestor role
      final requestorResponse = await ApiService.get('users?role=Requestor');
      
      // Fetch users with Trip Manager role
      final tripManagerResponse = await ApiService.get('users?role=Trip Manager');

      final List<Map<String, String>> allUsers = [];

      // Process Requestor users
      if (requestorResponse.statusCode == 200) {
        final requestorData = ApiService.parseResponse(requestorResponse);
        final requestorUsers = (requestorData['users'] as List).map((user) => {
          'id': user['user_id'].toString(),
          'name': user['name'].toString(),
        }).toList();
        allUsers.addAll(requestorUsers);
      }

      // Process Trip Manager users
      if (tripManagerResponse.statusCode == 200) {
        final tripManagerData = ApiService.parseResponse(tripManagerResponse);
        final tripManagerUsers = (tripManagerData['users'] as List).map((user) => {
          'id': user['user_id'].toString(),
          'name': user['name'].toString(),
        }).toList();
        allUsers.addAll(tripManagerUsers);
      }

      // Remove duplicates based on user ID and sort by name
      final Map<String, Map<String, String>> uniqueUsers = {};
      for (final user in allUsers) {
        uniqueUsers[user['id']!] = user;
      }

      if (mounted) {
        setState(() {
          _users = uniqueUsers.values.toList()
            ..sort((a, b) => a['name']!.compareTo(b['name']!));
        });
      }    } catch (e) {
      print('Error fetching users: $e');
    }
  }

  Future<void> _fetchCargos() async {
    try {
      final response = await ApiService.get('cargos');

      if (response.statusCode == 200) {
        final data = ApiService.parseResponse(response);
        if (mounted) {
          setState(() {
            _cargos = (data['cargos'] as List).map((cargo) => {
              'code': cargo['cargoCode'].toString(),
              'name': cargo['cargoName'].toString(),
            }).toList();
          });
        }
      }
    } catch (e) {
      print('Error fetching cargos: $e');
    }
  }

  Future<void> _selectDate(BuildContext context) async {
    // Use current date as the firstDate
    DateTime firstDate = DateTime.now();
    DateTime initialDate = _selectedDate ?? DateTime.now();

    // Debug output
    print('Trip date selection:');
    print('First date: $firstDate');
    print('Current selected date: $_selectedDate');

    // Debug output
    print('Initial date for picker: $initialDate');

    // Normalize dates to remove time components but keep local timezone
    firstDate = DateTime(firstDate.year, firstDate.month, firstDate.day);
    initialDate = DateTime(initialDate.year, initialDate.month, initialDate.day);

    // Ensure initialDate is not before firstDate (final safety check)
    if (initialDate.isBefore(firstDate)) {
      print('Warning: initialDate was before firstDate after normalization. Using firstDate instead.');
      initialDate = firstDate;
    }

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: DateTime(2101),
    );

    // Check if widget is still mounted after async operation
    if (!mounted) return;

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
        _dateController.text = DateFormat('E, dd-MMM-yy').format(picked);
      });
    }
  }

  Future<void> _selectTime(BuildContext context) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _selectedTime ?? TimeOfDay.now(),
      builder: (BuildContext context, Widget? child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: true),
          child: child!,
        );
      },
    );

    // Check if widget is still mounted after async operation
    if (!mounted) return;

    if (picked != null) {
      setState(() {
        _selectedTime = picked;
        // Convert to 24-hour format
        final now = DateTime.now();
        final dateTime = DateTime(
          now.year,
          now.month,
          now.day,
          picked.hour,
          picked.minute,
        );
        _timeController.text = DateFormat('HH:mm').format(dateTime);

        // If waiting trip is active, recalculate the return date and time
        if (_isWaiting) {
          _calculateReturnDateTime();
        }
      });
    }
  }

  void _calculateReturnDateTime() {
    if (_dateController.text.isEmpty || _timeController.text.isEmpty) return;

    try {
      // Parse the outbound date and time
      final DateTime outboundDate = DateFormat('E, dd-MMM-yy').parse(_dateController.text);
      final TimeOfDay outboundTime = TimeOfDay.fromDateTime(
        DateFormat('HH:mm').parse(_timeController.text)
      );

      // Create a DateTime object combining date and time
      final DateTime outboundDateTime = DateTime(
        outboundDate.year,
        outboundDate.month,
        outboundDate.day,
        outboundTime.hour,
        outboundTime.minute,
      );

      // Add waiting hours and minutes
      final int waitingHours = int.tryParse(_waitingHoursController.text) ?? 0;
      final int waitingMinutes = int.tryParse(_waitingMinutesController.text) ?? 0;
      final DateTime returnDateTime = outboundDateTime.add(
        Duration(hours: waitingHours, minutes: waitingMinutes)
      );

      // Update return date and time
      setState(() {
        _returnDate = returnDateTime;
        _returnTime = TimeOfDay.fromDateTime(returnDateTime);
        _returnTimeController.text = DateFormat('HH:mm').format(returnDateTime);
      });
    } catch (e) {
      print('Error calculating return date/time: $e');
    }
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final scaffoldMessenger = ScaffoldMessenger.of(context);

    // Validation for custom destinations
    if (_isFromOthers && _customFromDestinationController.text.trim().isEmpty) {
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context).pleaseEnterCustomFromDestination),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (_isToOthers && _customToDestinationController.text.trim().isEmpty) {
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context).pleaseEnterCustomToDestination),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Check if from and to destinations are the same
    if (_selectedFromDestination == _selectedToDestination && !_isFromOthers && !_isToOthers) {
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('From and To destinations cannot be the same'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // For custom destinations, check if they are the same
    if (_isFromOthers && _isToOthers &&
        _customFromDestinationController.text.trim().isNotEmpty &&
        _customToDestinationController.text.trim().isNotEmpty &&
        _customFromDestinationController.text.trim() == _customToDestinationController.text.trim()) {
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('From and To custom destinations cannot be the same'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Validate waiting duration if waiting trip is selected
    if (_isWaiting) {
      if (_waitingHoursController.text.isEmpty && _waitingMinutesController.text.isEmpty) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).pleaseEnterWaitingDuration),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      final int waitingHours = int.tryParse(_waitingHoursController.text) ?? 0;
      final int waitingMinutes = int.tryParse(_waitingMinutesController.text) ?? 0;

      if (waitingHours == 0 && waitingMinutes == 0) {
        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text('Waiting duration must be greater than 0'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }
    }

    setState(() => _isLoading = true);

    try {
      // Convert the date to ISO format
      final DateTime parsedDate = DateFormat('E, dd-MMM-yy').parse(_dateController.text);
      final String isoDate = DateFormat('yyyy-MM-dd').format(parsedDate);

      // Use the selected passengers and cargos directly
      final List<String> validPassengers = _selectedPassengers
          .where((userId) => userId.isNotEmpty)
          .toList();

      final List<String> validCargos = _selectedCargos
          .where((cargoCode) => cargoCode.isNotEmpty)
          .toList();

      final requestBody = {
        'from_destination': _selectedFromDestination,
        'to_destination': _selectedToDestination,
        'date': isoDate,
        'time': _timeController.text,        'passengers': validPassengers,
        'cargos': validCargos,
        'is_waiting': _isWaiting,
        'status': 'EDIT ON REVIEW',
        'notes': _notesController.text.trim(),
      };

      // Add custom destinations if needed
      if (_isFromOthers) {
        requestBody['custom_from'] = _customFromDestinationController.text.trim();
      }
      if (_isToOthers) {
        requestBody['custom_to'] = _customToDestinationController.text.trim();
      }

      // For waiting trips, add return time and date
      if (_isWaiting && _returnTime != null) {
        final returnTimeStr = '${_returnTime!.hour.toString().padLeft(2, '0')}:${_returnTime!.minute.toString().padLeft(2, '0')}';
        requestBody['return_time'] = returnTimeStr;

        if (_returnDate != null) {
          final returnDateStr = DateFormat('yyyy-MM-dd').format(_returnDate!);
          requestBody['return_date'] = returnDateStr;
        }
      }

      final response = await ApiService.put('trips/${widget.trip.tripCode}', requestBody);

      if (response.statusCode == 200) {
        if (mounted) {
          scaffoldMessenger.showSnackBar(            SnackBar(
              content: const Text('Trip updated successfully'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context, true); // Return true to indicate success
        }
      } else {
        final errorData = ApiService.parseResponse(response);
        throw Exception(errorData['message'] ?? 'Failed to update trip');
      }
    } catch (e) {
      print('Error updating trip: $e');
      if (mounted) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('An error occurred: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  // Helper method to get selected passenger names
  String _getSelectedPassengerNames() {
    final selectedUsers = _users.where((user) => _selectedPassengers.contains(user['id'])).toList();
    if (selectedUsers.isEmpty) return '';
    return selectedUsers.map((user) => user['name']).join(', ');
  }

  // Helper method to get selected cargo names
  String _getSelectedCargoNames() {
    final selectedCargos = _cargos.where((cargo) => _selectedCargos.contains(cargo['code'])).toList();
    if (selectedCargos.isEmpty) return '';
    return selectedCargos.map((cargo) => cargo['name']).join(', ');
  }

  // Show passenger selection dialog
  void _showPassengerSelectionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: Text(AppLocalizations.of(context).selectPassengers),
              content: Container(
                width: double.maxFinite,
                height: 400,
                child: Column(
                  children: [
                    // Search field
                    TextField(
                      decoration: InputDecoration(
                        hintText: AppLocalizations.of(context).searchForPassengers,
                        prefixIcon: const Icon(Icons.search),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      onChanged: (value) {
                        // Filter functionality can be added here if needed
                      },
                    ),
                    const SizedBox(height: 16),
                    // User list
                    Expanded(
                      child: ListView.builder(
                        itemCount: _users.length,
                        itemBuilder: (context, index) {
                          final user = _users[index];
                          final isSelected = _selectedPassengers.contains(user['id']);
                          return CheckboxListTile(
                            title: Text(user['name']!),
                            value: isSelected,
                            onChanged: (bool? checked) {
                              if (checked == true) {
                                if (!_selectedPassengers.contains(user['id']!)) {
                                  setState(() {
                                    _selectedPassengers.add(user['id']!);
                                  });
                                  setDialogState(() {});
                                }
                              } else {
                                setState(() {
                                  _selectedPassengers.remove(user['id']!);
                                });
                                setDialogState(() {});
                              }
                            },
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Done'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  // Show cargo selection dialog
  void _showCargoSelectionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: Text(AppLocalizations.of(context).selectCargos),
              content: Container(
                width: double.maxFinite,
                height: 400,
                child: Column(
                  children: [
                    // Search field
                    TextField(
                      decoration: InputDecoration(
                        hintText: AppLocalizations.of(context).searchForCargos,
                        prefixIcon: const Icon(Icons.search),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      onChanged: (value) {
                        // Filter functionality can be added here if needed
                      },
                    ),
                    const SizedBox(height: 16),
                    // Cargo list
                    Expanded(
                      child: ListView.builder(
                        itemCount: _cargos.length,
                        itemBuilder: (context, index) {
                          final cargo = _cargos[index];
                          final isSelected = _selectedCargos.contains(cargo['code']);
                          return CheckboxListTile(
                            title: Text(cargo['name']!),
                            value: isSelected,
                            onChanged: (bool? checked) {
                              if (checked == true) {
                                if (!_selectedCargos.contains(cargo['code']!)) {
                                  setState(() {
                                    _selectedCargos.add(cargo['code']!);
                                  });
                                  setDialogState(() {});
                                }
                              } else {
                                setState(() {
                                  _selectedCargos.remove(cargo['code']!);
                                });
                                setDialogState(() {});
                              }
                            },
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Done'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(      appBar: AppBar(
        title: Text(AppLocalizations.of(context).editTrip, style: const TextStyle(color: Colors.white)),
        backgroundColor: const Color(0xFF0D47A1),
        iconTheme: const IconThemeData(color: Colors.white),
        foregroundColor: Colors.white,
      ),
      body: _isLoading || _destinations.isEmpty
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    _buildMainTripSection(),

                    // Submit Button always at the bottom of the page
                    const SizedBox(height: 24),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF0D47A1),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          elevation: 2,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        onPressed: _isLoading ? null : _submitForm,
                        child: _isLoading
                            ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              )                            : Text(
                                AppLocalizations.of(context).saveChanges,
                                style: const TextStyle(fontSize: 16, color: Colors.white),
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  // Build the main trip section
  Widget _buildMainTripSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Title
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          decoration: BoxDecoration(
            color: const Color(0xFF0D47A1),
            borderRadius: BorderRadius.circular(4),
          ),          child: Text(
            AppLocalizations.of(context).trip,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
        const SizedBox(height: 16),        // Trip Code
        TextFormField(
                      initialValue: widget.trip.tripCode,
                      decoration: InputDecoration(
                        labelText: AppLocalizations.of(context).tripCode,
                        border: const OutlineInputBorder(),
                      ),
                      enabled: false,
                    ),
                    const SizedBox(height: 16),

                    // From and To destinations in a row
                    Row(
                      children: [
                        Expanded(
                          child: DropdownSearch<Map<String, String>>(
                            selectedItem: _selectedFromDestination != null
                              ? _destinations.firstWhere(
                                  (dest) => dest['code'] == _selectedFromDestination,
                                  orElse: () => {'code': '', 'name': ''},
                                )
                              : null,                            popupProps: PopupProps.menu(
                              showSearchBox: true,
                              searchFieldProps: TextFieldProps(
                                decoration: InputDecoration(
                                  hintText: AppLocalizations.of(context).searchForDestinations,
                                  prefixIcon: const Icon(Icons.search),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                ),
                              ),
                              itemBuilder: (context, dest, isSelected) {
                                return ListTile(
                                  selected: isSelected,
                                  title: Text(dest['name']!),
                                );
                              },
                            ),
                            dropdownDecoratorProps: DropDownDecoratorProps(
                              dropdownSearchDecoration: InputDecoration(
                                labelText: requiredLabelText(AppLocalizations.of(context).fromDestination),
                                hintText: AppLocalizations.of(context).selectOrigin,
                                border: const OutlineInputBorder(),
                              ),
                            ),
                            itemAsString: (Map<String, String>? dest) => dest?['name'] ?? '',
                            items: _destinations,
                            onChanged: (value) {
                              if (value != null) {
                                setState(() {
                                  _selectedFromDestination = value['code'];
                                  _isFromOthers = value['code'] == 'others';
                                  if (!_isFromOthers) {
                                    _customFromDestinationController.clear();
                                  }
                                });
                              }
                            },                            validator: (value) =>
                                value == null ? AppLocalizations.of(context).fromDestinationRequired : null,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: DropdownSearch<Map<String, String>>(
                            selectedItem: _selectedToDestination != null
                              ? _destinations.firstWhere(
                                  (dest) => dest['code'] == _selectedToDestination,
                                  orElse: () => {'code': '', 'name': ''},
                                )
                              : null,
                            popupProps: PopupProps.menu(
                              showSearchBox: true,                              searchFieldProps: TextFieldProps(
                                decoration: InputDecoration(
                                  hintText: AppLocalizations.of(context).searchForDestinations,
                                  prefixIcon: const Icon(Icons.search),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                ),
                              ),
                              itemBuilder: (context, dest, isSelected) {
                                return ListTile(
                                  selected: isSelected,
                                  title: Text(dest['name']!),
                                );
                              },
                            ),
                            dropdownDecoratorProps: DropDownDecoratorProps(
                              dropdownSearchDecoration: InputDecoration(
                                labelText: requiredLabelText(AppLocalizations.of(context).toDestination),
                                hintText: AppLocalizations.of(context).selectDestination,
                                border: const OutlineInputBorder(),
                              ),
                            ),
                            itemAsString: (Map<String, String>? dest) => dest?['name'] ?? '',
                            items: _destinations,
                            onChanged: (value) {
                              if (value != null) {
                                setState(() {
                                  _selectedToDestination = value['code'];
                                  _isToOthers = value['code'] == 'others';
                                  if (!_isToOthers) {
                                    _customToDestinationController.clear();
                                  }
                                  // Connected trip logic removed
                                });
                              }
                            },                            validator: (value) =>
                                value == null ? AppLocalizations.of(context).toDestinationRequired : null,
                          ),
                        ),
                      ],
                    ),

                    // Custom From and To in a row (if needed)
                    if (_isFromOthers || _isToOthers) ...[
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          if (_isFromOthers)
                            Expanded(                              child: TextField(
                                controller: _customFromDestinationController,
                                decoration: InputDecoration(
                                  label: requiredLabel(AppLocalizations.of(context).customFromDestination),
                                  border: const OutlineInputBorder(),
                                  hintText: AppLocalizations.of(context).enterCustomLocation,
                                ),
                              ),
                            ),
                          if (_isFromOthers && _isToOthers)
                            const SizedBox(width: 16),
                          if (_isToOthers)
                            Expanded(                              child: TextField(
                                controller: _customToDestinationController,
                                decoration: InputDecoration(
                                  label: requiredLabel(AppLocalizations.of(context).customToDestination),
                                  border: const OutlineInputBorder(),
                                  hintText: AppLocalizations.of(context).enterCustomLocation,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ],

                    const SizedBox(height: 16),                    // Notes field
                    TextField(
                      controller: _notesController,
                      decoration: InputDecoration(
                        labelText: AppLocalizations.of(context).notesOptional,
                        border: const OutlineInputBorder(),
                        hintText: AppLocalizations.of(context).addAdditionalNotes,
                      ),
                      maxLines: 3,
                    ),

                    const SizedBox(height: 16),

                    // Date and Time in a row
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(                            controller: _dateController,
                            decoration: InputDecoration(
                              label: requiredLabel(AppLocalizations.of(context).date),
                              border: const OutlineInputBorder(),
                              suffixIcon: const Icon(Icons.calendar_today),
                            ),
                            readOnly: true,
                            onTap: () => _selectDate(context),                            validator: (value) =>
                                value?.isEmpty ?? true ? AppLocalizations.of(context).dateIsRequired : null,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(                            controller: _timeController,
                            decoration: InputDecoration(
                              label: requiredLabel(AppLocalizations.of(context).time),
                              border: const OutlineInputBorder(),
                              suffixIcon: const Icon(Icons.access_time),
                            ),
                            readOnly: true,
                            onTap: () => _selectTime(context),                            validator: (value) =>
                                value?.isEmpty ?? true ? AppLocalizations.of(context).timeIsRequired : null,
                          ),
                        ),
                      ],
                    ),                    // Waiting Trip Checkbox
                    const SizedBox(height: 16),
                    CheckboxListTile(
                      title: Text(AppLocalizations.of(context).waitingTrip),
                      value: _isWaiting,
                      onChanged: (bool? value) {
                        setState(() {
                          _isWaiting = value ?? false;
                          if (!_isWaiting) {
                            _waitingHoursController.clear();
                            _waitingMinutesController.clear();
                          } else {
                            // Initialize with default values if empty
                            if (_waitingHoursController.text.isEmpty) {
                              _waitingHoursController.text = '1';
                            }
                            if (_waitingMinutesController.text.isEmpty) {
                              _waitingMinutesController.text = '0';
                            }
                            _calculateReturnDateTime();
                          }
                        });
                      },
                      controlAffinity: ListTileControlAffinity.leading,
                    ),

                    // Waiting Duration Fields (shown only when waiting trip is selected)
                    if (_isWaiting) ...[
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: TextField(                              controller: _waitingHoursController,
                              decoration: InputDecoration(
                                label: requiredLabel(AppLocalizations.of(context).hours),
                                border: const OutlineInputBorder(),
                                suffixText: 'hrs',
                              ),
                              keyboardType: TextInputType.number,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                              ],
                              onChanged: (_) => _calculateReturnDateTime(),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: TextField(                              controller: _waitingMinutesController,
                              decoration: InputDecoration(
                                label: requiredLabel(AppLocalizations.of(context).minutes),
                                border: const OutlineInputBorder(),
                                suffixText: 'min',
                              ),
                              keyboardType: TextInputType.number,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                                FilteringTextInputFormatter.allow(RegExp(r'^([0-5]?[0-9])$')),
                              ],
                              onChanged: (_) => _calculateReturnDateTime(),
                            ),
                          ),
                        ],
                      ),
                    ],                    // Passengers Section (Full Width)
                    const SizedBox(height: 16),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            AppLocalizations.of(context).passengers,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),                          const SizedBox(height: 8),
                          // Custom Multi-Select for Passengers
                          GestureDetector(
                            onTap: () => _showPassengerSelectionDialog(context),
                            child: Container(
                              width: double.infinity,
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey),
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      _selectedPassengers.isEmpty
                                          ? AppLocalizations.of(context).chooseOneOrMorePassengers
                                          : _getSelectedPassengerNames(),
                                      style: TextStyle(
                                        color: _selectedPassengers.isEmpty ? Colors.grey[600] : Colors.black,
                                      ),
                                    ),
                                  ),
                                  const Icon(Icons.arrow_drop_down),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Cargos Section (Full Width)
                    const SizedBox(height: 16),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            AppLocalizations.of(context).cargos,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),                          const SizedBox(height: 8),
                          // Custom Multi-Select for Cargos
                          GestureDetector(
                            onTap: () => _showCargoSelectionDialog(context),
                            child: Container(
                              width: double.infinity,
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey),
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      _selectedCargos.isEmpty
                                          ? AppLocalizations.of(context).chooseOneOrMoreCargos
                                          : _getSelectedCargoNames(),
                                      style: TextStyle(
                                        color: _selectedCargos.isEmpty ? Colors.grey[600] : Colors.black,
                                      ),
                                    ),
                                  ),
                                  const Icon(Icons.arrow_drop_down),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),// No submit button here - it's now at the bottom of the page
                  ],
                );
  }
}



