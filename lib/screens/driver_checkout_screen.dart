import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../generated/l10n/app_localizations.dart';
import '../services/api_service.dart';

class DriverCheckoutScreen extends StatefulWidget {
  final String driverCode;
  final Map<String, dynamic> activeCheckIn;

  const DriverCheckoutScreen({
    super.key,
    required this.driverCode,
    required this.activeCheckIn,
  });

  @override
  State<DriverCheckoutScreen> createState() => _DriverCheckoutScreenState();
}

class _DriverCheckoutScreenState extends State<DriverCheckoutScreen> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    // Log driver code and active check-in for debugging
    print('Driver checkout screen initialized with driver code: ${widget.driverCode}');
    print('Active check-in data: ${widget.activeCheckIn}');

    // Validate driver code
    if (widget.driverCode.isEmpty) {
      setState(() {
        _errorMessage = 'Error: Driver code is missing. Please go back and try again.';
      });
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  // Show confirmation dialog before checkout
  Future<void> _showCheckoutConfirmation() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(        title: Text(AppLocalizations.of(context).confirmCheckOut),
        content: Text(
          AppLocalizations.of(context).afterCheckOutRedirectMessage,
        ),
        actions: [          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(AppLocalizations.of(context).cancel),
          ),          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(AppLocalizations.of(context).proceed),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      _submitCheckout();
    }
  }

  Future<void> _submitCheckout() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Validate driver code before submission
    if (widget.driverCode.isEmpty) {
      setState(() {
        _errorMessage = 'Error: Driver code is missing. Please go back and try again.';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    print('Submitting checkout with driver code: ${widget.driverCode}');
    print('Check-in ID: ${widget.activeCheckIn['check_in_id']}');

    try {
      // Create the request body
      final Map<String, dynamic> requestBody = {
        'driver_code': widget.driverCode,
        'check_in_id': widget.activeCheckIn['check_in_id'],
      };

      // Make the API request
      final response = await ApiService.post(
        'drivers/check-out',
        requestBody,
      );

      if (response.statusCode == 200) {        if (mounted) {
          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.of(context).checkOutSuccessfulRedirecting),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
            ),
          );

          // Add a short delay before redirecting to check-in
          Future.delayed(const Duration(seconds: 2), () async {
            if (mounted) {
              try {
                print('Driver checkout: Redirecting to check-in screen without logging out');

                // Navigate to check-in screen without logging out
                // This ensures background tracking continues to work
                if (mounted) {
                  print('Driver checkout: Navigating to check-in screen');
                  _navigateToLogin();
                }
              } catch (e) {
                print('Driver checkout: Error during navigation: $e');
                // Still try to navigate to check-in screen even if there was an error
                if (mounted) {
                  _navigateToLogin();
                }
              }
            }
          });
        }
      } else {
        String errorMessage;
        try {
          final errorData = jsonDecode(response.body);
          errorMessage = errorData['error'] ?? 'Failed to check out';
        } catch (e) {
          errorMessage = 'Failed to check out: ${response.statusCode} ${response.reasonPhrase}';
        }

        setState(() {
          _errorMessage = errorMessage;
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error during checkout: $e');
      setState(() {
        _errorMessage = 'An error occurred: $e';
        _isLoading = false;
      });
    }
  }



  // Helper method to navigate to check-in screen after checkout
  void _navigateToLogin() {
    // Pass the current driver code to the check-in screen
    Navigator.of(context).pushNamedAndRemoveUntil(
      '/driver-check-in',
      (route) => false, // Remove all previous routes
      arguments: {
        'driverCode': widget.driverCode, // Pass the current driver code
      },
    );
  }

  String _formatDateTime(String dateTimeStr) {
    try {
      final dateTime = DateTime.parse(dateTimeStr);
      return DateFormat('MMM d, yyyy - HH:mm').format(dateTime);
    } catch (e) {
      return dateTimeStr;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context).driverCheckOut),
        backgroundColor: const Color(0xFF0D47A1),
        foregroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            // Navigate back to driver trips screen
            Navigator.pushReplacementNamed(
              context,
              '/driver-trips',
              arguments: {
                'driverCode': widget.driverCode,
              },
            );
          },
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Driver code display
                      Card(
                        elevation: 2,
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [                              Text(
                                AppLocalizations.of(context).driverInformation,
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),                              Text(
                                '${AppLocalizations.of(context).driverCodeColon} ${widget.driverCode}',
                                style: const TextStyle(fontSize: 16),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),

                      // Check-in information
                      Card(
                        elevation: 2,
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [                              Text(
                                AppLocalizations.of(context).checkInInformation,
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),                              Text(
                                '${AppLocalizations.of(context).checkInTimeColon} ${_formatDateTime(widget.activeCheckIn['check_in_time'])}',
                                style: const TextStyle(fontSize: 16),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),

                      // Information about removed features
                      Card(
                        elevation: 2,
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,                            children: [
                              Text(
                                AppLocalizations.of(context).checkOutInformation,
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),
                              Text(
                                AppLocalizations.of(context).clickButtonCompleteCheckOut,
                                style: const TextStyle(fontSize: 16),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),

                      // Error message
                      if (_errorMessage != null)
                        Container(
                          padding: const EdgeInsets.all(8),
                          color: Colors.red.shade100,
                          child: Text(
                            _errorMessage!,
                            style: TextStyle(color: Colors.red.shade900),
                          ),
                        ),

                      const SizedBox(height: 24),

                      // Submit button
                      SizedBox(
                        height: 50,
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF0D47A1),
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),                          onPressed: _showCheckoutConfirmation,
                          child: Text(
                            AppLocalizations.of(context).completeCheckOut,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
    );
  }
}


