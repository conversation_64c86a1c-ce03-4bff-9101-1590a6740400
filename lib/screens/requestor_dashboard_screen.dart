import 'dart:convert';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../generated/l10n/app_localizations.dart';
import '../main.dart';
import '../services/api_service.dart';
import '../sidebar.dart';
import '../utils/auto_refresh.dart';
import '../widgets/common_app_bar.dart';
import '../widgets/mobile_bottom_nav.dart';
import '../widgets/role_switcher.dart';
import 'my_trip_history_screen.dart';
import 'request_trip_screen.dart';
import 'trip_rating_list_screen.dart';

class RequestorDashboardScreen extends StatefulWidget {
  const RequestorDashboardScreen({super.key});

  @override
  State<RequestorDashboardScreen> createState() => _RequestorDashboardScreenState();
}

class _RequestorDashboardScreenState extends State<RequestorDashboardScreen> with AutoRefreshMixin {
  // Current index for bottom navigation
  int _currentIndex = 0;
  bool _isLoading = true;
  String _errorMessage = '';
  Map<String, int> _tripStatusCounts = {};
  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  bool _mounted = true;

  // Group statuses into categories
  final Map<String, List<String>> _statusGroups = {
    'REQUEST': ['REQUEST', 'EDIT ON REVIEW', 'CANCEL ON REVIEW'],
    'REQUEST ACCEPTED': ['DRIVER REJECTED', 'ASSIGN TO DRIVER', 'ASSIGN TO ONLINE TAXI', 'DRIVER CONFIRMATION', 'TRIP IN PROGRESS'],
    'WAITING FOR RATING': ['WAITING FOR RATING'],
  };

  @override
  void initState() {
    super.initState();
    _fetchTripStatusCounts();

    // Initialize auto-refresh functionality
    initAutoRefresh();
  }

  @override
  void dispose() {
    _mounted = false;

    // Clean up auto-refresh timers
    disposeAutoRefresh();

    super.dispose();
  }

  // Implement the refreshData method required by AutoRefreshMixin
  @override
  Future<void> refreshData({bool showSnackbar = true}) async {
    // Check if widget is still mounted before refreshing
    if (!_mounted) return;

    await _fetchTripStatusCounts();

    // Check if widget is still mounted after async operation
    if (!_mounted) return;

    if (showSnackbar) {
      showRefreshSnackbar('Dashboard data refreshed successfully');
    }
  }

  Future<void> _fetchTripStatusCounts() async {
    if (!_mounted) return;

    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      // Get the current user from the provider
      final appState = Provider.of<MyAppState>(context, listen: false);
      final currentUser = appState.currentUser;      if (currentUser == null) {
        setState(() {
          _errorMessage = AppLocalizations.of(context).youMustBeLoggedIn;
          _isLoading = false;
        });
        return;
      }

      final response = await ApiService.get('trips/requestor-status-counts/${currentUser.userId}');

      if (!_mounted) return;

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        setState(() {
          _tripStatusCounts = Map<String, int>.from(data['statusCounts']);
          _isLoading = false;
        });      } else {
        setState(() {
          _errorMessage = AppLocalizations.of(context).failedToLoadTripStatusCounts;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (!_mounted) return;
      setState(() {
        _errorMessage = '${AppLocalizations.of(context).error}: $e';
        _isLoading = false;
      });
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toUpperCase()) {
      case 'REQUEST':
        return Colors.orange.shade600;
      case 'REQUEST ACCEPTED':
        return Colors.blue.shade600;
      case 'WAITING FOR RATING':
        return Colors.purple.shade600;
      default:
        return Colors.grey.shade600;
    }
  }

  // Handle bottom navigation tap
  void _onBottomNavTap(int index) {
    // Check if widget is still mounted before navigation
    if (!_mounted) return;

    // Store navigator reference before potential async gap
    final navigator = Navigator.of(context);

    // If we're already on the dashboard and tapping dashboard again, just refresh
    if (index == 0 && _currentIndex == 0) {
      refreshData();
      return;
    }

    setState(() {
      _currentIndex = index;
    });

    // Navigate based on the selected index
    switch (index) {
      case 0: // Dashboard - already here
        break;
      case 1: // Request Trip
        navigator.push(
          MaterialPageRoute(builder: (context) => const RequestTripScreen()),
        );
      case 2: // Rate Trips
        navigator.push(
          MaterialPageRoute(builder: (context) => const TripRatingListScreen()),
        );
      case 3: // History
        navigator.push(
          MaterialPageRoute(builder: (context) => const MyTripHistoryScreen()),
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    // Check if we're on a small screen (mobile)
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600 && !kIsWeb;

    return Scaffold(
      key: scaffoldKey,      appBar: CommonAppBar(
        title: AppLocalizations.of(context).requestorDashboardTitle,
        onRefresh: () {
          setState(() {
            lastRefreshTime = DateTime.now();
          });
          refreshData();
        },
        showMenuIcon: true,
      ),
      drawer: const SidebarNavigation(),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage.isNotEmpty
              ? Center(child: Text(_errorMessage))              : _tripStatusCounts.isEmpty
                  ? Center(child: Text(AppLocalizations.of(context).noTripDataAvailableRequestor))
                  : RefreshIndicator(
                      onRefresh: _fetchTripStatusCounts,
                      child: SingleChildScrollView(
                        physics: const AlwaysScrollableScrollPhysics(),
                        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 6.0),                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Role Switcher for multi-role users
                            const RoleSwitcher(),
                            Text(
                              AppLocalizations.of(context).myTripRequests,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF0D47A1),
                              ),
                            ),
                            const SizedBox(height: 8),
                            // Create a responsive layout for the status groups
                            // Use a Row for web/landscape and Column for portrait mobile
                            _buildResponsiveStatusCards(),
                          ],
                        ),
                      ),
                    ),
      // Only show bottom navigation on mobile
      bottomNavigationBar: isSmallScreen
          ? MobileBottomNav(
              currentIndex: _currentIndex,
              onTap: _onBottomNavTap,
            )
          : null,
    );
  }

  // Calculate the total count for a status group
  int _getGroupCount(String groupName) {
    int count = 0;
    final statuses = _statusGroups[groupName] ?? [];
    for (final status in statuses) {
      count += _tripStatusCounts[status] ?? 0;
    }
    return count;
  }

  Widget _buildStatusGroupCard(String groupName, int count) {
    // Check if we're on a small screen (mobile)
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600 && !kIsWeb;

    final Color statusColor = _getStatusColor(groupName);
    // Use a lighter shade for the background
    final Color backgroundColor = statusColor.withAlpha(25); // Very light background

    return Expanded(
      child: Card(
        elevation: 1,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(6),
          side: BorderSide(color: statusColor, width: 0.5),
        ),
        color: backgroundColor,
        shadowColor: statusColor.withAlpha(40),
        child: InkWell(
          onTap: () {
            // Check if widget is still mounted before navigation
            if (!_mounted) return;

            // Store navigator reference before potential async gap
            final navigator = Navigator.of(context);

            // Navigate to appropriate screen based on status group
            if (groupName == 'WAITING FOR RATING') {
              navigator.pushNamed('/trip-rating');
            } else {
              // Navigate to filtered trip list for other status groups
              navigator.pushNamed(
                '/request-trip',
                arguments: {'statusGroup': groupName},
              );
            }
          },
          borderRadius: BorderRadius.circular(6),
          child: Padding(
            // Adjust padding based on screen size
            padding: EdgeInsets.all(isSmallScreen ? 12.0 : 16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  _getStatusIcon(groupName),
                  color: statusColor,
                  // Smaller icon on mobile
                  size: isSmallScreen ? 28 : 32,
                ),
                SizedBox(height: isSmallScreen ? 6 : 8),
                Text(
                  groupName,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                  style: TextStyle(
                    fontWeight: FontWeight.w800,
                    color: statusColor,
                    fontSize: isSmallScreen ? 14 : 16,
                  ),
                ),
                SizedBox(height: isSmallScreen ? 6 : 8),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: isSmallScreen ? 12 : 16,
                    vertical: isSmallScreen ? 6 : 8
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(isSmallScreen ? 6 : 8),
                    boxShadow: [
                      BoxShadow(
                        color: statusColor.withAlpha(40), // Lighter shadow
                        blurRadius: isSmallScreen ? 1 : 2,
                        offset: Offset(0, isSmallScreen ? 1 : 2),
                      ),
                    ],
                  ),
                  child: Text(
                    count.toString(),
                    style: TextStyle(
                      fontWeight: FontWeight.w800,
                      color: statusColor,
                      fontSize: isSmallScreen ? 20 : 24,
                    ),
                  ),
                ),
                SizedBox(height: isSmallScreen ? 6 : 8),
                Text(
                  _getStatusDescription(groupName),
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  style: TextStyle(
                    color: statusColor,
                    fontSize: isSmallScreen ? 10 : 12,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  IconData _getStatusIcon(String status) {
    switch (status.toUpperCase()) {
      case 'REQUEST':
        return Icons.new_releases;
      case 'REQUEST ACCEPTED':
        return Icons.check_circle;
      case 'WAITING FOR RATING':
        return Icons.star;
      default:
        return Icons.help;
    }
  }

  String _getStatusDescription(String status) {
    switch (status.toUpperCase()) {
      case 'REQUEST':
        return 'New, Edit, Cancel';
      case 'REQUEST ACCEPTED':
        return 'Assigned, Online Taxi, Confirmed, In Progress';
      case 'WAITING FOR RATING':
        return 'Completed trips waiting for rating';
      default:
        return '';
    }
  }

  // Build a responsive layout for status cards based on screen size
  Widget _buildResponsiveStatusCards() {
    // Check if we're on a small screen (mobile portrait)
    final screenWidth = MediaQuery.of(context).size.width;
    final bool isSmallScreen = screenWidth < 600 && !kIsWeb;

    try {
      if (isSmallScreen) {
        // Use a Column layout for mobile portrait with better spacing
        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // For mobile, we use a custom row that doesn't use Expanded
            // This avoids layout issues on small screens
            _buildMobileStatusCard('REQUEST', _getGroupCount('REQUEST')),
            const SizedBox(height: 8),
            _buildMobileStatusCard('REQUEST ACCEPTED', _getGroupCount('REQUEST ACCEPTED')),
            const SizedBox(height: 8),
            _buildMobileStatusCard('WAITING FOR RATING', _getGroupCount('WAITING FOR RATING')),
          ],
        );
      } else {
        // Use a Row layout for web or landscape
        return Row(
          children: [
            _buildStatusGroupCard('REQUEST', _getGroupCount('REQUEST')),
            const SizedBox(width: 8),
            _buildStatusGroupCard('REQUEST ACCEPTED', _getGroupCount('REQUEST ACCEPTED')),
            const SizedBox(width: 8),
            _buildStatusGroupCard('WAITING FOR RATING', _getGroupCount('WAITING FOR RATING')),
          ],
        );
      }
    } catch (e) {
      // Fallback layout in case of errors
      print('Error building responsive layout: $e');
      return Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                AppLocalizations.of(context).tripDataIsLoading,
                style: TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      );
    }
  }

  // Special version of the card for mobile that doesn't use Expanded
  Widget _buildMobileStatusCard(String groupName, int count) {
    final Color statusColor = _getStatusColor(groupName);
    final Color backgroundColor = statusColor.withAlpha(25);

    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(6),
        side: BorderSide(color: statusColor, width: 0.5),
      ),
      color: backgroundColor,
      shadowColor: statusColor.withAlpha(40),
      child: InkWell(
        onTap: () {
          // Check if widget is still mounted before navigation
          if (!_mounted) return;

          // Store navigator reference before potential async gap
          final navigator = Navigator.of(context);

          // Navigate to appropriate screen based on status group
          if (groupName == 'WAITING FOR RATING') {
            navigator.pushNamed('/trip-rating');
          } else {
            navigator.pushNamed(
              '/request-trip',
              arguments: {'statusGroup': groupName},
            );
          }
        },
        borderRadius: BorderRadius.circular(6),
        child: Padding(
          padding: EdgeInsets.all(12.0),
          child: Row(
            children: [
              // Icon and status on the left
              Expanded(
                flex: 3,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          _getStatusIcon(groupName),
                          color: statusColor,
                          size: 24,
                        ),
                        SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            groupName,
                            style: TextStyle(
                              fontWeight: FontWeight.w800,
                              color: statusColor,
                              fontSize: 14,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 4),
                    Text(
                      _getStatusDescription(groupName),
                      style: TextStyle(
                        color: statusColor,
                        fontSize: 10,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              // Count on the right
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(6),
                  boxShadow: [
                    BoxShadow(
                      color: statusColor.withAlpha(40),
                      blurRadius: 1,
                      offset: Offset(0, 1),
                    ),
                  ],
                ),
                child: Text(
                  count.toString(),
                  style: TextStyle(
                    fontWeight: FontWeight.w800,
                    color: statusColor,
                    fontSize: 18,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}



