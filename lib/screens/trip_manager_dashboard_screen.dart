import 'dart:convert';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../generated/l10n/app_localizations.dart';
import '../main.dart';
import '../services/api_service.dart';
import '../services/auth_service.dart';
import '../sidebar.dart';
import '../utils/auto_refresh.dart';
import '../widgets/common_app_bar.dart';
import '../widgets/mobile_bottom_nav.dart';
import '../widgets/role_switcher.dart';
import 'trip_approval_screen.dart';
import 'trip_monitoring_screen.dart';

class TripManagerDashboardScreen extends StatefulWidget {
  const TripManagerDashboardScreen({super.key});

  @override
  State<TripManagerDashboardScreen> createState() => _TripManagerDashboardScreenState();
}

class _TripManagerDashboardScreenState extends State<TripManagerDashboardScreen> with AutoRefreshMixin {
  // Current index for bottom navigation
  int _currentIndex = 0;
  bool _isLoading = true;
  String _errorMessage = '';
  Map<String, int> _tripStatusCounts = {};
  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  bool _mounted = true;

  // Auto-refresh is handled by the AutoRefreshMixin

  @override
  void initState() {
    super.initState();
    _fetchTripStatusCounts();

    // Initialize auto-refresh functionality
    initAutoRefresh();
  }

  @override
  void dispose() {
    _mounted = false;
    // Clean up auto-refresh timers
    disposeAutoRefresh();
    super.dispose();
  }

  // Implement the refreshData method required by AutoRefreshMixin
  @override
  Future<void> refreshData({bool showSnackbar = true}) async {
    await _fetchTripStatusCounts(showSnackbar: showSnackbar);
  }

  // Auto-refresh functionality is now handled by the AutoRefreshMixin

  // Show a snackbar message safely (without BuildContext across async gaps)
  void _showSnackbar(String message, {bool isError = false}) {
    if (!_mounted) return;

    // Use a post-frame callback to ensure we're not in the middle of a build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!_mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: isError ? Colors.red : Colors.green,
          duration: Duration(seconds: isError ? 3 : 2),
        ),
      );
    });
  }

  Future<void> _fetchTripStatusCounts({bool showSnackbar = true}) async {
    if (!_mounted) return;

    // Check if user is logged in
    final appState = Provider.of<MyAppState>(context, listen: false);
    print('Trip Manager Dashboard - Current user: ${appState.currentUser?.name ?? 'null'}');
    print('Trip Manager Dashboard - User role: ${appState.currentUser?.role ?? 'null'}');
    print('Trip Manager Dashboard - Is logged in: ${appState.isLoggedIn}');    if (appState.currentUser == null) {
      setState(() {
        _errorMessage = AppLocalizations.of(context).youMustBeLoggedIn;
        _isLoading = false;
      });
      print('Trip Manager Dashboard - Error: User not logged in');
      return;
    }

    // Update last refresh time is handled by the mixin
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      // Get the access token for debugging
      final token = await AuthService.getAccessToken();
      print('Trip Manager Dashboard - Access token: ${token != null ? 'token exists' : 'token is null'}');

      // Use ApiService for authenticated requests
      print('Trip Manager Dashboard - Fetching trip status counts');
      final response = await ApiService.get('/trips/status-counts');
      print('Trip Manager Dashboard - Response status code: ${response.statusCode}');
      print('Trip Manager Dashboard - Response body: ${response.body}');

      if (!_mounted) return;

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        setState(() {
          _tripStatusCounts = Map<String, int>.from(data['statusCounts']);
          _isLoading = false;
        });
        print('Trip Manager Dashboard - Trip status counts: $_tripStatusCounts');        // Show success message if requested
        if (showSnackbar) {
          _showSnackbar(AppLocalizations.of(context).dataRefreshedSuccessfully);
        }
      } else {
        setState(() {
          _errorMessage = '${AppLocalizations.of(context).failedToLoadTripStatusCounts}: ${response.statusCode} - ${response.body}';
          _isLoading = false;
        });
        print('Trip Manager Dashboard - Error: Failed to load trip status counts: ${response.statusCode} - ${response.body}');

        // Show error message if requested
        if (showSnackbar) {
          _showSnackbar(AppLocalizations.of(context).failedToLoadTripStatusCounts, isError: true);
        }
      }
    } catch (e) {
      if (!_mounted) return;      setState(() {
        _errorMessage = '${AppLocalizations.of(context).error}: $e';
        _isLoading = false;
      });
      print('Trip Manager Dashboard - Exception: $e');

      // Show error message if requested
      if (showSnackbar) {
        _showSnackbar('${AppLocalizations.of(context).error}: $e', isError: true);
      }
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toUpperCase()) {
      case 'REQUEST':
        return Colors.orange.shade600;
      case 'EDIT ON REVIEW':
        return Colors.deepOrange.shade600;
      case 'CANCEL ON REVIEW':
        return Colors.red.shade600;
      case 'ASSIGN TO DRIVER':
        return Colors.blue.shade600;
      case 'ASSIGN TO ONLINE TAXI':
        return Colors.green.shade600;
      case 'DRIVER REJECTED':
        return Colors.red.shade700;
      default:
        return Colors.purple.shade600;
    }
  }

  // Handle bottom navigation tap
  void _onBottomNavTap(int index) {
    // If we're already on the dashboard and tapping dashboard again, just refresh
    if (index == 0 && _currentIndex == 0) {
      refreshData();
      return;
    }

    setState(() {
      _currentIndex = index;
    });

    // Navigate based on the selected index
    switch (index) {
      case 0: // Dashboard - already here
        break;
      case 1: // Trip Monitoring
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const TripMonitoringScreen()),
        );
      case 2: // Trip Approvals
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const TripApprovalScreen()),
        );
      case 3: // Settings
        Navigator.pushNamed(
          context,
          '/driver-settings',
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    // Check if we're on a small screen (mobile)
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600 && !kIsWeb;

    return Scaffold(
      key: scaffoldKey,      appBar: CommonAppBar(
        title: AppLocalizations.of(context).dashboardTitle,
        onRefresh: () {
          setState(() {
            lastRefreshTime = DateTime.now();
          });
          refreshData();
        },
        showMenuIcon: true,
      ),
      drawer: const SidebarNavigation(),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage.isNotEmpty
              ? Center(child: Text(_errorMessage))
              : _tripStatusCounts.isEmpty
                  ? Center(child: Text(AppLocalizations.of(context).noTripDataAvailable))
                  : RefreshIndicator(
                      onRefresh: _fetchTripStatusCounts,
                      child: SingleChildScrollView(
                        physics: const AlwaysScrollableScrollPhysics(),                        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 6.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Role Switcher for Trip Managers
                            const RoleSwitcher(),
                            Text(
                              AppLocalizations.of(context).tripRequestsByStatus,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF0D47A1),
                              ),
                            ),
                            const SizedBox(height: 8),
                            // Responsive grid layout - 2 columns on mobile, 3 on larger screens
                            GridView.builder(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: isSmallScreen ? 2 : 3, // 2 columns on mobile
                                crossAxisSpacing: isSmallScreen ? 8.0 : 4.0, // Larger spacing for mobile
                                mainAxisSpacing: isSmallScreen ? 8.0 : 4.0, // Larger spacing for mobile
                                childAspectRatio: isSmallScreen ? 1.0 : 2.2, // Larger cells for mobile, original for desktop
                              ),
                              itemCount: _tripStatusCounts.entries.length,
                              itemBuilder: (context, index) {
                                final entry = _tripStatusCounts.entries.elementAt(index);
                                final status = entry.key;
                                final count = entry.value;
                                return _buildStatusGridItem(status, count);
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
      // Only show bottom navigation on mobile
      bottomNavigationBar: isSmallScreen
          ? MobileBottomNav(
              currentIndex: _currentIndex,
              onTap: _onBottomNavTap,
            )
          : null,
    );
  }

  Widget _buildStatusGridItem(String status, int count) {
    // Check if we're on a small screen (mobile)
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600 && !kIsWeb;

    final Color statusColor = _getStatusColor(status);
    // Use a lighter shade for the background to match requestor dashboard
    final Color backgroundColor = statusColor.withAlpha(25); // Very light background

    return Card(
      elevation: 1, // Increased to match requestor dashboard
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(6),
        side: BorderSide(color: statusColor, width: 0.5),
      ),
      color: backgroundColor,
      shadowColor: statusColor.withAlpha(40),
      child: InkWell(
        onTap: () {
          // Navigate to appropriate screen based on status
          if (status == 'EDIT ON REVIEW' || status == 'CANCEL ON REVIEW') {
            // Navigate to Trip Approval screen for review statuses
            Navigator.pushNamed(
              context,
              '/trip-approval',
              arguments: {'status': status},
            );
          } else {
            // Navigate to Trip Monitoring screen for all other statuses
            Navigator.pushNamed(
              context,
              '/trip-monitoring',
              arguments: {'status': status},
            );
          }
        },
        borderRadius: BorderRadius.circular(6),
        child: Padding(
          // Larger padding for mobile, compact for desktop
          padding: isSmallScreen
            ? EdgeInsets.all(12.0) // Larger padding for mobile
            : EdgeInsets.symmetric(horizontal: 6.0, vertical: 4.0), // Original compact padding for desktop
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                _getStatusIcon(status),
                color: statusColor,
                // Larger for mobile, original compact size for desktop
                size: isSmallScreen ? 28 : 24,
              ),
              SizedBox(height: isSmallScreen ? 6 : 3),
              Text(
                status.toUpperCase(),
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
                style: TextStyle(
                  fontWeight: FontWeight.w800,
                  color: statusColor,
                  // Larger for mobile, original compact size for desktop
                  fontSize: isSmallScreen ? 14 : 14,
                ),
              ),
              SizedBox(height: isSmallScreen ? 6 : 3),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: isSmallScreen ? 12 : 10,
                  vertical: isSmallScreen ? 6 : 4
                ),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(isSmallScreen ? 6 : 8),
                  boxShadow: [
                    BoxShadow(
                      color: statusColor.withAlpha(40), // Lighter shadow
                      blurRadius: isSmallScreen ? 1 : 2,
                      offset: Offset(0, isSmallScreen ? 1 : 2),
                    ),
                  ],
                ),
                child: Text(
                  count.toString(),
                  style: TextStyle(
                    fontWeight: FontWeight.w800,
                    color: statusColor,
                    // Larger for mobile, original compact size for desktop
                    fontSize: isSmallScreen ? 20 : 18,
                  ),
                ),
              ),
              SizedBox(height: isSmallScreen ? 6 : 3),
              Text(
                _getStatusDescription(status),
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
                maxLines: isSmallScreen ? 2 : 1,
                style: TextStyle(
                  color: statusColor,
                  fontSize: isSmallScreen ? 10 : 9,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getStatusIcon(String status) {
    switch (status.toUpperCase()) {
      case 'REQUEST':
        return Icons.new_releases;
      case 'EDIT ON REVIEW':
        return Icons.edit_note;
      case 'CANCEL ON REVIEW':
        return Icons.cancel;
      case 'ASSIGN TO DRIVER':
        return Icons.person_add;
      case 'ASSIGN TO ONLINE TAXI':
        return Icons.directions_car;
      case 'DRIVER REJECTED':
        return Icons.person_off;
      default:
        return Icons.help;
    }
  }
  // Get a description for each status type
  String _getStatusDescription(String status) {
    switch (status.toUpperCase()) {
      case 'REQUEST':
        return AppLocalizations.of(context).newTripRequests;
      case 'EDIT ON REVIEW':
        return AppLocalizations.of(context).tripEditRequestsPendingApproval;
      case 'CANCEL ON REVIEW':
        return AppLocalizations.of(context).tripCancellationRequestsPendingApproval;
      case 'ASSIGN TO DRIVER':
        return AppLocalizations.of(context).tripsReadyToBeAssignedToDrivers;
      case 'ASSIGN TO ONLINE TAXI':
        return AppLocalizations.of(context).tripsAssignedToOnlineTaxi;
      case 'DRIVER REJECTED':
        return AppLocalizations.of(context).tripsRejectedByDrivers;
      case 'DRIVER CONFIRMATION':
        return AppLocalizations.of(context).tripsConfirmedByDrivers;
      case 'TRIP IN PROGRESS':
        return AppLocalizations.of(context).tripsCurrentlyInProgress;
      case 'WAITING FOR RATING':
        return AppLocalizations.of(context).completedTripsWaitingForRating;
      default:
        return '';
    }
  }
}



