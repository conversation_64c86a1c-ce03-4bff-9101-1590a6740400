import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../generated/l10n/app_localizations.dart';
import '../main.dart';
import '../services/api_service.dart';
import '../sidebar.dart';
import '../utils/auto_refresh.dart';
import '../widgets/common_app_bar.dart';

class DriverCheckInManagementScreen extends StatefulWidget {
  const DriverCheckInManagementScreen({super.key});

  @override
  State<DriverCheckInManagementScreen> createState() => _DriverCheckInManagementScreenState();
}

class _DriverCheckInManagementScreenState extends State<DriverCheckInManagementScreen> with AutoRefreshMixin {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  bool _isLoading = true;
  String _errorMessage = '';
  List<Map<String, dynamic>> _checkIns = [];
  List<Map<String, dynamic>> _filteredCheckIns = [];
  bool _isUpdating = false;

  // Filter state
  DateTime? _selectedDate;
  String? _selectedDriverCode;
  String? _selectedValidationStatus;
  final TextEditingController _searchController = TextEditingController();

  // List of drivers for the dropdown
  List<Map<String, dynamic>> _drivers = [];

  @override
  void initState() {
    super.initState();
    _selectedDate = DateTime.now();
    _fetchDrivers();
    _fetchCheckIns();

    // Initialize auto-refresh
    initAutoRefresh();
  }

  @override
  void dispose() {
    _searchController.dispose();
    disposeAutoRefresh();
    super.dispose();
  }

  // Implement the refreshData method required by AutoRefreshMixin
  @override
  Future<void> refreshData({bool showSnackbar = true}) async {
    await _fetchCheckIns();
    if (showSnackbar) {
      showRefreshSnackbar('Check-in data refreshed successfully');
    }
  }

  // Fetch all drivers for the dropdown filter
  Future<void> _fetchDrivers() async {
    try {
      final response = await ApiService.get('drivers');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        if (mounted) {
          setState(() {
            _drivers = List<Map<String, dynamic>>.from(
              data['drivers'].map((driver) => {
                'code': driver['driver_code'],
                'name': driver['name'],
              })
            );
          });
        }
      } else {
        throw Exception('Failed to load drivers');
      }
    } catch (e) {
      print('Error fetching drivers: $e');
      if (mounted) {
        setState(() {
          _errorMessage = 'Error fetching drivers: $e';
        });
      }
    }
  }

  // Fetch check-in/check-out records with optional filters
  Future<void> _fetchCheckIns() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      // Build query parameters
      final Map<String, String> queryParams = {};

      if (_selectedDate != null) {
        queryParams['date'] = DateFormat('yyyy-MM-dd').format(_selectedDate!);
      }

      if (_selectedDriverCode != null && _selectedDriverCode!.isNotEmpty) {
        queryParams['driver_code'] = _selectedDriverCode!;
      }

      if (_selectedValidationStatus != null && _selectedValidationStatus!.isNotEmpty) {
        queryParams['validation_status'] = _selectedValidationStatus!;
      }

      // Construct the URL with query parameters
      String url = 'driver-check-ins';
      if (queryParams.isNotEmpty) {
        url += '?${queryParams.entries.map((e) => '${e.key}=${e.value}').join('&')}';
      }

      final response = await ApiService.get(url);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        if (mounted) {
          setState(() {
            _checkIns = List<Map<String, dynamic>>.from(data['check_ins']);
            _applySearchFilter();
            _isLoading = false;
          });
        }
      } else {
        throw Exception('Failed to load check-in records');
      }
    } catch (e) {
      print('Error fetching check-in records: $e');
      if (mounted) {
        setState(() {
          _errorMessage = 'Error fetching check-in records: $e';
          _isLoading = false;
        });
      }
    }
  }

  // Apply text search filter
  void _applySearchFilter() {
    final searchText = _searchController.text.toLowerCase();

    if (searchText.isEmpty) {
      _filteredCheckIns = List.from(_checkIns);
    } else {
      _filteredCheckIns = _checkIns.where((checkIn) {
        final driverName = checkIn['driver_name']?.toString().toLowerCase() ?? '';
        final driverCode = checkIn['driver_code']?.toString().toLowerCase() ?? '';

        return driverName.contains(searchText) ||
               driverCode.contains(searchText);
      }).toList();
    }
  }

  // Reset all filters
  void _resetFilters() {
    setState(() {
      _selectedDate = null;
      _selectedDriverCode = null;
      _selectedValidationStatus = null;
      _searchController.clear();
    });
    _fetchCheckIns();
  }

  // Build filters for mobile layout
  Widget _buildMobileFilters() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Date filter
        InkWell(
          onTap: () async {
            final DateTime? picked = await showDatePicker(
              context: context,
              initialDate: _selectedDate ?? DateTime.now(),
              firstDate: DateTime(2020),
              lastDate: DateTime(2030),
            );
            if (picked != null && picked != _selectedDate) {
              setState(() {
                _selectedDate = picked;
              });
              _fetchCheckIns();
            }
          },
          child: InputDecorator(
            decoration: const InputDecoration(
              labelText: 'Date',
              border: OutlineInputBorder(),
              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _selectedDate != null
                      ? DateFormat('yyyy-MM-dd').format(_selectedDate!)
                      : 'All Dates',
                ),
                const Icon(Icons.calendar_today, size: 18),
              ],
            ),
          ),
        ),
        const SizedBox(height: 12),

        // Driver filter
        DropdownButtonFormField<String>(
          decoration: const InputDecoration(
            labelText: 'Driver',
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          value: _selectedDriverCode,
          hint: const Text('All Drivers'),
          onChanged: (String? newValue) {
            setState(() {
              _selectedDriverCode = newValue;
            });
            _fetchCheckIns();
          },
          items: [
            const DropdownMenuItem<String>(
              value: null,
              child: Text('All Drivers'),
            ),
            ..._drivers.map((driver) {
              return DropdownMenuItem<String>(
                value: driver['code'],
                child: Text('${driver['code']} - ${driver['name']}'),
              );
            }),
          ],
        ),
        const SizedBox(height: 12),

        // Validation status filter
        DropdownButtonFormField<String>(
          decoration: const InputDecoration(
            labelText: 'Validation Status',
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          value: _selectedValidationStatus,
          hint: const Text('All Records'),
          onChanged: (String? newValue) {
            setState(() {
              _selectedValidationStatus = newValue;
            });
            _fetchCheckIns();
          },
          items: const [
            DropdownMenuItem<String>(
              value: null,
              child: Text('All Records'),
            ),
            DropdownMenuItem<String>(
              value: 'validated',
              child: Text('Validated'),
            ),
            DropdownMenuItem<String>(
              value: 'not_validated',
              child: Text('Not Validated'),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // Search field
        TextField(
          controller: _searchController,
          decoration: const InputDecoration(
            labelText: 'Search',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.search),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          onChanged: (value) {
            setState(() {
              _applySearchFilter();
            });
          },
        ),
        const SizedBox(height: 12),

        // Reset filters button
        ElevatedButton.icon(
          onPressed: _resetFilters,
          icon: const Icon(Icons.clear),
          label: const Text('Reset Filters'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.grey.shade200,
            foregroundColor: Colors.black87,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
        ),
      ],
    );
  }

  // Build filters for desktop layout
  Widget _buildDesktopFilters() {
    return Row(
      children: [
        // Date filter
        Expanded(
          child: InkWell(
            onTap: () async {
              final DateTime? picked = await showDatePicker(
                context: context,
                initialDate: _selectedDate ?? DateTime.now(),
                firstDate: DateTime(2020),
                lastDate: DateTime(2030),
              );
              if (picked != null && picked != _selectedDate) {
                setState(() {
                  _selectedDate = picked;
                });
                _fetchCheckIns();
              }
            },
            child: InputDecorator(
              decoration: const InputDecoration(
                labelText: 'Date',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    _selectedDate != null
                        ? DateFormat('yyyy-MM-dd').format(_selectedDate!)
                        : 'All Dates',
                  ),
                  const Icon(Icons.calendar_today, size: 18),
                ],
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),

        // Driver filter
        Expanded(
          child: DropdownButtonFormField<String>(
            decoration: const InputDecoration(
              labelText: 'Driver',
              border: OutlineInputBorder(),
              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            value: _selectedDriverCode,
            hint: const Text('All Drivers'),
            onChanged: (String? newValue) {
              setState(() {
                _selectedDriverCode = newValue;
              });
              _fetchCheckIns();
            },
            items: [
              const DropdownMenuItem<String>(
                value: null,
                child: Text('All Drivers'),
              ),
              ..._drivers.map((driver) {
                return DropdownMenuItem<String>(
                  value: driver['code'],
                  child: Text('${driver['code']} - ${driver['name']}'),
                );
              }),
            ],
          ),
        ),
        const SizedBox(width: 16),

        // Validation status filter
        Expanded(
          child: DropdownButtonFormField<String>(
            decoration: const InputDecoration(
              labelText: 'Validation Status',
              border: OutlineInputBorder(),
              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            value: _selectedValidationStatus,
            hint: const Text('All Records'),
            onChanged: (String? newValue) {
              setState(() {
                _selectedValidationStatus = newValue;
              });
              _fetchCheckIns();
            },
            items: const [
              DropdownMenuItem<String>(
                value: null,
                child: Text('All Records'),
              ),
              DropdownMenuItem<String>(
                value: 'validated',
                child: Text('Validated'),
              ),
              DropdownMenuItem<String>(
                value: 'not_validated',
                child: Text('Not Validated'),
              ),
            ],
          ),
        ),
        const SizedBox(width: 16),

        // Search field
        Expanded(
          child: TextField(
            controller: _searchController,
            decoration: const InputDecoration(
              labelText: 'Search',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.search),
              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            onChanged: (value) {
              setState(() {
                _applySearchFilter();
              });
            },
          ),
        ),
        const SizedBox(width: 16),

        // Reset filters button
        ElevatedButton.icon(
          onPressed: _resetFilters,
          icon: const Icon(Icons.clear),
          label: const Text('Reset'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.grey.shade200,
            foregroundColor: Colors.black87,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
        ),
      ],
    );
  }

  // Format timestamp for display
  String _formatDateTime(String? timestamp) {
    if (timestamp == null) return 'N/A';
    try {
      // Parse the timestamp and convert to local time
      final dateTime = DateTime.parse(timestamp).toLocal();
      return DateFormat('yyyy-MM-dd HH:mm:ss').format(dateTime);
    } catch (e) {
      return 'Invalid date';
    }
  }

  // Format timestamp for compact display
  String _formatDateTimeCompact(String? timestamp) {
    if (timestamp == null) return 'N/A';
    try {
      // Parse the timestamp and convert to local time
      final dateTime = DateTime.parse(timestamp).toLocal();
      return DateFormat('MM/dd HH:mm').format(dateTime);
    } catch (e) {
      return 'Invalid';
    }
  }

  // Validate or unvalidate a check-in/check-out
  Future<void> _validateCheckIn(int checkInId, bool isValidated) async {
    // Get the current user from provider
    final appState = Provider.of<MyAppState>(context, listen: false);
    final currentUser = appState.currentUser;    if (currentUser == null) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context).youMustBeLoggedIn)),
        );
      }
      return;
    }

    setState(() {
      _isUpdating = true;
    });

    try {
      // Try to parse userId as an integer
      int? userId;
      try {
        userId = int.parse(currentUser.userId);
        print('Successfully parsed userId: $userId');
      } catch (e) {
        print('Error parsing userId: ${currentUser.userId}, Error: $e');
        userId = 1; // Default to user ID 1 if parsing fails
      }

      final response = await ApiService.post(
        'driver-check-ins/$checkInId/validate',
        {
          'userId': userId,
          'isValidated': isValidated,
        },
      );

      print('Validation response status: ${response.statusCode}');
      print('Validation response body: ${response.body}');

      if (!mounted) return;

      if (response.statusCode == 200) {
        // Refresh the data
        await _fetchCheckIns();

        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isValidated ? AppLocalizations.of(context).checkinValidatedSuccessfully : AppLocalizations.of(context).validationRemoved),
            backgroundColor: isValidated ? Colors.green : Colors.orange,
          ),
        );
      } else {
        final errorData = jsonDecode(response.body);

        // Check if migration is required
        if (errorData.containsKey('migration_required') && errorData['migration_required'] == true) {
          _showMigrationDialog();
          return;
        }

        throw Exception(errorData['error'] ?? 'Failed to validate check-in');
      }
    } catch (e) {
      print('Error validating check-in: $e');

      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  // Show dialog with migration instructions
  void _showMigrationDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Database Migration Required'),
          content: const SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'The validation feature requires a database migration. Please run the following command in your terminal:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 16),
                SelectableText(
                  'cd C:\\FLEEX GH\\fleex\\express\npsql -U postgres -d fleex -f migrations/014_add_validation_to_driver_check_in.sql',
                ),
                SizedBox(height: 16),
                Text(
                  'After running the migration, restart the server and refresh this page.',
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }

  // Build mobile-friendly list view for check-ins
  Widget _buildMobileCheckInList() {
    return _filteredCheckIns.isEmpty
        ? _buildEmptyListView()
        : ListView.builder(
            physics: const AlwaysScrollableScrollPhysics(), // Ensures RefreshIndicator works even when list is short
            padding: const EdgeInsets.only(bottom: 16.0),
            itemCount: _filteredCheckIns.length,
            itemBuilder: (context, index) {
        final checkIn = _filteredCheckIns[index];
        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          child: ExpansionTile(
            title: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        checkIn['driver_name'] ?? 'Unknown',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        checkIn['driver_code'] ?? '',
                        style: TextStyle(
                          color: Colors.grey.shade700,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                // Validation status indicator
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: checkIn['is_validated'] == true
                        ? Colors.green.shade100
                        : Colors.grey.shade200,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    checkIn['is_validated'] == true ? 'Validated' : 'Not Validated',
                    style: TextStyle(
                      color: checkIn['is_validated'] == true
                          ? Colors.green.shade800
                          : Colors.grey.shade700,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            subtitle: Text(
              _formatDateTimeCompact(checkIn['check_in_time']),
              style: const TextStyle(fontSize: 12),
            ),
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Check-in details
                    _buildMobileDetailSection(
                      'Check-In Details',
                      [
                        'Time: ${_formatDateTime(checkIn['check_in_time'])}',
                      ],
                    ),
                    const Divider(),

                    // Check-out details
                    _buildMobileDetailSection(
                      'Check-Out Details',
                      [
                        'Time: ${_formatDateTime(checkIn['checkout_time'])}',
                        'Status: ${checkIn['is_checked_out'] == true ? 'Completed' : 'Pending'}',
                      ],
                    ),
                    const Divider(),

                    // Validation controls
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text('Validate this record:'),
                        Switch(
                          value: checkIn['is_validated'] == true,
                          onChanged: _isUpdating
                              ? null
                              : (bool value) {
                                  _validateCheckIn(checkIn['check_in_id'], value);
                                },
                        ),
                      ],
                    ),
                    if (checkIn['is_validated'] == true)
                      Padding(
                        padding: const EdgeInsets.only(top: 8.0),
                        child: Text(
                          'Validated by: ${checkIn['validator_name'] ?? 'Unknown'}\nTime: ${_formatDateTime(checkIn['validation_time'])}',
                          style: const TextStyle(fontSize: 12, fontStyle: FontStyle.italic),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // Helper method to build a detail section for mobile view
  Widget _buildMobileDetailSection(String title, List<String> details) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
        ),
        const SizedBox(height: 8),
        ...details.map((detail) => Padding(
              padding: const EdgeInsets.only(bottom: 4.0),
              child: Text(detail),
            )),
      ],
    );
  }

  // Build an empty list view with a message
  Widget _buildEmptyListView() {
    return ListView(
      physics: const AlwaysScrollableScrollPhysics(), // Ensures RefreshIndicator works
      children: [
        SizedBox(height: MediaQuery.of(context).size.height * 0.15),
        const Center(
          child: Icon(
            Icons.assignment_outlined,
            size: 80,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 16),
        const Center(
          child: Text(
            'No check-in records found',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
        ),
        const SizedBox(height: 8),
        Center(
          child: Text(
            'Try adjusting your filters or pull down to refresh',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  // Build desktop data table for check-ins
  Widget _buildDesktopCheckInTable() {
    if (_filteredCheckIns.isEmpty) {
      return _buildEmptyListView();
    }

    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(), // Ensures RefreshIndicator works
      scrollDirection: Axis.vertical,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: DataTable(
          headingRowColor: WidgetStateProperty.all(Colors.grey.shade200),
          columnSpacing: 12, // Slightly increased spacing for longer headers
          horizontalMargin: 10, // Reduce horizontal margin
          columns: const [
            DataColumn(
              label: Text('Driver', style: TextStyle(fontWeight: FontWeight.bold)),
              tooltip: 'Driver name and code',
            ),
            DataColumn(
              label: Text('Check-In', style: TextStyle(fontWeight: FontWeight.bold)),
              tooltip: 'Check-in time',
            ),
            DataColumn(
              label: Text('Check-Out', style: TextStyle(fontWeight: FontWeight.bold)),
              tooltip: 'Check-out time',
            ),
            DataColumn(
              label: Text('Valid', style: TextStyle(fontWeight: FontWeight.bold)),
              tooltip: 'Validation status',
            ),
          ],
          rows: _filteredCheckIns.map((checkIn) {
            return DataRow(
              cells: [
                // Driver
                DataCell(
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        checkIn['driver_name'] ?? 'Unknown',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      Text(
                        checkIn['driver_code'] ?? '',
                        style: TextStyle(
                          color: Colors.grey.shade700,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),

                // Check-In Time
                DataCell(
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        children: [
                          Text(
                            _formatDateTimeCompact(checkIn['check_in_time']),
                            style: const TextStyle(fontSize: 12),
                          ),
                          const SizedBox(width: 4),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                            decoration: BoxDecoration(
                              color: Colors.blue.shade100,
                              borderRadius: BorderRadius.circular(2),
                            ),
                            child: const Text(
                              'In',
                              style: TextStyle(
                                color: Colors.blue,
                                fontWeight: FontWeight.bold,
                                fontSize: 10,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Check-Out Time
                DataCell(
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        children: [
                          Text(
                            _formatDateTimeCompact(checkIn['checkout_time']),
                            style: const TextStyle(fontSize: 12),
                          ),
                          const SizedBox(width: 4),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                            decoration: BoxDecoration(
                              color: checkIn['is_checked_out'] == true
                                  ? Colors.green.shade100
                                  : Colors.grey.shade200,
                              borderRadius: BorderRadius.circular(2),
                            ),
                            child: Text(
                              checkIn['is_checked_out'] == true ? 'Out' : 'Pending',
                              style: TextStyle(
                                color: checkIn['is_checked_out'] == true
                                    ? Colors.green.shade800
                                    : Colors.grey.shade700,
                                fontWeight: FontWeight.bold,
                                fontSize: 10,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Validation (more compact)
                DataCell(
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Checkbox(
                        visualDensity: VisualDensity.compact,
                        value: checkIn['is_validated'] == true,
                        onChanged: _isUpdating
                            ? null
                            : (bool? value) {
                                _validateCheckIn(checkIn['check_in_id'], value ?? false);
                              },
                      ),
                      if (checkIn['is_validated'] == true)
                        Tooltip(
                          message: 'Validated by: ${checkIn['validator_name'] ?? 'Unknown'}\nTime: ${_formatDateTime(checkIn['validation_time'])}',
                          child: const Icon(Icons.check_circle, color: Colors.green, size: 16),
                        ),
                    ],
                  ),
                ),
              ],
            );
          }).toList(),
        ),
      ),
    );
  }



  @override
  Widget build(BuildContext context) {
    // Check if we're on a small screen (mobile)
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    return Scaffold(
      key: _scaffoldKey,
      appBar: CommonAppBar(
        title: 'Driver Check-In Management',
        onRefresh: () => refreshData(showSnackbar: true),
        showMenuIcon: true,
      ),
      drawer: const SidebarNavigation(),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage.isNotEmpty
              ? Center(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(Icons.error_outline, color: Colors.red, size: 48),
                        const SizedBox(height: 16),
                        Text(
                          AppLocalizations.of(context).errorLoadingData,
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                color: Colors.red,
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          _errorMessage,
                          textAlign: TextAlign.center,
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton.icon(
                          onPressed: () => _fetchCheckIns(),
                          icon: const Icon(Icons.refresh),
                          label: const Text('Try Again'),
                        ),
                      ],
                    ),
                  ),
                )
              : Column(
                  children: [
                    // Filters section
                    Padding(
                      padding: EdgeInsets.all(isSmallScreen ? 8.0 : 16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Filters',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF0D47A1),
                            ),
                          ),
                          const SizedBox(height: 8),
                          // Use mobile or desktop filters based on screen size
                          isSmallScreen ? _buildMobileFilters() : _buildDesktopFilters(),
                        ],
                      ),
                    ),

                    // Results count
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 8.0 : 16.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Showing ${_filteredCheckIns.length} records',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 8),

                    // Data table with responsive layout
                    Expanded(
                      child: RefreshIndicator(
                        onRefresh: () => refreshData(showSnackbar: false),
                        child: isSmallScreen ? _buildMobileCheckInList() : _buildDesktopCheckInTable(),
                      ),
                    ),
                  ],
                ),
    );
  }
}



