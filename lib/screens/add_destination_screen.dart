import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../generated/l10n/app_localizations.dart';
import '../services/api_service.dart';

class AddDestinationScreen extends StatefulWidget {
  const AddDestinationScreen({super.key});

  @override
  State<AddDestinationScreen> createState() => _AddDestinationScreenState();
}

// Custom formatter to convert text to uppercase
class UpperCaseTextFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    return TextEditingValue(
      text: newValue.text.toUpperCase(),
      selection: newValue.selection,
    );
  }
}

// Custom formatter to allow only numbers, hyphens, periods, and commas for coordinates
class CoordinatesTextFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    // Allow only digits, hyphens, periods, and commas
    final RegExp regExp = RegExp(r'[0-9\-\.,]');
    String newString = '';
    for (int i = 0; i < newValue.text.length; i++) {
      if (regExp.hasMatch(newValue.text[i])) {
        newString += newValue.text[i];
      }
    }

    return TextEditingValue(
      text: newString,
      selection: TextEditingValue.empty.selection.copyWith(
        baseOffset: newString.length,
        extentOffset: newString.length,
      ),
    );
  }
}

class _AddDestinationScreenState extends State<AddDestinationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _destinationCodeController = TextEditingController();
  final _destinationController = TextEditingController();
  final _addressController = TextEditingController();
  final _notesController = TextEditingController();
  final _latitudeLongitudeController = TextEditingController();
  final _hoursController = TextEditingController();
  final _minutesController = TextEditingController();
  final _initialController = TextEditingController();

  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _fetchNextDestinationCode();
  }

  Future<void> _fetchNextDestinationCode() async {
    try {
      final response = await ApiService.get('destinations/next-code');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (mounted) {
          setState(() {
            // Get the destination code from the API response
            final destinationCode = data['destinationCode'];

            // If the API returns a sequenceNumber, use it to format the code
            if (data.containsKey('sequenceNumber')) {
              final sequenceNumber = data['sequenceNumber'];
              _destinationCodeController.text = 'DST${sequenceNumber.toString().padLeft(4, '0')}';
            } else {
              // Fallback to the provided code with validation
              if (!destinationCode.startsWith('DST')) {
                _destinationCodeController.text = 'DST${destinationCode.replaceAll(RegExp(r'^[A-Za-z]+'), '')}';
              } else {
                _destinationCodeController.text = destinationCode;
              }
            }
          });
        }
      } else {        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.of(context).failedToGenerateDestinationCode),
              backgroundColor: Colors.red,
            ),
          );
        }
      }    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${AppLocalizations.of(context).anErrorOccurred}: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    // Calculate total duration in minutes
    int totalMinutes = 0;

    // Parse hours
    if (_hoursController.text.isNotEmpty) {
      final hours = int.tryParse(_hoursController.text) ?? 0;
      totalMinutes += hours * 60;
    }

    // Parse minutes
    if (_minutesController.text.isNotEmpty) {
      final minutes = int.tryParse(_minutesController.text) ?? 0;
      totalMinutes += minutes;
    }

    try {
      final response = await ApiService.post(
        'destinations',
        {
          'destination_code': _destinationCodeController.text,
          'destination': _destinationController.text.toUpperCase(),
          'address': _addressController.text.toUpperCase(),
          'notes': _notesController.text.toUpperCase(),
          'duration': totalMinutes.toString(), // Store duration in minutes
          'latitude_longitude': _latitudeLongitudeController.text,
          'initial': _initialController.text.toUpperCase(),
        },
      );

      if (response.statusCode == 201) {
        if (mounted) {
          Navigator.pop(context, true);
        }
      } else {
        final errorData = jsonDecode(response.body);        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(errorData['error'] ?? AppLocalizations.of(context).failedToAddDestination),
              backgroundColor: Colors.red,
            ),
          );
        }
      }    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${AppLocalizations.of(context).anErrorOccurred}: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(      appBar: AppBar(
        title: Text(
          AppLocalizations.of(context).addDestination,
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: const Color(0xFF0D47A1),
        iconTheme: const IconThemeData(color: Colors.white),
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [                      TextFormField(
                        controller: _destinationCodeController,
                        decoration: InputDecoration(
                          labelText: AppLocalizations.of(context).destinationCode,
                          border: const OutlineInputBorder(),
                        ),
                        readOnly: true,
                        enabled: false,
                      ),
                      const SizedBox(height: 16),                      TextFormField(
                        controller: _destinationController,
                        decoration: InputDecoration(
                          labelText: AppLocalizations.of(context).destinationName,
                          border: const OutlineInputBorder(),
                        ),
                        textCapitalization: TextCapitalization.characters,
                        inputFormatters: [
                          UpperCaseTextFormatter(),
                        ],
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return AppLocalizations.of(context).pleaseEnterDestinationName;
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),                      TextFormField(
                        controller: _addressController,
                        decoration: InputDecoration(
                          labelText: AppLocalizations.of(context).address,
                          border: const OutlineInputBorder(),
                        ),
                        textCapitalization: TextCapitalization.characters,
                        inputFormatters: [
                          UpperCaseTextFormatter(),
                        ],
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return AppLocalizations.of(context).pleaseEnterAddress;
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),                      TextFormField(
                        controller: _initialController,
                        decoration: InputDecoration(
                          labelText: AppLocalizations.of(context).initial,
                          border: const OutlineInputBorder(),
                          helperText: AppLocalizations.of(context).mustBe3Characters,
                        ),
                        textCapitalization: TextCapitalization.characters,
                        maxLength: 3,
                        inputFormatters: [
                          UpperCaseTextFormatter(),
                        ],
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return AppLocalizations.of(context).initialRequired;
                          }
                          if (value.length != 3) {
                            return AppLocalizations.of(context).initialMustBe3Characters;
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),                      TextFormField(
                        controller: _notesController,
                        decoration: InputDecoration(
                          labelText: AppLocalizations.of(context).notes,
                          border: const OutlineInputBorder(),
                        ),
                        textCapitalization: TextCapitalization.characters,
                        inputFormatters: [
                          UpperCaseTextFormatter(),
                        ],
                        keyboardType: TextInputType.text,
                      ),
                      const SizedBox(height: 16),
                      // Duration fields (hours and minutes)
                      Row(
                        children: [
                          Expanded(                            child: TextFormField(
                              controller: _hoursController,
                              decoration: InputDecoration(
                                labelText: AppLocalizations.of(context).durationHours,
                                border: const OutlineInputBorder(),
                                helperText: AppLocalizations.of(context).hours,
                              ),
                              keyboardType: TextInputType.number,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                              ],
                              validator: (value) {
                                if (value != null && value.isNotEmpty) {
                                  final hours = int.tryParse(value);
                                  if (hours == null || hours < 0) {
                                    return AppLocalizations.of(context).invalidHours;
                                  }
                                }
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(                            child: TextFormField(
                              controller: _minutesController,
                              decoration: InputDecoration(
                                labelText: AppLocalizations.of(context).durationMinutes,
                                border: const OutlineInputBorder(),
                                helperText: AppLocalizations.of(context).minutes,
                              ),
                              keyboardType: TextInputType.number,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                              ],
                              validator: (value) {
                                if (value != null && value.isNotEmpty) {
                                  final minutes = int.tryParse(value);
                                  if (minutes == null || minutes < 0 || minutes > 59) {
                                    return AppLocalizations.of(context).minutesMustBe0To59;
                                  }
                                }
                                return null;
                              },
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),                      TextFormField(
                        controller: _latitudeLongitudeController,
                        decoration: InputDecoration(
                          labelText: AppLocalizations.of(context).coordinatesLatLong,
                          border: const OutlineInputBorder(),
                          hintText: AppLocalizations.of(context).coordinatesFormat,
                        ),
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        inputFormatters: [
                          CoordinatesTextFormatter(),
                        ],
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return AppLocalizations.of(context).pleaseEnterCoordinates;
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton(
                        onPressed: _submitForm,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF0D47A1),
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),                        child: Text(
                          _isLoading ? AppLocalizations.of(context).adding : AppLocalizations.of(context).submit,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
    );
  }

  @override
  void dispose() {
    _destinationCodeController.dispose();
    _destinationController.dispose();
    _addressController.dispose();
    _notesController.dispose();
    _latitudeLongitudeController.dispose();
    _hoursController.dispose();
    _minutesController.dispose();
    _initialController.dispose();
    super.dispose();
  }
}





