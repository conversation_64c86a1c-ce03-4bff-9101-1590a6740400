import 'dart:convert';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../generated/l10n/app_localizations.dart';
import '../main.dart';
import '../services/api_service.dart';
import '../services/auth_service.dart';
import '../widgets/flag_button.dart';
import 'driver_trips_screen.dart';
import 'requestor_dashboard_screen.dart';
import 'trip_manager_dashboard_screen.dart';
import 'users_screen.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLoading = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
  }

  Future<void> _login() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final user = await AuthService.login(
        _usernameController.text,
        _passwordController.text,
      );

      if (mounted) {
        // Update the app state with the logged-in user
        Provider.of<MyAppState>(context, listen: false).setCurrentUser(user);

        // Navigate to the appropriate screen based on user role
        if (user.isAdmin || user.isTripManager) {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const TripManagerDashboardScreen()),
          );
        } else if (user.isDriver) {
          // For drivers, check if they have an active check-in
          _checkForActiveCheckIn(user);
        } else {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const RequestorDashboardScreen()),
          );        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          // Check for specific error types and use appropriate localized messages
          final errorMessage = e.toString().replaceAll('Exception: ', '');
          if (errorMessage.toLowerCase().contains('invalid credentials') ||
              errorMessage.toLowerCase().contains('invalid username') ||
              errorMessage.toLowerCase().contains('invalid password')) {
            _errorMessage = AppLocalizations.of(context).invalidCredentials;
          } else if (errorMessage.toLowerCase().contains('authentication failed')) {
            _errorMessage = AppLocalizations.of(context).authenticationFailed;
          } else {
            // For other errors, use the generic authentication failed message with details
            _errorMessage = AppLocalizations.of(context).authenticationFailedWithError(errorMessage);
          }
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loginWithMicrosoftEntra() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final user = await AuthService.loginWithMicrosoftEntra();

      if (mounted) {
        // Update the app state with the logged-in user
        Provider.of<MyAppState>(context, listen: false).setCurrentUser(user);

        // Navigate to the appropriate screen based on user role
        if (user.isAdmin || user.isTripManager) {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const TripManagerDashboardScreen()),
          );
        } else if (user.isDriver) {
          // For drivers, check if they have an active check-in
          _checkForActiveCheckIn(user);
        } else {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => UsersScreen()),
          );        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          // Check for specific error types and use appropriate localized messages
          final errorMessage = e.toString().replaceAll('Exception: ', '');
          if (errorMessage.toLowerCase().contains('invalid credentials') ||
              errorMessage.toLowerCase().contains('invalid username') ||
              errorMessage.toLowerCase().contains('invalid password')) {
            _errorMessage = AppLocalizations.of(context).invalidCredentials;
          } else if (errorMessage.toLowerCase().contains('authentication failed')) {
            _errorMessage = AppLocalizations.of(context).authenticationFailed;
          } else {
            // For other errors, use the generic authentication failed message with details
            _errorMessage = AppLocalizations.of(context).authenticationFailedWithError(errorMessage);
          }
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _checkForActiveCheckIn(dynamic user) async {
    try {
      // Call API to check if driver has an active check-in
      final response = await ApiService.get('users/${user.userId}/active-check-in');

      final result = jsonDecode(response.body);

      if (!mounted) return;

      if (result != null && result['has_active_check_in']) {
        print('Active check-in found, navigating to driver trips screen');
        // Navigate directly to driver trips screen
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => DriverTripsScreen(
              driverCode: result['driver_code'],
              activeCheckIn: result['active_check_in'],
            ),
          ),
        );
      } else {
        print('No active check-in found, navigating to users screen for check-in');
        // No active check-in, go to users screen for check-in
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => UsersScreen()),
        );
      }
    } catch (e) {
      print('API error checking for active check-in: $e');
      print('No active check-in found or error occurred');
      // Handle error case by navigating to users screen
      print('Navigating to users screen due to error');
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => UsersScreen()),
        );
      }
      return;
    }
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Check if we're on a small screen (mobile portrait)
    final bool isSmallScreen = MediaQuery.of(context).size.width < 600;

    return Scaffold(
      body: isSmallScreen && !kIsWeb
          ? _buildMobileLayout()
          : _buildDesktopLayout(),
    );
  }

  // Mobile layout - compact design without background image
  Widget _buildMobileLayout() {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Stack(
          children: [
            // Main content
            Center(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Logo and company name - more compact for mobile
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.directions_car,
                            size: 36,
                            color: Color(0xFF0D47A1),
                          ),
                          const SizedBox(width: 10),
                          Text(
                            'FLEEX',
                            style: TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF0D47A1),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 40),
                      // Username field
                      TextFormField(
                        controller: _usernameController,
                        decoration: InputDecoration(
                          labelText: AppLocalizations.of(context).username,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          prefixIcon: const Icon(Icons.person),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return AppLocalizations.of(context).pleaseEnterUsername;
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      // Password field
                      TextFormField(
                        controller: _passwordController,
                        obscureText: true,
                        decoration: InputDecoration(
                          labelText: AppLocalizations.of(context).password,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          prefixIcon: const Icon(Icons.lock),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return AppLocalizations.of(context).pleaseEnterPassword;
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 24),
                      // Error message
                      if (_errorMessage.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 16),
                          child: Text(
                            _errorMessage,
                            style: const TextStyle(color: Colors.red),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      // Login button
                      SizedBox(
                        height: 50,
                        child: _isLoading
                            ? const Center(child: CircularProgressIndicator())
                            : ElevatedButton(
                                onPressed: _login,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Color(0xFF0D47A1),
                                  foregroundColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                ),
                                child: Text(
                                  AppLocalizations.of(context).login,
                                  style: TextStyle(fontSize: 18),
                                ),
                              ),
                      ),
                      const SizedBox(height: 24),
                      // Or divider
                      Row(
                        children: [
                          Expanded(child: Divider(color: Colors.grey[400])),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            child: Text(
                              AppLocalizations.of(context).or,
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          Expanded(child: Divider(color: Colors.grey[400])),
                        ],
                      ),
                      const SizedBox(height: 24),
                      // Microsoft login button
                      Center(
                        child: Column(
                          children: [
                            Text(
                              AppLocalizations.of(context).signInWith,
                              style: TextStyle(
                                color: Colors.black54,
                                fontSize: 16,
                              ),
                            ),
                            const SizedBox(height: 16),
                            SizedBox(
                              height: 50,
                              child: _isLoading
                                  ? const Center(child: CircularProgressIndicator())
                                  : ElevatedButton.icon(
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.white,
                                        foregroundColor: Colors.black87,
                                        elevation: 1,
                                        side: BorderSide(color: Colors.grey[300]!),
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(8),
                                        ),
                                      ),
                                      icon: Image.network(
                                        'https://upload.wikimedia.org/wikipedia/commons/thumb/9/96/Microsoft_logo_%282012%29.svg/512px-Microsoft_logo_%282012%29.svg.png',
                                        height: 24,
                                      ),
                                      label: Text(
                                        AppLocalizations.of(context).signInWithMicrosoft,
                                        style: TextStyle(
                                          fontSize: 16,
                                        ),
                                      ),
                                      onPressed: _loginWithMicrosoftEntra,
                                    ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            // Language flag buttons positioned in top-right corner
            Positioned(
              top: 16,
              right: 16,
              child: LanguageFlagButtons(),
            ),
          ],
        ),
      ),
    );
  }

  // Desktop layout - side-by-side design
  Widget _buildDesktopLayout() {
    return Row(
      children: [
        // Left side - Image
        Expanded(
          child: Container(
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage('lib/assets/login-image.jpg'),
                fit: BoxFit.cover,
              ),
            ),
          ),
        ),
        // Right side - Login Form
        Expanded(
          child: Stack(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 40),
                child: Center(
                  child: SingleChildScrollView(
                    child: Form(
                      key: _formKey,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          // Logo and company name
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(
                                Icons.directions_car,
                                size: 48,
                                color: Color(0xFF0D47A1),
                              ),
                              const SizedBox(width: 16),
                              Text(
                                'FLEEX',
                                style: TextStyle(
                                  fontSize: 36,
                                  fontWeight: FontWeight.bold,
                                  color: Color(0xFF0D47A1),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 40),
                          // Welcome text
                          Text(
                            AppLocalizations.of(context).welcomeBack,
                            style: TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey[800],
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 10),
                          Text(
                            AppLocalizations.of(context).signInToContinue,
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey[600],
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 40),
                          // Username field
                          TextFormField(
                            controller: _usernameController,
                            decoration: InputDecoration(
                              labelText: AppLocalizations.of(context).username,
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                              prefixIcon: const Icon(Icons.person),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return AppLocalizations.of(context).pleaseEnterUsername;
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 20),
                          // Password field
                          TextFormField(
                            controller: _passwordController,
                            obscureText: true,
                            decoration: InputDecoration(
                              labelText: AppLocalizations.of(context).password,
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                              prefixIcon: const Icon(Icons.lock),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return AppLocalizations.of(context).pleaseEnterPassword;
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 20),
                          // Error message
                          if (_errorMessage.isNotEmpty)
                            Padding(
                              padding: const EdgeInsets.only(bottom: 20),
                              child: Text(
                                _errorMessage,
                                style: const TextStyle(color: Colors.red),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          // Login button
                          SizedBox(
                            height: 50,
                            child: _isLoading
                                ? const Center(child: CircularProgressIndicator())
                                : ElevatedButton(
                                    onPressed: _login,
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Color(0xFF0D47A1),
                                      foregroundColor: Colors.white,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                    ),
                                    child: Text(
                                      AppLocalizations.of(context).login,
                                      style: TextStyle(fontSize: 18),
                                    ),
                                  ),
                          ),
                          const SizedBox(height: 20),
                          // Or divider
                          Row(
                            children: [
                              Expanded(child: Divider(color: Colors.grey[400])),
                              Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 16),
                                child: Text(
                                  AppLocalizations.of(context).or,
                                  style: TextStyle(
                                    color: Colors.grey[600],
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              Expanded(child: Divider(color: Colors.grey[400])),
                            ],
                          ),
                          const SizedBox(height: 20),
                          // Microsoft login button
                          OutlinedButton(
                            onPressed: _isLoading ? null : _loginWithMicrosoftEntra,
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 15),
                              side: const BorderSide(color: Color(0xFF0D47A1)),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(Icons.account_circle, color: Color(0xFF0D47A1)),
                                const SizedBox(width: 10),
                                Text(
                                  AppLocalizations.of(context).signInWithMicrosoft,
                                  style: TextStyle(color: Color(0xFF0D47A1)),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              // Language flag buttons positioned in top-right corner
              Positioned(
                top: 20,
                right: 20,
                child: LanguageFlagButtons(),
              ),
            ],
          ),
        ),
      ],
    );
  }
}



