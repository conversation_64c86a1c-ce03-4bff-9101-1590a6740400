import 'dart:convert';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import '../generated/l10n/app_localizations.dart';
import '../models/cargo.dart';
import '../services/api_service.dart';
import '../sidebar.dart';
import '../widgets/common_app_bar.dart';
import 'add_cargo_screen.dart';
import 'edit_cargo_screen.dart';

class CargoSettingsScreen extends StatefulWidget {
  const CargoSettingsScreen({super.key});

  @override
  State<CargoSettingsScreen> createState() => CargoSettingsScreenState();
}

class CargoSettingsScreenState extends State<CargoSettingsScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  List<Cargo> _cargos = [];
  List<Cargo> _filteredCargos = [];
  bool _isLoading = true;
  String _errorMessage = '';
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _fetchCargos();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterCargos(String query) {
    setState(() {
      _searchQuery = query.toLowerCase();
      _filteredCargos = _cargos
          .where((cargo) =>
              cargo.cargoCode.toLowerCase().contains(_searchQuery) ||
              cargo.cargoName.toLowerCase().contains(_searchQuery))
          .toList();
    });
  }

  Future<void> _fetchCargos() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final response = await ApiService.get('cargos');

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = jsonDecode(response.body);
        if (data['cargos'] != null) {
          setState(() {
            _cargos = (data['cargos'] as List)
                .map((json) => Cargo.fromJson(json))
                .toList();
            _filteredCargos = _cargos;
          });        } else {
          setState(() {
            _errorMessage = AppLocalizations.of(context).noCargoDataFound;
          });
        }
      } else {
        setState(() {
          _errorMessage = AppLocalizations.of(context).failedToLoadCargos(response.statusCode.toString());
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = '${AppLocalizations.of(context).anErrorOccurred}: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _deleteCargo(Cargo cargo) async {
    // Store context reference before async gap
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    try {
      final response = await ApiService.delete('cargos/${cargo.cargoCode}');

      // Check if widget is still mounted before updating UI
      if (!mounted) return;      if (response.statusCode == 200) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).cargoDeletedSuccessfully),
            backgroundColor: Colors.green,
          ),
        );
        _fetchCargos();
      } else {
        final errorData = jsonDecode(response.body);
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(errorData['error'] ?? AppLocalizations.of(context).failedToDeleteCargo),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      // Check if widget is still mounted before updating UI
      if (!mounted) return;

      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('${AppLocalizations.of(context).anErrorOccurred}: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
  Future<void> _showDeleteConfirmation(Cargo cargo) async {
    return showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(AppLocalizations.of(context).confirmDelete),
          content: Text(AppLocalizations.of(context).confirmDeleteCargo(cargo.cargoCode)),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(AppLocalizations.of(context).cancel),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _deleteCargo(cargo);
              },
              child: Text(
                AppLocalizations.of(context).delete,
                style: const TextStyle(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );
  }

  // Build a mobile-friendly card for each cargo
  Widget _buildCargoCard(Cargo cargo) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Cargo code and name
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: const Color(0xFF0D47A1).withAlpha(25),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    cargo.cargoCode,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF0D47A1),
                    ),
                  ),
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(left: 8.0),
                    child: Text(
                      cargo.cargoName,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                      textAlign: TextAlign.end,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [                TextButton.icon(
                  icon: const Icon(Icons.edit, size: 18),
                  label: Text(AppLocalizations.of(context).edit),
                  style: TextButton.styleFrom(
                    foregroundColor: const Color(0xFF0D47A1),
                  ),
                  onPressed: () async {
                    final result = await Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => EditCargoScreen(cargo: cargo),
                      ),
                    );
                    if (result == true) {
                      _fetchCargos();
                    }
                  },
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  icon: const Icon(Icons.delete, size: 18),
                  label: Text(AppLocalizations.of(context).delete),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.red,
                  ),
                  onPressed: () => _showDeleteConfirmation(cargo),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Check if we're on a small screen (mobile)
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600 && !kIsWeb;

    return Scaffold(
      key: _scaffoldKey,      drawer: const SidebarNavigation(),
      appBar: CommonAppBar(
        title: AppLocalizations.of(context).cargoSettingsTitle,
        onRefresh: _fetchCargos,
        showMenuIcon: true,
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context).searchCargos,
                hintText: AppLocalizations.of(context).searchCargosHint,
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                filled: true,
                fillColor: Colors.white,
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          _filterCargos('');
                        },
                      )
                    : null,
              ),
              onChanged: _filterCargos,
            ),
          ),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _errorMessage.isNotEmpty
                    ? Center(child: Text(_errorMessage))                    : _filteredCargos.isEmpty
                        ? Center(
                            child: Text(
                              _searchQuery.isEmpty
                                  ? AppLocalizations.of(context).noCargosFound
                                  : AppLocalizations.of(context).noMatchingCargosFound,
                            ),
                          )
                        : isSmallScreen
                            // Mobile layout - card list
                            ? RefreshIndicator(
                                onRefresh: () async {
                                  await _fetchCargos();
                                },
                                child: ListView.builder(
                                  padding: const EdgeInsets.all(8.0),
                                  itemCount: _filteredCargos.length,
                                  itemBuilder: (context, index) {
                                    return _buildCargoCard(_filteredCargos[index]);
                                  },
                                ),
                              )
                            // Desktop layout - data table with horizontal scroll
                            : SingleChildScrollView(
                                scrollDirection: Axis.horizontal,
                                child: SingleChildScrollView(
                                  child: Padding(
                                    padding: const EdgeInsets.all(16.0),
                                    child: Theme(
                                      data: Theme.of(context).copyWith(
                                        dataTableTheme: DataTableThemeData(
                                          columnSpacing: 16,
                                          horizontalMargin: 16,
                                          headingTextStyle: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color: Color(0xFF0D47A1),
                                          ),
                                        ),
                                      ),
                                      child: DataTable(
                                        headingRowColor: WidgetStateProperty.all(
                                          const Color(0xFF0D47A1).withAlpha(25),
                                        ),                                        columns: [
                                          DataColumn(label: Text(AppLocalizations.of(context).cargoCode)),
                                          DataColumn(label: Text(AppLocalizations.of(context).cargoName)),
                                          DataColumn(label: Text(AppLocalizations.of(context).actions)),
                                        ],
                                        rows: _filteredCargos.map((cargo) {
                                          return DataRow(
                                            cells: [
                                              DataCell(Text(cargo.cargoCode)),
                                              DataCell(Text(cargo.cargoName)),
                                              DataCell(
                                                Row(
                                                  mainAxisSize: MainAxisSize.min,
                                                  children: [
                                                    IconButton(
                                                      icon: const Icon(Icons.edit),
                                                      onPressed: () async {
                                                        final result = await Navigator.push(
                                                          context,
                                                          MaterialPageRoute(
                                                            builder: (context) =>
                                                                EditCargoScreen(cargo: cargo),
                                                          ),
                                                        );
                                                        if (result == true) {
                                                          _fetchCargos();
                                                        }
                                                      },
                                                    ),
                                                    IconButton(
                                                      icon: const Icon(Icons.delete),
                                                      color: Colors.red,
                                                      onPressed: () => _showDeleteConfirmation(cargo),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          );
                                        }).toList(),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: const Color(0xFF0D47A1),
        child: const Icon(Icons.add),
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const AddCargoScreen()),
          );
          if (result == true) {
            _fetchCargos();
          }
        },
      ),
    );
  }
}







