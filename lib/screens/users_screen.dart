import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../generated/l10n/app_localizations.dart';
import '../main.dart';
import '../services/api_service.dart';
import '../sidebar.dart';
import '../widgets/common_app_bar.dart';

class UsersScreen extends StatefulWidget {
  @override
  UsersScreenState createState() => UsersScreenState();
}

class UsersScreenState extends State<UsersScreen> {
  bool _isLoading = false;
  String _errorMessage = '';
  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    // No need to check for active check-in here, it's already done in login screen
  }

  // Active check-in is now checked in the login screen
  // This method is removed to avoid redundant checks

  @override
  void dispose() {
    super.dispose();
  }

  // Handle driver check-in process
  void _handleCheckIn(BuildContext context) {
    final appState = Provider.of<MyAppState>(context, listen: false);
    final currentUser = appState.currentUser;
    final navigator = Navigator.of(context);
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    setState(() {
      _isLoading = true;
    });

    // Use a separate async function to handle the API call
    _performCheckIn(currentUser?.userId).then((driverCode) {
      if (mounted) {
        // Navigate to driver check-in screen first
        navigator.pushReplacementNamed(
          '/driver-check-in',
          arguments: {
            'driverCode': driverCode,
            'userId': currentUser?.userId ?? '',
          },
        );
      }
    }).catchError((error) {
      if (mounted) {
        print('Error during check-in: $error');
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('Check-in failed: $error'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }).whenComplete(() {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    });
  }

  // Perform the actual check-in API call
  Future<String> _performCheckIn(String? userId) async {
    if (userId == null) {
      throw Exception('User ID is null');
    }

    final response = await ApiService.get('drivers/user/$userId');

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      final driverCode = data['driver_code'];
      return driverCode;
    } else {
      throw Exception('Failed to get driver code: ${response.statusCode}');
    }
  }



  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<MyAppState>(context);
    final currentUser = appState.currentUser;

    return Scaffold(
      key: scaffoldKey,      appBar: CommonAppBar(
        title: AppLocalizations.of(context).dashboard,
        onRefresh: () {},
      ),
      drawer: (currentUser != null && !currentUser.isDriver) // Hide for drivers
          ? const SidebarNavigation()
          : null,
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage.isNotEmpty
              ? Center(child: Text(_errorMessage))
              : (currentUser != null && currentUser.isDriver)
                  // Driver dashboard with single Check In button
                  ? Center(
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Text(
                              'Driver Dashboard',
                              style: TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF0D47A1),
                              ),
                            ),
                            const SizedBox(height: 30),
                            SizedBox(
                              width: 200,
                              height: 60,
                              child: ElevatedButton(
                                onPressed: () {
                                  _handleCheckIn(context);
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: const Color(0xFF0D47A1),
                                  foregroundColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                ),
                                child: const Text(
                                  'Check In',
                                  style: TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                  // Redirect to appropriate dashboard based on role
                  : Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Text(
                            'Welcome to Fleex',
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF0D47A1),
                            ),
                          ),
                          const SizedBox(height: 20),
                          ElevatedButton(
                            onPressed: () {
                              if (currentUser?.isTripManager ?? false) {
                                Navigator.pushReplacementNamed(context, '/trip-manager-dashboard');
                              } else {
                                Navigator.pushReplacementNamed(context, '/requestor-dashboard');
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF0D47A1),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 15),
                            ),
                            child: const Text('Go to Dashboard'),
                          ),
                        ],
                      ),
                    ),
    );
  }
}













