import 'dart:convert';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../generated/l10n/app_localizations.dart';
import '../main.dart';
import '../models/trip.dart';
import '../services/api_service.dart';
import '../sidebar.dart';
import '../widgets/common_app_bar.dart';
import '../widgets/mobile_bottom_nav.dart';
import 'rating_screen.dart';

class TripRatingListScreen extends StatefulWidget {
  const TripRatingListScreen({super.key});

  @override
  State<TripRatingListScreen> createState() => _TripRatingListScreenState();
}

class _TripRatingListScreenState extends State<TripRatingListScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  List<Trip> _trips = [];
  List<Trip> _filteredTrips = [];
  bool _isLoading = false;
  String _errorMessage = '';
  String _searchQuery = '';
  DateTime? _selectedDate;
  final TextEditingController _dateController = TextEditingController();

  // Cache for driver names to avoid showing "Unassigned" when driver exists
  final Map<String, String> _driverNameCache = {};

  // Current index for bottom navigation
  int _currentIndex = 2; // Set to 2 for Rate Trips tab

  @override
  void initState() {
    super.initState();
    _fetchTrips();
  }



  @override
  void dispose() {
    _dateController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
        _dateController.text = DateFormat('E, dd-MMM-yy').format(picked);
      });
      // Apply filters after state update
      _filterTrips(_searchQuery);
    }
  }

  void _clearDateFilter() {
    setState(() {
      _selectedDate = null;
      _dateController.clear();
      _filteredTrips = _trips;
    });
    // Apply any existing text search after state update
    _filterTrips(_searchQuery);
  }

  Future<void> _fetchTrips() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      // Get the current user from the provider
      final appState = Provider.of<MyAppState>(context, listen: false);
      final currentUser = appState.currentUser;      if (currentUser == null) {
        setState(() {
          _errorMessage = AppLocalizations.of(context).youMustBeLoggedIn;
          _isLoading = false;
        });
        return;
      }

      // Fetch all trips
      final response = await ApiService.get('trips');

      if (response.statusCode == 200) {
        try {
          final Map<String, dynamic> data = jsonDecode(response.body);

          // Parse all trips
          final allTrips = (data['trips'] as List)
              .map((json) => Trip.fromJson(json))
              .toList();

          // Filter trips to only show those requested by the current user
          // AND with status "WAITING FOR RATING"
          final userTrips = allTrips.where((trip) {
            // First check if this trip belongs to the current user
            bool belongsToCurrentUser = false;
            if (trip.requestorId != null) {
              belongsToCurrentUser = trip.requestorId == currentUser.userId;
            } else {
              belongsToCurrentUser = trip.requestorName.toLowerCase() == currentUser.name.toLowerCase();
            }

            // Only include trips with status "WAITING FOR RATING"
            return belongsToCurrentUser && trip.status == 'WAITING FOR RATING';
          }).toList();

          // Clear the driver name cache to ensure we get fresh data
          _driverNameCache.clear();

          // Pre-load driver names for all trips
          for (final trip in userTrips) {
            if (trip.driverName != null && trip.driverName != 'Unassigned') {
              _driverNameCache[trip.tripCode] = trip.driverName!;
            } else {
              // Start fetching driver names in the background
              _fetchDriverName(trip);
            }
          }

          setState(() {
            _trips = userTrips;
            _filteredTrips = userTrips;
          });
        } catch (e) {
          print('JSON decode error: $e');
          setState(() {
            _errorMessage = 'Invalid response format. Server might be down.';
          });
        }
      } else {
        print('Non-200 response: ${response.statusCode}');
        setState(() {
          _errorMessage = 'Server error: ${response.statusCode}';
        });
      }
    } catch (e) {
      print('Network error: $e');
      setState(() {
        _errorMessage = 'Connection error. Is the server running?';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _filterTrips(String query) {
    setState(() {
      _searchQuery = query;
      _filteredTrips = _trips.where((trip) {
        // First apply date filter if selected
        if (_selectedDate != null) {
          try {
            final tripDate = DateFormat('E, dd-MMM-yy').parse(trip.date);
            return tripDate.year == _selectedDate!.year &&
                   tripDate.month == _selectedDate!.month &&
                   tripDate.day == _selectedDate!.day;
          } catch (e) {
            print('Error parsing date: ${trip.date}');
            return false;
          }
        }

        // Then apply text search if query is not empty
        if (query.isEmpty) {
          return true;
        }

        // Simple text search across all text fields
        final searchText = query.toLowerCase();
        // Get the driver name from cache if available
        final driverName = _driverNameCache.containsKey(trip.tripCode)
            ? _driverNameCache[trip.tripCode]!.toLowerCase()
            : (trip.driverName ?? 'Unassigned').toLowerCase();

        return trip.tripCode.toLowerCase().contains(searchText) ||
               trip.fromDestination.toLowerCase().contains(searchText) ||
               trip.toDestination.toLowerCase().contains(searchText) ||
               trip.date.toLowerCase().contains(searchText) ||
               trip.time.toLowerCase().contains(searchText) ||
               driverName.contains(searchText);
      }).toList();
    });
  }

  // Handle bottom navigation tap
  void _onBottomNavTap(int index) {
    if (index == _currentIndex) {
      // Refresh if tapping the current tab
      _fetchTrips();
      return;
    }

    setState(() {
      _currentIndex = index;
    });

    // Navigate based on the selected index
    switch (index) {
      case 0: // Dashboard
        Navigator.pushReplacementNamed(context, '/requestor-dashboard');
      case 1: // Request Trip
        Navigator.pushReplacementNamed(context, '/request-trip');
      case 2: // Rate Trips - already here
        break;
      case 3: // History
        Navigator.pushReplacementNamed(context, '/trip-history');
    }
  }

  // Get driver name for a trip
  String getDriverName(Trip trip) {
    // Check if we already have a cached value for this trip
    if (_driverNameCache.containsKey(trip.tripCode)) {
      return _driverNameCache[trip.tripCode]!;
    }

    // If the trip already has a driver name that's not "Unassigned", use it and cache it
    if (trip.driverName != null && trip.driverName != 'Unassigned') {
      _driverNameCache[trip.tripCode] = trip.driverName!;
      return trip.driverName!;
    }

    // If we don't have a cached value, start fetching it
    _fetchDriverName(trip);

    // Return a placeholder while we're fetching
    return AppLocalizations.of(context).loading;
  }

  // Fetch driver name from the API and update the cache
  Future<void> _fetchDriverName(Trip trip) async {
    try {
      // Fetch the driver information from the API
      final response = await ApiService.get('trips/code/${trip.tripCode}');

      String driverName = 'Unassigned';

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['driver_name'] != null && data['driver_name'] != 'Unassigned') {
          driverName = data['driver_name'];
        }
      }

      // Update the cache and trigger a rebuild
      if (mounted) {
        setState(() {
          _driverNameCache[trip.tripCode] = driverName;

          // Re-apply filters if there's a search query that might match driver names
          if (_searchQuery.isNotEmpty) {
            _filterTrips(_searchQuery);
          }
        });
      }
    } catch (e) {
      print('Error getting driver name for trip ${trip.tripCode}: $e');
      // Update the cache with the error state
      if (mounted) {
        setState(() {
          _driverNameCache[trip.tripCode] = 'Unassigned';
        });
      }
    }
  }

  // Build a mobile-friendly trip card
  Widget _buildTripCard(Trip trip) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Trip code and date row
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Trip: ${trip.tripCode}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Color(0xFF0D47A1),
                  ),
                ),
                Text(
                  '${trip.date} ${trip.time}',
                  style: TextStyle(
                    color: Colors.grey[700],
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            const Divider(),
            // From and To
            Row(
              children: [
                const Icon(Icons.location_on, color: Colors.red, size: 18),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'From: ${trip.fromDestination}',
                    style: const TextStyle(fontSize: 14),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                const Icon(Icons.location_on, color: Colors.green, size: 18),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'To: ${trip.toDestination}',
                    style: const TextStyle(fontSize: 14),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            // Driver
            Row(
              children: [
                const Icon(Icons.person, color: Colors.blue, size: 18),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Driver: ${getDriverName(trip)}',
                    style: const TextStyle(fontSize: 14),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            // Rate button
            SizedBox(
              width: double.infinity,              child: ElevatedButton.icon(
                icon: const Icon(Icons.star, color: Colors.amber),
                label: Text(AppLocalizations.of(context).rateThisTrip),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple.shade100,
                  foregroundColor: Colors.purple.shade900,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                onPressed: () async {
                  final result = await Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => RatingScreen(trip: trip),
                    ),
                  );
                  if (result == true) {
                    _fetchTrips();
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Check if we're on a small screen (mobile)
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600 && !kIsWeb;

    return Scaffold(
      key: _scaffoldKey,
      drawer: const SidebarNavigation(),      appBar: CommonAppBar(
        title: AppLocalizations.of(context).rateYourTrips,
        onRefresh: _fetchTrips,
        showMenuIcon: true,
      ),
      body: Column(
        children: [
          // Search and filter section - responsive layout
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: isSmallScreen
                ? Column(
                    children: [
                      // Search field
                      TextField(                      onChanged: _filterTrips,
                        decoration: InputDecoration(
                          labelText: AppLocalizations.of(context).search,
                          prefixIcon: const Icon(Icons.search),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),                  // Date filter
                      TextField(
                        controller: _dateController,
                        readOnly: true,
                        decoration: InputDecoration(
                          labelText: AppLocalizations.of(context).filterByDate,
                          prefixIcon: const Icon(Icons.calendar_today),
                          suffixIcon: _selectedDate != null
                              ? IconButton(
                                  icon: const Icon(Icons.clear),
                                  onPressed: _clearDateFilter,
                                )
                              : null,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        onTap: () => _selectDate(context),
                      ),
                    ],
                  )
                : Row(
                    children: [
                      Expanded(
                        flex: 2,                        child: TextField(
                          onChanged: _filterTrips,
                          decoration: InputDecoration(
                            labelText: AppLocalizations.of(context).search,
                            prefixIcon: const Icon(Icons.search),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        flex: 1,
                        child: TextField(                          controller: _dateController,
                          readOnly: true,
                          decoration: InputDecoration(
                            labelText: AppLocalizations.of(context).filterByDate,
                            prefixIcon: const Icon(Icons.calendar_today),
                            suffixIcon: _selectedDate != null
                                ? IconButton(
                                    icon: const Icon(Icons.clear),
                                    onPressed: _clearDateFilter,
                                  )
                                : null,
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                          onTap: () => _selectDate(context),
                        ),
                      ),
                    ],
                  ),
          ),
          // Trip list - different layouts for mobile and desktop
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _errorMessage.isNotEmpty
                    ? Center(child: Text(_errorMessage))
                    : _filteredTrips.isEmpty
                        ? Center(
                            child: Text(
                              _searchQuery.isEmpty && _selectedDate == null
                                  ? 'No trips waiting for rating'
                                  : 'No matching trips found',
                            ),
                          )
                        : isSmallScreen
                            // Mobile layout - vertical list of cards
                            ? RefreshIndicator(
                                onRefresh: () async {
                                  await _fetchTrips();
                                },
                                child: ListView.builder(
                                  padding: const EdgeInsets.all(8.0),
                                  itemCount: _filteredTrips.length,
                                  itemBuilder: (context, index) {
                                    return _buildTripCard(_filteredTrips[index]);
                                  },
                                ),
                              )
                            // Desktop layout - data table with horizontal and vertical scrolling
                            : Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: SingleChildScrollView(
                                  scrollDirection: Axis.horizontal,
                                  child: SingleChildScrollView(
                                    scrollDirection: Axis.vertical,
                                    child: Theme(
                                      data: Theme.of(context).copyWith(
                                        dataTableTheme: DataTableThemeData(
                                          columnSpacing: 16,
                                          horizontalMargin: 16,
                                          headingTextStyle: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color: Color(0xFF0D47A1),
                                          ),
                                        ),
                                      ),
                                      child: Card(
                                        elevation: 2,
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(10),
                                        ),                                        child: DataTable(
                                          headingRowColor: WidgetStateProperty.all(
                                            const Color(0xFF0D47A1).withAlpha(25),
                                          ),
                                          columns: [
                                            DataColumn(label: Text(AppLocalizations.of(context).tripCode)),
                                            DataColumn(label: Text(AppLocalizations.of(context).from.replaceAll(':', ''))),
                                            DataColumn(label: Text(AppLocalizations.of(context).to.replaceAll(':', ''))),
                                            DataColumn(label: Text(AppLocalizations.of(context).date)),
                                            DataColumn(label: Text(AppLocalizations.of(context).time)),
                                            DataColumn(label: Text(AppLocalizations.of(context).driver)),
                                            DataColumn(label: Text(AppLocalizations.of(context).actions)),
                                          ],
                                          rows: _filteredTrips.map((trip) {
                                            return DataRow(
                                              cells: [
                                                DataCell(Text(trip.tripCode)),
                                                DataCell(Text(trip.fromDestination)),
                                                DataCell(Text(trip.toDestination)),
                                                DataCell(Text(trip.date)),
                                                DataCell(Text(trip.time)),
                                                DataCell(Text(getDriverName(trip))),
                                                DataCell(                                                  ElevatedButton.icon(
                                                    icon: const Icon(Icons.star, color: Colors.amber),
                                                    label: Text(AppLocalizations.of(context).rateTrip),
                                                    style: ElevatedButton.styleFrom(
                                                      backgroundColor: Colors.purple.shade100,
                                                      foregroundColor: Colors.purple.shade900,
                                                    ),
                                                    onPressed: () async {
                                                      final result = await Navigator.push(
                                                        context,
                                                        MaterialPageRoute(
                                                          builder: (context) => RatingScreen(trip: trip),
                                                        ),
                                                      );
                                                      if (result == true) {
                                                        _fetchTrips();
                                                      }
                                                    },
                                                  ),
                                                ),
                                              ],
                                            );
                                          }).toList(),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
          ),
        ],
      ),
      // Only show bottom navigation on mobile
      bottomNavigationBar: isSmallScreen
          ? MobileBottomNav(
              currentIndex: _currentIndex,
              onTap: _onBottomNavTap,
            )
          : null,
    );
  }
}



