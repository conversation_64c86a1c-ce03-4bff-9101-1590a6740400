import 'dart:convert';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../generated/l10n/app_localizations.dart';
import '../models/car.dart';
import '../models/driver.dart';
import '../models/driver_schedule.dart';
import '../models/passenger.dart';
import '../models/trip.dart';
import '../models/trip_cargo.dart';
import '../services/api_service.dart';
import '../sidebar.dart';
import '../utils/auto_refresh.dart';
import '../widgets/common_app_bar.dart';
import '../widgets/mobile_bottom_nav.dart';
import 'trip_monitoring_drag.dart' as drag;

// Enum for sorting options
enum TripSortOption {
  datetime,
  tripCode,
  requestor,
  fromDestination,
  toDestination,
}

class TripMonitoringScreen extends StatefulWidget {
  const TripMonitoringScreen({super.key});
  @override
  State<TripMonitoringScreen> createState() => _TripMonitoringScreenState();
}

class _TripMonitoringScreenState extends State<TripMonitoringScreen>
    with AutoRefreshMixin {
  int _currentIndex = 1;
  List<Driver> _drivers = [];
  List<DriverSchedule> _schedules = [];
  List<Trip> _tripRequests = [];
  List<Trip> _driverRejectedTrips = [];
  List<Trip> _allRequestTrips = [];
  List<Trip> _allDriverRejectedTrips = [];
  Map<int, String> _driverAvailabilityStatus = {};
  Map<String, String> _destinationInitials = {};
  bool _isLoading = true;
  String _errorMessage = '';
  DateTime _selectedDate = DateTime.now().toLocal();
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  int _tripDurationMinutes = 30;
  int get _totalRequestCount =>
      _allRequestTrips.length + _allDriverRejectedTrips.length;
  final List<String> _timeSlots = [];
  final ScrollController _horizontalScrollController = ScrollController();
  double _timeSliderValue = 0.0;
  String _currentTimeSlot = '';  List<Car> _cars = [];
  Car? _selectedCar;
  int? _driverAssignedCarId;

  // Sort functionality state variables
  TripSortOption _sortOption = TripSortOption.datetime; // Default to datetime
  bool _sortAscending = false; // Default to newest first for datetime

  @override
  void initState() {
    super.initState();
    _generateTimeSlots();
    _fetchDestinationInitials().then((_) {
      _fetchData();
    });
    _fetchAllRequestTrips();
    _fetchAllDriverRejectedTrips();
    _setCurrentTimeSlot();
    _horizontalScrollController.addListener(_updateSliderFromScroll);
    initAutoRefresh();
  }

  Future<void> _fetchDestinationInitials() async {
    try {
      final response = await ApiService.get('destinations');
      if (response.statusCode == 200) {
        final data = ApiService.parseResponse(response);
        final destinations = data['destinations'] as List;
        final Map<String, String> initials = {};
        for (var dest in destinations) {
          final String destName = dest['destination'] ?? '';
          final String initial = dest['initial'] ?? '';
          if (destName.isNotEmpty) {
            if (initial.isNotEmpty) {
              initials[destName] = initial;
            } else {
              initials[destName] = destName.length >= 3
                  ? destName.substring(0, 3).toUpperCase()
                  : destName.padRight(3, 'X').toUpperCase();
            }
          }
        }
        if (mounted) {
          setState(() {
            _destinationInitials = initials;
          });
        }
      }
    } catch (e) {
      print('Error fetching destination initials: $e');
    }
  }

  void _setCurrentTimeSlot() {
    final now = DateTime.now().toLocal();
    final hour = now.hour;
    final minute = now.minute;
    print('_setCurrentTimeSlot called');
    print('Raw current time: ${now.toString()}');
    print('Hour: $hour, Minute: $minute');
    final roundedMinute = (minute < 30) ? 0 : 30;
    print('Rounded minute: $roundedMinute');
    final DateTime timeSlotDateTime =
        DateTime(now.year, now.month, now.day, hour, roundedMinute);
    _currentTimeSlot = DateFormat('HH:mm').format(timeSlotDateTime);
    print('Formatted time slot: $_currentTimeSlot');
    print('Time slot DateTime: ${timeSlotDateTime.toString()}');
  }

  void _updateSliderFromScroll() {
    if (_horizontalScrollController.hasClients) {
      final currentScroll = _horizontalScrollController.position.pixels;
      final maxScroll = _horizontalScrollController.position.maxScrollExtent;
      if (maxScroll > 0) {
        final newValue = currentScroll / maxScroll;
        if ((newValue - _timeSliderValue).abs() > 0.01 && mounted) {
          setState(() {
            _timeSliderValue = newValue;
          });
        }
      }
    }  }
  // Sorting functions
  List<Map<String, dynamic>> _sortTrips(List<Map<String, dynamic>> trips) {
    final List<Map<String, dynamic>> sortedTrips = List.from(trips);
    
    switch (_sortOption) {
      case TripSortOption.datetime:
        sortedTrips.sort((a, b) {
          try {            final aDate = a['date'] ?? '';
            final aTime = a['time'] ?? '';
            final bDate = b['date'] ?? '';
            final bTime = b['time'] ?? '';
            
            final aDateTime = DateTime.parse('$aDate T$aTime:00');
            final bDateTime = DateTime.parse('$bDate T$bTime:00');
            
            return _sortAscending ? aDateTime.compareTo(bDateTime) : bDateTime.compareTo(aDateTime);
          } catch (e) {            // Fallback to string comparison if parsing fails
            final aStr = '${a['date'] ?? ''} ${a['time'] ?? ''}';
            final bStr = '${b['date'] ?? ''} ${b['time'] ?? ''}';
            return _sortAscending ? aStr.compareTo(bStr) : bStr.compareTo(aStr);
          }        });
      case TripSortOption.tripCode:
        sortedTrips.sort((a, b) {
          final aCode = a['trip_code'] ?? '';
          final bCode = b['trip_code'] ?? '';
          return _sortAscending ? aCode.compareTo(bCode) : bCode.compareTo(aCode);
        });
      case TripSortOption.requestor:
        sortedTrips.sort((a, b) {
          final aRequestor = a['requestor_name'] ?? '';
          final bRequestor = b['requestor_name'] ?? '';
          return _sortAscending ? aRequestor.compareTo(bRequestor) : bRequestor.compareTo(aRequestor);
        });
      case TripSortOption.fromDestination:
        sortedTrips.sort((a, b) {
          final aFrom = a['from_destination'] ?? a['custom_from'] ?? '';
          final bFrom = b['from_destination'] ?? b['custom_from'] ?? '';
          return _sortAscending ? aFrom.compareTo(bFrom) : bFrom.compareTo(aFrom);
        });
      case TripSortOption.toDestination:
        sortedTrips.sort((a, b) {
          final aTo = a['to_destination'] ?? a['custom_to'] ?? '';
          final bTo = b['to_destination'] ?? b['custom_to'] ?? '';
          return _sortAscending ? aTo.compareTo(bTo) : bTo.compareTo(aTo);
        });
    }
    
    return sortedTrips;
  }

  String _getSortOptionDisplayName(TripSortOption option) {
    switch (option) {
      case TripSortOption.datetime:
        return 'Date & Time';
      case TripSortOption.tripCode:
        return 'Trip Code';
      case TripSortOption.requestor:
        return 'Requestor';
      case TripSortOption.fromDestination:
        return 'From';
      case TripSortOption.toDestination:
        return 'To';
    }
  }

  Future<void> _fetchAllRequestTrips() async {
    try {
      final response = await ApiService.get('trips?status=REQUEST');
      if (response.statusCode == 200) {
        final data = ApiService.parseResponse(response);
        if (mounted) {
          final allTrips =
              List<Trip>.from(data['trips'].map((trip) => Trip.fromJson(trip)));
          final filteredTrips =
              allTrips.where((trip) => trip.status != 'DELETED').toList();
          setState(() {
            _allRequestTrips = filteredTrips;
          });
        }
      }
    } catch (e) {
      print('Error fetching all REQUEST trips: $e');
    }
  }

  Future<void> _fetchAllDriverRejectedTrips() async {
    try {
      final response = await ApiService.get('trips?status=DRIVER REJECTED');
      if (response.statusCode == 200) {
        final data = ApiService.parseResponse(response);
        if (mounted) {
          final allTrips =
              List<Trip>.from(data['trips'].map((trip) => Trip.fromJson(trip)));
          final filteredTrips =
              allTrips.where((trip) => trip.status != 'DELETED').toList();
          setState(() {
            _allDriverRejectedTrips = filteredTrips;
          });
        }
      }
    } catch (e) {
      print('Error fetching all DRIVER REJECTED trips: $e');
    }
  }

  Future<void> _refreshAllData({bool showSnackbar = true}) async {
    setState(() {
      _isLoading = true;
    });
    try {
      await _fetchDestinationInitials();
      await _fetchDrivers();
      await _fetchDriverAvailability();
      await _fetchSchedules();
      await _fetchTripRequests();
      await _fetchDriverRejectedTrips();
      await _fetchAllRequestTrips();
      await _fetchAllDriverRejectedTrips();
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        if (showSnackbar) {
          showRefreshSnackbar(
              AppLocalizations.of(context).dataRefreshedSuccessfully);
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage =
              '${AppLocalizations.of(context).errorRefreshingData}: $e';
          _isLoading = false;
        });
        if (showSnackbar) {
          showRefreshSnackbar(
              '${AppLocalizations.of(context).errorRefreshingData}: $e',
              isError: true);
        }
      }
    }
  }

  String _formatTimeString(String time) {
    if (time.isEmpty) return '';
    try {
      if (time.length <= 5) return time;
      if (time.length <= 8) return time.substring(0, 5);
      final DateTime timeObj = DateFormat('HH:mm:ss').parse(time);
      return DateFormat('HH:mm').format(timeObj);
    } catch (e) {
      return time;
    }
  }

  void _scrollToCurrentTime() {
    if (!mounted) return;
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final now = DateTime.now().toLocal();
    print('NOW BUTTON PRESSED');
    print('Current DateTime: ${now.toString()}');
    print(
        'Current Date: ${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}');
    print(
        'Current Time: ${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}');
    print('Current TimeZone offset: ${now.timeZoneOffset}');
    print('Selected Date: ${_selectedDate.toString()}');
    scaffoldMessenger.showSnackBar(
      SnackBar(
        content:
            Text(AppLocalizations.of(context).currentDateTime(now.toString())),
        duration: const Duration(seconds: 5),
      ),
    );
    final bool dateChanged = _selectedDate.year != now.year ||
        _selectedDate.month != now.month ||
        _selectedDate.day != now.day;
    if (dateChanged) {
      print('Date changed, updating to current date');
      if (!mounted) return;
      setState(() {
        _selectedDate = DateTime(now.year, now.month, now.day);
      });
      _fetchData();
      return;
    }
    if (!_horizontalScrollController.hasClients) {
      print('Cannot scroll: ScrollController has no clients');
      return;
    }
    final int currentHour = now.hour;
    final int currentMinute = now.minute;
    final int totalMinutesSinceMidnight = (currentHour * 60) + currentMinute;
    final double proportionOfDay = totalMinutesSinceMidnight / 1440.0;
    final double maxScroll =
        _horizontalScrollController.position.maxScrollExtent;
    final double targetPosition = proportionOfDay * maxScroll;
    print('Current hour: $currentHour, Current minute: $currentMinute');
    print('Total minutes since midnight: $totalMinutesSinceMidnight');
    print('Proportion of day: $proportionOfDay');
    print('Maximum scroll extent: $maxScroll');
    print('Target scroll position: $targetPosition');
    _horizontalScrollController.animateTo(
      targetPosition,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
    if (!mounted) return;
    setState(() {
      _timeSliderValue = proportionOfDay;
    });
    _setCurrentTimeSlot();
  }

  @override
  void dispose() {
    _horizontalScrollController.dispose();
    disposeAutoRefresh();
    super.dispose();
  }

  @override
  Future<void> refreshData({bool showSnackbar = true}) async {
    if (!mounted) return;
    await _refreshAllData(showSnackbar: showSnackbar);
    if (!mounted) return;
  }

  void _generateTimeSlots() {
    print('_generateTimeSlots called');
    final now = DateTime.now().toLocal();
    print('Current DateTime: ${now.toString()}');
    DateTime startTime = DateTime(now.year, now.month, now.day, 0, 0);
    final DateTime endTime = DateTime(now.year, now.month, now.day, 0, 0)
        .add(const Duration(days: 1));
    print('Start time: ${startTime.toString()}');
    print('End time: ${endTime.toString()}');
    _timeSlots.clear();
    while (startTime.isBefore(endTime)) {
      final String timeSlot = DateFormat('HH:mm').format(startTime);
      _timeSlots.add(timeSlot);
      print('Added time slot: $timeSlot');
      startTime = startTime.add(const Duration(minutes: 30));
    }
    print('Generated ${_timeSlots.length} time slots');
    print('First time slot: ${_timeSlots.first}');
    print('Last time slot: ${_timeSlots.last}');
  }

  Future<void> _fetchDriverAvailability() async {
    try {
      final String formattedDate =
          DateFormat('yyyy-MM-dd').format(_selectedDate);
      final response = await ApiService.get(
          'driver-availabilities?start_date=$formattedDate&end_date=$formattedDate');
      if (response.statusCode == 200) {
        final data = ApiService.parseResponse(response);
        final availabilities = data['availabilities'] as List;
        final Map<int, String> driverStatus = {};
        for (var avail in availabilities) {
          final int driverId = avail['driver_id'] is String
              ? int.tryParse(avail['driver_id']) ?? 0
              : avail['driver_id'] ?? 0;
          final String status = avail['status'] ?? 'AVAILABLE';
          if (driverId > 0) {
            driverStatus[driverId] = status;
          }
        }
        if (mounted) {
          setState(() {
            _driverAvailabilityStatus = driverStatus;
          });
        }
        print(
            'Fetched availability for ${driverStatus.length} drivers on $formattedDate');
      } else {
        print('Failed to fetch driver availability: ${response.statusCode}');
      }
    } catch (e) {
      print('Error fetching driver availability: $e');
    }
  }

  Future<void> _fetchData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });
    try {
      if (_destinationInitials.isEmpty) {
        await _fetchDestinationInitials();
      }
      await _fetchDrivers();
      await _fetchDriverAvailability();
      await _fetchSchedules();
      await _fetchTripRequests();
      await _fetchDriverRejectedTrips();
      await _fetchAllRequestTrips();
      await _fetchAllDriverRejectedTrips();
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        WidgetsBinding.instance.addPostFrameCallback((_) {
          final now = DateTime.now().toLocal();
          final bool isToday = _selectedDate.year == now.year &&
              _selectedDate.month == now.month &&
              _selectedDate.day == now.day;
          if (isToday) {
            _scrollToCurrentTime();
          } else {
            _scrollToSpecificTime(8, 30);
          }
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = '${AppLocalizations.of(context).error}: $e';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _fetchDrivers() async {
    try {
      final response = await ApiService.get('drivers');
      if (response.statusCode == 200) {
        final data = ApiService.parseResponse(response);
        if (mounted) {
          setState(() {
            _drivers = List<Driver>.from(
                data['drivers'].map((driver) => Driver.fromJson(driver)));
            _drivers.add(Driver(
              driverId: -1,
              driverCode: 'ONLINE',
              name: 'ONLINE TAXI',
              initial: 'O',
              carCode: '',
            ));
          });
        }
      } else {
        throw Exception('Failed to load drivers');
      }
    } catch (e) {
      rethrow;
    }
  }

  Future<void> _fetchSchedules() async {
    try {
      final String apiDateFormat =
          DateFormat('yyyy-MM-dd').format(_selectedDate);
      List<DriverSchedule> schedules = [];
      print('Fetching trips for date: $apiDateFormat');
      final assignedTripsResponse = await ApiService.get(
          'multi-status-trips?date=$apiDateFormat&statuses=ASSIGN TO DRIVER,COMPLETED,TRIP IN PROGRESS,WAITING FOR RATING,ASSIGN TO ONLINE TAXI,BACK TO BASE');
      print('Response status code: ${assignedTripsResponse.statusCode}');
      if (assignedTripsResponse.statusCode == 200) {
        final data = ApiService.parseResponse(assignedTripsResponse);
        print('Received ${data['trips']?.length ?? 0} trips from API');
        if (data['trips'] != null) {
          for (var trip in data['trips']) {
            print(
                'Trip: ${trip['trip_code']}, Date: ${trip['date']}, Status: ${trip['status']}, Total Duration: ${trip['total_duration']}');
          }
        }
        if (data['trips'] == null || (data['trips'] as List).isEmpty) {
          print('No trips found for date: $apiDateFormat');
          if (mounted) {
            setState(() {
              _schedules = [];
              _isLoading = false;
            });
          }
          return;
        }
        final assignedTrips = data['trips'].where((trip) {
          final String status = trip['status']?.toString().toUpperCase() ?? '';
          return status == 'ASSIGN TO DRIVER' ||
              status == 'COMPLETED' ||
              status == 'TRIP IN PROGRESS' ||
              status == 'WAITING FOR RATING';
        }).toList();
        if (assignedTrips.isEmpty) {
          if (mounted) {
            setState(() {
              _isLoading = false;
            });
          }
        }
        for (var trip in data['trips']) {
          String tripDate = trip['date'] ?? '';
          final String formattedSelectedDate =
              DateFormat('yyyy-MM-dd').format(_selectedDate);
          try {
            if (tripDate.isNotEmpty) {
              DateTime parsedTripDate;
              if (tripDate.contains('T')) {
                parsedTripDate = DateTime.parse(tripDate);
              } else if (tripDate.contains('-')) {
                parsedTripDate = DateFormat('yyyy-MM-dd').parse(tripDate);
              } else if (tripDate.contains('/')) {
                parsedTripDate = DateFormat('MM/dd/yyyy').parse(tripDate);
              } else {
                throw Exception('Unknown date format');
              }
              tripDate = DateFormat('yyyy-MM-dd').format(parsedTripDate);
            }
          } catch (e) {}
          bool isExactMatch = (tripDate == formattedSelectedDate);
          bool isPreviousDate = false;
          print('Checking trip date match for trip ${trip['trip_code']}:');
          print('  Trip date: $tripDate');
          print('  Selected date: $formattedSelectedDate');
          print('  Direct string comparison match: $isExactMatch');
          try {
            final DateTime tripDateTime = DateTime.parse(tripDate);
            final DateTime selectedDateTime =
                DateTime.parse(formattedSelectedDate);
            print('  Parsed trip date: ${tripDateTime.toIso8601String()}');
            print(
                '  Parsed selected date: ${selectedDateTime.toIso8601String()}');
            final bool dateTimeMatch =
                tripDateTime.year == selectedDateTime.year &&
                    tripDateTime.month == selectedDateTime.month &&
                    tripDateTime.day == selectedDateTime.day;
            print('  DateTime component match: $dateTimeMatch');
            if (dateTimeMatch) {
              isExactMatch = true;
            }
            if (!isExactMatch) {
              final String formattedTripDate =
                  DateFormat('yyyy-MM-dd').format(tripDateTime);
              print('  Formatted trip date: $formattedTripDate');
              print('  Formatted selected date: $formattedSelectedDate');
              if (formattedTripDate == formattedSelectedDate) {
                isExactMatch = true;
                print('  Formatted date match: true');
              }
            }
            if (!isExactMatch) {
              isPreviousDate = tripDateTime.isBefore(selectedDateTime);
              print('  Trip date is before selected date: $isPreviousDate');
              if (!isPreviousDate) {
                final String formattedTripDate =
                    DateFormat('yyyy-MM-dd').format(tripDateTime);
                final String formattedSelectedDate =
                    DateFormat('yyyy-MM-dd').format(selectedDateTime);
                print('  Formatted trip date: $formattedTripDate');
                print('  Formatted selected date: $formattedSelectedDate');
                isPreviousDate =
                    formattedTripDate.compareTo(formattedSelectedDate) < 0;
                print(
                    '  Formatted date comparison shows previous date: $isPreviousDate');
              }
            }
            print('  Final isExactMatch: $isExactMatch');
            print('  Final isPreviousDate: $isPreviousDate');
          } catch (e) {
            print('  Error parsing dates: $e');
          }
          bool shouldIncludeTrip = isExactMatch;
          print(
              'Initial shouldIncludeTrip (based on exact match): $shouldIncludeTrip');
          if (!shouldIncludeTrip && isPreviousDate) {
            print('Checking if previous date trip should be included:');
            if (trip['total_duration'] != null &&
                trip['total_duration'].toString().isNotEmpty) {
              print('  Trip has total_duration: ${trip['total_duration']}');
              try {
                final DateTime totalDuration =
                    DateTime.parse(trip['total_duration']);
                print(
                    '  Parsed total_duration: ${totalDuration.toIso8601String()}');
                final DateTime selectedDateTime =
                    DateTime.parse(formattedSelectedDate);
                print(
                    '  Parsed selected date: ${selectedDateTime.toIso8601String()}');
                final DateTime totalDurationDate = DateTime(
                    totalDuration.year, totalDuration.month, totalDuration.day);
                print(
                    '  Total duration date only: ${totalDurationDate.toIso8601String()}');
                final DateTime selectedDate = DateTime(selectedDateTime.year,
                    selectedDateTime.month, selectedDateTime.day);
                print(
                    '  Selected date only: ${selectedDate.toIso8601String()}');
                final DateTime tripDate = DateTime.parse(trip['date']);
                final DateTime tripDateOnly =
                    DateTime(tripDate.year, tripDate.month, tripDate.day);
                print('  Trip date only: ${tripDateOnly.toIso8601String()}');
                shouldIncludeTrip =
                    (selectedDate.isAtSameMomentAs(tripDateOnly) ||
                            selectedDate.isAfter(tripDateOnly)) &&
                        (selectedDate.isAtSameMomentAs(totalDurationDate) ||
                            selectedDate.isBefore(totalDurationDate));
                print('Cross-date trip check for ${trip['trip_code']}:');
                print('  Trip start date: ${tripDateOnly.toIso8601String()}');
                print(
                    '  Trip end date: ${totalDurationDate.toIso8601String()}');
                print('  Selected date: ${selectedDate.toIso8601String()}');
                print(
                    '  Is on or after start date: ${selectedDate.isAtSameMomentAs(tripDateOnly) || selectedDate.isAfter(tripDateOnly)}');
                print(
                    '  Is on or before end date: ${selectedDate.isAtSameMomentAs(totalDurationDate) || selectedDate.isBefore(totalDurationDate)}');
                print('  Should include trip: $shouldIncludeTrip');
                if (shouldIncludeTrip) {
                  print(
                      'Including cross-date trip: ${trip['trip_code']} from ${trip['date']} to ${trip['total_duration']} on $formattedSelectedDate');
                }
              } catch (e) {
                print(
                    'Error parsing total_duration for trip ${trip['trip_code']}: $e');
              }
            }
          }
          if (!shouldIncludeTrip) {
            continue;
          }
          final String? timeFromApi = trip['time'];
          if (timeFromApi == null || timeFromApi.isEmpty) {
            continue;
          }
          DateTime startTime;
          DateTime? originalStartTime;
          final bool isCrossDateTrip = isPreviousDate && shouldIncludeTrip;
          try {
            String normalizedTime = timeFromApi;
            if (timeFromApi.length <= 5 && timeFromApi.contains(':')) {
            } else if (timeFromApi.length <= 8 && timeFromApi.contains(':')) {
              normalizedTime = timeFromApi.substring(0, 5);
            }
            originalStartTime =
                DateTime.parse('${tripDate}T$normalizedTime:00');
            if (isCrossDateTrip) {
              print(
                  'Cross-date trip ${trip['trip_code']} on subsequent date: Setting start time to 00:00');
              print(
                  'Original start time: ${originalStartTime.toIso8601String()}');
              startTime = DateTime(_selectedDate.year, _selectedDate.month,
                  _selectedDate.day, 0, 0);
            } else {
              startTime = originalStartTime;
            }
          } catch (e) {
            print('Error parsing start time for trip ${trip['trip_code']}: $e');
            continue;
          }
          DateTime endTime;
          if (isCrossDateTrip) {
            if (trip['total_duration'] != null &&
                trip['total_duration'].toString().isNotEmpty) {
              try {
                final DateTime totalDuration =
                    DateTime.parse(trip['total_duration']);
                if (totalDuration.year == _selectedDate.year &&
                    totalDuration.month == _selectedDate.month &&
                    totalDuration.day == _selectedDate.day) {
                  endTime = totalDuration;
                } else {
                  endTime = DateTime(_selectedDate.year, _selectedDate.month,
                      _selectedDate.day, 23, 59);
                }
              } catch (e) {
                endTime = DateTime(_selectedDate.year, _selectedDate.month,
                    _selectedDate.day, 23, 59);
              }
            } else {
              endTime = DateTime(_selectedDate.year, _selectedDate.month,
                  _selectedDate.day, 23, 59);
            }
            print(
                'Cross-date trip ${trip['trip_code']} on subsequent date: Setting end time to ${endTime.hour}:${endTime.minute}');
          } else {
            endTime = startTime.add(const Duration(minutes: 30));
          }
          int driverId = 0;
          if (trip['driver_id'] != null) {
            if (trip['driver_id'] is String) {
              driverId = int.tryParse(trip['driver_id']) ?? 0;
            } else if (trip['driver_id'] is int) {
              driverId = trip['driver_id'];
            }
          }
          String driverCode = trip['driver_code'] ?? 'UNASSIGNED';
          String driverName = trip['driver_name'] ?? 'Unassigned';
          bool driverFound = false;
          if (driverId > 0) {
            for (var driver in _drivers) {
              if (driver.driverId == driverId) {
                driverName = driver.name;
                driverCode = driver.driverCode;
                driverFound = true;
                break;
              }
            }
          } else if (driverCode != 'UNASSIGNED' && driverCode.isNotEmpty) {
            for (var driver in _drivers) {
              if (driver.driverCode == driverCode) {
                driverName = driver.name;
                driverId = driver.driverId;
                driverFound = true;
                break;
              }
            }
          }
          if (driverId > 0 ||
              (driverCode != 'UNASSIGNED' && driverCode.isNotEmpty)) {
            try {
              int? tripId;
              if (trip['trip_id'] != null) {
                if (trip['trip_id'] is String) {
                  tripId = int.tryParse(trip['trip_id']);
                } else if (trip['trip_id'] is int) {
                  tripId = trip['trip_id'];
                }
              }
              DateTime? totalDuration;
              if (trip['total_duration'] != null &&
                  trip['total_duration'].toString().isNotEmpty) {
                try {
                  totalDuration = DateTime.parse(trip['total_duration']);
                  if (!isCrossDateTrip) {
                    endTime = totalDuration;
                  }
                } catch (e) {
                  print(
                      'Error parsing total_duration for trip ${trip['trip_code']}: $e');
                }
              }
              final String fromDest = trip['from_destination'] ?? '';
              final String toDest = trip['to_destination'] ?? '';
              String? fromInitial = trip['custom_from'] != null
                  ? 'OTH'
                  : _destinationInitials[fromDest];
              String? toInitial = trip['custom_to'] != null
                  ? 'OTH'
                  : _destinationInitials[toDest];
              fromInitial ??= 'OTH';
              toInitial ??= 'OTH';
              int? carId;
              if (trip['car_id'] != null) {
                if (trip['car_id'] is String) {
                  carId = int.tryParse(trip['car_id']);
                } else if (trip['car_id'] is int) {
                  carId = trip['car_id'];
                }
              }
              String? carCode = trip['car_code'];
              if ((carCode == null || carCode.isEmpty) && driverId > 0) {
                for (var driver in _drivers) {
                  if (driver.driverId == driverId) {
                    carCode = driver.carCode;
                    break;
                  }
                }
              }
              final schedule = DriverSchedule(
                driverId: driverId,
                driverCode: driverCode,
                driverName: driverName,
                tripId: tripId,
                tripCode: trip['trip_code'],
                startTime: startTime,
                endTime: endTime,
                fromDestination: fromDest,
                toDestination: toDest,
                fromDestinationInitial: fromInitial,
                toDestinationInitial: toInitial,
                status: trip['status'],
                totalDuration: totalDuration,
                originalStartTime: isCrossDateTrip ? originalStartTime : null,
                carId: carId,
                carCode: carCode,
                carPlateNumber: trip['plate_number'],
                isWaiting: trip['is_waiting'] ?? false,
              );
              schedules.add(schedule);
            } catch (e) {}
          } else {
            final driverInfo = await _lookupDriverByTripCode(trip['trip_code']);
            if (driverInfo != null) {
              final foundDriverId = driverInfo['driver_id'] as int?;
              final foundDriverCode = driverInfo['driver_code'] as String?;
              if (foundDriverId != null && foundDriverId > 0) {
                for (var driver in _drivers) {
                  if (driver.driverId == foundDriverId) {
                    driverName = driver.name;
                    driverId = driver.driverId;
                    driverCode = driver.driverCode;
                    driverFound = true;
                    break;
                  }
                }
              } else if (foundDriverCode != null &&
                  foundDriverCode.isNotEmpty) {
                for (var driver in _drivers) {
                  if (driver.driverCode == foundDriverCode) {
                    driverName = driver.name;
                    driverId = driver.driverId;
                    driverCode = foundDriverCode;
                    driverFound = true;
                    break;
                  }
                }
              }
              if (!driverFound &&
                  (foundDriverId != null ||
                      (foundDriverCode != null &&
                          foundDriverCode.isNotEmpty))) {
                if (foundDriverId != null && foundDriverId > 0) {
                  driverId = foundDriverId;
                }
                if (foundDriverCode != null && foundDriverCode.isNotEmpty) {
                  driverCode = foundDriverCode;
                }
              }
              DateTime? totalDuration;
              if (trip['total_duration'] != null &&
                  trip['total_duration'].toString().isNotEmpty) {
                try {
                  totalDuration = DateTime.parse(trip['total_duration']);
                  if (!isCrossDateTrip) {
                    endTime = totalDuration;
                  }
                } catch (e) {
                  print(
                      'Error parsing total_duration for trip ${trip['trip_code']}: $e');
                }
              }
              final String fromDest = trip['from_destination'] ?? '';
              final String toDest = trip['to_destination'] ?? '';
              String? fromInitial = trip['custom_from'] != null
                  ? 'OTH'
                  : _destinationInitials[fromDest];
              String? toInitial = trip['custom_to'] != null
                  ? 'OTH'
                  : _destinationInitials[toDest];
              fromInitial ??= 'OTH';
              toInitial ??= 'OTH';
              int? carId;
              if (trip['car_id'] != null) {
                if (trip['car_id'] is String) {
                  carId = int.tryParse(trip['car_id']);
                } else if (trip['car_id'] is int) {
                  carId = trip['car_id'];
                }
              }
              schedules.add(DriverSchedule(
                driverId: driverId,
                driverCode: driverCode,
                driverName: driverName,
                tripCode: trip['trip_code'],
                startTime: startTime,
                endTime: endTime,
                fromDestination: fromDest,
                toDestination: toDest,
                fromDestinationInitial: fromInitial,
                toDestinationInitial: toInitial,
                status: trip['status'],
                totalDuration: totalDuration,
                originalStartTime: isCrossDateTrip ? originalStartTime : null,
                carId: carId,
                carCode: trip['car_code'],
                carPlateNumber: trip['plate_number'],
                isWaiting: trip['is_waiting'] ?? false,
              ));
            } else {
              DateTime? totalDuration;
              if (trip['total_duration'] != null &&
                  trip['total_duration'].toString().isNotEmpty) {
                try {
                  totalDuration = DateTime.parse(trip['total_duration']);
                  if (!isCrossDateTrip) {
                    endTime = totalDuration;
                  }
                } catch (e) {
                  print(
                      'Error parsing total_duration for trip ${trip['trip_code']}: $e');
                }
              }
              final String fromDest = trip['from_destination'] ?? '';
              final String toDest = trip['to_destination'] ?? '';
              String? fromInitial = trip['custom_from'] != null
                  ? 'OTH'
                  : _destinationInitials[fromDest];
              String? toInitial = trip['custom_to'] != null
                  ? 'OTH'
                  : _destinationInitials[toDest];
              fromInitial ??= 'OTH';
              toInitial ??= 'OTH';
              int? carId;
              if (trip['car_id'] != null) {
                if (trip['car_id'] is String) {
                  carId = int.tryParse(trip['car_id']);
                } else if (trip['car_id'] is int) {
                  carId = trip['car_id'];
                }
              }
              schedules.add(DriverSchedule(
                driverId: 0,
                driverCode: 'UNASSIGNED',
                driverName: 'Unassigned',
                tripCode: trip['trip_code'],
                startTime: startTime,
                endTime: endTime,
                fromDestination: fromDest,
                toDestination: toDest,
                fromDestinationInitial: fromInitial,
                toDestinationInitial: toInitial,
                status: trip['status'],
                totalDuration: totalDuration,
                originalStartTime: isCrossDateTrip ? originalStartTime : null,
                carId: carId,
                carCode: trip['car_code'],
                carPlateNumber: trip['plate_number'],
                isWaiting: trip['is_waiting'] ?? false,
              ));
            }
          }
        }
      } else {}
      print('Before deduplication: ${schedules.length} schedules');
      if (schedules.isEmpty) {
        print('No schedules after deduplication');
      } else {
        final Map<String, List<DriverSchedule>> schedulesByDriver = {};
        for (var schedule in schedules) {
          final String driverKey =
              '${schedule.driverName} (ID=${schedule.driverId}, Code=${schedule.driverCode})';
          if (!schedulesByDriver.containsKey(driverKey)) {
            schedulesByDriver[driverKey] = [];
          }
          schedulesByDriver[driverKey]!.add(schedule);
        }
        schedulesByDriver.forEach((driverKey, driverSchedules) {
          print('Driver: $driverKey has ${driverSchedules.length} schedules');
          driverSchedules.sort((a, b) => a.startTime.compareTo(b.startTime));
          for (var schedule in driverSchedules) {
            print(
                '  Trip: ${schedule.tripCode}, Start: ${schedule.startTime.toIso8601String()}, End: ${schedule.endTime.toIso8601String()}');
          }
        });
      }
      try {
        final onlineTaxiResponse =
            await ApiService.get('trips?status=ASSIGN TO ONLINE TAXI');
        if (onlineTaxiResponse.statusCode == 200) {
          final onlineData = ApiService.parseResponse(onlineTaxiResponse);
          final allOnlineTrips = List<Trip>.from(
              onlineData['trips'].map((trip) => Trip.fromJson(trip)));
          final onlineTaxiTrips = allOnlineTrips.where((trip) {
            final bool statusMatch = trip.status == 'ASSIGN TO ONLINE TAXI' &&
                trip.status != 'DELETED';
            final bool dateMatch =
                _areSameDate(trip.originalDate, apiDateFormat);
            bool isCrossDateTrip = false;
            if (!dateMatch && trip.totalDuration != null) {
              try {
                final DateTime tripDate = DateTime.parse(trip.originalDate);
                final DateTime selectedDate = DateTime.parse(apiDateFormat);
                if (tripDate.isBefore(selectedDate)) {
                  final DateTime totalDuration =
                      DateTime.parse(trip.totalDuration!);
                  final DateTime totalDurationDate = DateTime(
                      totalDuration.year,
                      totalDuration.month,
                      totalDuration.day);
                  final DateTime selectedDateOnly = DateTime(
                      selectedDate.year, selectedDate.month, selectedDate.day);
                  final DateTime tripDateOnly =
                      DateTime(tripDate.year, tripDate.month, tripDate.day);
                  isCrossDateTrip = (selectedDateOnly
                              .isAtSameMomentAs(tripDateOnly) ||
                          selectedDateOnly.isAfter(tripDateOnly)) &&
                      (selectedDateOnly.isAtSameMomentAs(totalDurationDate) ||
                          selectedDateOnly.isBefore(totalDurationDate));
                  print(
                      'Cross-date ONLINE TAXI trip check for ${trip.tripCode}:');
                  print('  Trip start date: ${tripDateOnly.toIso8601String()}');
                  print(
                      '  Trip end date: ${totalDurationDate.toIso8601String()}');
                  print(
                      '  Selected date: ${selectedDateOnly.toIso8601String()}');
                  print(
                      '  Is on or after start date: ${selectedDateOnly.isAtSameMomentAs(tripDateOnly) || selectedDateOnly.isAfter(tripDateOnly)}');
                  print(
                      '  Is on or before end date: ${selectedDateOnly.isAtSameMomentAs(totalDurationDate) || selectedDateOnly.isBefore(totalDurationDate)}');
                  print('  Is cross-date trip: $isCrossDateTrip');
                  if (isCrossDateTrip) {
                    print(
                        'Including cross-date ONLINE TAXI trip: ${trip.tripCode} from ${trip.originalDate} with total_duration ${trip.totalDuration}');
                  }
                }
              } catch (e) {
                print(
                    'Error checking for cross-date ONLINE TAXI trip ${trip.tripCode}: $e');
              }
            }
            return statusMatch && (dateMatch || isCrossDateTrip);
          }).toList();
          for (var trip in onlineTaxiTrips) {
            try {
              final String timeStr = trip.time;
              final DateTime startTime;
              DateTime? originalStartTime;
              bool isCrossDateTrip = false;
              try {
                if (trip.totalDuration != null) {
                  try {
                    final DateTime tripDate = DateTime.parse(trip.originalDate);
                    final DateTime selectedDate = _selectedDate;
                    if (tripDate.year != selectedDate.year ||
                        tripDate.month != selectedDate.month ||
                        tripDate.day != selectedDate.day) {
                      final DateTime totalDuration =
                          DateTime.parse(trip.totalDuration!);
                      final DateTime totalDurationDate = DateTime(
                          totalDuration.year,
                          totalDuration.month,
                          totalDuration.day);
                      final DateTime selectedDateOnly = DateTime(
                          selectedDate.year,
                          selectedDate.month,
                          selectedDate.day);
                      final DateTime tripDateOnly =
                          DateTime(tripDate.year, tripDate.month, tripDate.day);
                      isCrossDateTrip =
                          (selectedDateOnly.isAtSameMomentAs(tripDateOnly) ||
                                  selectedDateOnly.isAfter(tripDateOnly)) &&
                              (selectedDateOnly
                                      .isAtSameMomentAs(totalDurationDate) ||
                                  selectedDateOnly.isBefore(totalDurationDate));
                      print(
                          'Cross-date ONLINE TAXI trip check for ${trip.tripCode}:');
                      print(
                          '  Trip start date: ${tripDateOnly.toIso8601String()}');
                      print(
                          '  Trip end date: ${totalDurationDate.toIso8601String()}');
                      print(
                          '  Selected date: ${selectedDateOnly.toIso8601String()}');
                      print(
                          '  Is on or after start date: ${selectedDateOnly.isAtSameMomentAs(tripDateOnly) || selectedDateOnly.isAfter(tripDateOnly)}');
                      print(
                          '  Is on or before end date: ${selectedDateOnly.isAtSameMomentAs(totalDurationDate) || selectedDateOnly.isBefore(totalDurationDate)}');
                      print('  Is cross-date trip: $isCrossDateTrip');
                    }
                  } catch (e) {
                    print(
                        'Error checking for cross-date ONLINE TAXI trip ${trip.tripCode}: $e');
                  }
                }
                if (timeStr.length <= 5 && timeStr.contains(':')) {
                  final List<String> timeParts = timeStr.split(':');
                  final int hour = int.parse(timeParts[0]);
                  final int minute = int.parse(timeParts[1]);
                  originalStartTime = DateTime(
                      DateTime.parse(trip.originalDate).year,
                      DateTime.parse(trip.originalDate).month,
                      DateTime.parse(trip.originalDate).day,
                      hour,
                      minute);
                } else {
                  originalStartTime =
                      DateTime.parse('${trip.originalDate}T$timeStr:00');
                }
                if (isCrossDateTrip) {
                  print(
                      'Cross-date ONLINE TAXI trip ${trip.tripCode} on subsequent date: Setting start time to 00:00');
                  print(
                      'Original start time: ${originalStartTime.toIso8601String()}');
                  startTime = DateTime(_selectedDate.year, _selectedDate.month,
                      _selectedDate.day, 0, 0);
                } else {
                  startTime = originalStartTime;
                }
              } catch (e) {
                print(
                    'Error parsing start time for ONLINE TAXI trip ${trip.tripCode}: $e');
                continue;
              }
              DateTime endTime;
              if (trip.totalDuration != null) {
                try {
                  final DateTime tripDate = DateTime.parse(trip.originalDate);
                  final DateTime selectedDate = _selectedDate;
                  if (tripDate.year != selectedDate.year ||
                      tripDate.month != selectedDate.month ||
                      tripDate.day != selectedDate.day) {
                    isCrossDateTrip = true;
                  }
                } catch (e) {
                  print(
                      'Error checking for cross-date ONLINE TAXI trip ${trip.tripCode}: $e');
                }
              }
              if (isCrossDateTrip) {
                if (trip.totalDuration != null) {
                  try {
                    final DateTime totalDuration =
                        DateTime.parse(trip.totalDuration!);
                    if (totalDuration.year == _selectedDate.year &&
                        totalDuration.month == _selectedDate.month &&
                        totalDuration.day == _selectedDate.day) {
                      endTime = totalDuration;
                    } else {
                      endTime = DateTime(_selectedDate.year,
                          _selectedDate.month, _selectedDate.day, 23, 59);
                    }
                  } catch (e) {
                    endTime = DateTime(_selectedDate.year, _selectedDate.month,
                        _selectedDate.day, 23, 59);
                  }
                } else {
                  endTime = DateTime(_selectedDate.year, _selectedDate.month,
                      _selectedDate.day, 23, 59);
                }
                print(
                    'Cross-date ONLINE TAXI trip ${trip.tripCode} on subsequent date: Setting end time to ${endTime.hour}:${endTime.minute}');
              } else {
                endTime = startTime.add(const Duration(minutes: 60));
              }
              final String fromDest = trip.fromDestination;
              final String toDest = trip.toDestination;
              String? fromInitial = _destinationInitials[fromDest];
              String? toInitial = _destinationInitials[toDest];
              fromInitial ??= 'OTH';
              toInitial ??= 'OTH';
              final schedule = DriverSchedule(
                driverId: -1,
                driverCode: 'ONLINE',
                driverName: 'ONLINE TAXI',
                tripId: trip.tripId,
                tripCode: trip.tripCode,
                startTime: startTime,
                endTime: endTime,
                fromDestination: fromDest,
                toDestination: toDest,
                fromDestinationInitial: fromInitial,
                toDestinationInitial: toInitial,
                status: trip.status,
                totalDuration: trip.totalDuration != null
                    ? DateTime.parse(trip.totalDuration!)
                    : endTime,
                originalStartTime: isCrossDateTrip ? originalStartTime : null,
                carId: trip.carId,
                carCode: trip.carCode,
                carPlateNumber: trip.plateNumber,
                isWaiting: trip.isWaiting ?? false,
              );
              if (!schedules.any((s) => s.tripCode == trip.tripCode)) {
                schedules.add(schedule);
              }
            } catch (e) {}
          }
        }
      } catch (onlineError) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.of(context)
                  .errorFetchingOnlineTaxiTrips(onlineError.toString())),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
      final Map<String, DriverSchedule> uniqueSchedules = {};
      for (var schedule in schedules) {
        uniqueSchedules[schedule.tripCode] = schedule;
      }
      schedules = uniqueSchedules.values.toList();
      print(
          'After deduplication before setState: ${schedules.length} schedules');
      if (mounted) {
        setState(() {
          _schedules = schedules;
        });
      }
    } catch (e) {
      rethrow;
    }
  }

  void _onDateChanged(DateTime date) {
    if (!mounted) return;
    setState(() {
      _selectedDate = date;
      _timeSliderValue = 0.354;
    });
    _fetchData();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;
      _scrollToSpecificTime(8, 30);
    });
  }

  void _scrollToSpecificTime(int hour, int minute) {
    if (!mounted) return;
    if (!_horizontalScrollController.hasClients) {
      print('Cannot scroll: ScrollController has no clients');
      return;
    }
    final int totalMinutesSinceMidnight = (hour * 60) + minute;
    final double proportionOfDay = totalMinutesSinceMidnight / 1440.0;
    final double maxScroll =
        _horizontalScrollController.position.maxScrollExtent;
    final double targetPosition = proportionOfDay * maxScroll;
    print('Scrolling to $hour:$minute');
    print('Total minutes since midnight: $totalMinutesSinceMidnight');
    print('Proportion of day: $proportionOfDay');
    print('Maximum scroll extent: $maxScroll');
    print('Target scroll position: $targetPosition');
    _horizontalScrollController.animateTo(
      targetPosition,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
    if (!mounted) return;
    setState(() {
      _timeSliderValue = proportionOfDay;
    });
  }

  Future<void> _fetchTripRequests() async {
    try {
      final String formattedDate =
          DateFormat('yyyy-MM-dd').format(_selectedDate);
      try {
        final response = await ApiService.get('trips?status=REQUEST');
        if (response.statusCode == 200) {
          final data = ApiService.parseResponse(response);
          if (mounted) {
            final allTrips = List<Trip>.from(
                data['trips'].map((trip) => Trip.fromJson(trip)));
            final requestTrips = allTrips.where((trip) {
              final bool statusMatch =
                  trip.status == 'REQUEST' && trip.status != 'DELETED';
              final bool dateMatch =
                  _areSameDate(trip.originalDate, formattedDate);
              bool isCrossDateTrip = false;
              if (!dateMatch && trip.totalDuration != null) {
                try {
                  final DateTime tripDate = DateTime.parse(trip.originalDate);
                  final DateTime selectedDate = DateTime.parse(formattedDate);
                  if (tripDate.isBefore(selectedDate)) {
                    final DateTime totalDuration =
                        DateTime.parse(trip.totalDuration!);
                    final DateTime totalDurationDate = DateTime(
                        totalDuration.year,
                        totalDuration.month,
                        totalDuration.day);
                    final DateTime selectedDateOnly = DateTime(
                        selectedDate.year,
                        selectedDate.month,
                        selectedDate.day);
                    final DateTime tripDateOnly =
                        DateTime(tripDate.year, tripDate.month, tripDate.day);
                    isCrossDateTrip = (selectedDateOnly
                                .isAtSameMomentAs(tripDateOnly) ||
                            selectedDateOnly.isAfter(tripDateOnly)) &&
                        (selectedDateOnly.isAtSameMomentAs(totalDurationDate) ||
                            selectedDateOnly.isBefore(totalDurationDate));
                    if (isCrossDateTrip) {
                      print(
                          'Including cross-date REQUEST trip: ${trip.tripCode} from ${trip.originalDate} with total_duration ${trip.totalDuration}');
                    }
                  }
                } catch (e) {
                  print(
                      'Error checking for cross-date REQUEST trip ${trip.tripCode}: $e');
                }
              }
              return statusMatch && (dateMatch || isCrossDateTrip);
            }).toList();
            for (var trip in requestTrips) {
              if (trip.tripId == null && trip.tripCode.isNotEmpty) {
                _lookupTripIdByCode(trip);
              }
            }
            setState(() {
              _tripRequests = requestTrips;
            });
            return;
          }
        }
      } catch (apiError) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.of(context)
                  .errorFetchingTrips(apiError.toString())),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
      if (mounted) {
        setState(() {
          _tripRequests = [];
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _tripRequests = [];
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                AppLocalizations.of(context).errorFetchingTrips(e.toString())),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  Future<void> _fetchDriverRejectedTrips() async {
    try {
      final String formattedDate =
          DateFormat('yyyy-MM-dd').format(_selectedDate);
      try {
        final response = await ApiService.get('trips?status=DRIVER REJECTED');
        if (response.statusCode == 200) {
          final data = ApiService.parseResponse(response);
          if (mounted) {
            final allTrips = List<Trip>.from(
                data['trips'].map((trip) => Trip.fromJson(trip)));
            final driverRejectedTrips = allTrips.where((trip) {
              final bool statusMatch =
                  trip.status == 'DRIVER REJECTED' && trip.status != 'DELETED';
              final bool dateMatch =
                  _areSameDate(trip.originalDate, formattedDate);
              bool isCrossDateTrip = false;
              if (!dateMatch && trip.totalDuration != null) {
                try {
                  final DateTime tripDate = DateTime.parse(trip.originalDate);
                  final DateTime selectedDate = DateTime.parse(formattedDate);
                  if (tripDate.isBefore(selectedDate)) {
                    final DateTime totalDuration =
                        DateTime.parse(trip.totalDuration!);
                    final DateTime totalDurationDate = DateTime(
                        totalDuration.year,
                        totalDuration.month,
                        totalDuration.day);
                    final DateTime selectedDateOnly = DateTime(
                        selectedDate.year,
                        selectedDate.month,
                        selectedDate.day);
                    final DateTime tripDateOnly =
                        DateTime(tripDate.year, tripDate.month, tripDate.day);
                    isCrossDateTrip = (selectedDateOnly
                                .isAtSameMomentAs(tripDateOnly) ||
                            selectedDateOnly.isAfter(tripDateOnly)) &&
                        (selectedDateOnly.isAtSameMomentAs(totalDurationDate) ||
                            selectedDateOnly.isBefore(totalDurationDate));
                    if (isCrossDateTrip) {
                      print(
                          'Including cross-date DRIVER REJECTED trip: ${trip.tripCode} from ${trip.originalDate} with total_duration ${trip.totalDuration}');
                    }
                  }
                } catch (e) {
                  print(
                      'Error checking for cross-date DRIVER REJECTED trip ${trip.tripCode}: $e');
                }
              }
              return statusMatch && (dateMatch || isCrossDateTrip);
            }).toList();
            for (var trip in driverRejectedTrips) {
              if (trip.tripId == null && trip.tripCode.isNotEmpty) {
                _lookupTripIdByCode(trip);
              }
            }
            setState(() {
              _driverRejectedTrips = driverRejectedTrips;
            });
            return;
          }
        }
      } catch (apiError) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.of(context)
                  .errorFetchingDriverRejectedTrips(apiError.toString())),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
      if (mounted) {
        setState(() {
          _driverRejectedTrips = [];
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _driverRejectedTrips = [];
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)
                .errorFetchingDriverRejectedTrips(e.toString())),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  List<DriverSchedule> _getDriverSchedules(String driverCode) {
    int driverId = 0;
    for (var driver in _drivers) {
      if (driver.driverCode == driverCode) {
        driverId = driver.driverId;
        break;
      }
    }
    List<DriverSchedule> result;
    if (driverId > 0) {
      result = _schedules
          .where((schedule) => schedule.driverId == driverId)
          .toList();
    } else {
      result = _schedules
          .where((schedule) => schedule.driverCode == driverCode)
          .toList();
    }
    return result;
  }

  List<DriverSchedule> _getSchedulesForTimeSlot(
      String driverCode, String timeSlot) {
    final List<String> timeParts = timeSlot.split(':');
    final int slotHour = int.parse(timeParts[0]);
    final int slotMinute = int.parse(timeParts[1]);
    final DateTime baseDate =
        DateTime(_selectedDate.year, _selectedDate.month, _selectedDate.day);
    final DateTime slotStart = DateTime(
        baseDate.year, baseDate.month, baseDate.day, slotHour, slotMinute);
    final DateTime slotEnd = slotStart.add(const Duration(minutes: 30));
    final List<DriverSchedule> driverSchedules =
        _getDriverSchedules(driverCode);
    final List<DriverSchedule> schedulesInSlot = [];
    for (var schedule in driverSchedules) {
      final bool isStartingHere = schedule.isInTimeSlot(slotStart, slotEnd);
      if (isStartingHere) {
        schedulesInSlot.add(schedule);
      }
    }
    for (var schedule in driverSchedules) {
      if (schedulesInSlot.contains(schedule)) continue;
      final bool continuesHere =
          schedule.continuesIntoTimeSlot(slotStart, slotEnd);
      if (continuesHere) {
        schedulesInSlot.add(schedule);
      }
    }
    return schedulesInSlot;
  }

  Future<List<Driver>> _fetchAvailableDrivers(DateTime date) async {
    try {
      final String formattedDate = DateFormat('yyyy-MM-dd').format(date);
      print('Fetching available drivers for date: $formattedDate');
      final response = await ApiService.get('available-drivers/$formattedDate');
      if (response.statusCode == 200) {
        final data = ApiService.parseResponse(response);
        final List<Driver> availableDrivers = List<Driver>.from(
            data['drivers'].map((driver) => Driver.fromJson(driver)));
        availableDrivers.add(Driver(
          driverId: -1,
          driverCode: 'ONLINE',
          name: 'ONLINE TAXI',
          initial: 'O',
          carCode: '',
        ));
        return availableDrivers;
      } else {
        throw Exception(
            'Failed to load available drivers: ${response.statusCode}');
      }
    } catch (e) {
      print('Error fetching available drivers: $e');
      return _drivers;
    }
  }

  Future<List<Car>> _fetchAvailableCars(DateTime date) async {
    try {
      final String formattedDate = DateFormat('yyyy-MM-dd').format(date);
      print('Fetching available cars for date: $formattedDate');
      final response = await ApiService.get('available-cars/$formattedDate');
      if (response.statusCode == 200) {
        final data = ApiService.parseResponse(response);
        final List<Car> availableCars =
            List<Car>.from(data['cars'].map((car) => Car.fromJson(car)));
        print(
            'Found ${availableCars.length} available cars for date $formattedDate');
        for (var car in availableCars) {
          print('Available car: ${car.carCode} (ID: ${car.carId})');
        }
        return availableCars;
      } else {
        print('Failed to load available cars: ${response.statusCode}');
        throw Exception(
            'Failed to load available cars: ${response.statusCode}');
      }
    } catch (e) {
      print('Error fetching available cars: $e');
      throw Exception(
          'Unable to fetch available cars for the selected date. Please try again.');
    }
  }

  void _showSetDriverDialog(Trip trip) async {
    print('_showSetDriverDialog for ${trip.tripCode}: notes="${trip.notes}"');
    print(
        'Trip details: fromDestination=${trip.fromDestination}, toDestination=${trip.toDestination}');
    print(
        'Trip date: ${trip.date}, time: ${trip.time}, totalDuration: ${trip.totalDuration}');
    print('Trip isWaiting: ${trip.isWaiting}');
    _tripDurationMinutes = 30;
    print('Default trip duration set to: $_tripDurationMinutes minutes');
    Trip updatedTrip = trip;
    if (trip.tripCode.isNotEmpty) {
      try {
        final response = await ApiService.get('trips/${trip.tripCode}');
        if (response.statusCode == 200) {
          final data = ApiService.parseResponse(response);
          print('Trip details response for ${trip.tripCode}: $data');
          print('Notes for trip ${trip.tripCode}: ${data['notes']}');
          print('Is waiting for trip ${trip.tripCode}: ${data['is_waiting']}');
          List<Passenger>? passengers;
          if (data['passengers'] != null) {
            passengers = (data['passengers'] as List)
                .map((passengerJson) => Passenger.fromJson(passengerJson))
                .toList();
          }
          List<TripCargo>? cargos;
          if (data['cargos'] != null) {
            cargos = (data['cargos'] as List)
                .map((cargoJson) => TripCargo.fromJson(cargoJson))
                .toList();
          }
          updatedTrip = Trip(
            tripId: trip.tripId,
            tripCode: trip.tripCode,
            fromDestination: trip.fromDestination,
            toDestination: trip.toDestination,
            date: trip.date,
            originalDate: trip.originalDate,
            time: trip.time,
            status: trip.status,
            requestorId: trip.requestorId,
            requestorName: trip.requestorName,
            driverId: trip.driverId,
            driverCode: trip.driverCode,
            driverName: trip.driverName,
            rating: trip.rating,
            comments: trip.comments,
            rejectionReason: trip.rejectionReason,
            completionNotes: trip.completionNotes,
            completionImagePath: trip.completionImagePath,
            totalDuration: trip.totalDuration,
            notes: data['notes'] ?? '',
            isWaiting: data['is_waiting'] ?? false,
            passengers: passengers,
            cargos: cargos,
          );
          print(
              'Updated trip notes for ${updatedTrip.tripCode}: "${updatedTrip.notes}"');
          print(
              'Updated trip isWaiting for ${updatedTrip.tripCode}: ${updatedTrip.isWaiting}');
          bool durationSet = false;
          if (data['total_duration'] != null &&
              data['total_duration'].toString().isNotEmpty) {
            try {
              final String tripDate = trip.originalDate.isNotEmpty
                  ? trip.originalDate
                  : DateFormat('yyyy-MM-dd').format(DateTime.now());
              final String tripTime =
                  trip.time.isNotEmpty ? trip.time : '00:00';
              final DateTime tripStartTime =
                  DateTime.parse('${tripDate}T$tripTime:00');
              final DateTime totalDurationTime =
                  DateTime.parse(data['total_duration']);
              final int durationMinutes =
                  totalDurationTime.difference(tripStartTime).inMinutes;
              if (durationMinutes > 0 && durationMinutes < 24 * 60) {
                _tripDurationMinutes = durationMinutes;
                durationSet = true;
                print(
                    'Priority 1: Using existing trip total_duration: $_tripDurationMinutes minutes');
              } else {
                print(
                    'Priority 1: Existing trip duration is invalid ($durationMinutes minutes), trying destination duration');
              }
            } catch (e) {
              print(
                  'Priority 1: Error calculating duration from total_duration: $e, trying destination duration');
            }
          } else {
            print(
                'Priority 1: No existing trip total_duration found, trying destination duration');
          }
          if (!durationSet) {
            print('Priority 2: Attempting to get duration from destination...');
            if (data['to_duration_minutes'] != null) {
              try {
                final int destDuration =
                    int.tryParse(data['to_duration_minutes'].toString()) ?? 0;
                print(
                    'Priority 2a: Parsed destination duration from API response: $destDuration minutes');
                if (destDuration > 0) {
                  _tripDurationMinutes = destDuration;
                  durationSet = true;
                  print(
                      'Priority 2a: Using destination duration from API response: $_tripDurationMinutes minutes');
                } else {
                  print(
                      'Priority 2a: Destination duration from API response is zero or negative, trying fallback method');
                }
              } catch (e) {
                print(
                    'Priority 2a: Error parsing destination duration from API response: $e, trying fallback method');
              }
            } else {
              print(
                  'Priority 2a: No to_duration_minutes in API response, trying fallback method');
            }
            if (!durationSet) {
              print(
                  'Priority 2b: Attempting fallback method to fetch destination duration...');
              try {
                final destResponse = await ApiService.get('destinations');
                if (destResponse.statusCode == 200) {
                  final destData = ApiService.parseResponse(destResponse);
                  final destinations = destData['destinations'] as List;
                  final String toDestCode = data['to_destination_code'] ?? '';
                  final String toDestination =
                      data['to_destination'] ?? trip.toDestination;
                  print(
                      'Priority 2b: Looking for destination - code: "$toDestCode", name: "$toDestination"');
                  var matchingDestList = <dynamic>[];
                  if (toDestCode.isNotEmpty) {
                    matchingDestList = destinations
                        .where((dest) => dest['destinationCode'] == toDestCode)
                        .toList();
                    print(
                        'Priority 2b: Found ${matchingDestList.length} destinations matching code "$toDestCode"');
                  }
                  if (matchingDestList.isEmpty && toDestination.isNotEmpty) {
                    matchingDestList = destinations
                        .where((dest) => dest['destination'] == toDestination)
                        .toList();
                    print(
                        'Priority 2b: Found ${matchingDestList.length} destinations matching name "$toDestination"');
                  }
                  if (matchingDestList.isNotEmpty) {
                    final matchingDest = matchingDestList[0];
                    print(
                        'Priority 2b: Matching destination: ${matchingDest['destination']}, duration: ${matchingDest['duration']}');
                    if (matchingDest['duration'] != null) {
                      final int destDuration =
                          int.tryParse(matchingDest['duration'].toString()) ??
                              0;
                      print(
                          'Priority 2b: Parsed destination duration: $destDuration minutes');
                      if (destDuration > 0) {
                        _tripDurationMinutes = destDuration;
                        durationSet = true;
                        print(
                            'Priority 2b: Using destination duration: $_tripDurationMinutes minutes');
                      } else {
                        print(
                            'Priority 2b: Destination duration is zero or negative, keeping default');
                      }
                    } else {
                      print(
                          'Priority 2b: Destination duration is null, keeping default');
                    }
                  } else {
                    print(
                        'Priority 2b: No matching destination found, keeping default duration');
                  }
                } else {
                  print(
                      'Priority 2b: Failed to fetch destinations (${destResponse.statusCode}), keeping default duration');
                }
              } catch (e) {
                print(
                    'Priority 2b: Error fetching destination details: $e, keeping default duration');
              }
            }
          }
          if (!durationSet) {
            print(
                'Final: No duration could be determined, using default: $_tripDurationMinutes minutes');
          } else {
            print(
                'Final: Duration successfully set to: $_tripDurationMinutes minutes');
          }
        }
      } catch (e) {
        print('Error fetching trip details: $e');
        print(
            'Using original trip for ${trip.tripCode}: isWaiting=${trip.isWaiting}');
      }
    }
    trip = updatedTrip;
    print(
        'Final trip used in dialog for ${trip.tripCode}: isWaiting=${trip.isWaiting}');
    if (trip.tripId == null) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                AppLocalizations.of(context).lookingUpTripId(trip.tripCode)),
            duration: const Duration(seconds: 2),
          ),
        );
      }
      await _lookupTripIdByCode(trip);
      if (!mounted) return;
      final updatedTrip = _tripRequests.firstWhere(
        (t) => t.tripCode == trip.tripCode,
        orElse: () => trip,
      );
      if (updatedTrip.tripId == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)
                .cannotAssignDriverMissingId(trip.tripCode)),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
        return;
      }
      trip = updatedTrip;
    }
    if (!mounted) return;
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context).loadingAvailableDrivers),
          duration: const Duration(seconds: 1),
        ),
      );
    }
    DateTime tripDate;
    try {
      tripDate = DateFormat('yyyy-MM-dd').parse(trip.originalDate);
    } catch (e) {
      try {
        tripDate = DateFormat('E, dd-MMM-yy').parse(trip.date);
      } catch (e2) {
        print(
            'Error parsing trip date: ${trip.date} or ${trip.originalDate}. Using today\'s date as fallback.');
        tripDate = DateTime.now();
      }
    }
    final String formattedApiDate = DateFormat('yyyy-MM-dd').format(tripDate);
    print('Using date for driver availability check: $formattedApiDate');
    final List<Driver> availableDrivers =
        await _fetchAvailableDrivers(tripDate);
    String fromNotes = '';
    String toNotes = '';
    final destinationsResponse = await ApiService.get('destinations');
    if (destinationsResponse.statusCode == 200) {
      final data = ApiService.parseResponse(destinationsResponse);
      final destinations = data['destinations'] as List;
      for (var dest in destinations) {
        if (dest['destination'] == trip.fromDestination) {
          fromNotes = dest['notes'] ?? '';
        }
        if (dest['destination'] == trip.toDestination) {
          toNotes = dest['notes'] ?? '';
        }
      }
    }
    Driver? selectedDriver;
    setState(() {
      _selectedCar = null;
      _cars = [];
    });
    try {
      print('Fetching available cars for date: $formattedApiDate');
      final List<Car> availableCars = await _fetchAvailableCars(tripDate);
      print(
          'Fetched ${availableCars.length} available cars for date $formattedApiDate');
      if (mounted) {
        setState(() {
          _cars = availableCars;
        });
      }
    } catch (e) {
      print('Error fetching available cars: $e');
      try {
        print('Falling back to pre-fetching all cars for the dropdown');
        final carsResponse = await ApiService.get('cars');
        if (carsResponse.statusCode == 200) {
          final carsData = ApiService.parseResponse(carsResponse);
          final List<dynamic> allCarsJson = carsData['cars'] as List;
          print('Pre-fetched ${allCarsJson.length} cars');
          if (mounted) {
            setState(() {
              _cars =
                  allCarsJson.map((carJson) => Car.fromJson(carJson)).toList();
            });
          }
        } else {
          print('Failed to pre-fetch cars: ${carsResponse.statusCode}');
        }
      } catch (e) {
        print('Error pre-fetching all cars: $e');
      }
    }
    if (!mounted) return;
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text(AppLocalizations.of(context)
                  .assignDriverOrOnlineTaxi(trip.tripCode)),
              titlePadding: const EdgeInsets.fromLTRB(24, 24, 24, 8),
              contentPadding: const EdgeInsets.fromLTRB(24, 8, 24, 0),
              content: SizedBox(
                width: 400,
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.blue.shade50,
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(color: Colors.blue.shade200),
                        ),
                        child: Text(
                          AppLocalizations.of(context)
                              .assignDriverStatusExplanation,
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade50,
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(color: Colors.grey.shade300),
                        ),
                        child: Text(
                          AppLocalizations.of(context)
                              .tripDateLabel(trip.date, formattedApiDate),
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('${AppLocalizations.of(context).from}:',
                                    style: const TextStyle(
                                        fontWeight: FontWeight.bold)),
                                Text(trip.fromDestination),
                                if (fromNotes.isNotEmpty)
                                  Container(
                                    margin: const EdgeInsets.only(
                                        top: 4, bottom: 8),
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Colors.yellow.shade50,
                                      borderRadius: BorderRadius.circular(4),
                                      border: Border.all(
                                          color: Colors.yellow.shade200),
                                    ),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                            '${AppLocalizations.of(context).notes}:',
                                            style: const TextStyle(
                                                fontWeight: FontWeight.bold,
                                                fontSize: 12)),
                                        Text(fromNotes,
                                            style:
                                                const TextStyle(fontSize: 12)),
                                      ],
                                    ),
                                  ),
                              ],
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('${AppLocalizations.of(context).to}:',
                                    style: const TextStyle(
                                        fontWeight: FontWeight.bold)),
                                Text(trip.toDestination),
                                if (toNotes.isNotEmpty)
                                  Container(
                                    margin: const EdgeInsets.only(
                                        top: 4, bottom: 8),
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Colors.yellow.shade50,
                                      borderRadius: BorderRadius.circular(4),
                                      border: Border.all(
                                          color: Colors.yellow.shade200),
                                    ),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                            '${AppLocalizations.of(context).notes}:',
                                            style: const TextStyle(
                                                fontWeight: FontWeight.bold,
                                                fontSize: 12)),
                                        Text(toNotes,
                                            style:
                                                const TextStyle(fontSize: 12)),
                                      ],
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                    '${AppLocalizations.of(context).time}: ${_formatTimeString(trip.time)}'),
                                Text(
                                    '${AppLocalizations.of(context).requestor}: ${trip.requestorName}'),
                                if (trip.isWaiting == true)
                                  Padding(
                                    padding: const EdgeInsets.only(top: 4.0),
                                    child: Row(
                                      children: [
                                        Icon(Icons.schedule,
                                            size: 16,
                                            color: Colors.orange.shade600),
                                        const SizedBox(width: 4),
                                        Container(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 6, vertical: 2),
                                          decoration: BoxDecoration(
                                            color: Colors.orange.shade100,
                                            borderRadius:
                                                BorderRadius.circular(4),
                                            border: Border.all(
                                                color: Colors.orange.shade300),
                                          ),
                                          child: Text(
                                            AppLocalizations.of(context)
                                                .waitingTrip,
                                            style: TextStyle(
                                              color: Colors.orange.shade800,
                                              fontWeight: FontWeight.bold,
                                              fontSize: 12,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      Container(
                        margin: const EdgeInsets.only(top: 12),
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.green.shade50,
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(color: Colors.green.shade200),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('${AppLocalizations.of(context).tripNotes}:',
                                style: const TextStyle(
                                    fontWeight: FontWeight.bold, fontSize: 14)),
                            const SizedBox(height: 4),
                            Text(
                              trip.notes != null && trip.notes!.isNotEmpty
                                  ? trip.notes!
                                  : AppLocalizations.of(context)
                                      .noNotesProvided,
                              style: TextStyle(
                                fontSize: 13,
                                fontStyle:
                                    trip.notes != null && trip.notes!.isNotEmpty
                                        ? FontStyle.normal
                                        : FontStyle.italic,
                                color:
                                    trip.notes != null && trip.notes!.isNotEmpty
                                        ? Colors.black
                                        : Colors.grey.shade700,
                              ),
                            ),
                          ],
                        ),
                      ),
                      if (trip.passengers != null &&
                          trip.passengers!.isNotEmpty) ...[
                        const SizedBox(height: 12),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.blue.shade50,
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(color: Colors.blue.shade200),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                  '${AppLocalizations.of(context).passengers}:',
                                  style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 14)),
                              const SizedBox(height: 4),
                              ...trip.passengers!.map((passenger) => Padding(
                                    padding: const EdgeInsets.only(bottom: 2),
                                    child: Text(
                                      '• ${passenger.name}',
                                      style: const TextStyle(fontSize: 13),
                                    ),
                                  )),
                            ],
                          ),
                        ),
                      ],
                      if (trip.cargos != null && trip.cargos!.isNotEmpty) ...[
                        const SizedBox(height: 12),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.orange.shade50,
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(color: Colors.orange.shade200),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text('${AppLocalizations.of(context).cargo}:',
                                  style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 14)),
                              const SizedBox(height: 4),
                              ...trip.cargos!.map((cargo) => Padding(
                                    padding: const EdgeInsets.only(bottom: 2),
                                    child: Text(
                                      '• ${cargo.name}${cargo.code != null && cargo.code!.isNotEmpty ? ' (${cargo.code})' : ''}',
                                      style: const TextStyle(fontSize: 13),
                                    ),
                                  )),
                            ],
                          ),
                        ),
                      ],
                      const SizedBox(height: 12),
                      Text('${AppLocalizations.of(context).tripDuration}:',
                          style: const TextStyle(fontWeight: FontWeight.bold)),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(AppLocalizations.of(context).hours,
                                    style: const TextStyle(fontSize: 12)),
                                TextFormField(
                                  controller: TextEditingController(
                                      text: (_tripDurationMinutes ~/ 60)
                                          .toString()),
                                  decoration: InputDecoration(
                                    border: const OutlineInputBorder(),
                                    contentPadding: const EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 8),
                                    hintText:
                                        AppLocalizations.of(context).hours,
                                  ),
                                  keyboardType: TextInputType.number,
                                  inputFormatters: [
                                    FilteringTextInputFormatter.digitsOnly,
                                  ],
                                  onChanged: (value) {
                                    if (value.isNotEmpty) {
                                      final parsedHours = int.tryParse(value);
                                      if (parsedHours != null &&
                                          parsedHours >= 0) {
                                        final minutesPart =
                                            _tripDurationMinutes % 60;
                                        _tripDurationMinutes =
                                            (parsedHours * 60) + minutesPart;
                                        print(
                                            'Duration updated to: $_tripDurationMinutes minutes (${parsedHours}h ${minutesPart}m)');
                                      }
                                    }
                                  },
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(AppLocalizations.of(context).minutes,
                                    style: const TextStyle(fontSize: 12)),
                                TextFormField(
                                  controller: TextEditingController(
                                      text: (_tripDurationMinutes % 60)
                                          .toString()),
                                  decoration: InputDecoration(
                                    border: const OutlineInputBorder(),
                                    contentPadding: const EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 8),
                                    hintText:
                                        AppLocalizations.of(context).minutes,
                                  ),
                                  keyboardType: TextInputType.number,
                                  inputFormatters: [
                                    FilteringTextInputFormatter.digitsOnly,
                                  ],
                                  onChanged: (value) {
                                    if (value.isNotEmpty) {
                                      final parsedMinutes = int.tryParse(value);
                                      if (parsedMinutes != null &&
                                          parsedMinutes >= 0 &&
                                          parsedMinutes < 60) {
                                        final hoursPart =
                                            _tripDurationMinutes ~/ 60;
                                        _tripDurationMinutes =
                                            (hoursPart * 60) + parsedMinutes;
                                        print(
                                            'Duration updated to: $_tripDurationMinutes minutes (${hoursPart}h ${parsedMinutes}m)');
                                      } else if (parsedMinutes != null &&
                                          parsedMinutes >= 60) {
                                        final additionalHours =
                                            parsedMinutes ~/ 60;
                                        final remainingMinutes =
                                            parsedMinutes % 60;
                                        final hoursPart =
                                            _tripDurationMinutes ~/ 60;
                                        _tripDurationMinutes =
                                            ((hoursPart + additionalHours) *
                                                    60) +
                                                remainingMinutes;
                                        print(
                                            'Duration updated to: $_tripDurationMinutes minutes (${hoursPart + additionalHours}h ${remainingMinutes}m)');
                                      }
                                    }
                                  },
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Text('${AppLocalizations.of(context).assignTo}:',
                          style: const TextStyle(fontWeight: FontWeight.bold)),
                      const SizedBox(height: 8),
                      DropdownButtonFormField<Driver>(
                        decoration: const InputDecoration(
                          border: OutlineInputBorder(),
                          contentPadding:
                              EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        ),
                        hint: Text(AppLocalizations.of(context).selectADriver),
                        value: selectedDriver,
                        items: availableDrivers.map((driver) {
                          return DropdownMenuItem<Driver>(
                            value: driver,
                            child: Text(driver.name),
                          );
                        }).toList(),
                        onChanged: (Driver? value) async {
                          setState(() {
                            selectedDriver = value;
                          });
                          if (value != null && value.driverId != -1) {
                            try {
                              final List<Car> availableCars =
                                  await _fetchAvailableCars(tripDate);
                              print(
                                  'Fetched ${availableCars.length} available cars for date $formattedApiDate');
                              Car? driverAssignedCar;
                              try {
                                final response = await ApiService.get(
                                    'drivers/${value.driverCode}/cars');
                                if (response.statusCode == 200) {
                                  final data =
                                      ApiService.parseResponse(response);
                                  final List<dynamic> driverCarsJson =
                                      data['cars'] as List;
                                  final List<Car> driverCars = driverCarsJson
                                      .map((carJson) => Car.fromJson(carJson))
                                      .toList();
                                  print(
                                      'Driver ${value.driverCode} has ${driverCars.length} total cars');
                                  for (var driverCar in driverCars) {
                                    print(
                                        'Checking driver car: ${driverCar.carCode} (ID: ${driverCar.carId}), isAssigned: ${driverCar.isAssigned}');
                                    if (driverCar.isAssigned) {
                                      final matchingCars = availableCars
                                          .where((availableCar) =>
                                              availableCar.carId ==
                                              driverCar.carId)
                                          .toList();
                                      if (matchingCars.isNotEmpty) {
                                        driverAssignedCar = matchingCars.first;
                                        print(
                                            'Found driver assigned car that is available: ${driverAssignedCar.displayName}');
                                        break;
                                      } else {
                                        print(
                                            'Driver assigned car ${driverCar.carCode} is not available for this date');
                                      }
                                    }
                                  }
                                  print(
                                      'Driver ${value.driverCode} assigned car available: ${driverAssignedCar != null}');
                                }
                              } catch (e) {
                                print('Error fetching driver cars: $e');
                              }
                              if (mounted) {
                                setState(() {
                                  _cars = availableCars;
                                  _driverAssignedCarId =
                                      driverAssignedCar?.carId;
                                  if (driverAssignedCar != null) {
                                    _selectedCar = driverAssignedCar;
                                    print(
                                        'Pre-selected driver\'s assigned car: ${driverAssignedCar.displayName}');
                                  } else if (_cars.isNotEmpty) {
                                    _selectedCar = _cars.first;
                                    print(
                                        'No assigned car available, selected first available car: ${_cars.first.displayName}');
                                  } else {
                                    _selectedCar = null;
                                    print('No cars available');
                                  }
                                });
                              }
                            } catch (e) {
                              print('Error fetching available cars: $e');
                            }
                          } else {
                            setState(() {
                              _selectedCar = null;
                              _cars = [];
                              _driverAssignedCarId = null;
                            });
                          }
                        },
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 16),
                          Text('${AppLocalizations.of(context).car}:',
                              style:
                                  const TextStyle(fontWeight: FontWeight.bold)),
                          const SizedBox(height: 8),
                          DropdownButtonFormField<Car>(
                            decoration: const InputDecoration(
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 8),
                            ),
                            hint: Text(selectedDriver == null ||
                                    selectedDriver!.driverId == -1
                                ? AppLocalizations.of(context).selectDriverFirst
                                : AppLocalizations.of(context).selectCar),
                            value: _selectedCar,
                            items: _cars.map((car) {
                              bool isDriversAssignedCar = false;
                              if (selectedDriver != null &&
                                  selectedDriver!.driverId != -1) {
                                isDriversAssignedCar =
                                    (_driverAssignedCarId != null &&
                                        car.carId == _driverAssignedCarId);
                              }
                              String displayText = car.displayName;
                              if (isDriversAssignedCar) {
                                displayText += ' (Driver\'s Car)';
                              }
                              return DropdownMenuItem<Car>(
                                value: car,
                                child: Text(
                                  displayText,
                                  style: TextStyle(
                                    fontWeight: isDriversAssignedCar
                                        ? FontWeight.bold
                                        : FontWeight.normal,
                                    color: isDriversAssignedCar
                                        ? Colors.blue
                                        : null,
                                  ),
                                ),
                              );
                            }).toList(),
                            onChanged: selectedDriver == null ||
                                    selectedDriver!.driverId == -1
                                ? null
                                : (Car? value) {
                                    setState(() {
                                      _selectedCar = value;
                                    });
                                  },
                          ),
                          if (_cars.isEmpty &&
                              selectedDriver != null &&
                              selectedDriver!.driverId != -1)
                            Container(
                              margin: const EdgeInsets.only(top: 8),
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Colors.amber.shade50,
                                borderRadius: BorderRadius.circular(4),
                                border:
                                    Border.all(color: Colors.amber.shade200),
                              ),
                              child: Text(
                                AppLocalizations.of(context)
                                    .noCarsAvailableMessage,
                                style: const TextStyle(
                                    fontSize: 12, color: Colors.amber),
                              ),
                            ),
                        ],
                      ),
                      if (availableDrivers.length <= 1)
                        Container(
                          margin: const EdgeInsets.only(top: 8),
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.amber.shade50,
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(color: Colors.amber.shade200),
                          ),
                          child: Text(
                            AppLocalizations.of(context)
                                .noDriversAvailableMessage,
                            style: const TextStyle(
                                fontSize: 12, color: Colors.amber),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(AppLocalizations.of(context).cancel),
                ),
                ElevatedButton(
                  onPressed: selectedDriver == null
                      ? null
                      : () {
                          final int durationMinutes = _tripDurationMinutes;
                          final int hours = durationMinutes ~/ 60;
                          final int minutes = durationMinutes % 60;
                          print(
                              'Assigning driver with duration: $durationMinutes minutes ($hours hours and $minutes minutes)');
                          print(
                              '_tripDurationMinutes value: $_tripDurationMinutes');
                          _assignDriverToTrip(
                              trip, selectedDriver!, durationMinutes);
                          Navigator.of(context).pop();
                        },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF0D47A1),
                    foregroundColor: Colors.white,
                    disabledBackgroundColor: Colors.grey.shade300,
                  ),
                  child: Text(AppLocalizations.of(context).assign),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<void> _lookupTripIdByCode(Trip trip) async {
    try {
      final response = await ApiService.get('trips/code/${trip.tripCode}');
      if (response.statusCode == 200) {
        final data = ApiService.parseResponse(response);
        print(
            '_lookupTripIdByCode response for ${trip.tripCode}: ${response.body}');
        final int tripId = data['trip_id'];
        final String? driverCode = data['driver_code'];
        final String? notes = data['notes'];
        print('Notes from API for ${trip.tripCode}: "$notes"');
        setState(() {
          final updatedTrip = Trip(
            tripId: tripId,
            tripCode: trip.tripCode,
            fromDestination: trip.fromDestination,
            toDestination: trip.toDestination,
            fromDestinationInitial: trip.fromDestinationInitial,
            toDestinationInitial: trip.toDestinationInitial,
            date: trip.date,
            originalDate: trip.originalDate,
            time: trip.time,
            status: trip.status,
            requestorId: trip.requestorId,
            requestorName: trip.requestorName,
            driverId: trip.driverId,
            driverCode: driverCode,
            driverName: trip.driverName,
            carId: trip.carId,
            carCode: trip.carCode,
            plateNumber: trip.plateNumber,
            rating: trip.rating,
            comments: trip.comments,
            rejectionReason: trip.rejectionReason,
            completionNotes: trip.completionNotes,
            completionImagePath: trip.completionImagePath,
            totalDuration: trip.totalDuration,
            notes: data['notes'] ?? '',
            isWaiting: trip.isWaiting,
            passengers: trip.passengers,
            cargos: trip.cargos,
          );
          final index =
              _tripRequests.indexWhere((t) => t.tripCode == trip.tripCode);
          if (index != -1) {
            _tripRequests[index] = updatedTrip;
          }
        });
      } else {}
    } catch (e) {}
  }

  bool _areSameDate(String date1, String date2) {
    try {
      print('_areSameDate comparing: "$date1" and "$date2"');
      DateTime dt1;
      DateTime dt2;
      try {
        dt1 = DateTime.parse(date1).toLocal();
        print('  Parsed date1 as ISO: ${dt1.toIso8601String()}');
      } catch (e) {
        try {
          if (date1.contains('-')) {
            dt1 = DateFormat('yyyy-MM-dd').parse(date1);
            print('  Parsed date1 as yyyy-MM-dd: ${dt1.toIso8601String()}');
          } else if (date1.contains('/')) {
            dt1 = DateFormat('MM/dd/yyyy').parse(date1);
            print('  Parsed date1 as MM/dd/yyyy: ${dt1.toIso8601String()}');
          } else {
            print('  Unknown date format for date1: $date1');
            throw Exception('Unknown date format for date1');
          }
        } catch (e2) {
          print('  Failed to parse date1: $date1, error: $e2');
          return false;
        }
      }
      try {
        dt2 = DateTime.parse(date2).toLocal();
        print('  Parsed date2 as ISO: ${dt2.toIso8601String()}');
      } catch (e) {
        try {
          if (date2.contains('-')) {
            dt2 = DateFormat('yyyy-MM-dd').parse(date2);
            print('  Parsed date2 as yyyy-MM-dd: ${dt2.toIso8601String()}');
          } else if (date2.contains('/')) {
            dt2 = DateFormat('MM/dd/yyyy').parse(date2);
            print('  Parsed date2 as MM/dd/yyyy: ${dt2.toIso8601String()}');
          } else {
            print('  Unknown date format for date2: $date2');
            throw Exception('Unknown date format for date2');
          }
        } catch (e2) {
          print('  Failed to parse date2: $date2, error: $e2');
          return false;
        }
      }
      final bool sameDate =
          dt1.year == dt2.year && dt1.month == dt2.month && dt1.day == dt2.day;
      print('  Date comparison:');
      print(
          '    Date1: ${dt1.year}-${dt1.month.toString().padLeft(2, '0')}-${dt1.day.toString().padLeft(2, '0')}');
      print(
          '    Date2: ${dt2.year}-${dt2.month.toString().padLeft(2, '0')}-${dt2.day.toString().padLeft(2, '0')}');
      print('    Same date: $sameDate');
      if (sameDate) {
        return true;
      }
      final bool date1BeforeDate2 = dt1.isBefore(dt2);
      print('  Date1 is before Date2: $date1BeforeDate2');
      return false;
    } catch (e) {
      print('  Error in _areSameDate: $e');
      return false;
    }
  }

  String _formatDateToLocal(String dateStr) {
    try {
      final DateTime parsedDate = DateTime.parse(dateStr).toLocal();
      return DateFormat('E, dd-MMM-yy').format(parsedDate);
    } catch (e) {
      print('Error formatting date $dateStr to local: $e');
      return dateStr;
    }
  }

  Future<int?> _getTripIdFromCode(String tripCode) async {
    try {
      final response = await ApiService.get('trips/code/$tripCode');
      if (response.statusCode == 200) {
        final data = ApiService.parseResponse(response);
        final tripId = data['trip_id'];
        print('Found trip ID $tripId for trip code $tripCode');
        return tripId;
      } else {
        print(
            'Failed to get trip ID for code $tripCode: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      print('Error getting trip ID from code $tripCode: $e');
      return null;
    }
  }

  Future<void> _showAllRequestTrips() async {
    try {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).fetchingTrips),
            duration: const Duration(seconds: 1),
          ),
        );
      }
      await _fetchAllRequestTrips();
      await _fetchAllDriverRejectedTrips();
      final requestResponse = await ApiService.get('trips?status=REQUEST');
      final driverRejectedResponse =
          await ApiService.get('trips?status=DRIVER REJECTED');
      if (requestResponse.statusCode == 200 &&
          driverRejectedResponse.statusCode == 200) {
        final requestData = ApiService.parseResponse(requestResponse);
        final driverRejectedData =
            ApiService.parseResponse(driverRejectedResponse);
        final requestTrips = requestData['trips'] as List;
        final driverRejectedTrips = driverRejectedData['trips'] as List;
        final processedRequestTrips = requestTrips.map((trip) {
          final processedTrip = Map<String, dynamic>.from(trip);
          if (processedTrip['date'] != null) {
            processedTrip['formatted_date'] =
                _formatDateToLocal(processedTrip['date']);
          } else {
            processedTrip['formatted_date'] = 'Unknown date';
          }
          return processedTrip;
        }).toList();
        final processedDriverRejectedTrips = driverRejectedTrips.map((trip) {
          final processedTrip = Map<String, dynamic>.from(trip);
          if (processedTrip['date'] != null) {
            processedTrip['formatted_date'] =
                _formatDateToLocal(processedTrip['date']);
          } else {
            processedTrip['formatted_date'] = 'Unknown date';
          }          return processedTrip;
        }).toList();

        if (mounted) {
          showDialog(
            context: context,            builder: (context) => StatefulBuilder(
              builder: (context, setDialogState) {
                // Apply sorting inside the StatefulBuilder to reflect changes
                final sortedRequestTrips = _sortTrips(processedRequestTrips);
                final sortedDriverRejectedTrips = _sortTrips(processedDriverRejectedTrips);
                
                return AlertDialog(
                  title: Row(
                    children: [
                      Expanded(
                        child: Text(AppLocalizations.of(context).allTripRequests),
                      ),
                      // Sort controls
                      PopupMenuButton<TripSortOption>(
                        icon: const Icon(Icons.sort),
                        tooltip: 'Sort by',
                        onSelected: (TripSortOption option) {
                          setDialogState(() {
                            if (_sortOption == option) {
                              _sortAscending = !_sortAscending;
                            } else {
                              _sortOption = option;
                              _sortAscending = true;
                            }
                          });
                        },
                        itemBuilder: (BuildContext context) => TripSortOption.values.map((option) {
                          return PopupMenuItem<TripSortOption>(
                            value: option,
                            child: Row(
                              children: [
                                if (_sortOption == option) ...[
                                  Icon(
                                    _sortAscending ? Icons.arrow_upward : Icons.arrow_downward,
                                    size: 16,
                                  ),
                                  const SizedBox(width: 8),
                                ],
                                Text(_getSortOptionDisplayName(option)),
                              ],
                            ),
                          );
                        }).toList(),
                      ),
                    ],
                  ),                  content: SizedBox(
                    width: double.maxFinite,
                    height: 500,
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Sort indicator
                          Container(
                            padding: const EdgeInsets.all(8),
                            margin: const EdgeInsets.only(bottom: 16),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade100,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                const Icon(Icons.sort, size: 16),
                                const SizedBox(width: 8),
                                Text(
                                  'Sorted by ${_getSortOptionDisplayName(_sortOption)} (${_sortAscending ? 'Ascending' : 'Descending'})',
                                  style: const TextStyle(fontSize: 12),
                                ),
                              ],
                            ),
                          ),
                          Text(
                            AppLocalizations.of(context).requestTrips.toUpperCase(),
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              color: Colors.blue,
                            ),
                          ),
                          Text(AppLocalizations.of(context)
                              .foundRequestTrips(sortedRequestTrips.length)),
                          const SizedBox(height: 8),
                          if (sortedRequestTrips.isEmpty)
                            Padding(
                              padding: const EdgeInsets.only(bottom: 16),
                              child: Text(
                                  AppLocalizations.of(context).noRequestTripsFound,
                                  style:
                                      const TextStyle(fontStyle: FontStyle.italic)),
                            )
                          else
                            ...sortedRequestTrips.map((trip) => Padding(
                              padding: const EdgeInsets.symmetric(vertical: 4),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        'Trip ${trip['trip_code']}',
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                          color: Colors.blue,
                                        ),
                                      ),
                                      ElevatedButton(
                                        onPressed: () {
                                          _showDeleteConfirmDialogFromAllRequests(
                                              context, trip['trip_code']);
                                        },
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: Colors.red.shade700,
                                          foregroundColor: Colors.white,
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 6),
                                          minimumSize: const Size(60, 24),
                                          textStyle:
                                              const TextStyle(fontSize: 10),
                                        ),
                                        child: Text(AppLocalizations.of(context)
                                            .delete),
                                      ),
                                    ],
                                  ),
                                  Text(
                                    'From: ${trip['from_destination'] ?? trip['custom_from'] ?? 'Unknown'}',
                                    style: const TextStyle(fontSize: 12),
                                  ),
                                  Text(
                                    'To: ${trip['to_destination'] ?? trip['custom_to'] ?? 'Unknown'}',
                                    style: const TextStyle(fontSize: 12),
                                  ),
                                  Text(
                                    'Date: ${trip['formatted_date']}, Time: ${trip['time'] ?? 'Unknown'}',
                                    style: const TextStyle(fontSize: 12),
                                  ),
                                  Text(
                                    'Requestor: ${trip['requestor_name'] ?? 'Unknown'}',
                                    style: const TextStyle(fontSize: 12),
                                  ),
                                  const Divider(),
                                ],
                              ),
                            )),
                      const SizedBox(height: 16),
                      Text(
                        AppLocalizations.of(context).driverRejectedTripsTitle,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: Colors.red,
                        ),
                      ),                      Text(AppLocalizations.of(context)
                          .foundDriverRejectedTrips(
                              sortedDriverRejectedTrips.length)),
                      const SizedBox(height: 8),
                      if (sortedDriverRejectedTrips.isEmpty)
                        Text(
                            AppLocalizations.of(context)
                                .noDriverRejectedTripsFound,
                            style: const TextStyle(fontStyle: FontStyle.italic))
                      else
                        ...sortedDriverRejectedTrips.map((trip) => Padding(
                              padding: const EdgeInsets.symmetric(vertical: 4),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        'Trip ${trip['trip_code']}',
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                          color: Colors.red,
                                        ),
                                      ),
                                      Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          ElevatedButton(
                                            onPressed: () {
                                              final tripObj = Trip(
                                                tripCode: trip['trip_code'],
                                                fromDestination:
                                                    trip['from_destination'] ??
                                                        trip['custom_from'] ??
                                                        'Unknown',
                                                toDestination:
                                                    trip['to_destination'] ??
                                                        trip['custom_to'] ??
                                                        'Unknown',
                                                date: trip['formatted_date'] ??
                                                    'Unknown',
                                                originalDate:
                                                    trip['date'] ?? '',
                                                time: trip['time'] ?? 'Unknown',
                                                status: 'DRIVER REJECTED',
                                                requestorName:
                                                    trip['requestor_name'] ??
                                                        'Unknown',
                                                rejectionReason:
                                                    trip['rejection_reason'],
                                                isWaiting:
                                                    trip['is_waiting'] ?? false,
                                              );
                                              _showRejectionReasonDialog(
                                                  tripObj);
                                            },
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor:
                                                  Colors.red.shade50,
                                              foregroundColor:
                                                  Colors.red.shade900,
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 6),
                                              minimumSize: const Size(60, 24),
                                              textStyle:
                                                  const TextStyle(fontSize: 10),
                                            ),
                                            child: Text(
                                                AppLocalizations.of(context)
                                                    .viewReason),
                                          ),
                                          const SizedBox(width: 4),
                                          ElevatedButton(
                                            onPressed: () {
                                              _showDeleteConfirmDialogFromAllRequests(
                                                  context, trip['trip_code']);
                                            },
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor:
                                                  Colors.red.shade700,
                                              foregroundColor: Colors.white,
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 6),
                                              minimumSize: const Size(60, 24),
                                              textStyle:
                                                  const TextStyle(fontSize: 10),
                                            ),
                                            child: Text(
                                                AppLocalizations.of(context)
                                                    .delete),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  Text(
                                    'From: ${trip['from_destination'] ?? trip['custom_from'] ?? 'Unknown'}',
                                    style: const TextStyle(fontSize: 12),
                                  ),
                                  Text(
                                    'To: ${trip['to_destination'] ?? trip['custom_to'] ?? 'Unknown'}',
                                    style: const TextStyle(fontSize: 12),
                                  ),
                                  Text(
                                    'Date: ${trip['formatted_date']}, Time: ${trip['time'] ?? 'Unknown'}',
                                    style: const TextStyle(fontSize: 12),
                                  ),
                                  Text(
                                    'Requestor: ${trip['requestor_name'] ?? 'Unknown'}',
                                    style: const TextStyle(fontSize: 12),
                                  ),
                                  const Divider(),
                                ],
                              ),
                            )),
                    ],
                  ),
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () async {
                    Navigator.of(context).pop();
                    await _fetchAllRequestTrips();
                    await _fetchAllDriverRejectedTrips();
                    _refreshAllData();
                  },
                  child: Text(AppLocalizations.of(context).refreshData),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(AppLocalizations.of(context).close),                ),
              ],
            );
          },
        ),
      );
    }
  } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.of(context)
                  .errorFetchingTripsWithStatusCodes(
                      '${requestResponse.statusCode} / ${driverRejectedResponse.statusCode}')),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                AppLocalizations.of(context).errorFetchingTrips(e.toString())),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
      print('Error fetching trips: $e');
    }
  }

  Future<Map<String, dynamic>?> _lookupDriverByTripCode(String tripCode) async {
    try {
      final response = await ApiService.get('trips/code/$tripCode');
      if (response.statusCode == 200) {
        final data = ApiService.parseResponse(response);
        int? driverId;
        if (data['driver_id'] != null) {
          if (data['driver_id'] is String) {
            driverId = int.tryParse(data['driver_id']);
          } else if (data['driver_id'] is int) {
            driverId = data['driver_id'];
          }
        }
        final String? driverCode = data['driver_code'];
        print(
            'Found driver info for trip $tripCode: driver_id=$driverId, driver_code=$driverCode');
        return {
          'driver_id': driverId,
          'driver_code': driverCode,
        };
      } else {
        print(
            'Failed to look up driver info for $tripCode: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      print('Error looking up driver info for $tripCode: $e');
      return null;
    }
  }

  Future<void> _assignDriverToTrip(
      Trip trip, Driver driver, int durationMinutes) async {
    try {
      final int tripDurationMinutes = durationMinutes;
      print('Using duration minutes: $tripDurationMinutes');
      if (_selectedCar != null) {
        print(
            'Selected car: ${_selectedCar!.carCode} (ID: ${_selectedCar!.carId})');
      } else {
        print('No car selected');
      }
      if (trip.tripCode.isEmpty) {
        throw Exception('Trip code is empty. Cannot assign driver.');
      }
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).assigningDriver),
            duration: const Duration(seconds: 1),
          ),
        );
      }
      if (driver.driverId == -1) {
        print('Assigning trip ${trip.tripCode} to ONLINE TAXI');
        print('Trip duration in minutes: $tripDurationMinutes');
        dynamic response;
        String? totalDuration;
        try {
          final String tripDate = trip.originalDate.isNotEmpty
              ? trip.originalDate
              : DateFormat('yyyy-MM-dd').format(DateTime.now());
          final String tripTime = trip.time.isNotEmpty ? trip.time : '00:00';
          final DateTime tripDateTime =
              DateTime.parse('${tripDate}T$tripTime:00');
          final DateTime endDateTime =
              tripDateTime.add(Duration(minutes: tripDurationMinutes));
          totalDuration = endDateTime.toIso8601String();
          print('Trip start time: $tripDateTime');
          print('Trip end time: $endDateTime');
          print('Total duration (end time) ISO string: $totalDuration');
          response = await ApiService.put(
            'trips/code/${trip.tripCode}',
            {
              'status': 'ASSIGN TO ONLINE TAXI',
              'isManagerAction': true,
            },
          );
          if (response.statusCode == 200) {
            final tripId =
                trip.tripId ?? await _getTripIdFromCode(trip.tripCode);
            if (tripId != null) {
              print('Updating trip duration for online taxi trip ID: $tripId');
              print('Duration in minutes: $tripDurationMinutes');
              print('Total duration (end time) to save: $totalDuration');
              final durationResponse = await ApiService.put(
                'trips/$tripId/duration',
                {
                  'total_duration': totalDuration,
                },
              );
              if (durationResponse.statusCode == 200) {
                print('Trip duration updated successfully for online taxi');
                print('Response: ${durationResponse.body}');
              } else {
                print(
                    'Failed to update trip duration for online taxi: ${durationResponse.statusCode}');
                print('Response body: ${durationResponse.body}');
              }
            } else {
              print('Could not find trip ID for trip code ${trip.tripCode}');
            }
          }
        } catch (e) {
          print(
              'Error calculating or updating trip duration for online taxi: $e');
          response = await ApiService.put(
            'trips/code/${trip.tripCode}',
            {
              'status': 'ASSIGN TO ONLINE TAXI',
              'isManagerAction': true,
            },
          );
        }
        if (response.statusCode == 200) {
          final responseData = jsonDecode(response.body);
          print('Update status response: $responseData');
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(AppLocalizations.of(context)
                    .tripAssignedToOnlineTaxiSuccess(trip.tripCode)),
                backgroundColor: Colors.green,
                duration: const Duration(seconds: 3),
              ),
            );
            _refreshAllData();
          }
        } else {
          final errorData = jsonDecode(response.body);
          throw Exception(
              'Failed to update status: ${errorData['error'] ?? 'Unknown error'}');
        }
        return;
      }
      print(
          'Assigning driver to trip ${trip.tripCode}: Driver ID=${driver.driverId}');
      print(
          'Sending driver assignment request with driver_id=${driver.driverId}, driver_code=${driver.driverCode}');
      print('Trip duration in minutes: $tripDurationMinutes');
      final String endpoint = 'trips/code/${trip.tripCode}/assign-driver';
      print('Using endpoint: $endpoint');
      DateTime tripDateTime;
      dynamic response;
      String? totalDuration;
      try {
        final String tripDate = trip.originalDate.isNotEmpty
            ? trip.originalDate
            : DateFormat('yyyy-MM-dd').format(DateTime.now());
        final String tripTime = trip.time.isNotEmpty ? trip.time : '00:00';
        tripDateTime = DateTime.parse('${tripDate}T$tripTime:00');
        final DateTime endDateTime =
            tripDateTime.add(Duration(minutes: tripDurationMinutes));
        totalDuration = endDateTime.toIso8601String();
        print('Trip start time: $tripDateTime');
        print('Trip end time: $endDateTime');
        print('Total duration (end time) ISO string: $totalDuration');
        response = await ApiService.put(
          endpoint,
          {
            'driver_id': driver.driverId,
            'driver_code': driver.driverCode,
            'car_id': _selectedCar?.carId,
            'status': 'ASSIGN TO DRIVER',
            'isManagerAction': true,
          },
        );
        if (response.statusCode == 200) {
          final tripId = trip.tripId ?? await _getTripIdFromCode(trip.tripCode);
          if (tripId != null) {
            print('Updating trip duration for trip ID: $tripId');
            print('Duration in minutes: $tripDurationMinutes');
            print('Total duration (end time) to save: $totalDuration');
            final durationResponse = await ApiService.put(
              'trips/$tripId/duration',
              {
                'total_duration': totalDuration,
              },
            );
            if (durationResponse.statusCode == 200) {
              print('Trip duration updated successfully');
              print('Response: ${durationResponse.body}');
            } else {
              print(
                  'Failed to update trip duration: ${durationResponse.statusCode}');
              print('Response body: ${durationResponse.body}');
            }
          } else {
            print('Could not find trip ID for trip code ${trip.tripCode}');
          }
        }
      } catch (e) {
        print('Error calculating or updating trip duration: $e');
        response = await ApiService.put(
          endpoint,
          {
            'driver_id': driver.driverId,
            'driver_code': driver.driverCode,
            'car_id': _selectedCar?.carId,
            'status': 'ASSIGN TO DRIVER',
            'isManagerAction': true,
          },
        );
      }
      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        print('Assign driver response: $responseData');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'Driver ${driver.name} assigned to trip ${trip.tripCode}'
                  '${_selectedCar != null ? ' with car ${_selectedCar!.carCode}' : ''}'
                  '. Status changed to ASSIGN TO DRIVER.'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 3),
            ),
          );
          _refreshAllData();
        }
      } else {
        final errorData = jsonDecode(response.body);
        throw Exception(
            'Failed to assign driver: ${errorData['error'] ?? 'Unknown error'}');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error assigning driver: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
      print('Error assigning driver: $e');
    }
  }

  Future<void> _deleteTrip(Trip trip) async {
    try {
      if (trip.tripCode.isEmpty) {
        throw Exception('Trip code is empty. Cannot delete trip.');
      }
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).deletingTrip),
            duration: const Duration(seconds: 1),
          ),
        );
      }
      print('Updating trip ${trip.tripCode} status to DELETED');
      final response = await ApiService.put(
        'trips/code/${trip.tripCode}',
        {
          'status': 'DELETED',
          'isManagerAction': true,
        },
      );
      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        print('Delete trip response: $responseData');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Trip ${trip.tripCode} has been deleted.'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 3),
            ),
          );
          _refreshAllData();
        }
      } else {
        final errorData = jsonDecode(response.body);
        throw Exception(
            'Failed to delete trip: ${errorData['error'] ?? 'Unknown error'}');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                AppLocalizations.of(context).errorDeletingTrip(e.toString())),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
      print('Error deleting trip: $e');
    }
  }

  void _showDeleteConfirmDialog(Trip trip) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(AppLocalizations.of(context)
              .deleteTripConfirmation(trip.tripCode)),
          content: Text(
            AppLocalizations.of(context).deleteTripWarning,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(AppLocalizations.of(context).cancel),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _deleteTrip(trip);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: Text(AppLocalizations.of(context).delete),
            ),
          ],
        );
      },
    );
  }

  void _showDeleteConfirmDialogFromAllRequests(
      BuildContext dialogContext, String tripCode) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
              AppLocalizations.of(context).deleteTripConfirmation(tripCode)),
          content: Text(
            AppLocalizations.of(context).deleteTripWarning,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(AppLocalizations.of(context).cancel),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _deleteTripByCode(tripCode, dialogContext);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: Text(AppLocalizations.of(context).delete),
            ),
          ],
        );
      },
    );
  }

  Future<void> _deleteTripByCode(
      String tripCode, BuildContext dialogContext) async {
    try {
      if (tripCode.isEmpty) {
        throw Exception('Trip code is empty. Cannot delete trip.');
      }
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).deletingTrip),
            duration: const Duration(seconds: 1),
          ),
        );
      }
      print('Updating trip $tripCode status to DELETED');
      final response = await ApiService.put(
        'trips/code/$tripCode',
        {
          'status': 'DELETED',
          'isManagerAction': true,
        },
      );
      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        print('Delete trip response: $responseData');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Trip $tripCode has been deleted.'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 3),
            ),
          );
          final bool needsRefresh = true;
          if (dialogContext.mounted) {
            Navigator.of(dialogContext).pop();
            if (needsRefresh) {
              _refreshAllData();
              _showAllRequestTrips();
            }
          }
        }
      } else {
        final errorData = jsonDecode(response.body);
        throw Exception(
            'Failed to delete trip: ${errorData['error'] ?? 'Unknown error'}');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                AppLocalizations.of(context).errorDeletingTrip(e.toString())),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
      print('Error deleting trip: $e');
    }
  }

  Widget _buildTripRequestItem(Trip trip) {
    return ListTile(
      dense: true,
      title: Text(
        'Trip ${trip.tripCode}',
        style: const TextStyle(fontWeight: FontWeight.bold),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${trip.fromDestination} to ${trip.toDestination}',
            style: const TextStyle(fontSize: 12),
          ),
          Text(
            'Date: ${trip.date} | Time: ${_formatTimeString(trip.time)}',
            style: TextStyle(fontSize: 11, color: Colors.grey.shade700),
          ),
          Text(
            'Requestor: ${trip.requestorName}',
            style: TextStyle(fontSize: 11, color: Colors.grey.shade700),
          ),
        ],
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          ElevatedButton(
            onPressed: () {
              _showDeleteConfirmDialog(trip);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red.shade700,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 6),
              minimumSize: const Size(60, 28),
              textStyle: const TextStyle(fontSize: 11),
            ),
            child: Text(AppLocalizations.of(context).deleteAction),
          ),
          const SizedBox(width: 4),
          ElevatedButton(
            onPressed: () {
              _showSetDriverDialog(trip);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF0D47A1),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 6),
              minimumSize: const Size(60, 28),
              textStyle: const TextStyle(fontSize: 11),
            ),
            child: Text(AppLocalizations.of(context).assignAction),
          ),
        ],
      ),
    );
  }

  void _showRejectionReasonDialog(Trip trip) {
    print('_showRejectionReasonDialog called for trip ${trip.tripCode}');
    print('Rejection reason: ${trip.rejectionReason}');
    final String messageText =
        trip.rejectionReason != null && trip.rejectionReason!.isNotEmpty
            ? AppLocalizations.of(context).tripRejectedWithReason
            : AppLocalizations.of(context).tripRejectedNoReason;
    final bool hasReason =
        trip.rejectionReason != null && trip.rejectionReason!.isNotEmpty;
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(AppLocalizations.of(context).rejectionReason),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                AppLocalizations.of(context).tripCodeLabel(trip.tripCode),
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              Text(
                messageText,
                style: const TextStyle(fontStyle: FontStyle.italic),
              ),
              if (hasReason) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: Text(
                    trip.rejectionReason!,
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
              ],
            ],
          ),
          actions: [
            TextButton(
              child: Text(AppLocalizations.of(context).close),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ],
        );
      },
    );
  }

  Widget _buildDriverRejectedTripItem(Trip trip) {
    return ListTile(
      dense: true,
      title: Text(
        'Trip ${trip.tripCode}',
        style: const TextStyle(fontWeight: FontWeight.bold),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${trip.fromDestination} to ${trip.toDestination}',
            style: const TextStyle(fontSize: 12),
          ),
          Text(
            'Date: ${trip.date} | Time: ${_formatTimeString(trip.time)}',
            style: TextStyle(fontSize: 11, color: Colors.grey.shade700),
          ),
          Text(
            'Requestor: ${trip.requestorName}',
            style: TextStyle(fontSize: 11, color: Colors.grey.shade700),
          ),
          Text(
            AppLocalizations.of(context).statusDriverRejected,
            style: TextStyle(
                fontSize: 11,
                color: Colors.red.shade700,
                fontWeight: FontWeight.bold),
          ),
        ],
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          ElevatedButton(
            onPressed: () => _showRejectionReasonDialog(trip),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red.shade50,
              foregroundColor: Colors.red.shade900,
              padding: const EdgeInsets.symmetric(horizontal: 6),
              minimumSize: const Size(60, 28),
              textStyle: const TextStyle(fontSize: 11),
            ),
            child: Text(AppLocalizations.of(context).viewReason),
          ),
          const SizedBox(width: 4),
          ElevatedButton(
            onPressed: () {
              _showDeleteConfirmDialog(trip);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red.shade700,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 6),
              minimumSize: const Size(60, 28),
              textStyle: const TextStyle(fontSize: 11),
            ),
            child: Text(AppLocalizations.of(context).deleteAction),
          ),
          const SizedBox(width: 4),
          ElevatedButton(
            onPressed: () {
              _showSetDriverDialog(trip);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF0D47A1),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 6),
              minimumSize: const Size(60, 28),
              textStyle: const TextStyle(fontSize: 11),
            ),
            child: Text(AppLocalizations.of(context).assignAction),
          ),
        ],
      ),
    );
  }

  Widget _buildScheduleCell(String driverCode, String timeSlot) {
    final List<DriverSchedule> schedules =
        _getSchedulesForTimeSlot(driverCode, timeSlot);
    int driverId = 0;
    for (var driver in _drivers) {
      if (driver.driverCode == driverCode) {
        driverId = driver.driverId;
        break;
      }
    }
    String driverStatus = 'AVAILABLE';
    if (driverId > 0 && _driverAvailabilityStatus.containsKey(driverId)) {
      driverStatus = _driverAvailabilityStatus[driverId]!;
    }
    final now = DateTime.now().toLocal();
    final bool isCurrentTimeSlot = timeSlot == _currentTimeSlot &&
        _selectedDate.year == now.year &&
        _selectedDate.month == now.month &&
        _selectedDate.day == now.day;
    final int hour = int.parse(timeSlot.split(':')[0]);
    Color cellBackgroundColor;
    if (driverStatus == 'UNAVAILABLE') {
      cellBackgroundColor = Colors.red.shade50;
    } else {
      if (hour >= 0 && hour < 6) {
        cellBackgroundColor = Colors.indigo.shade50;
      } else if (hour >= 6 && hour < 12) {
        cellBackgroundColor = Colors.yellow.shade50;
      } else if (hour >= 12 && hour < 18) {
        cellBackgroundColor = Colors.orange.shade50;
      } else {
        cellBackgroundColor = Colors.purple.shade50;
      }
    }
    if (schedules.isNotEmpty) {
      if (schedules.length == 1) {
        final schedule = schedules.first;
        final List<String> timeParts = timeSlot.split(':');
        final int slotHour = int.parse(timeParts[0]);
        final int slotMinute = int.parse(timeParts[1]);
        final int scheduleHour = schedule.startTime.hour;
        final int scheduleMinute = schedule.startTime.minute;
        final bool isStartTime = (scheduleHour == slotHour) &&
            ((slotMinute == 0 && scheduleMinute < 30) ||
                (slotMinute == 30 && scheduleMinute >= 30));
        print(
            'Schedule ${schedule.tripCode} - timeSlot: $timeSlot, startTime: ${schedule.formatTime(schedule.startTime)}, isStartTime: $isStartTime');
        Color backgroundColor;
        Color borderColor;
        Color textColor;
        if (schedule.status == 'COMPLETED') {
          backgroundColor =
              isStartTime ? Colors.purple.shade100 : Colors.purple.shade50;
          borderColor =
              isCurrentTimeSlot ? Colors.blue : Colors.purple.shade300;
          textColor = Colors.purple.shade800;
        } else if (schedule.status == 'TRIP IN PROGRESS') {
          backgroundColor =
              isStartTime ? Colors.amber.shade100 : Colors.amber.shade50;
          borderColor = isCurrentTimeSlot ? Colors.blue : Colors.amber.shade300;
          textColor = Colors.amber.shade800;
        } else if (schedule.status == 'WAITING FOR RATING') {
          backgroundColor =
              isStartTime ? Colors.teal.shade100 : Colors.teal.shade50;
          borderColor = isCurrentTimeSlot ? Colors.blue : Colors.teal.shade300;
          textColor = Colors.teal.shade800;
        } else if (schedule.status == 'ASSIGN TO DRIVER' &&
            schedule.driverCode != 'UNASSIGNED' &&
            schedule.driverCode.isNotEmpty) {
          backgroundColor =
              isStartTime ? Colors.green.shade100 : Colors.green.shade50;
          borderColor = isCurrentTimeSlot ? Colors.blue : Colors.green.shade300;
          textColor = Colors.green.shade800;
        } else if (schedule.status == 'ASSIGN TO DRIVER' &&
            (schedule.driverCode == 'UNASSIGNED' ||
                schedule.driverCode.isEmpty)) {
          backgroundColor =
              isStartTime ? Colors.orange.shade100 : Colors.orange.shade50;
          borderColor =
              isCurrentTimeSlot ? Colors.blue : Colors.orange.shade300;
          textColor = Colors.orange.shade800;
        } else if (schedule.status == 'ASSIGN TO ONLINE TAXI' ||
            schedule.driverCode == 'ONLINE') {
          backgroundColor = isStartTime
              ? Colors.deepOrange.shade100
              : Colors.deepOrange.shade50;
          borderColor =
              isCurrentTimeSlot ? Colors.blue : Colors.deepOrange.shade300;
          textColor = Colors.deepOrange.shade800;
        } else if (schedule.status == 'BACK TO BASE') {
          backgroundColor =
              isStartTime ? Colors.indigo.shade100 : Colors.indigo.shade50;
          borderColor =
              isCurrentTimeSlot ? Colors.blue : Colors.indigo.shade300;
          textColor = Colors.indigo.shade800;
        } else {
          backgroundColor =
              isStartTime ? Colors.blue.shade100 : Colors.blue.shade50;
          borderColor = isCurrentTimeSlot ? Colors.blue : Colors.blue.shade300;
          textColor = Colors.black;
        }
        return drag.buildDraggableScheduleCell(
          context: context,
          schedule: schedule,
          isStartTime: isStartTime,
          backgroundColor: backgroundColor,
          borderColor: borderColor,
          textColor: textColor,
          isCurrentTimeSlot: isCurrentTimeSlot,
          setState: (callback) {
            setState(callback);
          },
          refreshCallback: () {
            _refreshAllData();
          },
        );
      } else {
        return drag.buildMultiScheduleCell(
          context: context,
          schedules: schedules,
          timeSlot: timeSlot,
          isCurrentTimeSlot: isCurrentTimeSlot,
          setState: (callback) {
            setState(callback);
          },
          refreshCallback: () {
            _refreshAllData();
          },
        );
      }
    } else {
      return Container(
        decoration: BoxDecoration(
          color: cellBackgroundColor,
          border: Border.all(
            color: isCurrentTimeSlot ? Colors.blue : Colors.grey.shade300,
            width: isCurrentTimeSlot ? 2.0 : 1.0,
          ),
        ),
        child: driverStatus != 'AVAILABLE'
            ? Center(
                child: Text(
                  driverStatus,
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: Colors.red.shade700,
                  ),
                ),
              )
            : null,
      );
    }
  }

  void _onBottomNavTap(int index) {
    if (index != _currentIndex) {
      switch (index) {
        case 0:
          Navigator.pushReplacementNamed(context, '/trip-manager-dashboard');
        case 1:
        case 2:
          Navigator.pushReplacementNamed(context, '/trip-approval');
        case 3:
          Navigator.pushReplacementNamed(context, '/trip-manager-settings');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600 && !kIsWeb;
    return Scaffold(
      key: _scaffoldKey,
      appBar: CommonAppBar(
        title: AppLocalizations.of(context).tripMonitoringTitle,
        onRefresh: () {
          setState(() {
            lastRefreshTime = DateTime.now();
          });
          refreshData();
        },
        showMenuIcon: true,
      ),
      drawer: const SidebarNavigation(),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage.isNotEmpty
              ? Center(child: Text(_errorMessage))
              : SingleChildScrollView(
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: GestureDetector(
                          onHorizontalDragEnd: (details) {
                            if (details.primaryVelocity! > 0) {
                              _onDateChanged(_selectedDate
                                  .subtract(const Duration(days: 1)));
                            } else if (details.primaryVelocity! < 0) {
                              _onDateChanged(
                                  _selectedDate.add(const Duration(days: 1)));
                            }
                          },
                          child: Row(
                            children: [
                              IconButton(
                                icon: const Icon(Icons.arrow_back),
                                onPressed: () {
                                  _onDateChanged(_selectedDate
                                      .subtract(const Duration(days: 1)));
                                },
                              ),
                              Expanded(
                                child: GestureDetector(
                                  onTap: () async {
                                    final now = DateTime.now();
                                    final DateTime? picked =
                                        await showDatePicker(
                                      context: context,
                                      initialDate: _selectedDate.isAfter(
                                              now.add(const Duration(
                                                  days: 365 * 5)))
                                          ? now
                                          : _selectedDate,
                                      firstDate: DateTime(2020),
                                      lastDate: now
                                          .add(const Duration(days: 365 * 5)),
                                    );
                                    if (picked != null &&
                                        picked != _selectedDate) {
                                      _onDateChanged(picked);
                                    }
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 8.0),
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                      border: Border.all(
                                          color: Colors.grey.shade300),
                                      borderRadius: BorderRadius.circular(4.0),
                                    ),
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Text(
                                          isSmallScreen
                                              ? DateFormat('E, dd-MMM-yy')
                                                  .format(_selectedDate)
                                              : DateFormat('EEEE, dd-MMM-yyyy')
                                                  .format(_selectedDate),
                                          style: const TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        if (isSmallScreen)
                                          Text(
                                            AppLocalizations.of(context)
                                                .swipeToChangeDate,
                                            style: const TextStyle(
                                              fontSize: 12,
                                              color: Colors.grey,
                                            ),
                                          ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                              IconButton(
                                icon: const Icon(Icons.arrow_forward),
                                onPressed: () {
                                  _onDateChanged(_selectedDate
                                      .add(const Duration(days: 1)));
                                },
                              ),
                            ],
                          ),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: isSmallScreen
                            ? Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 16.0),
                                child: Column(
                                  crossAxisAlignment:
                                      CrossAxisAlignment.stretch,
                                  children: [
                                    Row(
                                      children: [
                                        Expanded(
                                          child: ElevatedButton.icon(
                                            onPressed: () {
                                              Navigator.pushNamed(
                                                  context, '/driver-tracking');
                                            },
                                            icon: const Icon(Icons.location_on,
                                                size: 16),
                                            label: Text(
                                                AppLocalizations.of(context)
                                                    .trackDrivers),
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor:
                                                  Colors.green.shade700,
                                              foregroundColor: Colors.white,
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      vertical: 10.0),
                                            ),
                                          ),
                                        ),
                                        const SizedBox(width: 8),
                                        Expanded(
                                          child: ElevatedButton.icon(
                                            onPressed: _refreshAllData,
                                            icon: const Icon(Icons.refresh,
                                                size: 16),
                                            label: Text(
                                                AppLocalizations.of(context)
                                                    .refresh),
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor:
                                                  const Color(0xFF0D47A1),
                                              foregroundColor: Colors.white,
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      vertical: 10.0),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 8),
                                    Stack(
                                      children: [
                                        SizedBox(
                                          width: double.infinity,
                                          child: ElevatedButton.icon(
                                            onPressed: _showAllRequestTrips,
                                            icon: const Icon(Icons.assignment),
                                            label: Text(
                                                AppLocalizations.of(context)
                                                    .allRequests),
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor:
                                                  Colors.purple.shade700,
                                              foregroundColor: Colors.white,
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      vertical: 10.0),
                                            ),
                                          ),
                                        ),
                                        if (_totalRequestCount > 0)
                                          Positioned(
                                            right: 8,
                                            top: 8,
                                            child: Container(
                                              padding:
                                                  const EdgeInsets.all(2.0),
                                              decoration: BoxDecoration(
                                                color: Colors.red,
                                                borderRadius:
                                                    BorderRadius.circular(10.0),
                                              ),
                                              constraints: const BoxConstraints(
                                                minWidth: 20,
                                                minHeight: 20,
                                              ),
                                              child: Text(
                                                '$_totalRequestCount',
                                                style: const TextStyle(
                                                  color: Colors.white,
                                                  fontSize: 12,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                                textAlign: TextAlign.center,
                                              ),
                                            ),
                                          ),
                                      ],
                                    ),
                                  ],
                                ),
                              )
                            : Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  ElevatedButton.icon(
                                    onPressed: () {
                                      Navigator.pushNamed(
                                          context, '/driver-tracking');
                                    },
                                    icon: const Icon(Icons.location_on),
                                    label: Text(AppLocalizations.of(context)
                                        .trackDrivers),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.green.shade700,
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 12.0),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Stack(
                                    children: [
                                      ElevatedButton.icon(
                                        onPressed: _showAllRequestTrips,
                                        icon: const Icon(Icons.assignment),
                                        label: Text(AppLocalizations.of(context)
                                            .allRequests),
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor:
                                              Colors.purple.shade700,
                                          foregroundColor: Colors.white,
                                          padding: const EdgeInsets.only(
                                              right: 16.0, left: 12.0),
                                        ),
                                      ),
                                      if (_totalRequestCount > 0)
                                        Positioned(
                                          right: 0,
                                          top: 0,
                                          child: Container(
                                            padding: const EdgeInsets.all(2.0),
                                            decoration: BoxDecoration(
                                              color: Colors.red,
                                              borderRadius:
                                                  BorderRadius.circular(10.0),
                                            ),
                                            constraints: const BoxConstraints(
                                              minWidth: 20,
                                              minHeight: 20,
                                            ),
                                            child: Text(
                                              '$_totalRequestCount',
                                              style: const TextStyle(
                                                color: Colors.white,
                                                fontSize: 12,
                                                fontWeight: FontWeight.bold,
                                              ),
                                              textAlign: TextAlign.center,
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                  const SizedBox(width: 8),
                                  ElevatedButton.icon(
                                    onPressed: _refreshAllData,
                                    icon: const Icon(Icons.refresh),
                                    label: Text(AppLocalizations.of(context)
                                        .refreshData),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: const Color(0xFF0D47A1),
                                      foregroundColor: Colors.white,
                                    ),
                                  ),
                                ],
                              ),
                      ),
                      RepaintBoundary(
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  width: 120,
                                  height: 40,
                                  alignment: Alignment.centerLeft,
                                  decoration: BoxDecoration(
                                    color:
                                        const Color(0xFF0D47A1).withAlpha(25),
                                    border:
                                        Border.all(color: Colors.grey.shade300),
                                  ),
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 8.0),
                                  child: Text(
                                    AppLocalizations.of(context).driver,
                                    style: const TextStyle(
                                        fontWeight: FontWeight.bold),
                                  ),
                                ),
                                ..._drivers.map((driver) => Container(
                                      width: 120,
                                      height: 90,
                                      alignment: Alignment.centerLeft,
                                      decoration: BoxDecoration(
                                        border: Border.all(
                                            color: Colors.grey.shade300),
                                      ),
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 8.0),
                                      child: Text(
                                        driver.name,
                                        style: const TextStyle(
                                            fontWeight: FontWeight.bold),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    )),
                              ],
                            ),
                            Expanded(
                              child: GestureDetector(
                                onHorizontalDragEnd: isSmallScreen
                                    ? (details) {
                                        if (_horizontalScrollController
                                            .hasClients) {
                                          final currentPosition =
                                              _horizontalScrollController
                                                  .position.pixels;
                                          final maxScroll =
                                              _horizontalScrollController
                                                  .position.maxScrollExtent;
                                          final screenWidth =
                                              MediaQuery.of(context).size.width;
                                          final scrollAmount =
                                              (details.primaryVelocity! < 0)
                                                  ? screenWidth * 0.5
                                                  : -screenWidth * 0.5;
                                          final newPosition =
                                              (currentPosition + scrollAmount)
                                                  .clamp(0.0, maxScroll);
                                          _horizontalScrollController.animateTo(
                                            newPosition,
                                            duration: const Duration(
                                                milliseconds: 300),
                                            curve: Curves.easeOut,
                                          );
                                          setState(() {
                                            _timeSliderValue =
                                                newPosition / maxScroll;
                                          });
                                        }
                                      }
                                    : null,
                                child: SingleChildScrollView(
                                  controller: _horizontalScrollController,
                                  scrollDirection: Axis.horizontal,
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: _timeSlots
                                            .map((timeSlot) => Container(
                                                  width: 180,
                                                  height: 40,
                                                  alignment: Alignment.center,
                                                  decoration: BoxDecoration(
                                                    color:
                                                        const Color(0xFF0D47A1)
                                                            .withAlpha(25),
                                                    border: Border.all(
                                                        color: Colors
                                                            .grey.shade300),
                                                  ),
                                                  child: Text(
                                                    timeSlot,
                                                    style: const TextStyle(
                                                        fontWeight:
                                                            FontWeight.bold),
                                                  ),
                                                ))
                                            .toList(),
                                      ),
                                      ..._drivers.map((driver) => Row(
                                            children: _timeSlots
                                                .map((timeSlot) => SizedBox(
                                                      width: 180,
                                                      height: 90,
                                                      child: _buildScheduleCell(
                                                          driver.driverCode,
                                                          timeSlot),
                                                    ))
                                                .toList(),
                                          )),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16.0, vertical: 8.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  AppLocalizations.of(context)
                                      .navigateTimeSlots,
                                  style: const TextStyle(
                                      fontWeight: FontWeight.bold),
                                ),
                                ElevatedButton.icon(
                                  onPressed: _scrollToCurrentTime,
                                  icon: Icon(Icons.access_time,
                                      size: isSmallScreen ? 16 : 24),
                                  label: Text(isSmallScreen
                                      ? AppLocalizations.of(context).now
                                      : AppLocalizations.of(context)
                                          .currentTime),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: const Color(0xFF0D47A1),
                                    foregroundColor: Colors.white,
                                    padding: EdgeInsets.symmetric(
                                        horizontal: isSmallScreen ? 8.0 : 16.0,
                                        vertical: isSmallScreen ? 4.0 : 8.0),
                                  ),
                                ),
                              ],
                            ),
                            Row(
                              children: [
                                const Text('00:00',
                                    style: TextStyle(fontSize: 12)),
                                Expanded(
                                  child: Slider(
                                    value: _timeSliderValue,
                                    onChanged: (value) {
                                      setState(() {
                                        _timeSliderValue = value;
                                      });
                                      if (_horizontalScrollController
                                          .hasClients) {
                                        final maxScroll =
                                            _horizontalScrollController
                                                .position.maxScrollExtent;
                                        _horizontalScrollController
                                            .jumpTo(maxScroll * value);
                                      }
                                    },
                                    activeColor: const Color(0xFF0D47A1),
                                  ),
                                ),
                                const Text('23:30',
                                    style: TextStyle(fontSize: 12)),
                              ],
                            ),
                            if (isSmallScreen)
                              const Padding(
                                padding: EdgeInsets.only(top: 4.0),
                                child: Text(
                                  'Tip: Swipe horizontally on the schedule to navigate',
                                  style: TextStyle(
                                      fontSize: 12, color: Colors.grey),
                                ),
                              ),
                          ],
                        ),
                      ),
                      Container(
                        margin: const EdgeInsets.symmetric(
                            horizontal: 16.0, vertical: 8.0),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade50,
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(12.0),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          AppLocalizations.of(context)
                                              .tripRequestsForDate(
                                                  DateFormat('EEE, dd-MMM-yy')
                                                      .format(_selectedDate)),
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 16,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Text(
                                    AppLocalizations.of(context)
                                        .requestCount(_tripRequests.length),
                                    style: TextStyle(
                                      color: Colors.grey.shade700,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  IconButton(
                                    icon: const Icon(Icons.refresh),
                                    tooltip: AppLocalizations.of(context)
                                        .refreshTripRequestsTooltip,
                                    onPressed: _refreshAllData,
                                  ),
                                ],
                              ),
                            ),
                            const Divider(height: 1),
                            _tripRequests.isEmpty
                                ? Padding(
                                    padding: const EdgeInsets.all(16.0),
                                    child: Center(
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Text(
                                            AppLocalizations.of(context)
                                                .noTripRequestsForDate,
                                            style: const TextStyle(
                                                color: Colors.grey,
                                                fontSize: 16),
                                          ),
                                          const SizedBox(height: 8),
                                          Text(
                                            AppLocalizations.of(context)
                                                .selectedDateLabel(
                                                    DateFormat('E, dd-MMM-yy')
                                                        .format(_selectedDate)),
                                            style: TextStyle(
                                                color: Colors.grey.shade600,
                                                fontSize: 14),
                                          ),
                                          const SizedBox(height: 16),
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              ElevatedButton(
                                                onPressed: _refreshAllData,
                                                style: ElevatedButton.styleFrom(
                                                  backgroundColor:
                                                      const Color(0xFF0D47A1),
                                                  foregroundColor: Colors.white,
                                                ),
                                                child: Text(
                                                    AppLocalizations.of(context)
                                                        .refreshData),
                                              ),
                                              const SizedBox(width: 8),
                                              Stack(
                                                children: [
                                                  ElevatedButton(
                                                    onPressed: () =>
                                                        _showAllRequestTrips(),
                                                    style: ElevatedButton
                                                        .styleFrom(
                                                      backgroundColor:
                                                          Colors.blue.shade700,
                                                      foregroundColor:
                                                          Colors.white,
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                          horizontal: 16.0),
                                                    ),
                                                    child: Text(
                                                        AppLocalizations.of(
                                                                context)
                                                            .requestTrips),
                                                  ),
                                                  if (_totalRequestCount > 0)
                                                    Positioned(
                                                      right: 0,
                                                      top: 0,
                                                      child: Container(
                                                        padding:
                                                            const EdgeInsets
                                                                .all(2.0),
                                                        decoration:
                                                            BoxDecoration(
                                                          color: Colors.red,
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(
                                                                      10.0),
                                                        ),
                                                        constraints:
                                                            const BoxConstraints(
                                                          minWidth: 20,
                                                          minHeight: 20,
                                                        ),
                                                        child: Text(
                                                          '$_totalRequestCount',
                                                          style:
                                                              const TextStyle(
                                                            color: Colors.white,
                                                            fontSize: 12,
                                                            fontWeight:
                                                                FontWeight.bold,
                                                          ),
                                                          textAlign:
                                                              TextAlign.center,
                                                        ),
                                                      ),
                                                    ),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                  )
                                : ListView.separated(
                                    shrinkWrap: true,
                                    physics:
                                        const NeverScrollableScrollPhysics(),
                                    itemCount: _tripRequests.length,
                                    separatorBuilder: (context, index) =>
                                        const Divider(height: 1),
                                    itemBuilder: (context, index) =>
                                        _buildTripRequestItem(
                                            _tripRequests[index]),
                                  ),
                          ],
                        ),
                      ),
                      Container(
                        margin: const EdgeInsets.symmetric(
                            horizontal: 16.0, vertical: 8.0),
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          border: Border.all(color: Colors.red.shade300),
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(12.0),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          AppLocalizations.of(context)
                                              .driverRejectedTripsForDate(
                                                  DateFormat('EEE, dd-MMM-yy')
                                                      .format(_selectedDate)),
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 16,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Text(
                                    AppLocalizations.of(context)
                                        .tripCount(_driverRejectedTrips.length),
                                    style: TextStyle(
                                      color: Colors.grey.shade700,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  IconButton(
                                    icon: const Icon(Icons.refresh),
                                    tooltip: AppLocalizations.of(context)
                                        .refreshDriverRejectedTripsTooltip,
                                    onPressed: _refreshAllData,
                                  ),
                                ],
                              ),
                            ),
                            const Divider(height: 1),
                            _driverRejectedTrips.isEmpty
                                ? Padding(
                                    padding: const EdgeInsets.all(16.0),
                                    child: Center(
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Text(
                                            AppLocalizations.of(context)
                                                .noDriverRejectedTripsForDate,
                                            style: const TextStyle(
                                                color: Colors.grey,
                                                fontSize: 16),
                                          ),
                                          const SizedBox(height: 8),
                                          Text(
                                            'Selected date: ${DateFormat('E, dd-MMM-yy').format(_selectedDate)}',
                                            style: TextStyle(
                                                color: Colors.grey.shade600,
                                                fontSize: 14),
                                          ),
                                        ],
                                      ),
                                    ),
                                  )
                                : ListView.separated(
                                    shrinkWrap: true,
                                    physics:
                                        const NeverScrollableScrollPhysics(),
                                    itemCount: _driverRejectedTrips.length,
                                    separatorBuilder: (context, index) =>
                                        const Divider(height: 1),
                                    itemBuilder: (context, index) =>
                                        _buildDriverRejectedTripItem(
                                            _driverRejectedTrips[index]),
                                  ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              AppLocalizations.of(context).legend,
                              style:
                                  const TextStyle(fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(height: 8),
                            Wrap(
                              spacing: 16,
                              runSpacing: 8,
                              children: [
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Container(
                                      width: 16,
                                      height: 16,
                                      decoration: BoxDecoration(
                                        color: Colors.blue.shade100,
                                        border: Border.all(
                                            color: Colors.blue.shade300),
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(AppLocalizations.of(context)
                                        .otherTripStatuses),
                                  ],
                                ),
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Container(
                                      width: 16,
                                      height: 16,
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                          colors: [
                                            Colors.blue.shade100,
                                            Colors.purple.shade100
                                          ],
                                        ),
                                        border: Border.all(
                                            color: Colors.grey.shade400),
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(AppLocalizations.of(context)
                                        .multipleTrips),
                                  ],
                                ),
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Container(
                                      width: 16,
                                      height: 16,
                                      decoration: BoxDecoration(
                                        color: Colors.blue.shade50,
                                        border: Border.all(
                                            color: Colors.blue.shade200),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(AppLocalizations.of(context)
                                        .tripDurationLegend),
                                  ],
                                ),
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Container(
                                      width: 16,
                                      height: 16,
                                      decoration: BoxDecoration(
                                        color: Colors.purple.shade100,
                                        border: Border.all(
                                            color: Colors.purple.shade300),
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(AppLocalizations.of(context)
                                        .completedStatus),
                                  ],
                                ),
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Container(
                                      width: 16,
                                      height: 16,
                                      decoration: BoxDecoration(
                                        color: Colors.purple.shade50,
                                        border: Border.all(
                                            color: Colors.purple.shade200),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(AppLocalizations.of(context)
                                        .completedDuration),
                                  ],
                                ),
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Container(
                                      width: 16,
                                      height: 16,
                                      decoration: BoxDecoration(
                                        color: Colors.amber.shade100,
                                        border: Border.all(
                                            color: Colors.amber.shade300),
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(AppLocalizations.of(context)
                                        .tripInProgress),
                                  ],
                                ),
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Container(
                                      width: 16,
                                      height: 16,
                                      decoration: BoxDecoration(
                                        color: Colors.amber.shade50,
                                        border: Border.all(
                                            color: Colors.amber.shade200),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(AppLocalizations.of(context)
                                        .tripInProgressDuration),
                                  ],
                                ),
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Container(
                                      width: 16,
                                      height: 16,
                                      decoration: BoxDecoration(
                                        color: Colors.teal.shade100,
                                        border: Border.all(
                                            color: Colors.teal.shade300),
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(AppLocalizations.of(context)
                                        .waitingForRating),
                                  ],
                                ),
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Container(
                                      width: 16,
                                      height: 16,
                                      decoration: BoxDecoration(
                                        color: Colors.teal.shade50,
                                        border: Border.all(
                                            color: Colors.teal.shade200),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(AppLocalizations.of(context)
                                        .waitingForRatingDuration),
                                  ],
                                ),
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Container(
                                      width: 16,
                                      height: 16,
                                      decoration: BoxDecoration(
                                        color: Colors.green.shade100,
                                        border: Border.all(
                                            color: Colors.green.shade300),
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(AppLocalizations.of(context)
                                        .assignToDriverWithDriver),
                                  ],
                                ),
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Container(
                                      width: 16,
                                      height: 16,
                                      decoration: BoxDecoration(
                                        color: Colors.green.shade50,
                                        border: Border.all(
                                            color: Colors.green.shade200),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(AppLocalizations.of(context)
                                        .assignToDriverDuration),
                                  ],
                                ),
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Container(
                                      width: 16,
                                      height: 16,
                                      decoration: BoxDecoration(
                                        color: Colors.orange.shade100,
                                        border: Border.all(
                                            color: Colors.orange.shade300),
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(AppLocalizations.of(context)
                                        .assignToDriverWithoutDriver),
                                  ],
                                ),
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Container(
                                      width: 16,
                                      height: 16,
                                      decoration: BoxDecoration(
                                        color: Colors.orange.shade50,
                                        border: Border.all(
                                            color: Colors.orange.shade200),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(AppLocalizations.of(context)
                                        .assignToDriverWithoutDriverDuration),
                                  ],
                                ),
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Container(
                                      width: 16,
                                      height: 16,
                                      decoration: BoxDecoration(
                                        border: Border.all(
                                            color: Colors.red, width: 2.0),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(AppLocalizations.of(context)
                                        .currentTime),
                                  ],
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            Text(
                              AppLocalizations.of(context).timePeriodsTitle,
                              style:
                                  const TextStyle(fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Container(
                                  width: 16,
                                  height: 16,
                                  decoration: BoxDecoration(
                                    color: Colors.indigo.shade50,
                                    border:
                                        Border.all(color: Colors.grey.shade300),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Text(AppLocalizations.of(context).nightPeriod),
                                const SizedBox(width: 16),
                                Container(
                                  width: 16,
                                  height: 16,
                                  decoration: BoxDecoration(
                                    color: Colors.yellow.shade50,
                                    border:
                                        Border.all(color: Colors.grey.shade300),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Text(
                                    AppLocalizations.of(context).morningPeriod),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Container(
                                  width: 16,
                                  height: 16,
                                  decoration: BoxDecoration(
                                    color: Colors.orange.shade50,
                                    border:
                                        Border.all(color: Colors.grey.shade300),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Text(AppLocalizations.of(context)
                                    .afternoonPeriod),
                                const SizedBox(width: 16),
                                Container(
                                  width: 16,
                                  height: 16,
                                  decoration: BoxDecoration(
                                    color: Colors.purple.shade50,
                                    border:
                                        Border.all(color: Colors.grey.shade300),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Text(
                                    AppLocalizations.of(context).eveningPeriod),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
      bottomNavigationBar: isSmallScreen
          ? MobileBottomNav(
              currentIndex: _currentIndex,
              onTap: _onBottomNavTap,
            )
          : null,
    );
  }
}
