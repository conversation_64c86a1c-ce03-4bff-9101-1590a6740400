import 'dart:convert';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import '../generated/l10n/app_localizations.dart';
import '../models/car.dart';
import '../services/api_service.dart';

class AddDriverScreen extends StatefulWidget {
  const AddDriverScreen({super.key});

  @override
  State<AddDriverScreen> createState() => _AddDriverScreenState();
}

class _AddDriverScreenState extends State<AddDriverScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _initialController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _driverCodeController = TextEditingController(); // New controller for driver code
  bool _isLoading = false;
  bool _isLoadingCode = true;
  bool _isLoadingCars = true;
  List<Car> _cars = [];
  String? _selectedCarCode;

  @override
  void initState() {
    super.initState();
    _fetchNextDriverCode();
    _fetchCars();
  }

  @override
  void dispose() {
    _driverCodeController.dispose(); // Dispose the controller
    super.dispose();
  }

  Future<void> _fetchNextDriverCode() async {
    setState(() => _isLoadingCode = true);
    try {
      final response = await ApiService.get('drivers/next-code');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        setState(() {
          // Get the driver code from the API response
          final driverCode = data['driverCode'];

          // If the API returns a sequenceNumber, use it to format the code
          if (data.containsKey('sequenceNumber')) {
            final sequenceNumber = data['sequenceNumber'];
            _driverCodeController.text = 'DRV${sequenceNumber.toString().padLeft(4, '0')}';
          } else {
            // Fallback to the provided code with validation
            if (!driverCode.startsWith('DRV')) {
              _driverCodeController.text = 'DRV${driverCode.replaceAll(RegExp(r'^[A-Za-z]+'), '')}';
            } else {
              _driverCodeController.text = driverCode;
            }
          }
          _isLoadingCode = false;
        });
      } else {        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.of(context).failedToGenerateDriverCode),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('An error occurred: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _fetchCars() async {
    setState(() => _isLoadingCars = true);
    try {
      // Use the available cars endpoint to get only cars that are not assigned to any driver
      final response = await ApiService.get('available-cars');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        setState(() {
          _cars = (data['cars'] as List).map((json) => Car.fromJson(json)).toList();
          _isLoadingCars = false;
        });
      } else {        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.of(context).failedToLoadCars),
              backgroundColor: Colors.red,
            ),
          );
        }      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${AppLocalizations.of(context).anErrorOccurred}: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final response = await ApiService.post(
        'drivers',
        {
          'driver_code': _driverCodeController.text, // Use the generated driver code
          'name': _nameController.text.toUpperCase(),
          'initial': _initialController.text.toUpperCase(),
          'car_code': _selectedCarCode,
          'password': _passwordController.text,
        },
      );

      if (mounted) {
        if (response.statusCode == 201) {
          Navigator.pop(context, true);
        } else {
          final errorData = jsonDecode(response.body);          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(errorData['error'] ?? AppLocalizations.of(context).failedToAddDriver),
              backgroundColor: Colors.red,
            ),
          );
        }
      }    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${AppLocalizations.of(context).anErrorOccurred}: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(      appBar: AppBar(
        backgroundColor: const Color(0xFF0D47A1),
        foregroundColor: Colors.white,
        title: Text(AppLocalizations.of(context).addDriver),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Form(
                  key: _formKey,                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      TextFormField(
                        controller: _driverCodeController,
                        decoration: InputDecoration(
                          labelText: AppLocalizations.of(context).driverCode,
                          hintText: _isLoadingCode ? AppLocalizations.of(context).generatingCode : null,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          filled: true,
                          fillColor: Colors.grey[200],
                          suffixIcon: _isLoadingCode
                            ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: Center(
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                  ),
                                ),
                              )
                            : const Icon(Icons.lock_outline),
                        ),
                        enabled: false,                        style: const TextStyle(
                          color: Colors.black87,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),TextFormField(
                        controller: _nameController,
                        decoration: InputDecoration(
                          labelText: AppLocalizations.of(context).name,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          helperText: AppLocalizations.of(context).nameUppercaseHelper,
                        ),
                        textCapitalization: TextCapitalization.characters,
                        onChanged: (value) {
                          // Convert to uppercase as user types
                          final cursorPos = _nameController.selection;
                          _nameController.text = value.toUpperCase();
                          // Maintain cursor position after text change
                          _nameController.selection = cursorPos;
                        },                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return AppLocalizations.of(context).pleaseEnterName;
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),                      TextFormField(
                        controller: _initialController,
                        decoration: InputDecoration(
                          labelText: AppLocalizations.of(context).initial,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          helperText: AppLocalizations.of(context).mustBe3Characters,
                        ),
                        textCapitalization: TextCapitalization.characters,
                        maxLength: 3,
                        onChanged: (value) {
                          // Convert to uppercase as user types
                          final cursorPos = _initialController.selection;
                          _initialController.text = value.toUpperCase();
                          // Maintain cursor position after text change
                          _initialController.selection = cursorPos;
                        },                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return AppLocalizations.of(context).initialRequired;
                          }
                          if (value.length != 3) {
                            return AppLocalizations.of(context).initialMustBe3Characters;
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      DropdownSearch<Car>(                        popupProps: PopupProps.menu(
                          showSearchBox: true,
                          searchFieldProps: TextFieldProps(
                            decoration: InputDecoration(
                              hintText: AppLocalizations.of(context).searchForACar,
                              prefixIcon: const Icon(Icons.search),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          ),
                          itemBuilder: (context, car, isSelected) {
                            return ListTile(
                              selected: isSelected,
                              title: Text('${car.carCode} - ${car.manufacturer} ${car.modelName}'),
                              subtitle: Text('Type: ${car.type}, Plate: ${car.plateNumber}'),
                            );
                          },
                        ),                        dropdownDecoratorProps: DropDownDecoratorProps(
                          dropdownSearchDecoration: InputDecoration(
                            labelText: AppLocalizations.of(context).carCode,
                            hintText: _isLoadingCars ? AppLocalizations.of(context).loadingCars : AppLocalizations.of(context).selectACar,
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                        ),
                        enabled: !_isLoadingCars,
                        items: _cars,
                        itemAsString: (Car car) => '${car.carCode} - ${car.manufacturer} ${car.modelName}',
                        onChanged: (Car? car) {
                          setState(() {
                            _selectedCarCode = car?.carCode;
                          });
                        },
                        selectedItem: _selectedCarCode != null
                          ? _cars.firstWhere(
                              (car) => car.carCode == _selectedCarCode,
                              orElse: () => Car(
                                carCode: '',
                                manufacturer: '',
                                modelName: '',
                                odometer: 0,
                                type: '',
                                plateNumber: '',
                                color: '',
                              ),
                            )
                          : null,                        validator: (value) {
                          if (value == null) {
                            return AppLocalizations.of(context).pleaseSelectACar;
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),                      TextFormField(
                        controller: _passwordController,
                        decoration: InputDecoration(
                          labelText: AppLocalizations.of(context).password,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        obscureText: true,                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return AppLocalizations.of(context).pleaseEnterPassword;
                          }
                          if (value.length < 6) {
                            return AppLocalizations.of(context).passwordMinLength;
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),                      TextFormField(
                        controller: _confirmPasswordController,
                        decoration: InputDecoration(
                          labelText: AppLocalizations.of(context).confirmPassword,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        obscureText: true,                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return AppLocalizations.of(context).pleaseConfirmPassword;
                          }
                          if (value != _passwordController.text) {
                            return AppLocalizations.of(context).passwordsDoNotMatch;
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton(
                        onPressed: _submitForm,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Color(0xFF0D47A1),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        child: Text(AppLocalizations.of(context).addDriver),
                      ),
                    ],
                  ),
                ),
              ),
            ),
    );
  }
}











