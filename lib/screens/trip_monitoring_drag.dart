import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../generated/l10n/app_localizations.dart';
import '../models/driver_schedule.dart';
import '../models/passenger.dart';
import '../models/trip.dart';
import '../models/trip_cargo.dart';
import '../services/api_service.dart';

/// Fetch car plate number from the schedule
/// This function returns the plate number from the schedule if available,
/// otherwise it returns a default value
/// For BACK TO BASE trips, it returns an empty string to hide car information
String fetchCarPlate(DriverSchedule schedule, BuildContext context) {
  // If the trip status is BACK TO BASE, return an empty string
  if (schedule.status == 'BACK TO BASE') {
    return '';
  }

  // If the schedule has a plate number, use it
  if (schedule.carPlateNumber != null && schedule.carPlateNumber!.isNotEmpty) {
    return schedule.carPlateNumber!;
  }

  // If there's no plate number, return a default value
  return AppLocalizations.of(context).noPlate;
}

/// Check if car information should be displayed for a schedule
/// Returns false for BACK TO BASE trips
bool shouldShowCarInfo(DriverSchedule schedule) {
  return schedule.status != 'BACK TO BASE';
}

/// Get car code for display, taking into account BACK TO BASE status
/// Returns empty string for BACK TO BASE trips
String getCarCodeForDisplay(DriverSchedule schedule) {
  if (schedule.status == 'BACK TO BASE') {
    return '';
  }

  return schedule.carCode ?? '';
}

/// This file contains the implementation of the drag functionality for the trip monitoring screen
/// It should be imported into the trip_monitoring_screen.dart file

/// Update the schedule end time and refresh the UI without triggering a full data refresh
Future<bool> updateScheduleEndTime(
  BuildContext context,
  DriverSchedule schedule,
  DateTime newEndTime,
  Function(Function()) setState
) async {
  if (schedule.tripId == null) {    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(AppLocalizations.of(context).cannotAdjustDurationNoTripId),
        backgroundColor: Colors.red,
      ),
    );
    return false;
  }

  // Store the original end time in case we need to revert
  final originalEndTime = schedule.endTime;

  try {
    // Update the schedule locally first for immediate feedback
    schedule.updateEndTime(newEndTime);

    // Update the UI
    // Update UI with new end time
    setState(() {});

    // Send the update to the server
    final response = await ApiService.put(
      'trips/${schedule.tripId}/duration',
      {
        'total_duration': newEndTime.toIso8601String(),
      },
    );

    if (response.statusCode == 200) {


      // Show success message if the widget is still mounted
      if (context.mounted) {        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).updatedDurationForTrip(schedule.tripCode, schedule.getDurationMinutes().toString())),
            backgroundColor: Colors.green,
          ),
        );
      }

      return true;
    } else {
      // Revert the local change if the server update fails
      schedule.updateEndTime(originalEndTime);
      // Revert UI to original end time
      setState(() {});

      // Show error message if the widget is still mounted
      if (context.mounted) {        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).failedToUpdateDuration(response.statusCode.toString())),
            backgroundColor: Colors.red,
          ),
        );
      }


      return false;
    }
  } catch (e) {
    // Revert the local change if there's an error
    schedule.updateEndTime(originalEndTime);
    // Revert UI to original end time
    setState(() {});

    // Show error message if the widget is still mounted
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(        SnackBar(
          content: Text(AppLocalizations.of(context).errorUpdatingDuration(e.toString())),
          backgroundColor: Colors.red,
        ),
      );
    }


    return false;
  }
}

/// Handle duration adjustment for schedule cells
Future<void> handleScheduleDrag(
  BuildContext context,
  DriverSchedule schedule,
  Function(Function()) setState,
  bool increase
) async {
  if (schedule.tripId == null) {    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(AppLocalizations.of(context).cannotAdjustDuration),
        backgroundColor: Colors.red,
      ),
    );
    return;
  }

  // Add or subtract 30 minutes based on the increase parameter
  final int slotsToAdd = increase ? 1 : -1;

  // Calculate the new end time
  DateTime newEndTime = schedule.endTime.add(Duration(minutes: slotsToAdd * 30));

  // Ensure the end time is not before the start time
  if (newEndTime.isBefore(schedule.startTime)) {
    newEndTime = schedule.startTime.add(const Duration(minutes: 30));
  }
  // Show loading indicator
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Text(AppLocalizations.of(context).updatingTripDuration),
      duration: const Duration(seconds: 1),
    ),
  );

  // Update the schedule end time
  await updateScheduleEndTime(context, schedule, newEndTime, setState);
}

/// Build a draggable schedule cell for the trip monitoring screen
Widget buildDraggableScheduleCell({
  required BuildContext context,
  required DriverSchedule schedule,
  required bool isStartTime,
  required Color backgroundColor,
  required Color borderColor,
  required Color textColor,
  required bool isCurrentTimeSlot,
  required Function(Function()) setState,
  Function? refreshCallback,
}) {
  // Use the original isStartTime parameter which is correctly determined in _buildScheduleCell
  if (isStartTime) {
    // If it's the start time, show the trip details directly in the cell
    // Wrap the entire cell in a GestureDetector to show trip details dialog when clicked
    return GestureDetector(
      onTap: () => showTripDetailsDialog(context, schedule, setState: setState, refreshCallback: refreshCallback),
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          border: Border.all(
            color: borderColor,
            width: isCurrentTimeSlot ? 2.0 : 1.0,
          ),
          borderRadius: BorderRadius.circular(4),
        ),
        // Make the cell fit the content without overflow
        constraints: const BoxConstraints(minHeight: 80),
        padding: const EdgeInsets.all(3),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Trip header with code and duration controls
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Trip code
                Text(
                  schedule.tripCode,
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: textColor,
                  ),
                ),
                // Duration controls
                Container(
                  padding: const EdgeInsets.all(1),
                  decoration: BoxDecoration(
                    color: textColor.withAlpha(25),
                    borderRadius: BorderRadius.circular(2),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Decrease duration button - use GestureDetector to prevent tap propagation
                      GestureDetector(
                        onTap: () async {
                          await handleScheduleDrag(context, schedule, setState, false);
                        },
                        behavior: HitTestBehavior.opaque, // Ensure the tap is captured
                        child: Icon(
                          Icons.remove,
                          size: 14,
                          color: textColor,
                        ),
                      ),
                      const SizedBox(width: 2),
                      // Increase duration button - use GestureDetector to prevent tap propagation
                      GestureDetector(
                        onTap: () async {
                          await handleScheduleDrag(context, schedule, setState, true);
                        },
                        behavior: HitTestBehavior.opaque, // Ensure the tap is captured
                        child: Icon(
                          Icons.add,
                          size: 14,
                          color: textColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const Divider(height: 4, thickness: 0.5),
            // Trip details in two columns
            Expanded(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Left column
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // From destination
                        RichText(
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,                          text: TextSpan(
                            style: TextStyle(fontSize: 8, color: textColor),
                            children: [
                              TextSpan(text: AppLocalizations.of(context).fromLabel, style: const TextStyle(fontWeight: FontWeight.bold)),
                              TextSpan(text: schedule.fromDestinationInitial ?? schedule.fromDestination),
                            ],
                          ),
                        ),
                        // To destination
                        RichText(
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,                          text: TextSpan(
                            style: TextStyle(fontSize: 8, color: textColor),
                            children: [
                              TextSpan(text: AppLocalizations.of(context).toLabel, style: const TextStyle(fontWeight: FontWeight.bold)),
                              TextSpan(text: schedule.toDestinationInitial ?? schedule.toDestination),
                            ],
                          ),
                        ),
                        // Status
                        RichText(
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                          text: TextSpan(
                            style: TextStyle(fontSize: 8, color: textColor, fontWeight: FontWeight.bold),                            children: [
                              TextSpan(text: AppLocalizations.of(context).statusLabel),
                              TextSpan(text: schedule.status),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Right column
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Driver or Online indicator
                        RichText(
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                          text: TextSpan(
                            style: TextStyle(fontSize: 8, color: textColor),                            children: [
                              TextSpan(
                                text: schedule.driverCode == 'ONLINE' ? AppLocalizations.of(context).onlineLabel : AppLocalizations.of(context).driverLabel,
                                style: const TextStyle(fontWeight: FontWeight.bold)
                              ),
                              TextSpan(
                                text: schedule.driverCode == 'ONLINE' ? AppLocalizations.of(context).taxiText : schedule.driverName
                              ),
                            ],
                          ),
                        ),
                        // Duration
                        RichText(
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                          text: TextSpan(
                            style: TextStyle(fontSize: 8, color: textColor),                            children: [
                              TextSpan(text: AppLocalizations.of(context).durationLabel, style: const TextStyle(fontWeight: FontWeight.bold)),
                              TextSpan(text: '${schedule.getDurationMinutes()} min'),
                            ],
                          ),
                        ),
                        // Car - without background but with plate number - hide for BACK TO BASE trips
                        if (shouldShowCarInfo(schedule))
                          Padding(
                            padding: const EdgeInsets.only(top: 2),
                            child: RichText(
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                              text: TextSpan(
                                style: TextStyle(fontSize: 9, color: textColor),                                children: [
                                  TextSpan(text: AppLocalizations.of(context).carLabel, style: const TextStyle(fontWeight: FontWeight.bold)),                                  TextSpan(
                                    text: getCarCodeForDisplay(schedule).isNotEmpty
                                        ? '${getCarCodeForDisplay(schedule)} (${fetchCarPlate(schedule, context)})'
                                        : AppLocalizations.of(context).noCarAssigned,
                                    style: const TextStyle(fontWeight: FontWeight.bold),
                                  ),
                                ],
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  } else {
    // For continuation cells, show a simpler version with continuation indicator
    return GestureDetector(
      onTap: () => showTripDetailsDialog(context, schedule, setState: setState, refreshCallback: refreshCallback),
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          border: Border.all(
            color: borderColor,
            width: isCurrentTimeSlot ? 2.0 : 1.0,
          ),
        ),
        padding: const EdgeInsets.all(2),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Continuation indicator with online status if applicable
            Padding(
              padding: const EdgeInsets.only(left: 4),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    '${schedule.tripCode} (cont.)',
                    style: TextStyle(
                      fontSize: 8,
                      fontStyle: FontStyle.italic,
                      color: textColor,
                    ),
                  ),                  if (schedule.driverCode == 'ONLINE')
                    Text(
                      AppLocalizations.of(context).onlineTaxiText,
                      style: TextStyle(
                        fontSize: 7,
                        fontWeight: FontWeight.bold,
                        color: textColor,
                      ),
                    ),
                  // Show car information without background - hide for BACK TO BASE trips
                  if (shouldShowCarInfo(schedule))
                    Padding(
                      padding: const EdgeInsets.only(top: 2),
                      child: Text(
                        'Car: ${getCarCodeForDisplay(schedule).isNotEmpty ? '${getCarCodeForDisplay(schedule)} (${fetchCarPlate(schedule, context)})' : AppLocalizations.of(context).noCar}',
                        style: TextStyle(
                          fontSize: 8,
                          fontWeight: FontWeight.bold,
                          color: textColor,
                        ),
                      ),
                    ),
                ],
              ),
            ),
            // Duration controls
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Decrease duration button
                GestureDetector(
                  onTap: () async {
                    await handleScheduleDrag(context, schedule, setState, false);
                  },
                  behavior: HitTestBehavior.opaque, // Ensure the tap is captured
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: textColor.withAlpha(25),
                      borderRadius: BorderRadius.circular(2),
                    ),
                    child: Icon(
                      Icons.remove,
                      size: 12,
                      color: textColor.withAlpha(179),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // Increase duration button
                GestureDetector(
                  onTap: () async {
                    await handleScheduleDrag(context, schedule, setState, true);
                  },
                  behavior: HitTestBehavior.opaque, // Ensure the tap is captured
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: textColor.withAlpha(25),
                      borderRadius: BorderRadius.circular(2),
                    ),
                    child: Icon(
                      Icons.add,
                      size: 12,
                      color: textColor.withAlpha(179),
                    ),
                  ),
                ),
                const SizedBox(width: 4),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// Update trip status to REQUEST
Future<void> updateTripStatusToRequest(BuildContext context, DriverSchedule schedule, Function(Function())? setState, {Function? refreshCallback}) async {
  try {
    // Check if trip code is valid
    if (schedule.tripCode.isEmpty) {
      throw Exception('Trip code is empty. Cannot update status.');
    }

    // Show loading indicator if widget is still mounted
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context).updatingTripStatus),
          duration: const Duration(seconds: 1),
        ),
      );
    }    // Update trip status using trip code
    final response = await ApiService.put(
      'trips/code/${schedule.tripCode}',
      {
        'status': 'REQUEST',
        'isManagerAction': true, // Flag to indicate this is a manager action
      },
    );

    if (response.statusCode == 200) {
      // Check if widget is still mounted before using context
      if (context.mounted) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).tripStatusChangedToRequest(schedule.tripCode)),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );

        // Close the dialog
        Navigator.of(context).pop();

        // Refresh the data if setState is provided
        if (setState != null) {
          // This will trigger a refresh in the parent widget
          setState(() {
            // This empty setState will trigger a rebuild of the parent widget
          });
        }

        // Call the refreshCallback if provided
        if (refreshCallback != null) {
          // Call the parent's _refreshAllData method to fully refresh all data
          refreshCallback.call();
        } else {
          // If no refreshCallback is provided, show a snackbar
          Future.delayed(Duration.zero, () {
            if (context.mounted) {
              final scaffoldState = ScaffoldMessenger.of(context);
              scaffoldState.showSnackBar(
                SnackBar(
                  content: Text(AppLocalizations.of(context).refreshingAllData),
                  duration: const Duration(seconds: 1),
                ),
              );
            }
          });
        }
      }
    } else {
      final errorData = jsonDecode(response.body);
      throw Exception('Failed to update status: ${errorData['error'] ?? 'Unknown error'}');
    }
  } catch (e) {
    // Show error message if widget is still mounted
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context).errorGeneric(e.toString())),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }
}

/// Update trip status to DELETED
Future<void> updateTripStatusToDeleted(BuildContext context, DriverSchedule schedule, Function(Function())? setState, {Function? refreshCallback}) async {
  try {
    // Check if trip code is valid
    if (schedule.tripCode.isEmpty) {
      throw Exception('Trip code is empty. Cannot delete trip.');
    }    // Show loading indicator if widget is still mounted
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context).deletingTripMessage),
          duration: const Duration(seconds: 1),
        ),
      );
    }    // Update trip status using trip code
    final response = await ApiService.put(
      'trips/code/${schedule.tripCode}',
      {
        'status': 'DELETED',
        'isManagerAction': true, // Flag to indicate this is a manager action
      },
    );

    if (response.statusCode == 200) {
      // Check if widget is still mounted before using context
      if (context.mounted) {        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).tripHasBeenDeleted(schedule.tripCode)),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );

        // Close the dialog
        Navigator.of(context).pop();

        // Refresh the data if setState is provided
        if (setState != null) {
          // This will trigger a refresh in the parent widget
          setState(() {
            // This empty setState will trigger a rebuild of the parent widget
          });
        }

        // Call the refreshCallback if provided
        if (refreshCallback != null) {
          // Call the parent's _refreshAllData method to fully refresh all data
          refreshCallback.call();
        } else {
          // If no refreshCallback is provided, show a snackbar
          Future.delayed(Duration.zero, () {
            if (context.mounted) {
              final scaffoldState = ScaffoldMessenger.of(context);              scaffoldState.showSnackBar(
                SnackBar(
                  content: Text(AppLocalizations.of(context).refreshingAllData),
                  duration: const Duration(seconds: 1),
                ),
              );
            }
          });
        }
      }
    } else {
      final errorData = jsonDecode(response.body);
      throw Exception('Failed to delete trip: ${errorData['error'] ?? 'Unknown error'}');
    }
  } catch (e) {
    // Show error message if widget is still mounted
    if (context.mounted) {      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context).errorDeletingTripMessage(e.toString())),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }
}

/// Show confirmation dialog before deleting a trip
void showDeleteConfirmDialog(BuildContext context, DriverSchedule schedule, Function(Function())? setState, {Function? refreshCallback}) {
  showDialog(
    context: context,
    builder: (BuildContext dialogContext) {
      return AlertDialog(        title: Text(AppLocalizations.of(context).deleteTripTitle(schedule.tripCode)),        content: Text(
          AppLocalizations.of(context).deleteTripWarning,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: Text(AppLocalizations.of(context).cancel),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(dialogContext).pop();
              updateTripStatusToDeleted(context, schedule, setState, refreshCallback: refreshCallback);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Text(AppLocalizations.of(context).deleteTrip),
          ),
        ],
      );
    },
  );
}

/// Show a dialog with detailed trip information including passengers and cargo
void showTripDetailsDialog(BuildContext context, DriverSchedule schedule, {Function(Function())? setState, Function? refreshCallback}) async {
  // Fetch complete trip details with passengers and cargo if tripId is available
  List<Passenger>? passengers;
  List<TripCargo>? cargos;
  DriverSchedule updatedSchedule = schedule; // Start with the original schedule
  
  if (schedule.tripId != null) {
    try {
      final response = await ApiService.get('trips/code/${schedule.tripCode}');
      if (response.statusCode == 200) {
        final data = ApiService.parseResponse(response);
          // Debug print for is_waiting field
        print('Trip details API response for ${schedule.tripCode}: is_waiting=${data['is_waiting']}');
        print('Raw API response: ${jsonEncode(data)}');
        
        // Parse passengers from API response
        if (data['passengers'] != null) {
          passengers = (data['passengers'] as List)
              .map((passengerJson) => Passenger.fromJson(passengerJson))
              .toList();
        }

        // Parse cargos from API response
        if (data['cargos'] != null) {
          cargos = (data['cargos'] as List)
              .map((cargoJson) => TripCargo.fromJson(cargoJson))
              .toList();
        }
        
        // Create an updated schedule with fresh is_waiting data from API
        updatedSchedule = DriverSchedule(
          driverId: schedule.driverId,
          driverCode: schedule.driverCode,
          driverName: schedule.driverName,
          tripId: schedule.tripId,
          tripCode: schedule.tripCode,
          startTime: schedule.startTime,
          endTime: schedule.endTime,
          fromDestination: schedule.fromDestination,
          toDestination: schedule.toDestination,
          fromDestinationInitial: schedule.fromDestinationInitial,
          toDestinationInitial: schedule.toDestinationInitial,
          status: schedule.status,
          totalDuration: schedule.totalDuration,
          originalStartTime: schedule.originalStartTime,
          carId: schedule.carId,
          carCode: schedule.carCode,
          carPlateNumber: schedule.carPlateNumber,
          isWaiting: data['is_waiting'] ?? false, // Use fresh data from API
        );
        
        print('Updated schedule for ${schedule.tripCode}: isWaiting=${updatedSchedule.isWaiting}');
        print('About to check condition: updatedSchedule.isWaiting == true is ${updatedSchedule.isWaiting == true}');
      }
    } catch (e) {
      print('Error fetching trip details: $e');
      // Continue with original data if fetch fails
    }  }

  // Check if widget is still mounted before showing dialog
  if (!context.mounted) return;

  // Show the trip details dialog with passengers and cargo
  showDialog(
    context: context,
    builder: (BuildContext context) {
        return Dialog(
          // Make the dialog larger
          insetPadding: const EdgeInsets.symmetric(horizontal: 40.0, vertical: 24.0),
          // Dialog content
          child: Container(
            width: 600, // Set a fixed width
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title with close button
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,                  children: [                    Text(
                      AppLocalizations.of(context).tripDetailsTitle(updatedSchedule.tripCode),
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ],
                ),
                const Divider(),
                // Content in a scrollable container
                Flexible(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Two-column layout for basic info
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Left column
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,                                children: [
                                  // Trip status
                                  _buildDetailRow(AppLocalizations.of(context).statusDetailLabel, updatedSchedule.status),                                  // Show waiting trip indicator if this is a waiting trip
                                  if (updatedSchedule.isWaiting == true) ...[
                                    () {
                                      print('RENDERING WAITING INDICATOR for ${updatedSchedule.tripCode}');
                                      return Padding(
                                        padding: const EdgeInsets.only(bottom: 8.0),
                                        child: Row(
                                          children: [
                                            Icon(Icons.schedule, size: 16, color: Colors.orange.shade600),
                                            const SizedBox(width: 4),
                                            Container(
                                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                              decoration: BoxDecoration(
                                                color: Colors.orange.shade100,
                                                borderRadius: BorderRadius.circular(4),
                                                border: Border.all(color: Colors.orange.shade300),
                                              ),
                                              child: Text(
                                                AppLocalizations.of(context).waitingTrip,
                                                style: TextStyle(
                                                  color: Colors.orange.shade800,
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 12,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    }(),
                                  ] else ...[
                                    () {
                                      print('NOT RENDERING WAITING INDICATOR for ${updatedSchedule.tripCode}, isWaiting=${updatedSchedule.isWaiting}');
                                      return const SizedBox.shrink();
                                    }(),
                                  ],

                                  // Trip timing
                                  _buildDetailRow(AppLocalizations.of(context).startTimeLabel, DateFormat('HH:mm').format(updatedSchedule.startTime)),
                                  _buildDetailRow(AppLocalizations.of(context).endTimeLabel, DateFormat('HH:mm').format(updatedSchedule.endTime)),
                                  _buildDetailRow(AppLocalizations.of(context).durationDetailLabel, '${updatedSchedule.getDurationMinutes()} ${AppLocalizations.of(context).minutesUnit}'),
                                ],
                              ),
                            ),
                            // Right column
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,                                children: [
                                  // Driver information
                                  _buildDetailRow(AppLocalizations.of(context).driverDetailLabel, updatedSchedule.driverName),
                                  _buildDetailRow(AppLocalizations.of(context).driverCodeDetailLabel, updatedSchedule.driverCode),

                                  // Trip locations
                                  _buildDetailRow(AppLocalizations.of(context).fromDetailLabel, '${updatedSchedule.fromDestination} (${updatedSchedule.fromDestinationInitial ?? ""})'),
                                  _buildDetailRow(AppLocalizations.of(context).toDetailLabel, '${updatedSchedule.toDestination} (${updatedSchedule.toDestinationInitial ?? ""})'),
                                ],
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 16),                        // Additional information section
                        const Divider(),
                        Text(
                          AppLocalizations.of(context).additionalInformation,
                          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                        ),
                        const SizedBox(height: 8),
                        _buildDetailRow(AppLocalizations.of(context).tripIdLabel, schedule.tripId?.toString() ?? AppLocalizations.of(context).notAssigned),

                        // Car information section without background - hide for BACK TO BASE trips
                        if (shouldShowCarInfo(schedule) &&
                            (schedule.carId != null || getCarCodeForDisplay(schedule).isNotEmpty))
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,                            children: [
                              const SizedBox(height: 8),
                              Text(
                                AppLocalizations.of(context).carInformation,
                                style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                              ),
                              const SizedBox(height: 4),
                              if (schedule.carId != null && shouldShowCarInfo(schedule))
                                _buildDetailRow(AppLocalizations.of(context).carIdLabel, schedule.carId.toString()),
                              if (getCarCodeForDisplay(schedule).isNotEmpty)
                                _buildDetailRow(AppLocalizations.of(context).carCodeDetailLabel, getCarCodeForDisplay(schedule)),                              if (getCarCodeForDisplay(schedule).isNotEmpty)
                                _buildDetailRow(AppLocalizations.of(context).plateNumberLabel, fetchCarPlate(schedule, context)),
                            ],
                          ),

                        // Passengers Section
                        if (passengers != null && passengers.isNotEmpty)
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,                            children: [
                              const SizedBox(height: 16),
                              Text(
                                AppLocalizations.of(context).passengersSection,
                                style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                              ),
                              const SizedBox(height: 8),
                              Container(
                                width: double.infinity,
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.blue.shade50,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(color: Colors.blue.shade200),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: passengers.map((passenger) => Padding(
                                    padding: const EdgeInsets.only(bottom: 4),
                                    child: Text(
                                      '• ${passenger.name}',
                                      style: TextStyle(color: Colors.blue.shade800),
                                    ),
                                  )).toList(),
                                ),
                              ),
                            ],
                          ),

                        // Cargo Section
                        if (cargos != null && cargos.isNotEmpty)
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,                            children: [
                              const SizedBox(height: 16),
                              Text(
                                AppLocalizations.of(context).cargoSection,
                                style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                              ),
                              const SizedBox(height: 8),
                              Container(
                                width: double.infinity,
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.orange.shade50,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(color: Colors.orange.shade200),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: cargos.map((cargo) => Padding(
                                    padding: const EdgeInsets.only(bottom: 4),
                                    child: Text(
                                      '• ${cargo.name}',
                                      style: TextStyle(color: Colors.orange.shade800),
                                    ),
                                  )).toList(),
                                ),
                              ),
                            ],
                          ),
                      ],
                    ),
                  ),
                ),
                // Bottom buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Action buttons on the left
                    Row(
                      children: [
                        // Only show the Return to Request button if the status is not already REQUEST
                        if (schedule.status != 'REQUEST')
                          ElevatedButton(
                            onPressed: () {
                              updateTripStatusToRequest(context, schedule, setState, refreshCallback: refreshCallback);
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                            ),
                            child: Text(AppLocalizations.of(context).returnToRequest),
                          ),
                        // Add some spacing if both buttons are shown
                        if (schedule.status != 'REQUEST' && schedule.status != 'DELETED')
                          const SizedBox(width: 8),
                        // Delete button - don't show if status is already DELETED
                        if (schedule.status != 'DELETED')
                          ElevatedButton(
                            onPressed: () {
                              // Show confirmation dialog before deleting
                              showDeleteConfirmDialog(context, schedule, setState, refreshCallback: refreshCallback);
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                              foregroundColor: Colors.white,
                            ),
                            child: Text(AppLocalizations.of(context).deleteTrip),
                          ),
                      ],
                    ),
                    // Close button on the right
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: Text(AppLocalizations.of(context).close),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

/// Helper function to build a detail row
Widget _buildDetailRow(String label, String value) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 4.0),
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Text(
            '$label:',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        Expanded(
          child: Text(value),
        ),
      ],
    ),
  );
}

/// Fetch trip details from the API
Future<Trip?> fetchTripDetails(String? tripId) async {
  if (tripId == null) return null;

  try {
    final response = await ApiService.get(
      'trips/$tripId',
    );

    if (response.statusCode == 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      return Trip.fromJson(data);
    }
  } catch (e) {
    print('Error fetching trip details: $e');
  }

  return null;
}

/// Get color based on trip status for indicators
Color _getStatusColor(String status, bool isStartingHere) {
  if (!isStartingHere) {
    return Colors.grey.shade400;
  }

  switch (status.toUpperCase()) {
    case 'COMPLETED':
      return Colors.purple.shade700;
    case 'ASSIGN TO DRIVER':
      return Colors.green.shade700;
    case 'DRIVER CONFIRMATION':
      return Colors.blue.shade700;
    case 'TRIP IN PROGRESS':
      return Colors.amber.shade700;
    case 'WAITING FOR RATING':
      return Colors.teal.shade700;
    case 'ASSIGN TO ONLINE TAXI':
      return Colors.deepOrange.shade700;
    case 'BACK TO BASE':
      return Colors.indigo.shade700;
    default:
      return Colors.blue.shade700;
  }
}

/// Build a cell that contains multiple schedules
Widget buildMultiScheduleCell({
  required BuildContext context,
  required List<DriverSchedule> schedules,
  required String timeSlot,
  required bool isCurrentTimeSlot,
  required Function(Function()) setState,
  Function? refreshCallback,
}) {
  // Sort schedules by start time to ensure consistent display
  schedules.sort((a, b) => a.startTime.compareTo(b.startTime));

  // Determine if any schedule starts in this time slot
  bool hasStartingSchedule = false;
  for (var schedule in schedules) {
    if (schedule.formatTime(schedule.startTime) == timeSlot) {
      hasStartingSchedule = true;
      break;
    }
  }

  // Determine border color based on current time slot
  final Color borderColor = isCurrentTimeSlot ? Colors.blue : Colors.grey.shade400;

  // Use a gradient background to indicate multiple trips
  return Container(
    decoration: BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: hasStartingSchedule
            ? [Colors.blue.shade100, Colors.purple.shade100]
            : [Colors.blue.shade50, Colors.purple.shade50],
      ),
      border: Border.all(
        color: borderColor,
        width: isCurrentTimeSlot ? 2.0 : 1.0,
      ),
      borderRadius: BorderRadius.circular(4),
    ),
    padding: const EdgeInsets.all(3),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header showing number of trips
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
          decoration: BoxDecoration(
            color: Colors.black.withAlpha(25),
            borderRadius: BorderRadius.circular(2),
          ),          child: Text(
            AppLocalizations.of(context).tripsCount(schedules.length),
            style: const TextStyle(
              fontSize: 9,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        const SizedBox(height: 2),
        // List of trips (limited to first 3 with indicator if more)
        Expanded(
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: schedules.length > 3 ? 4 : schedules.length,
            padding: EdgeInsets.zero,
            itemBuilder: (context, index) {
              // If we're showing the 4th item (index 3) and there are more than 3 schedules,
              // show a "more" indicator instead of the actual schedule
              if (index == 3 && schedules.length > 3) {
                return Container(
                  padding: const EdgeInsets.symmetric(vertical: 1),
                  child: Text(
                    '+ ${schedules.length - 3} more...',
                    style: const TextStyle(fontSize: 8, fontStyle: FontStyle.italic),
                  ),
                );
              }

              // Get the schedule for this index
              final schedule = schedules[index];
              final bool isStartingHere = schedule.formatTime(schedule.startTime) == timeSlot;

              // Create a compact representation of each trip
              return GestureDetector(
                onTap: () => showTripDetailsDialog(context, schedule, setState: setState, refreshCallback: refreshCallback),
                child: Container(
                  margin: const EdgeInsets.only(bottom: 2),
                  padding: const EdgeInsets.symmetric(horizontal: 3, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.white.withAlpha(179),
                    borderRadius: BorderRadius.circular(2),
                    border: Border.all(color: Colors.grey.shade300, width: 0.5),
                  ),
                  child: Row(
                    children: [
                      // Trip indicator
                      Container(
                        width: 3,
                        height: 12,
                        decoration: BoxDecoration(
                          color: _getStatusColor(schedule.status, isStartingHere),
                          borderRadius: BorderRadius.circular(1),
                        ),
                      ),
                      const SizedBox(width: 3),
                      // Trip code and basic info
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Trip code with online indicator if applicable
                            Row(
                              children: [
                                Text(
                                  schedule.tripCode,
                                  style: TextStyle(
                                    fontSize: 7,
                                    fontWeight: FontWeight.bold,
                                    color: _getStatusColor(schedule.status, isStartingHere),
                                  ),
                                ),
                                if (schedule.driverCode == 'ONLINE')
                                  Container(
                                    margin: const EdgeInsets.only(left: 3),
                                    padding: const EdgeInsets.symmetric(horizontal: 2, vertical: 1),
                                    decoration: BoxDecoration(
                                      color: Colors.deepOrange.shade100,
                                      borderRadius: BorderRadius.circular(2),
                                    ),                                    child: Text(
                                      AppLocalizations.of(context).onlineTaxiText,
                                      style: TextStyle(
                                        fontSize: 5,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.deepOrange.shade800,
                                      ),
                                    ),
                                  ),
                                // Always show car information without background
                                Padding(
                                  padding: const EdgeInsets.only(left: 3),
                                  child: Text(
                                    'Car: ${(schedule.carCode != null && schedule.carCode!.isNotEmpty) ? '${schedule.carCode!} (${fetchCarPlate(schedule, context)})' : AppLocalizations.of(context).noCar}',
                                    style: const TextStyle(
                                      fontSize: 6,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            // From/To with initials
                            Text(
                              '${schedule.fromDestinationInitial ?? schedule.fromDestination} → ${schedule.toDestinationInitial ?? schedule.toDestination}',
                              style: const TextStyle(fontSize: 6),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                      // Duration controls - show for all trips, not just starting ones
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          InkWell(
                            onTap: () async {
                              await handleScheduleDrag(context, schedule, setState, false);
                            },
                            child: Icon(
                              Icons.remove,
                              size: 10,
                              color: Colors.grey.shade700,
                            ),
                          ),
                          const SizedBox(width: 2),
                          InkWell(
                            onTap: () async {
                              await handleScheduleDrag(context, schedule, setState, true);
                            },
                            child: Icon(
                              Icons.add,
                              size: 10,
                              color: Colors.grey.shade700,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    ),
  );
}




