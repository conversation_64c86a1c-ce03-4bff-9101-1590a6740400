import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../generated/l10n/app_localizations.dart';
import '../main.dart';
import '../models/trip.dart';
import '../services/api_service.dart';
import '../services/auto_location_tracking_service.dart';
import '../services/background_location_service.dart';
import '../utils/auto_refresh.dart';
import '../widgets/common_app_bar.dart';
import '../widgets/odometer_dialog.dart';

class DriverTripsScreen extends StatefulWidget {
  final String driverCode;
  final Map<String, dynamic>? activeCheckIn;

  const DriverTripsScreen({
    super.key,
    required this.driverCode,
    this.activeCheckIn,
  });

  @override
  DriverTripsScreenState createState() => DriverTripsScreenState();
}

class DriverTripsScreenState extends State<DriverTripsScreen> with AutoRefreshMixin {
  List<Trip> _trips = [];
  List<Trip> _filteredTrips = [];
  bool _isLoading = false;
  String _errorMessage = '';
  DateTime? _selectedDate;
  String? _selectedStatus;
  int _assignToDriverCount = 0;

  // Driver code state (used when driver code is not provided in widget)
  String _driverCode = '';

  // Check-in/checkout state
  bool _hasActiveCheckIn = false;
  Map<String, dynamic> _activeCheckIn = {};

  // Trip completion state
  final TextEditingController _notesController = TextEditingController();

  // List of available statuses for filtering
  // Note: WAITING FOR RATING, COMPLETED, and DRIVER REJECTED are excluded as they're filtered out
  final List<String> _statuses = [
    'ASSIGN TO DRIVER',
    'DRIVER CONFIRMATION',
    'TRIP IN PROGRESS',
    'BACK TO BASE',
  ];

  // Auto tracking service
  final AutoLocationTrackingService _autoTrackingService = AutoLocationTrackingService();

  // Background location service
  final BackgroundLocationService _backgroundService = BackgroundLocationService();


  @override
  void initState() {
    super.initState();

    // If we have a driver code, fetch trips
    if (widget.driverCode.isNotEmpty) {
      // If we already have active check-in data passed from login flow
      if (widget.activeCheckIn != null) {
        setState(() {
          _hasActiveCheckIn = true;
          _activeCheckIn = widget.activeCheckIn!;
        });
        print('Using provided active check-in data: ${widget.activeCheckIn}');

        // We'll recheck tracking status in the delayed call below
        // Now fetch trips since we have confirmed active check-in
        _fetchDriverTrips();
      } else {
        // Otherwise check for active check-in
        print('No active check-in data provided, checking API...');
        _checkForActiveCheckIn();
      }
    } else {
      // If no driver code, try to get it from the user ID
      _getDriverCodeFromUserId();
    }

    // Initialize filtered trips
    _filteredTrips = _trips;

    // Initialize auto-refresh functionality
    initAutoRefresh();

    // Ensure tracking status is in the correct state
    // This helps fix persistence state issues with driver tracking
    Future.delayed(Duration.zero, () {
      if (mounted) {
        _recheckTrackingStatus();
      }
    });
  }

  // Get driver code from user ID if not provided
  Future<void> _getDriverCodeFromUserId() async {
    final appState = Provider.of<MyAppState>(context, listen: false);
    final currentUser = appState.currentUser;

    if (currentUser == null || !currentUser.isDriver) {
      return;
    }

    try {
      print('Getting driver code for user ID: ${currentUser.userId}');

      // Try both endpoints to get the driver code
      // First try the endpoint in server-driver-trips.js
      final response1 = await ApiService.get('drivers/user/${currentUser.userId}');

      print('Driver API response 1 status: ${response1.statusCode}');
      if (response1.statusCode == 200 && mounted) {
        final data = jsonDecode(response1.body);
        print('Driver API response 1 data: $data');
        final driverCode = data['driver_code'];

        if (driverCode != null && driverCode.isNotEmpty) {
          // Update state with driver code
          setState(() {
            // Use a temporary variable since we can't modify widget properties
            _driverCode = driverCode;
          });

          print('Got driver code from endpoint 1: $_driverCode');

          // Check for active check-in first, which will fetch trips if check-in is active
          _checkForActiveCheckIn();
          return;
        }
      } else {
        print('Error response 1: ${response1.body}');
      }

      // If the first endpoint failed, try the second endpoint in server-driver-check-in.js
      final response2 = await ApiService.get('drivers/user/${currentUser.userId}');

      print('Driver API response 2 status: ${response2.statusCode}');
      if (response2.statusCode == 200 && mounted) {
        final data = jsonDecode(response2.body);
        print('Driver API response 2 data: $data');
        final driverCode = data['driver_code'];

        // Update state with driver code
        setState(() {
          // Use a temporary variable since we can't modify widget properties
          _driverCode = driverCode;
        });

        print('Got driver code from endpoint 2: $_driverCode');

        // Check for active check-in first, which will fetch trips if check-in is active
        _checkForActiveCheckIn();
      } else {
        print('Error response 2: ${response2.body}');
        if (mounted) {
          setState(() {
            _errorMessage = 'Failed to get driver information. Please try again.';
          });
        }
      }
    } catch (e) {
      print('Error getting driver code: $e');
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to get driver information: $e';
        });
      }
    }
  }

  @override
  void dispose() {
    // Clean up auto-refresh timers and controllers
    disposeAutoRefresh();
    _notesController.dispose();
    super.dispose();
  }



  // Implement the refreshData method required by AutoRefreshMixin
  @override
  Future<void> refreshData({bool showSnackbar = true}) async {
    print('Refreshing driver trips data with recheck for tracking and check-in status...');

    // First check for active check-in status
    await _checkForActiveCheckIn();

    // Then fetch trips if we're still mounted (check-in might have redirected)
    if (mounted) {
      await _fetchDriverTrips();
    }

    // Recheck tracking status to ensure it's in the correct state
    if (mounted) {
      await _recheckTrackingStatus();
    }

    if (showSnackbar && mounted) {
      showRefreshSnackbar('Trip data refreshed successfully');
    }
  }

  // Check if the driver has an active check-in that hasn't been checked out
  Future<void> _checkForActiveCheckIn() async {
    if (!mounted) return;

    // Use the driver code from widget or from state if widget's is empty
    final driverCode = widget.driverCode.isNotEmpty ? widget.driverCode : _driverCode;

    // Don't proceed if we don't have a driver code
    if (driverCode.isEmpty) {
      print('Cannot check for active check-in: driver code is empty');
      return;
    }

    try {
      print('Checking for active check-in for driver: $driverCode');
      final response = await ApiService.get('drivers/$driverCode/active-check-in');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final hasActiveCheckIn = data['has_active_check_in'] ?? false;

        if (hasActiveCheckIn) {
          print('Active check-in found: $hasActiveCheckIn');
          setState(() {
            _hasActiveCheckIn = true;
            _activeCheckIn = data['active_check_in'];
          });

          // Recheck tracking status since check-in state has changed
          await _recheckTrackingStatus();

          // Now fetch trips since we have confirmed active check-in
          _fetchDriverTrips();
        } else {
          // No active check-in (200 response with has_active_check_in: false)
          print('Server confirmed no active check-in (200 with has_active_check_in: false)');
          setState(() {
            _hasActiveCheckIn = false;
            _activeCheckIn = {};
          });

          // Redirect to check-in screen when no active check-in is found
          print('Redirecting to check-in screen since no active check-in was found');

          // Use a short delay to avoid navigation during state update
          Future.delayed(Duration.zero, () {
            if (mounted) {
              final effectiveDriverCode = widget.driverCode.isNotEmpty ? widget.driverCode : _driverCode;

              if (effectiveDriverCode.isNotEmpty) {
                Navigator.pushReplacementNamed(
                  context,
                  '/driver-check-in',
                  arguments: {'driverCode': effectiveDriverCode},
                );
              } else {
                // Show error if driver code is missing
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Error: Driver code is missing. Please try again.'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            }
          });

          // We'll still recheck tracking status and fetch trips in case navigation fails
          await _recheckTrackingStatus();
          _fetchDriverTrips();

          print('No active check-in found, redirecting to check-in screen');
        }
      } else {
        print('Error checking for active check-in: ${response.statusCode}');
        print('Response body: ${response.body}');
        // On error, still try to track based on schedule
        print('Error checking for active check-in, but still continuing with tracking');
        setState(() {
          _hasActiveCheckIn = false;
          _activeCheckIn = {};
        });

        // Recheck tracking status and fetch trips
        await _recheckTrackingStatus();
        _fetchDriverTrips();
      }
    } catch (e) {
      print('Exception checking for active check-in: $e');
      // On exception, still try to track based on schedule
      print('Exception checking for active check-in, but still continuing with tracking');
      setState(() {
        _hasActiveCheckIn = false;
        _activeCheckIn = {};
      });

      // Recheck tracking status and fetch trips
      await _recheckTrackingStatus();
      _fetchDriverTrips();
    }
  }

  Future<void> _fetchDriverTrips() async {
    print('Fetching driver trips...');
    if (!mounted) return;

    // Use the driver code from widget or from state if widget's is empty
    final driverCode = widget.driverCode.isNotEmpty ? widget.driverCode : _driverCode;

    // Don't proceed if we don't have a driver code
    if (driverCode.isEmpty) {
      setState(() {
        _errorMessage = 'Driver code not available';
        _isLoading = false;
      });
      return;
    }

    try {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });

      // Build the endpoint with optional query parameters
      String endpoint = 'trips/driver/$driverCode';
      final List<String> queryParams = [];

      // Add date filter if selected
      if (_selectedDate != null) {
        // Format date as YYYY-MM-DD for API request
        final formattedDate = '${_selectedDate!.year}-${_selectedDate!.month.toString().padLeft(2, '0')}-${_selectedDate!.day.toString().padLeft(2, '0')}';
        queryParams.add('date=$formattedDate');
        print('Filtering by date: $formattedDate');
      }

      // Add status filter if selected
      if (_selectedStatus != null && _selectedStatus!.isNotEmpty) {
        queryParams.add('status=${Uri.encodeComponent(_selectedStatus!)}');
      }

      // Append query parameters to endpoint if any
      if (queryParams.isNotEmpty) {
        endpoint += '?${queryParams.join('&')}';
      }

      final response = await ApiService.get(endpoint);

      if (!mounted) return;

      if (response.statusCode == 200) {
        final dynamic decodedResponse = jsonDecode(response.body);

        if (!mounted) return;

        if (decodedResponse is Map<String, dynamic>) {
          final List<dynamic> tripsList = List.from(decodedResponse['trips'] ?? []);

          // Debug: Print the raw trip data to see if total_duration is included
          print('API Response - trips data:');
          for (var trip in tripsList) {
            print('Trip ${trip['trip_code']}: total_duration=${trip['total_duration']}, status=${trip['status']}');
          }

          // Get the count from the API response if available
          if (decodedResponse.containsKey('counts') &&
              decodedResponse['counts'] is Map<String, dynamic> &&
              decodedResponse['counts'].containsKey('assignToDriver')) {
            _assignToDriverCount = decodedResponse['counts']['assignToDriver'] as int;
          }

          if (mounted) {
            setState(() {
              _trips = tripsList.map((json) => Trip.fromJson(json)).toList();

              // Debug: Print the parsed trip objects to see if total_duration is correctly parsed
              print('Parsed Trip objects:');
              for (var trip in _trips) {
                print('Trip ${trip.tripCode}: totalDuration=${trip.totalDuration}, calculated minutes=${trip.getDurationMinutes()}');
              }

              // Sort trips by date and time strings (earliest first)
              _trips.sort((a, b) {
                // First compare by originalDate (YYYY-MM-DD format)
                final int dateComparison = a.originalDate.compareTo(b.originalDate);
                if (dateComparison != 0) {
                  return dateComparison;
                }
                // If dates are equal, compare by time
                return a.time.compareTo(b.time);
              });

              // Apply filters to create filtered trips list
              _applyFilters();

              // If count wasn't in the API response, calculate it locally
              if (!decodedResponse.containsKey('counts')) {
                _assignToDriverCount = _trips.where((trip) => trip.status == 'ASSIGN TO DRIVER').length;
              }
            });

            // Recheck tracking status after fetching trips to update based on active trips
            _recheckTrackingStatus();
          }
        } else if (decodedResponse is List) {
          // Legacy support for list response format

          // Debug: Print the raw trip data to see if total_duration is included
          print('API Response - trips data (list format):');
          for (var trip in decodedResponse) {
            print('Trip ${trip['trip_code']}: total_duration=${trip['total_duration']}, status=${trip['status']}');
          }

          if (mounted) {
            setState(() {
              _trips = decodedResponse.map((json) => Trip.fromJson(json)).toList();

              // Debug: Print the parsed trip objects to see if total_duration is correctly parsed
              print('Parsed Trip objects (list format):');
              for (var trip in _trips) {
                print('Trip ${trip.tripCode}: totalDuration=${trip.totalDuration}, calculated minutes=${trip.getDurationMinutes()}');
              }

              // Sort trips by date and time strings (earliest first)
              _trips.sort((a, b) {
                // First compare by originalDate (YYYY-MM-DD format)
                final int dateComparison = a.originalDate.compareTo(b.originalDate);
                if (dateComparison != 0) {
                  return dateComparison;
                }
                // If dates are equal, compare by time
                return a.time.compareTo(b.time);
              });

              // Apply filters to create filtered trips list
              _applyFilters();

              // Calculate count locally for list response format
              _assignToDriverCount = _trips.where((trip) => trip.status == 'ASSIGN TO DRIVER').length;
            });

            // Recheck tracking status after fetching trips to update based on active trips
            _recheckTrackingStatus();
          }
        } else {
          throw FormatException('Unexpected response format: ${decodedResponse.runtimeType}');
        }
      } else {
        if (mounted) {
          setState(() {
            _errorMessage = 'Failed to load trips: ${response.statusCode}';
          });
        }
      }
    } catch (e, stackTrace) {
      print('Error: $e');
      print('Stack trace: $stackTrace');
      if (mounted) {
        setState(() {
          _errorMessage = 'An error occurred: $e';
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
  // Apply filters to the trips list
  void _applyFilters() {
    _filteredTrips = List.from(_trips);

    // First, only include trips with allowed statuses
    final allowedStatuses = {
      'ASSIGN TO DRIVER',
      'DRIVER CONFIRMATION', 
      'TRIP IN PROGRESS',
      'BACK TO BASE'
    };
    
    _filteredTrips = _filteredTrips.where((trip) =>
      allowedStatuses.contains(trip.status)
    ).toList();

    // Apply date filter if selected
    if (_selectedDate != null) {
      // Format date as YYYY-MM-DD for comparison with trip.originalDate
      final formattedDate = '${_selectedDate!.year}-${_selectedDate!.month.toString().padLeft(2, '0')}-${_selectedDate!.day.toString().padLeft(2, '0')}';

      print('Filtering trips by date: $formattedDate');
      print('Available dates in trips: ${_trips.map((t) => t.originalDate).toSet().toList()}');

      // Filter trips by comparing with the original date from API
      _filteredTrips = _filteredTrips.where((trip) {
        // Check if the trip's original date matches our formatted date
        final matches = trip.originalDate == formattedDate;
        print('Trip ${trip.tripCode} date ${trip.originalDate} matches filter? $matches');
        return matches;
      }).toList();

      print('Filtered trips count: ${_filteredTrips.length}');

      // If no trips match, show a message
      if (_filteredTrips.isEmpty && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).noTripsFoundForDate(formattedDate)),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }

    // Apply status filter if selected
    if (_selectedStatus != null && _selectedStatus!.isNotEmpty) {
      _filteredTrips = _filteredTrips.where((trip) => trip.status == _selectedStatus).toList();
    }
  }

  // Note: End time calculation is now handled by _getFormattedEndTime method

  // Note: Detail rows are now built directly in the UI



  // Show trip completion dialog with notes and photo options
  Future<void> _showTripCompletionDialog(String tripCode) async {
    // Reset state for new completion
    _notesController.text = '';

    // Create local variables for the dialog
    String? localBase64Image;
    Uint8List? localImageBytes;

    // Check if widget is still mounted before showing dialog
    if (!mounted) return;

    // Show the dialog and wait for result
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return StatefulBuilder(
          builder: (dialogContext, setDialogState) {
            return AlertDialog(
              title: Text(AppLocalizations.of(context).completeTrip),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [                    Text(
                      AppLocalizations.of(context).addNotesPhotoOptional,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 16),                    TextField(
                      controller: _notesController,
                      decoration: InputDecoration(
                        labelText: AppLocalizations.of(context).notes,
                        border: const OutlineInputBorder(),
                        hintText: AppLocalizations.of(context).enterNotesOptional,
                      ),
                      maxLines: 3,
                    ),
                    const SizedBox(height: 16),                    Text(
                      AppLocalizations.of(context).photo,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    // Display selected image or placeholder
                    localImageBytes != null
                        ? Container(
                            height: 120,
                            width: double.infinity,
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(4),
                              child: kIsWeb && localBase64Image != null
                                ? Image.network(
                                    localBase64Image!,
                                    fit: BoxFit.contain,
                                    errorBuilder: (context, error, stackTrace) {
                                      print('Error loading image: $error');
                                      return const Center(child: Text('Error loading image', textAlign: TextAlign.center));
                                    },
                                  )
                                : Image.memory(
                                    localImageBytes!,
                                    fit: BoxFit.contain,
                                    errorBuilder: (context, error, stackTrace) {
                                      print('Error displaying image: $error');
                                      return const Center(child: Text('Error displaying image', textAlign: TextAlign.center));
                                    },
                                  ),
                            ),
                          )
                        : Container(
                            height: 120,
                            width: double.infinity,
                            decoration: BoxDecoration(
                              color: Colors.grey[300],
                              borderRadius: BorderRadius.circular(4),
                              border: Border.all(color: Colors.grey),
                            ),
                            child: const Center(child: Text('No image selected', textAlign: TextAlign.center)),
                          ),
                    const SizedBox(height: 8),
                    Center(
                      child: ElevatedButton.icon(
                        onPressed: () async {
                          try {
                            final ImagePicker picker = ImagePicker();
                            final XFile? image = await picker.pickImage(
                              source: kIsWeb ? ImageSource.gallery : ImageSource.camera,
                              imageQuality: 70,
                              maxWidth: 400,
                              maxHeight: 400,
                            );

                            // Check if dialog is still mounted after async operation
                            if (!dialogContext.mounted) return;

                            if (image != null) {
                              final bytes = await image.readAsBytes();

                              // Check if dialog is still mounted after async operation
                              if (!dialogContext.mounted) return;

                              // Determine the correct MIME type
                              String mimeType = image.mimeType ?? 'image/jpeg';
                              if (mimeType.isEmpty) {
                                // Default to jpeg if no mime type is available
                                mimeType = 'image/jpeg';
                              }

                              final base64Image = base64Encode(bytes);
                              final dataUrl = 'data:$mimeType;base64,$base64Image';

                              print('Image format: $mimeType');
                              print('Image path: ${image.path}');
                              print('Image size: ${bytes.length} bytes');

                              // Update only the dialog state
                              setDialogState(() {
                                localImageBytes = bytes;
                                localBase64Image = dataUrl;
                              });

                              print('Image selected successfully: ${bytes.length} bytes');
                            }
                          } catch (e) {
                            print('Error picking image: $e');
                            if (dialogContext.mounted) {
                              ScaffoldMessenger.of(dialogContext).showSnackBar(
                                SnackBar(
                                  content: Text('Error selecting image: $e'),
                                  backgroundColor: Colors.red,
                                ),
                              );
                            }
                          }
                        },
                        icon: const Icon(Icons.camera_alt),
                        label: Text(AppLocalizations.of(context).takePhoto),
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(                  onPressed: () {
                    Navigator.of(dialogContext).pop(null); // Cancel
                  },
                  child: Text(AppLocalizations.of(context).cancel),
                ),
                ElevatedButton(
                  onPressed: () {
                    // Return the notes and image data
                    Navigator.of(dialogContext).pop({
                      'notes': _notesController.text,
                      'base64Image': localBase64Image,
                      'imageBytes': localImageBytes,
                    });
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                  child: Text(AppLocalizations.of(context).completeTrip),
                ),
              ],
            );
          },
        );
      },
    );

    // Check if widget is still mounted after dialog closes
    if (!mounted) return;

    // Process the result
    if (result != null) {
      // Complete the trip with the provided data
      await _completeTrip(tripCode, notes: result['notes'], imageData: result['base64Image']);
    }
  }
  // Complete trip with optional notes and photo
  Future<void> _completeTrip(String tripCode, {String? notes, String? imageData}) async {
    if (!mounted) return;

    // Store scaffold messenger and localized strings to avoid async gap issues
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final tripCompletedSuccessfully = AppLocalizations.of(context).tripCompletedSuccessfully;
    final failedToCompleteTrip = AppLocalizations.of(context).failedToCompleteTrip;
    final errorCompletingTrip = AppLocalizations.of(context).errorCompletingTrip;

    try {
      setState(() {
        _isLoading = true;
      });

      if (tripCode.isEmpty) {
        throw Exception('Trip code is empty');
      }

      // Create request body with status and optional notes/image
      final Map<String, dynamic> requestBody = {
        'status': 'WAITING FOR RATING',
      };

      // Add notes if provided
      if (notes != null && notes.isNotEmpty) {
        requestBody['notes'] = notes;
      }      // Add image data if available
      if (imageData != null) {
        print('Adding image data to request: ${imageData.length} characters');
        requestBody['imageData'] = imageData;
      }

      final response = await ApiService.put(
        'drivers/trips/$tripCode/status',
        requestBody,
      );

      if (!mounted) return;

      if (response.statusCode == 200) {        // Refresh the trip list
        await _fetchDriverTrips();
        
        // Recheck tracking status since trip status has changed
        // This is already called in _fetchDriverTrips, but we'll call it explicitly for clarity
        await _recheckTrackingStatus();
        
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(tripCompletedSuccessfully),
            backgroundColor: Colors.green,
          ),
        );
      } else {        if (mounted) {
          setState(() {
            _isLoading = false;
          });
          
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text('$failedToCompleteTrip: ${response.statusCode}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      print('Error completing trip: $e');
      if (mounted) {        setState(() {
          _isLoading = false;
        });
        
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('$errorCompletingTrip: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Show dialog to get rejection reason
  Future<void> _showRejectTripDialog(String tripCode) async {
    final TextEditingController reasonController = TextEditingController();
    String? rejectionReason;

    // Check if widget is still mounted before showing dialog
    if (!mounted) return;

    try {
      // Show dialog and wait for result
      final result = await showDialog<bool>(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext dialogContext) {
          return AlertDialog(
            title: Text(AppLocalizations.of(context).rejectTrip),            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(AppLocalizations.of(context).pleaseProvideRejectionReason),
                const SizedBox(height: 16),
                TextField(
                  controller: reasonController,
                  decoration: InputDecoration(
                    labelText: AppLocalizations.of(context).rejectionReason,
                    border: const OutlineInputBorder(),
                    hintText: AppLocalizations.of(context).enterRejectionReason,
                  ),
                  maxLines: 3,
                ),
              ],
            ),
            actions: [              TextButton(
                onPressed: () => Navigator.of(dialogContext).pop(false),
                child: Text(AppLocalizations.of(context).cancel),
              ),
              ElevatedButton(
                onPressed: () {
                  rejectionReason = reasonController.text.trim();
                  // Validate that a reason is provided
                  if (rejectionReason!.isEmpty) {
                    ScaffoldMessenger.of(dialogContext).showSnackBar(
                      const SnackBar(
                        content: Text('Please provide a reason for rejecting the trip'),
                        backgroundColor: Colors.red,
                        duration: Duration(seconds: 2),
                      ),
                    );
                    return;
                  }
                  Navigator.of(dialogContext).pop(true);
                },                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: Text(AppLocalizations.of(context).rejectTrip),
              ),
            ],
          );
        },
      );

      // Check if widget is still mounted after dialog closes
      if (!mounted) return;

      // If user confirmed rejection, update the trip status with the reason
      if (result == true) {
        await _updateTripStatus(tripCode, 'DRIVER REJECTED', rejectionReason: rejectionReason);
      }
    } finally {
      // Clean up the controller
      reasonController.dispose();
    }
  }

  // Check if a car has had its odometer updated today
  Future<bool> _isFirstCarTripOfDay(dynamic carId) async {
    // Ensure carId is properly formatted
    final safeCarId = carId is int ? carId : int.tryParse(carId?.toString() ?? '0') ?? 0;
    try {
      // Get current date in YYYY-MM-DD format (local time)
      final now = DateTime.now().toLocal();
      final today = '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';

      // Also log UTC time for comparison
      final utcNow = DateTime.now().toUtc();
      final utcDate = '${utcNow.year}-${utcNow.month.toString().padLeft(2, '0')}-${utcNow.day.toString().padLeft(2, '0')}';

      print('Local date being used: $today (${now.toString()})');
      print('UTC date for reference: $utcDate (${utcNow.toString()})');
      print('Checking if car $safeCarId has odometer history on $today');

      // Call API to check if this car has any odometer history entries today
      final response = await ApiService.get('trips/car/$safeCarId/check-first-trip?date=$today');

      // Check if widget is still mounted after async operation
      if (!mounted) return false;

      print('API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        print('API response data: $data');
        final isFirstTrip = data['isFirstTrip'] ?? true;
        final historyCount = data['historyCount'] ?? 0;

        print('Is first odometer update today: $isFirstTrip (history entries: $historyCount)');
        return isFirstTrip; // Default to true if the API doesn't return the expected format
      } else {
        print('Error checking if first odometer update: ${response.statusCode}');
        print('Response body: ${response.body}');
        return true; // Default to true (show odometer input) if there's an error
      }
    } catch (e) {
      print('Exception checking if first odometer update: $e');
      return true; // Default to true (show odometer input) if there's an exception
    }
  }

  // Get the current driver's ID from active check-in or API
  Future<int?> _getCurrentDriverId() async {
    int? driverId;

    // Store user information before any async operations
    MyAppState? appState;
    dynamic currentUser;

    try {
      // Get the current user from app state - do this before any async operations
      if (context.mounted) {
        appState = Provider.of<MyAppState>(context, listen: false);
        currentUser = appState.currentUser;
      }
    } catch (e) {
      print('Error accessing app state: $e');
    }

    // First try to get driver ID from active check-in data
    if (_activeCheckIn.containsKey('driver_id')) {
      if (_activeCheckIn['driver_id'] is String) {
        driverId = int.tryParse(_activeCheckIn['driver_id']);
        print('Parsed driver ID from active check-in string: $driverId');
      } else if (_activeCheckIn['driver_id'] is int) {
        driverId = _activeCheckIn['driver_id'];
        print('Using driver ID from active check-in as int: $driverId');
      }
    }

    // If we couldn't get the driver ID from active check-in, try to get it from the API
    if (driverId == null && currentUser != null) {
      try {
        // Get driver details from API
        final response = await ApiService.get('drivers/user/${currentUser.userId}');

        if (response.statusCode == 200) {
          final data = jsonDecode(response.body);
          print('Driver API response data for current user: $data');

          if (data.containsKey('driver_detail_id')) {
            if (data['driver_detail_id'] is String) {
              driverId = int.tryParse(data['driver_detail_id']);
              print('Parsed driver ID from API response: $driverId');
            } else if (data['driver_detail_id'] is int) {
              driverId = data['driver_detail_id'];
              print('Using driver ID from API response: $driverId');
            }
          }
        } else {
          print('Error getting driver details: ${response.statusCode}');
        }
      } catch (e) {
        print('Error getting driver ID from API: $e');
      }
    }

    return driverId;
  }

  // Update trip status
  Future<void> _updateTripStatus(String tripCode, String newStatus, {String? rejectionReason}) async {
    if (!mounted) return;

    // For trip completion, show the dialog instead of directly updating status
    if (newStatus == 'WAITING FOR RATING') {
      await _showTripCompletionDialog(tripCode);
      return;
    }

    // Store scaffold messenger to avoid async gap issues
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    try {
      if (!mounted) return;

      setState(() {
        _isLoading = true;
      });

      if (tripCode.isEmpty) {
        throw Exception('Trip code is empty');
      }      // If status is changing to TRIP IN PROGRESS, check if we need to update car odometer
      if (newStatus == 'TRIP IN PROGRESS') {
        print('🔧 Status changing to TRIP IN PROGRESS, checking for car odometer update');

        // Find the trip in the list
        final trip = _trips.firstWhere(
          (t) => t.tripCode == tripCode,
          orElse: () => throw Exception('Trip not found in local data'),
        );

        print('🔧 Found trip: ${trip.tripCode}, carId: ${trip.carId}');

        // Check if the trip has a car assigned
        if (trip.carId != null) {
          print('🔧 Trip has car assigned with ID: ${trip.carId}');          // Check if this is the first odometer update of the day for this car
          print('🔧 Checking if this is the first odometer update of the day for car ${trip.carId}');
          final isFirstTrip = await _isFirstCarTripOfDay(trip.carId!);

          if (!mounted) return;

          print('🔧 Is first odometer update of the day: $isFirstTrip');

          if (isFirstTrip) {
            print('🔧 This is the first odometer update of the day for car ${trip.carId}, getting car details');

            // Get current car odometer
            final carResponse = await ApiService.get('cars/${trip.carId}');

            // Check if widget is still mounted after async operation
            if (!mounted) return;

            print('Car details response status: ${carResponse.statusCode}');

            if (carResponse.statusCode == 200) {
              final carData = jsonDecode(carResponse.body);
              print('Car data received: $carData');
              // Convert odometer to integer, handling both string and int types
              final currentOdometer = carData['odometer'] is String
                  ? int.tryParse(carData['odometer']) ?? 0
                  : carData['odometer'] ?? 0;
              final carCode = carData['car_code'] ?? 'Unknown';
              print('Car code: $carCode, current odometer: $currentOdometer');

              // Get driver ID - first try from the trip, then from the current user if null
              int? driverId = trip.driverId;

              // If driverId is null, try to get it from the current user
              if (driverId == null) {
                driverId = await _getCurrentDriverId();
                print('Driver ID from trip was null, using current driver ID: $driverId');
              }

              // Check if widget is still mounted after getting driver ID
              if (!mounted) return;

              // Use the standalone odometer dialog to handle the odometer update
              // This completely isolates the dialog from the main widget tree
              print('Using standalone odometer dialog to update odometer');
              final odometerUpdateResult = await OdometerDialog.showAndUpdateOdometer(
                context,
                trip.carId,
                carCode,
                currentOdometer,
                tripId: trip.tripId,
                driverId: driverId,
              );

              // Check if widget is still mounted after odometer update
              if (!mounted) return;

              // If odometer update failed or was cancelled, abort the status update
              if (!odometerUpdateResult) {
                print('Odometer update failed or was cancelled, aborting trip status update');
                if (mounted) {
                  setState(() {
                    _isLoading = false;
                  });
                }
                return; // Exit the method without updating trip status
              }
            } else {
              print('Error getting car details: ${carResponse.statusCode}');
              // Continue with trip status update even if we couldn't get car details
            }
          }
        }
      }

      // Check if widget is still mounted before continuing
      if (!mounted) return;

      // Prepare request body based on status
      final Map<String, dynamic> requestBody = {'status': newStatus};      // Add rejection reason if provided
      if (newStatus == 'DRIVER REJECTED' && rejectionReason != null && rejectionReason.isNotEmpty) {
        requestBody['rejectionReason'] = rejectionReason;
      }

      final response = await ApiService.put(
        'drivers/trips/$tripCode/status',
        requestBody,
      );

      if (!mounted) return;

      if (response.statusCode == 200) {
        // Use a microtask to delay the state update until the next frame
        // This helps avoid state update conflicts
        Future.microtask(() async {
          if (!mounted) return;

          // Refresh the trip list
          await _fetchDriverTrips();

          // Check if widget is still mounted after fetching trips
          if (!mounted) return;

          // Use another microtask to separate the tracking status update
          Future.microtask(() async {
            if (!mounted) return;

            // Recheck tracking status since trip status has changed
            await _recheckTrackingStatus();

            // Check if widget is still mounted after rechecking tracking status
            if (!mounted) return;

            scaffoldMessenger.showSnackBar(
              SnackBar(
                content: Text('Trip status updated to $newStatus'),
                backgroundColor: Colors.green,
              ),
            );
          });
        });
      } else if (response.statusCode == 400 && newStatus == 'DRIVER REJECTED') {
        // Handle the case where the server rejects the request due to missing rejection reason
        if (mounted) {
          setState(() {
            _isLoading = false;
          });

          // Try to parse the error message from the response
          String errorMessage = 'Failed to reject trip: Rejection reason is required';
          try {
            final responseData = jsonDecode(response.body);
            if (responseData['error'] != null) {
              errorMessage = 'Failed to reject trip: ${responseData['error']}';
            }
          } catch (e) {
            // If we can't parse the response, use the default error message
          }

          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text(errorMessage),
              backgroundColor: Colors.red,
            ),
          );
        }
      } else {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });

          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text('Failed to update trip status: ${response.statusCode}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      print('Error updating trip status: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('Error updating trip status: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      // Ensure loading state is reset even if there's an error
      // Use a microtask to delay the state update until the next frame
      Future.microtask(() {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      });
    }
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: AppLocalizations.of(context).myTrips,
        automaticallyImplyLeading: false,
        onRefresh: () {
          setState(() {
            lastRefreshTime = DateTime.now();
          });
          refreshData();
        },
        additionalActions: [
          // Trip history button
          IconButton(
            icon: const Icon(Icons.history),
            tooltip: AppLocalizations.of(context).tripHistory,            onPressed: () {
              // Get the effective driver code (from widget or state)
              final effectiveDriverCode = widget.driverCode.isNotEmpty ? widget.driverCode : _driverCode;

              if (effectiveDriverCode.isEmpty) {
                // Show error message if driver code is empty
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(AppLocalizations.of(context).errorDriverCodeMissing),
                    backgroundColor: Colors.red,
                  ),
                );
                return;
              }

              Navigator.pushNamed(
                context,
                '/driver-trip-history',
                arguments: {'driverCode': effectiveDriverCode},
              );
            },
          ),
          // Show auto tracking status
          _buildAutoTrackingIndicator(),
        ],
      ),
      floatingActionButton: _hasActiveCheckIn ? Column(
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [          // Back to Base button
          FloatingActionButton.extended(
            onPressed: _createBackToBaseTrip,
            icon: const Icon(Icons.home, color: Colors.white),
            label: Text(AppLocalizations.of(context).backToBase, style: const TextStyle(color: Colors.white)),
            backgroundColor: Colors.blue,
            heroTag: 'backToBase',
          ),
          const SizedBox(height: 16),
          // Check Out button
          FloatingActionButton.extended(
            onPressed: () {
              // Get the effective driver code (from widget or state)
              final effectiveDriverCode = widget.driverCode.isNotEmpty ? widget.driverCode : _driverCode;

              print('Navigating to checkout screen with driver code: $effectiveDriverCode');
              print('Active check-in data: $_activeCheckIn');              if (effectiveDriverCode.isEmpty) {
                // Show error message if driver code is empty
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(AppLocalizations.of(context).errorDriverCodeMissing),
                    backgroundColor: Colors.red,
                  ),
                );
                return;
              }

              Navigator.pushReplacementNamed(
                context,
                '/driver-checkout',
                arguments: {
                  'driverCode': effectiveDriverCode,
                  'activeCheckIn': _activeCheckIn,
                },
              );
            },
            icon: const Icon(Icons.logout, color: Colors.white),
            label: Text(AppLocalizations.of(context).checkOut, style: const TextStyle(color: Colors.white)),
            backgroundColor: Colors.red,
            heroTag: 'checkOut',
          ),
        ],
      ) : null,
      body: Column(
        children: [
          // Filter section
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey.shade100,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title and count
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,                  children: [
                    Text(
                      AppLocalizations.of(context).filterTrips,
                      style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.blue,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Text(
                        AppLocalizations.of(context).assignToDriverCount(_assignToDriverCount),
                        style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                // Filter controls
                Row(
                  children: [
                    // Date filter
                    Expanded(
                      child: InkWell(
                        onTap: () async {
                          final DateTime? picked = await showDatePicker(
                            context: context,
                            initialDate: _selectedDate ?? DateTime.now(),
                            firstDate: DateTime(2020),
                            lastDate: DateTime(2030),
                          );
                          if (picked != null && picked != _selectedDate) {
                            setState(() {
                              _selectedDate = picked;
                            });
                            _fetchDriverTrips();
                          }
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade300),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,                            children: [
                              Text(
                                _selectedDate == null
                                    ? AppLocalizations.of(context).selectDate
                                    : '${_selectedDate!.day.toString().padLeft(2, '0')}/${_selectedDate!.month.toString().padLeft(2, '0')}/${_selectedDate!.year}',
                                style: TextStyle(
                                  color: _selectedDate == null ? Colors.grey : Colors.black,
                                  fontWeight: _selectedDate == null ? FontWeight.normal : FontWeight.bold,
                                ),
                              ),
                              Icon(_selectedDate == null ? Icons.calendar_today : Icons.calendar_month,
                                  size: 16,
                                  color: _selectedDate == null ? Colors.grey : Colors.blue),
                            ],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    // Status filter
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(4),
                        ),                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<String>(
                            isExpanded: true,
                            hint: Text(AppLocalizations.of(context).selectStatus),
                            value: _selectedStatus,
                            items: [
                              DropdownMenuItem<String>(
                                value: '',
                                child: Text(AppLocalizations.of(context).allStatuses),
                              ),
                              ..._statuses.map((String status) => DropdownMenuItem<String>(
                                value: status,
                                child: Text(status),
                              )),
                            ],
                            onChanged: (String? newValue) {
                              setState(() {
                                _selectedStatus = newValue;
                              });
                              _fetchDriverTrips();
                            },
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                // Clear filters button
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () {
                        setState(() {
                          _selectedDate = null;
                          _selectedStatus = null;
                        });
                        _fetchDriverTrips();
                      },
                      child: Text(AppLocalizations.of(context).clearFilters),
                    ),
                  ],
                ),
              ],
            ),
          ),
          // Trips list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _errorMessage.isNotEmpty
                    ? Center(child: Text(_errorMessage))
                    : _filteredTrips.isEmpty
                        ? Center(child: Text(AppLocalizations.of(context).noTripsMatchFilters))
                        : RefreshIndicator(
                            onRefresh: _fetchDriverTrips,
                            child: ListView.builder(
                              itemCount: _filteredTrips.length,
                              itemBuilder: (context, index) {
                                final trip = _filteredTrips[index];
                                return Card(
                                    margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                    elevation: 2,
                                    child: Padding(
                                      padding: const EdgeInsets.all(16.0),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,                            children: [
                              Text(
                                AppLocalizations.of(context).tripCodeNumber(trip.tripCode),
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 18,
                                  color: Color(0xFF0D47A1),
                                ),
                              ),
                                              Container(
                                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                                decoration: BoxDecoration(
                                                  color: _getStatusColor(trip.status),
                                                  borderRadius: BorderRadius.circular(8),
                                                ),
                                                child: Text(
                                                  trip.status,
                                                  style: const TextStyle(
                                                    color: Colors.white,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 12),
                                          Row(
                                            children: [                              const Icon(Icons.calendar_today, size: 16, color: Colors.grey),
                              const SizedBox(width: 8),
                              Text(
                                '${AppLocalizations.of(context).dateLabel} ${trip.date}',
                                style: const TextStyle(
                                  fontSize: 16,
                                  color: Colors.black87,
                                ),
                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 8),
                                          Row(
                                            children: [                              const Icon(Icons.access_time, size: 16, color: Colors.grey),
                              const SizedBox(width: 8),
                              Text(
                                '${AppLocalizations.of(context).startTimeLabel} ${trip.time}',
                                style: const TextStyle(
                                  fontSize: 16,
                                  color: Colors.black87,
                                ),
                              ),
                                            ],
                                          ),
                                          if (trip.totalDuration != null && trip.totalDuration!.isNotEmpty)
                                          Padding(
                                            padding: const EdgeInsets.only(top: 8.0),
                                            child: Row(
                                              children: [                                const Icon(Icons.access_time, size: 16, color: Colors.grey),
                                const SizedBox(width: 8),
                                Text(
                                  '${AppLocalizations.of(context).endTimeLabel} ${_getFormattedEndTime(trip)}',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    color: Colors.black87,
                                  ),
                                ),
                                              ],
                                            ),
                                          ),
                                          const SizedBox(height: 8),
                                          Row(
                                            children: [                              const Icon(Icons.location_on, size: 16, color: Colors.grey),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  '${AppLocalizations.of(context).fromLabel} ${trip.fromDestination}',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    color: Colors.black87,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              const Icon(Icons.location_on, size: 16, color: Colors.grey),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  '${AppLocalizations.of(context).toLabel} ${trip.toDestination}',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    color: Colors.black87,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 8),
                                          Row(
                                            children: [                              const Icon(Icons.timelapse, size: 16, color: Colors.grey),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  '${AppLocalizations.of(context).durationLabel} ${AppLocalizations.of(context).durationInMinutes(trip.getDurationMinutes())}',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    color: Colors.black87,
                                  ),
                                                  maxLines: 1,
                                                  overflow: TextOverflow.ellipsis,
                                                ),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 8),
                                          Row(
                                            children: [                              const Icon(Icons.person, size: 16, color: Colors.grey),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  '${AppLocalizations.of(context).requestorLabel} ${trip.requestorName}',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    color: Colors.black87,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),                                            ],
                                          ),
                                          // Show waiting trip indicator if trip is a waiting trip
                                          if (trip.isWaiting == true)
                                            Padding(
                                              padding: const EdgeInsets.only(top: 8.0),
                                              child: Row(
                                                children: [
                                                  const Icon(Icons.schedule, size: 16, color: Colors.orange),
                                                  const SizedBox(width: 8),
                                                  Container(
                                                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                                    decoration: BoxDecoration(
                                                      color: Colors.orange.shade100,
                                                      borderRadius: BorderRadius.circular(8),
                                                      border: Border.all(color: Colors.orange.shade300),
                                                    ),
                                                    child: Text(
                                                      AppLocalizations.of(context).waitingTrip,
                                                      style: TextStyle(
                                                        color: Colors.orange.shade800,
                                                        fontWeight: FontWeight.bold,
                                                        fontSize: 14,
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          if (trip.carCode != null && trip.carCode!.isNotEmpty)
                                            Padding(
                                              padding: const EdgeInsets.only(top: 8.0),
                                              child: Row(
                                                children: [                                  const Icon(Icons.directions_car, size: 16, color: Colors.grey),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      '${AppLocalizations.of(context).carLabel} ${trip.carCode}',
                                      style: const TextStyle(
                                        fontSize: 16,
                                        color: Colors.black87,
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),                                  ),
                                                ],
                                              ),
                                            ),
                                          const SizedBox(height: 16),
                                          if (trip.status == 'ASSIGN TO DRIVER')
                                            Column(
                                              children: [
                                                SizedBox(
                                                  width: double.infinity,
                                                  child: ElevatedButton(
                                                    onPressed: () => _updateTripStatus(trip.tripCode, 'DRIVER CONFIRMATION'),
                                                    style: ElevatedButton.styleFrom(
                                                      backgroundColor: const Color(0xFF0D47A1),
                                                      foregroundColor: Colors.white,
                                                    ),
                                                    child: Text(AppLocalizations.of(context).confirmTrip),
                                                  ),
                                                ),
                                                const SizedBox(height: 8),
                                                SizedBox(
                                                  width: double.infinity,
                                                  child: ElevatedButton(
                                                    onPressed: () => _showRejectTripDialog(trip.tripCode),
                                                    style: ElevatedButton.styleFrom(
                                                      backgroundColor: Colors.red,
                                                      foregroundColor: Colors.white,
                                                    ),
                                                    child: Text(AppLocalizations.of(context).rejectTrip),
                                                  ),
                                                ),
                                              ],
                                            )
                                          else if (trip.status == 'DRIVER CONFIRMATION')
                                            SizedBox(
                                              width: double.infinity,
                                              child: ElevatedButton(
                                                onPressed: () => _updateTripStatus(trip.tripCode, 'TRIP IN PROGRESS'),
                                                style: ElevatedButton.styleFrom(
                                                  backgroundColor: Colors.green,
                                                  foregroundColor: Colors.white,
                                                ),
                                                child: Text(AppLocalizations.of(context).startTrip),
                                              ),
                                            )
                                          else if (trip.status == 'TRIP IN PROGRESS')
                                            SizedBox(
                                              width: double.infinity,                                              child: ElevatedButton(
                                                onPressed: () => _updateTripStatus(trip.tripCode, 'WAITING FOR RATING'),
                                                style: ElevatedButton.styleFrom(
                                                  backgroundColor: Colors.orange,
                                                  foregroundColor: Colors.white,
                                                ),
                                                child: Text(AppLocalizations.of(context).finishTrip),
                                              ),
                                            )
                                          else if (trip.status == 'BACK TO BASE')
                                            SizedBox(
                                              width: double.infinity,                                              child: ElevatedButton(
                                                onPressed: () => _updateTripStatus(trip.tripCode, 'COMPLETED'),
                                                style: ElevatedButton.styleFrom(
                                                  backgroundColor: Colors.orange,
                                                  foregroundColor: Colors.white,
                                                ),
                                                child: Text(AppLocalizations.of(context).finishTrip),
                                              ),
                                            ),
                                        ],
                                      ),
                                    )
                                );
                        },
                      ),
                    ),
          ),
        ],
      )
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toUpperCase()) {
      case 'ASSIGN TO DRIVER':
        return Colors.orange;
      case 'DRIVER CONFIRMATION':
        return Colors.blue;
      case 'TRIP IN PROGRESS':
        return Colors.green;
      case 'WAITING FOR RATING':
        return Colors.purple;
      case 'DRIVER REJECTED':
        return Colors.red;
      case 'BACK TO BASE':
        return Colors.indigo;
      case 'COMPLETED':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }

  // Helper method to get formatted end time for a trip
  String _getFormattedEndTime(Trip trip) {
    try {
      // Parse the trip date and time
      final String tripDate = trip.originalDate.isNotEmpty ? trip.originalDate : DateFormat('yyyy-MM-dd').format(DateTime.now());
      final String tripTime = trip.time.isNotEmpty ? trip.time : '00:00';

      // Create a DateTime object for the trip start time
      final DateTime tripStartTime = DateTime.parse('${tripDate}T$tripTime:00');

      // Add the duration in minutes to get the end time
      final DateTime endTime = tripStartTime.add(Duration(minutes: trip.getDurationMinutes()));

      // Format the end time as HH:mm (24-hour format)
      return DateFormat('HH:mm').format(endTime);
    } catch (e) {
      print('Error calculating end time: $e');
      return 'N/A';
    }
  }

  // Build auto tracking indicator widget
  Widget _buildAutoTrackingIndicator() {
    final bool isAutoEnabled = _autoTrackingService.isEnabled;
    final bool isTracking = _autoTrackingService.isInitialized &&
                           _autoTrackingService.locationService.isTracking;
    final bool hasActiveTrips = _hasActiveTrips();

    // Status text depends on whether driver has active trips
    final String statusText = isAutoEnabled
        ? (isTracking ? 'ON' : (hasActiveTrips ? 'READY' : 'OFF'))
        : 'OFF';    // Tooltip text includes information about active trips
    final String tooltipText = isAutoEnabled
        ? (isTracking
            ? AppLocalizations.of(context).locationTrackingActive
            : (hasActiveTrips
                ? AppLocalizations.of(context).trackingReady
                : AppLocalizations.of(context).trackingDisabledNoActiveTrips))
        : AppLocalizations.of(context).automaticTrackingDisabled;

    return GestureDetector(
      onTap: () {
        // Show more detailed information when tapped
        _showTrackingInfoDialog();
      },
      child: Tooltip(
        message: tooltipText,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: isTracking ? Colors.green.shade100 :
                  (isAutoEnabled && hasActiveTrips ? Colors.blue.shade100 : Colors.grey.shade200),
            borderRadius: BorderRadius.circular(4),
            border: Border.all(
              color: isTracking ? Colors.green :
                    (isAutoEnabled && hasActiveTrips ? Colors.blue : Colors.grey),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                isTracking ? Icons.gps_fixed :
                           (isAutoEnabled && hasActiveTrips ? Icons.schedule : Icons.gps_off),
                size: 16,
                color: isTracking ? Colors.green :
                       (isAutoEnabled && hasActiveTrips ? Colors.blue : Colors.grey),
              ),
              const SizedBox(width: 4),
              Text(
                statusText,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: isTracking ? Colors.green :
                         (isAutoEnabled && hasActiveTrips ? Colors.blue : Colors.grey),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Show tracking information dialog
  void _showTrackingInfoDialog() {
    // Check if widget is still mounted before showing dialog
    if (!mounted) return;

    showDialog(
      context: context,
      builder: (dialogContext) => TrackingInfoDialog(autoTrackingService: _autoTrackingService),
    );  }  // Create a "Back to Base" trip
  Future<void> _createBackToBaseTrip() async {
    if (!mounted) return;

    // Store scaffold messenger and localized strings to avoid async gap issues
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final backToBaseTripCreatedSuccessfully = AppLocalizations.of(context).backToBaseTripCreatedSuccessfully;

    try {
      setState(() {
        _isLoading = true;
      });

      // Get the effective driver code (from widget or state)
      final effectiveDriverCode = widget.driverCode.isNotEmpty ? widget.driverCode : _driverCode;

      if (effectiveDriverCode.isEmpty) {
        throw Exception('Driver code is missing');
      }

      // Get the current user from app state to get the user ID (do this before async calls)
      final appState = Provider.of<MyAppState>(context, listen: false);
      final currentUser = appState.currentUser;

      // Get driver ID using the reusable method
      final driverId = await _getCurrentDriverId();

      if (!mounted) return;

      if (driverId == null) {
        throw Exception('Driver ID not found in active check-in data or from API');
      }

      // Get driver user ID (needed as requestor_id)
      int? driverUserId;
      if (currentUser != null) {
        try {
          driverUserId = int.parse(currentUser.userId);
          print('Using driver user ID from current user: $driverUserId');
        } catch (e) {
          print('Error parsing current user ID: $e');
        }
      }

      if (driverUserId == null) {
        throw Exception('Driver user ID not found');
      }

      // Get current date and time in the format expected by the API
      final now = DateTime.now();
      final date = '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';
      final time = '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';

      // Calculate end time (1 hour from now)
      final endTime = now.add(const Duration(hours: 1));
      final totalDuration = endTime.toIso8601String();

      // Create request body - using the standard trip creation endpoint format
      final Map<String, dynamic> requestBody = {
        'from_destination': 'others', // Use 'others' to indicate custom location
        'to_destination': 'others',   // Use 'others' to indicate custom location
        'custom_from': 'CURRENT DRIVER LOCATION',
        'custom_to': 'BASE',
        'date': date,
        'time': time,
        'status': 'BACK TO BASE',
        'requestor_id': driverUserId, // Set the driver's user ID as the requestor
        'driver_id': driverId,        // Set the driver ID as the assigned driver
        'total_duration': totalDuration,
      };

      print('Using driver user ID $driverUserId as requestor and driver ID $driverId as driver for Back to Base trip');

      print('Creating Back to Base trip with data: $requestBody');

      // Make API call to create trip using the standard trip creation endpoint
      // This will use the same 'ORD' sequence as when requestors add trips
      final response = await ApiService.post(
        'trips',
        requestBody,
      );

      if (!mounted) return;

      if (response.statusCode == 201) {
        final responseData = jsonDecode(response.body);
        print('Back to Base trip created successfully: $responseData');

        // Refresh the trip list
        await _fetchDriverTrips();

        // Recheck tracking status since a new trip has been created
        // This is already called in _fetchDriverTrips, but we'll call it explicitly for clarity
        await _recheckTrackingStatus();        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(backToBaseTripCreatedSuccessfully),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        print('Failed to create Back to Base trip: ${response.statusCode}');
        print('Response body: ${response.body}');

        if (mounted) {
          setState(() {
            _isLoading = false;
          });          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.of(context).failedToCreateBackToBaseTrip(response.statusCode)),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      print('Error creating Back to Base trip: $e');

      if (mounted) {
        setState(() {
          _isLoading = false;
        });        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).errorCreatingBackToBaseTrip(e.toString())),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Check if driver has active trips (TRIP IN PROGRESS or BACK TO BASE)
  bool _hasActiveTrips() {
    return _trips.any((trip) =>
      trip.status == 'TRIP IN PROGRESS' || trip.status == 'BACK TO BASE'
    );
  }

  // Recheck and ensure tracking status is in the correct state
  // This helps fix persistence state issues with driver tracking
  Future<void> _recheckTrackingStatus() async {
    print('Rechecking tracking status to ensure consistency...');

    try {
      if (!mounted) return;

      // Get the app state to access user information
      MyAppState? appState;
      try {
        appState = Provider.of<MyAppState>(context, listen: false);
      } catch (e) {
        print('Error accessing app state: $e');
        return;
      }

      final currentUser = appState.currentUser;

      // Skip if no user is logged in
      if (currentUser == null) {
        print('Cannot recheck tracking status: No user is logged in');
        return;
      }

      // Get user ID for tracking service
      int userId;
      try {
        userId = int.parse(currentUser.userId);
      } catch (e) {
        print('Error parsing user ID: $e');
        return;
      }

      // Check if tracking service is initialized
      final bool isInitialized = _autoTrackingService.isInitialized;
      final bool isEnabled = _autoTrackingService.isEnabled;
      final bool isTracking = _autoTrackingService.isTracking;

      // Check if driver has active trips
      final bool hasActiveTrips = _hasActiveTrips();

      print('Current tracking state - Initialized: $isInitialized, Enabled: $isEnabled, Tracking: $isTracking');
      print('Current check-in state - Has active check-in: $_hasActiveCheckIn');
      print('Current trips state - Has active trips: $hasActiveTrips');

      // Debug the active check-in data
      if (_hasActiveCheckIn) {
        print('Active check-in data: $_activeCheckIn');
      }

      // If driver has active check-in, ensure tracking is properly initialized
      if (_hasActiveCheckIn) {
        if (!isInitialized) {
          print('Driver has active check-in but tracking is not initialized. Initializing...');

          // Get driver ID if available
          int? driverId;
          if (_activeCheckIn.containsKey('driver_id')) {
            // Log the type and value for debugging
            print('Driver ID type: ${_activeCheckIn['driver_id'].runtimeType}, value: ${_activeCheckIn['driver_id']}');

            // Handle both string and int formats for driver_id
            if (_activeCheckIn['driver_id'] is String) {
              driverId = int.tryParse(_activeCheckIn['driver_id']);
              print('Parsed driver ID from string: $driverId');
            } else if (_activeCheckIn['driver_id'] is int) {
              driverId = _activeCheckIn['driver_id'];
              print('Using driver ID as int: $driverId');
            } else {
              print('Warning: driver_id is neither a String nor an int. Type: ${_activeCheckIn['driver_id'].runtimeType}');
            }
          } else {
            print('Warning: No driver_id found in active check-in data');
          }

          // Check if widget is still mounted
          if (!mounted) return;

          // Initialize tracking service
          print('Initializing tracking service with userId: $userId, driverId: $driverId');
          final initialized = await _autoTrackingService.initialize(
            userId,
            driverId: driverId,
          );

          // Check if widget is still mounted
          if (!mounted) return;

          if (initialized) {
            print('Successfully initialized tracking service');

            // Also ensure background service is initialized
            final isBackgroundRunning = await _backgroundService.isRunning();

            // Check if widget is still mounted
            if (!mounted) return;

            if (!isBackgroundRunning) {
              print('Starting background location service for persistent tracking');
              await _backgroundService.startService(
                userId: userId,
                driverId: driverId,
              );

              // Check if widget is still mounted
              if (!mounted) return;

              print('Background location service started');
            }
          } else {
            print('Failed to initialize tracking service: ${_autoTrackingService.errorMessage}');
          }
        }

        // Only enable tracking if driver has active trips
        if (hasActiveTrips) {
          if (!isEnabled && isInitialized) {
            print('Driver has active trips but tracking is not enabled. Enabling...');

            // Check if widget is still mounted
            if (!mounted) return;

            await _autoTrackingService.enable();

            // Check if widget is still mounted
            if (!mounted) return;

            print('Tracking service enabled');

            // Also ensure background service is running
            final isBackgroundRunning = await _backgroundService.isRunning();

            // Check if widget is still mounted
            if (!mounted) return;

            if (!isBackgroundRunning) {
              print('Starting background location service for persistent tracking');

              // Safely extract driver ID from active check-in
              int? checkInDriverId;
              if (_activeCheckIn.containsKey('driver_id')) {
                if (_activeCheckIn['driver_id'] is String) {
                  checkInDriverId = int.tryParse(_activeCheckIn['driver_id']);
                } else if (_activeCheckIn['driver_id'] is int) {
                  checkInDriverId = _activeCheckIn['driver_id'];
                }
              }

              await _backgroundService.startService(
                userId: userId,
                driverId: checkInDriverId,
              );

              // Check if widget is still mounted
              if (!mounted) return;

              print('Background location service started');
            }
          }

          // Force a location update if tracking is active
          if (isInitialized && isEnabled && isTracking) {
            print('Forcing location update to ensure latest position is recorded');

            // Use a separate try-catch block for location updates to isolate errors
            try {
              await _autoTrackingService.forceLocationUpdate();

              // Check if widget is still mounted
              if (!mounted) return;

              // Also force update in background service
              await _backgroundService.forceLocationUpdate();
            } catch (e) {
              print('Error forcing location update: $e');
              // Continue execution even if location update fails
            }
          }
        } else {
          // Disable tracking if driver has no active trips but tracking is enabled
          if (isEnabled) {
            print('Driver has no active trips. Disabling tracking...');

            // Check if widget is still mounted
            if (!mounted) return;

            await _autoTrackingService.disable();

            // Check if widget is still mounted
            if (!mounted) return;

            print('Tracking service disabled');
          }
        }
      } else {
        // If driver has no active check-in, initialize tracking but don't enable it
        if (!isInitialized) {
          print('Driver has no active check-in. Initializing tracking service...');

          // Get driver ID from the app state
          int? driverId;

          // Try to get driver ID from user ID
          try {
            // Get driver details from API
            final response = await ApiService.get('drivers/user/${currentUser.userId}');

            // Check if widget is still mounted
            if (!mounted) return;

            if (response.statusCode == 200) {
              final data = jsonDecode(response.body);
              print('Driver API response data: $data');

              if (data.containsKey('driver_detail_id')) {
                // Log the type and value for debugging
                print('Driver ID type: ${data['driver_detail_id'].runtimeType}, value: ${data['driver_detail_id']}');

                // Handle both string and int formats for driver_id
                if (data['driver_detail_id'] is String) {
                  driverId = int.tryParse(data['driver_detail_id']);
                  print('Parsed driver ID from string: $driverId');
                } else if (data['driver_detail_id'] is int) {
                  driverId = data['driver_detail_id'];
                  print('Using driver ID as int: $driverId');
                }
              }
            }
          } catch (e) {
            print('Error getting driver ID: $e');
            // Continue execution even if getting driver ID fails
          }

          // Check if widget is still mounted
          if (!mounted) return;

          // Initialize tracking service
          print('Initializing tracking service with userId: $userId, driverId: $driverId');
          final initialized = await _autoTrackingService.initialize(
            userId,
            driverId: driverId,
          );

          // Check if widget is still mounted
          if (!mounted) return;

          if (initialized) {
            print('Successfully initialized tracking service');

            // Also ensure background service is initialized
            final isBackgroundRunning = await _backgroundService.isRunning();

            // Check if widget is still mounted
            if (!mounted) return;

            if (!isBackgroundRunning) {
              print('Starting background location service for persistent tracking');
              await _backgroundService.startService(
                userId: userId,
                driverId: driverId,
              );

              // Check if widget is still mounted
              if (!mounted) return;

              print('Background location service started');
            }
          } else {
            print('Failed to initialize tracking service: ${_autoTrackingService.errorMessage}');
          }
        }

        // Only enable tracking if driver has active trips
        if (hasActiveTrips) {
          if (!isEnabled && isInitialized) {
            print('Driver has active trips. Enabling tracking...');

            // Check if widget is still mounted
            if (!mounted) return;

            await _autoTrackingService.enable();

            // Check if widget is still mounted
            if (!mounted) return;

            print('Tracking service enabled');
          }
        } else {
          // Disable tracking if driver has no active trips but tracking is enabled
          if (isEnabled) {
            print('Driver has no active trips. Disabling tracking...');

            // Check if widget is still mounted
            if (!mounted) return;

            await _autoTrackingService.disable();

            // Check if widget is still mounted
            if (!mounted) return;

            print('Tracking service disabled');
          }
        }
      }

      print('Tracking status recheck completed');
    } catch (e, stackTrace) {
      print('Error rechecking tracking status: $e');
      print('Stack trace: $stackTrace');

      // Log more details about the active check-in data to help diagnose issues
      if (_hasActiveCheckIn) {
        print('Active check-in data during error: $_activeCheckIn');
        if (_activeCheckIn.containsKey('driver_id')) {
          print('Driver ID type during error: ${_activeCheckIn['driver_id'].runtimeType}');
        }
      }
    }
  }

  // This method is no longer used since we're not using time-based tracking anymore
  // We now track based on whether the driver has active trips
}

// Stateful dialog to show tracking information with auto-refresh
class TrackingInfoDialog extends StatefulWidget {
  final AutoLocationTrackingService autoTrackingService;

  const TrackingInfoDialog({super.key, required this.autoTrackingService});

  @override
  TrackingInfoDialogState createState() => TrackingInfoDialogState();
}

class TrackingInfoDialogState extends State<TrackingInfoDialog> {
  Timer? _refreshTimer;
  bool _hasActiveTrips = false;

  @override
  void initState() {
    super.initState();

    // Check if driver has active trips
    _checkActiveTrips();

    // Set up a timer to refresh the dialog every second
    _refreshTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          // This will rebuild the dialog with updated values
          _checkActiveTrips();
        });
      }
    });
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  // Check if driver has active trips
  void _checkActiveTrips() {
    // Get the parent DriverTripsScreenState
    final DriverTripsScreenState? parentState =
        context.findAncestorStateOfType<DriverTripsScreenState>();

    if (parentState != null) {
      _hasActiveTrips = parentState._hasActiveTrips();
    }
  }
  // Format last update time for display
  String formatLastUpdateTime(DateTime lastUpdateTime) {
    final now = DateTime.now();
    final difference = now.difference(lastUpdateTime);

    if (difference.inSeconds < 60) {
      return AppLocalizations.of(context).secondsAgo(difference.inSeconds);
    } else if (difference.inMinutes < 60) {
      return AppLocalizations.of(context).minutesAgo(difference.inMinutes);
    } else {
      return DateFormat('HH:mm:ss').format(lastUpdateTime);
    }
  }

  // Helper method to build info rows
  Widget buildInfoRow(String label, String value, {Color? color}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              color: color,
              fontWeight: color != null ? FontWeight.bold : FontWeight.normal,
            ),
            softWrap: true,
          ),
        ),
      ],
    );
  }

  // This method is no longer used since we're not using time-based tracking anymore
  // We now track based on whether the driver has active trips

  @override
  Widget build(BuildContext context) {
    final bool isAutoEnabled = widget.autoTrackingService.isEnabled;
    final bool isTracking = widget.autoTrackingService.isInitialized &&
                           widget.autoTrackingService.locationService.isTracking;
    final position = widget.autoTrackingService.locationService.currentPosition;
    final lastUpdateTime = widget.autoTrackingService.locationService.lastUpdateTime;

    // Get current date for display
    final DateTime now = DateTime.now().toLocal();

    return AlertDialog(
      // Make the dialog wider
      insetPadding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 24.0),
      contentPadding: const EdgeInsets.fromLTRB(24.0, 20.0, 24.0, 24.0),      title: Row(
        children: [
          Icon(
            isTracking ? Icons.gps_fixed : Icons.gps_off,
            color: isTracking ? Colors.green : Colors.grey,
          ),
          const SizedBox(width: 8),
          Text(
            AppLocalizations.of(context).locationTrackingStatus,
            style: const TextStyle(fontSize: 16),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,        children: [
          buildInfoRow(
            AppLocalizations.of(context).statusLabel,
            isTracking ? AppLocalizations.of(context).active : AppLocalizations.of(context).inactive,
            color: isTracking ? Colors.green : Colors.grey,
          ),
          const SizedBox(height: 8),
          buildInfoRow(
            AppLocalizations.of(context).autoTrackingLabel,
            isAutoEnabled ? AppLocalizations.of(context).enabled : AppLocalizations.of(context).disabled,
            color: isAutoEnabled ? Colors.blue : Colors.grey,
          ),
          const SizedBox(height: 8),
          buildInfoRow(
            AppLocalizations.of(context).activeTripsLabel,
            _hasActiveTrips ? AppLocalizations.of(context).yes : AppLocalizations.of(context).no,
            color: _hasActiveTrips ? Colors.green : Colors.red,
          ),
          const SizedBox(height: 8),
          buildInfoRow(
            AppLocalizations.of(context).currentTimeLabel,
            DateFormat('HH:mm:ss').format(DateTime.now()),
          ),
          const SizedBox(height: 8),
          buildInfoRow(
            AppLocalizations.of(context).todayLabel,
            DateFormat('EEEE, MMMM d, yyyy').format(now),
          ),
          if (position != null) ...[
            const SizedBox(height: 8),
            buildInfoRow(
              AppLocalizations.of(context).locationLabel,
              '${position.latitude.toStringAsFixed(6)}, ${position.longitude.toStringAsFixed(6)}',
            ),
          ],
          if (lastUpdateTime != null) ...[
            const SizedBox(height: 8),
            buildInfoRow(
              AppLocalizations.of(context).lastUpdateLabel,
              formatLastUpdateTime(lastUpdateTime),
            ),
          ],
          const SizedBox(height: 16),
          Text(
            AppLocalizations.of(context).trackingExplanation,
            style: const TextStyle(fontSize: 12, fontStyle: FontStyle.italic),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(AppLocalizations.of(context).close.toUpperCase()),
        ),
      ],
    );
  }
}



