import 'dart:convert';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import '../generated/l10n/app_localizations.dart';
import '../services/api_service.dart';
import '../sidebar.dart';
import '../widgets/common_app_bar.dart';
import 'add_destination_screen.dart';
import 'edit_destination_screen.dart';

class Destination {
  final String destinationCode;
  final String destination;
  final String address;
  final String notes; // Previously named 'duration'
  final String latitudeLongitude;
  final int durationMinutes; // Duration in minutes
  final String initial; // New initial field
  final bool isActive; // New is_active field

  Destination({
    required this.destinationCode,
    required this.destination,
    required this.address,
    required this.notes,
    required this.latitudeLongitude,
    required this.initial, // Initial is now required
    this.durationMinutes = 0, // Default to 0 minutes
    this.isActive = true, // Default to active
  });

  factory Destination.fromJson(Map<String, dynamic> json) {
    // Debug: Print the raw JSON data
    print('Destination JSON: $json');

    // Try to parse duration as minutes from the duration field
    int minutes = 0;
    try {
      final durationStr = json['duration'].toString();
      if (durationStr.isNotEmpty) {
        minutes = int.tryParse(durationStr) ?? 0;
      }
    } catch (e) {
      // If parsing fails, default to 0
      minutes = 0;
    }

    // Debug: Print the notes field
    print('Notes field: ${json['notes']}');

    // Ensure initial is not null or empty, default to first 3 chars of destination if missing
    String initialValue = json['initial'] ?? '';
    if (initialValue.isEmpty && json['destination'] != null) {
      // If initial is empty, use the first 3 characters of the destination name
      final destName = json['destination'].toString();
      initialValue = destName.length >= 3 ? destName.substring(0, 3) : destName.padRight(3, 'X');
    }

    return Destination(
      destinationCode: json['destinationCode'],
      destination: json['destination'],
      address: json['address'],
      notes: json['notes'] ?? '', // Use actual notes field
      latitudeLongitude: json['latitudeLongitude'],
      durationMinutes: minutes,
      initial: initialValue.toUpperCase(), // Ensure initial is uppercase and not empty
      isActive: json['isActive'] == null ? true : json['isActive'] == true,
    );
  }

  // Format duration as hours and minutes
  String get formattedDuration {
    if (durationMinutes <= 0) return '';

    final hours = durationMinutes ~/ 60;
    final minutes = durationMinutes % 60;

    if (hours > 0) {
      return '$hours hr ${minutes > 0 ? '$minutes min' : ''}';
    } else {
      return '$minutes min';
    }
  }
}

class DestinationSettingsScreen extends StatefulWidget {
  const DestinationSettingsScreen({super.key});

  @override
  DestinationSettingsScreenState createState() => DestinationSettingsScreenState();
}

class DestinationSettingsScreenState extends State<DestinationSettingsScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  List<Destination> _destinations = [];
  List<Destination> _filteredDestinations = [];
  bool _isLoading = true;
  String _errorMessage = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _fetchDestinations();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterDestinations(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredDestinations = _destinations; // Show all destinations when search is empty
      } else {
        _filteredDestinations = _destinations
            .where((destination) =>
                destination.destinationCode.toLowerCase().contains(query.toLowerCase()) ||
                destination.destination.toLowerCase().contains(query.toLowerCase()) ||
                destination.address.toLowerCase().contains(query.toLowerCase()) ||
                destination.initial.toLowerCase().contains(query.toLowerCase()))
            .toList();
      }
    });
  }

  Future<void> _fetchDestinations() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final response = await ApiService.get('destinations');

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = jsonDecode(response.body);
        // Debug: Print the API response
        print('API Response: ${response.body}');

        if (data['destinations'] != null) {
          setState(() {
            _destinations = (data['destinations'] as List)
                .map((json) => Destination.fromJson(json))
                .toList();
            _filteredDestinations = _destinations; // Initialize filtered list with all destinations
            _isLoading = false;
          });
        } else {
          throw Exception('No destinations data found in response');
        }      } else {
        final errorData = jsonDecode(response.body);
        if (mounted) {
          throw Exception(errorData['error'] ?? AppLocalizations.of(context).failedToLoadDestinations(response.statusCode.toString()));
        } else {
          throw Exception(errorData['error'] ?? 'Failed to load destinations: ${response.statusCode}');
        }
      }
    } catch (e, stackTrace) {
      print('Error fetching destinations: $e');
      print('Stack trace: $stackTrace');

      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Error: $e';
        });        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${AppLocalizations.of(context).failedToLoadDestinations('')}: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _deleteDestination(Destination destination) async {
    try {
      final response = await ApiService.delete('destinations/${destination.destinationCode}');

      if (response.statusCode == 200) {        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.of(context).destinationDeletedSuccessfully),
              backgroundColor: Colors.green,
            ),
          );
          _fetchDestinations();
        }
      } else {
        final errorData = jsonDecode(response.body);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(errorData['error'] ?? AppLocalizations.of(context).failedToDeleteDestination),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${AppLocalizations.of(context).anErrorOccurred}: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
  void _showDeleteConfirmation(Destination destination) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(AppLocalizations.of(context).confirmDelete),
          content: Text(AppLocalizations.of(context).confirmDeleteDestination(destination.destinationCode)),
          actions: <Widget>[
            TextButton(
              child: Text(AppLocalizations.of(context).cancel),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text(AppLocalizations.of(context).delete),
              onPressed: () {
                Navigator.of(context).pop();
                _deleteDestination(destination);
              },
            ),
          ],
        );
      },
    );
  }

  // Build a mobile-friendly card for each destination
  Widget _buildDestinationCard(Destination destination) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Destination code and name
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: const Color(0xFF0D47A1).withAlpha(25),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        destination.destinationCode,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF0D47A1),
                        ),
                      ),
                    ),
                    if (destination.initial.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4, left: 4),
                        child: Text(
                          'Initial: ${destination.initial}',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                      ),
                  ],
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(left: 8.0),
                    child: Text(
                      destination.destination,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                      textAlign: TextAlign.end,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            // Address
            Row(
              children: [
                const Icon(Icons.location_on, size: 16, color: Colors.grey),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    destination.address,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            // Notes
            if (destination.notes.isNotEmpty)
              Row(
                children: [
                  const Icon(Icons.note, size: 16, color: Colors.grey),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      'Notes: ${destination.notes}',
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            const SizedBox(height: 4),
            // Duration
            if (destination.durationMinutes > 0)
              Row(
                children: [
                  const Icon(Icons.timer, size: 16, color: Colors.grey),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      'Duration: ${destination.formattedDuration}',
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            const SizedBox(height: 4),
            // Coordinates
            Row(
              children: [
                const Icon(Icons.map, size: 16, color: Colors.grey),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    'Coordinates: ${destination.latitudeLongitude}',
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [                TextButton.icon(
                  icon: const Icon(Icons.edit, size: 18),
                  label: Text(AppLocalizations.of(context).edit),
                  style: TextButton.styleFrom(
                    foregroundColor: const Color(0xFF0D47A1),
                  ),
                  onPressed: () async {
                    final result = await Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => EditDestinationScreen(destination: destination),
                      ),
                    );
                    if (result == true) {
                      _fetchDestinations();
                    }
                  },
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  icon: const Icon(Icons.delete, size: 18),
                  label: Text(AppLocalizations.of(context).delete),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.red,
                  ),
                  onPressed: () => _showDeleteConfirmation(destination),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Check if we're on a small screen (mobile)
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600 && !kIsWeb;

    return Scaffold(
      key: _scaffoldKey,
      drawer: const SidebarNavigation(),
      appBar: CommonAppBar(
        title: AppLocalizations.of(context).destinationSettingsTitle,
        onRefresh: _fetchDestinations,
        showMenuIcon: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage.isNotEmpty
              ? Center(child: Text(_errorMessage))
              : Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: TextField(
                        controller: _searchController,                        decoration: InputDecoration(
                          labelText: AppLocalizations.of(context).searchDestinations,
                          hintText: AppLocalizations.of(context).searchDestinationsHint,
                          prefixIcon: const Icon(Icons.search),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          filled: true,
                          fillColor: Colors.white,
                          suffixIcon: _searchController.text.isNotEmpty
                              ? IconButton(
                                  icon: const Icon(Icons.clear),
                                  onPressed: () {
                                    _searchController.clear();
                                    _filterDestinations('');
                                  },
                                )
                              : null,
                        ),
                        onChanged: _filterDestinations,
                      ),
                    ),
                    Expanded(
                      child: _filteredDestinations.isEmpty                          ? Center(
                              child: Text(
                                _searchController.text.isEmpty
                                    ? AppLocalizations.of(context).noDestinationsFound
                                    : AppLocalizations.of(context).noMatchingDestinationsFound,
                              ),
                            )
                          : isSmallScreen
                              // Mobile layout - card list
                              ? RefreshIndicator(
                                  onRefresh: () async {
                                    await _fetchDestinations();
                                  },
                                  child: ListView.builder(
                                    padding: const EdgeInsets.all(8.0),
                                    itemCount: _filteredDestinations.length,
                                    itemBuilder: (context, index) {
                                      return _buildDestinationCard(_filteredDestinations[index]);
                                    },
                                  ),
                                )
                              // Desktop layout - data table with horizontal scroll
                              : SingleChildScrollView(
                                  scrollDirection: Axis.horizontal,
                                  child: SingleChildScrollView(
                                    child: Padding(
                                      padding: const EdgeInsets.all(16.0),
                                      child: Theme(
                                        data: Theme.of(context).copyWith(
                                          dataTableTheme: DataTableThemeData(
                                            columnSpacing: 16,
                                            horizontalMargin: 16,
                                            headingTextStyle: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                              color: Color(0xFF0D47A1),
                                            ),
                                          ),
                                        ),
                                        child: DataTable(
                                          headingRowColor: WidgetStateProperty.resolveWith<Color?>(
                                            (Set<WidgetState> states) => const Color(0xFF0D47A1).withAlpha(25),
                                          ),                                          columns: [
                                            DataColumn(label: Text(AppLocalizations.of(context).destinationCode)),
                                            DataColumn(label: Text(AppLocalizations.of(context).initial)),
                                            DataColumn(label: Text(AppLocalizations.of(context).destination)),
                                            DataColumn(label: Text(AppLocalizations.of(context).address)),
                                            DataColumn(label: Text(AppLocalizations.of(context).notes)),
                                            DataColumn(label: Text(AppLocalizations.of(context).duration)),
                                            DataColumn(label: Text('Coordinates')), // Keep as is since this is technical
                                            DataColumn(label: Text(AppLocalizations.of(context).actions)),
                                          ],
                                          rows: _filteredDestinations.map((destination) {
                                            return DataRow(
                                              cells: [
                                                DataCell(Text(destination.destinationCode)),
                                                DataCell(Text(destination.initial)),
                                                DataCell(Text(destination.destination)),
                                                DataCell(Text(destination.address)),
                                                DataCell(Text(destination.notes)),
                                                DataCell(Text(destination.formattedDuration)),
                                                DataCell(Text(destination.latitudeLongitude)),
                                                DataCell(
                                                  Row(
                                                    mainAxisSize: MainAxisSize.min,
                                                    children: [
                                                      IconButton(
                                                        icon: const Icon(Icons.edit),
                                                        color: const Color(0xFF0D47A1),
                                                        onPressed: () async {
                                                          final result = await Navigator.push(
                                                            context,
                                                            MaterialPageRoute(
                                                              builder: (context) =>
                                                                  EditDestinationScreen(destination: destination),
                                                            ),
                                                          );
                                                          if (result == true) {
                                                            _fetchDestinations();
                                                          }
                                                        },
                                                      ),
                                                      IconButton(
                                                        icon: const Icon(Icons.delete),
                                                        color: Colors.red,
                                                        onPressed: () => _showDeleteConfirmation(destination),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            );
                                          }).toList(),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                    ),
                  ],
                ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: const Color(0xFF0D47A1),
        child: const Icon(Icons.add),
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const AddDestinationScreen()),
          );
          if (result == true) {
            _fetchDestinations();
          }
        },
      ),
    );
  }
}










