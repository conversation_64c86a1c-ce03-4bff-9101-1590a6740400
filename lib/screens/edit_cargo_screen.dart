import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../generated/l10n/app_localizations.dart';
import '../models/cargo.dart';
import '../services/api_service.dart';

class EditCargoScreen extends StatefulWidget {
  final Cargo cargo;

  const EditCargoScreen({super.key, required this.cargo});

  @override
  EditCargoScreenState createState() => EditCargoScreenState();
}

// Custom formatter to convert text to uppercase
class UpperCaseTextFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    return TextEditingValue(
      text: newValue.text.toUpperCase(),
      selection: newValue.selection,
    );
  }
}

class EditCargoScreenState extends State<EditCargoScreen> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _cargoCodeController;
  late final TextEditingController _cargoNameController;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _cargoCodeController = TextEditingController(text: widget.cargo.cargoCode);
    _cargoNameController = TextEditingController(text: widget.cargo.cargoName.toUpperCase());
  }

  @override
  void dispose() {
    _cargoCodeController.dispose();
    _cargoNameController.dispose();
    super.dispose();
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final response = await ApiService.put(
        'cargos/${widget.cargo.cargoCode}',
        {
          'cargo_name': _cargoNameController.text.toUpperCase(),
        },
      );

      if (response.statusCode == 200) {
        if (mounted) {
          Navigator.pop(context, true);
        }
      } else {
        final errorData = jsonDecode(response.body);        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(errorData['error'] ?? AppLocalizations.of(context).failedToUpdateCargo),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${AppLocalizations.of(context).anErrorOccurred}: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(      appBar: AppBar(
        title: Text(
          AppLocalizations.of(context).editCargo,
          style: const TextStyle(color: Colors.white), // Making text white
        ),
        backgroundColor: const Color(0xFF0D47A1),
        iconTheme: const IconThemeData(color: Colors.white), // Making icons white
        foregroundColor: Colors.white, // This ensures all content is white
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      TextFormField(
                        controller: _cargoCodeController,                        decoration: InputDecoration(
                          labelText: AppLocalizations.of(context).cargoCode,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          filled: true,
                          fillColor: Colors.grey[200],
                        ),
                        enabled: false,
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _cargoNameController,                        decoration: InputDecoration(
                          labelText: AppLocalizations.of(context).cargoName,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        textCapitalization: TextCapitalization.characters,
                        inputFormatters: [
                          UpperCaseTextFormatter(),
                        ],                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return AppLocalizations.of(context).pleaseEnterCargoName;
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton(
                        onPressed: _submitForm,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF0D47A1),
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),                        child: Text(
                          _isLoading ? AppLocalizations.of(context).updating : AppLocalizations.of(context).updateCargo,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white, // Ensuring text is white
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
    );
  }
}




