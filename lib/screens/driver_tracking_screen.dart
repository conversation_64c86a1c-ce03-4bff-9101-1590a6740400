import 'package:flutter/foundation.dart' show kIsWeb, defaultTargetPlatform, TargetPlatform;
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import '../generated/l10n/app_localizations.dart';
import '../models/driver.dart';
import '../models/trip.dart';
import '../services/api_service.dart';
import '../sidebar.dart';
import '../utils/auto_refresh.dart';
import '../widgets/common_app_bar.dart';

class DriverTrackingScreen extends StatefulWidget {
  const DriverTrackingScreen({super.key});

  @override
  State<DriverTrackingScreen> createState() => _DriverTrackingScreenState();
}

class _DriverTrackingScreenState extends State<DriverTrackingScreen> with AutoRefreshMixin {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  List<Driver> _drivers = [];
  Map<int, LatLng> _driverLocations = {};
  Map<int, DateTime> _driverLastUpdated = {}; // Store last updated timestamps
  Map<int, List<Trip>> _driverTripsInProgress = {}; // Store trips in progress for each driver (can have multiple)
  Map<int, List<Trip>> _driverBackToBaseTrips = {}; // Store BACK TO BASE trips for each driver
  bool _isLoading = true;
  String _errorMessage = '';
  int? _selectedDriverId; // Track the currently selected driver
  // Initialize map controllers in initState
  late final MapController _mapController; // Controller for the web map
  late final MapController _mobileMapController; // Controller for the mobile map

  // We'll use London coordinates (51.5074, -0.1278) as the default center if no locations are available

  @override
  void initState() {
    super.initState();
    // Initialize map controllers here
    _mapController = MapController();
    _mobileMapController = MapController();

    // Set loading state
    _isLoading = true;

    // Log platform information for debugging
    print('Platform detection: kIsWeb = $kIsWeb');
    print('Platform detection: defaultTargetPlatform = $defaultTargetPlatform');
    print('Platform detection: _isMobilePlatform() = ${_isMobilePlatform()}');

    // Fetch drivers, their locations, and trips in progress
    _fetchDrivers().then((_) {
      _fetchDriverLocations();
      _fetchTripsInProgress();
    });

    // Initialize auto-refresh functionality
    initAutoRefresh();
  }



  @override
  void dispose() {
    // Clean up auto-refresh timers
    disposeAutoRefresh();
    super.dispose();
  }

  // Implement the refreshData method required by AutoRefreshMixin
  @override
  Future<void> refreshData({bool showSnackbar = true}) async {
    await _refreshAllData(showSnackbar: showSnackbar);
  }

  // Refresh all data
  Future<void> _refreshAllData({bool showSnackbar = true}) async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _fetchDrivers();
      await _fetchDriverLocations();
      await _fetchTripsInProgress();

      if (mounted) {
        setState(() {
          _isLoading = false;
        });        if (showSnackbar) {
          showRefreshSnackbar(AppLocalizations.of(context).dataRefreshedSuccessfully);
        }
      }
    } catch (e) {
      if (mounted) {        setState(() {
          _errorMessage = AppLocalizations.of(context).errorRefreshingData(e.toString());
          _isLoading = false;
        });

        if (showSnackbar) {
          showRefreshSnackbar(AppLocalizations.of(context).errorRefreshingData(e.toString()), isError: true);
        }
      }
    }
  }

  // Fetch all drivers
  Future<void> _fetchDrivers() async {
    try {
      final response = await ApiService.get('drivers');

      if (response.statusCode == 200) {
        final data = ApiService.parseResponse(response);

        if (mounted) {
          setState(() {
            _drivers = List<Driver>.from(
              data['drivers'].map((driver) => Driver.fromJson(driver))
            );
          });
        }      } else {
        if (mounted) {
          throw Exception(AppLocalizations.of(context).failedToLoadDrivers);
        }
      }} catch (e) {
      if (mounted) {
        final localizations = AppLocalizations.of(context);
        setState(() {
          _errorMessage = localizations.errorFetchingDrivers(e.toString());
        });
      }
    }
  }

  // Fetch driver locations from the API
  Future<void> _fetchDriverLocations() async {
    print('Fetching driver locations...');
    try {
      // Set loading state
      if (mounted) {
        setState(() {
          _isLoading = true;
          _errorMessage = '';
        });
      }

      print('Fetching driver locations from API');

      // Call the API to get the latest driver locations
      final response = await ApiService.get('driver-locations/latest');

      if (response.statusCode == 200) {
        final data = ApiService.parseResponse(response);
        print('API response data: $data');
        final locations = data['locations'] as List;
        print('Found ${locations.length} driver locations');

      // Process the locations data
        final Map<int, LatLng> driverLocations = {};
        final Map<int, DateTime> driverLastUpdated = {};

        for (var location in locations) {
          // Parse the driver_id (convert from string to int if needed)
          int driverId;
          if (location['driver_id'] is String) {
            driverId = int.tryParse(location['driver_id'] as String) ?? 0;
          } else if (location['driver_id'] is int) {
            driverId = location['driver_id'] as int;
          } else {
            // Skip this location if driver_id is invalid
            print('Invalid driver_id type: ${location['driver_id'].runtimeType}');
            continue;
          }
          
          // Check if the driver is active in our drivers list
          final driver = _drivers.firstWhere(
            (d) => d.driverId == driverId,
            orElse: () => Driver(
              driverId: driverId,
              driverCode: '',
              name: '',
              initial: '',
              carCode: '',
              isActive: false, // Default unknown drivers to inactive
            ),
          );
          
          // Skip inactive drivers
          if (!driver.isActive) {
            print('Skipping inactive driver: ${driver.driverId}, ${driver.name}');
            continue;
          }

          // Print the location data for debugging
          print('Processing location: $location');

          // Parse the timestamp
          DateTime? timestamp;
          if (location['timestamp'] != null) {
            try {
              timestamp = DateTime.parse(location['timestamp'].toString());
              driverLastUpdated[driverId] = timestamp;
              print('Timestamp for driver $driverId: $timestamp');
            } catch (e) {
              print('Error parsing timestamp: $e');
            }
          }

          // Parse the latitude_longitude string (format: "lat,lng")
          final String latLngStr = location['latitude_longitude'] as String;
          print('Latitude/longitude string: $latLngStr');
          final List<String> parts = latLngStr.split(',');

          if (parts.length == 2) {
            try {
              final double lat = double.parse(parts[0]);
              final double lng = double.parse(parts[1]);
              driverLocations[driverId] = LatLng(lat, lng);
              print('Added location for driver $driverId: $lat,$lng');
            } catch (e) {
              print('Error parsing coordinates: $e');
            }
          }
        }

        // If no locations were found, just set empty locations and show the empty state UI
        if (driverLocations.isEmpty) {
          if (mounted) {
            setState(() {
              _driverLocations = {}; // Ensure it's empty
              _driverLastUpdated = {}; // Clear timestamps
              _isLoading = false;
            });
          }
          return;
        }

        if (mounted) {
          setState(() {
            _driverLocations = driverLocations;
            _driverLastUpdated = driverLastUpdated;
            _isLoading = false;
            _errorMessage = ''; // Clear any previous error
          });
        }
      } else {
        throw Exception('Failed to load driver locations: ${response.statusCode}');
      }
    } catch (e) {
      print('Error in _fetchDriverLocations: $e');
      // If there's an error, show the error message
      if (mounted) {
        setState(() {
          _errorMessage = 'Error fetching driver locations: $e';
          _isLoading = false;
          // Keep any existing driver locations to avoid a blank screen
          if (_driverLocations.isEmpty) {
            // If we have no locations, show a default location
            _driverLocations = {
              0: LatLng(51.5074, -0.1278) // London as default
            };
          }
        });
      }

      // Try to fetch trips anyway, even if driver locations failed
      try {
        await _fetchTripsInProgress();
      } catch (tripError) {
        print('Error fetching trips after driver location error: $tripError');
      }
    }
  }

  // Fetch trips in progress and back to base for drivers
  Future<void> _fetchTripsInProgress() async {
    print('Fetching trips in progress and back to base...');
    try {
      // Get trips with status TRIP IN PROGRESS and BACK TO BASE using the dedicated endpoint
      print('Fetching trips in progress from API');

      final response = await ApiService.get('trips-in-progress');

      if (response.statusCode == 200) {
        final data = ApiService.parseResponse(response);
        final trips = data['trips'] as List;
        print('Found ${trips.length} trips (TRIP IN PROGRESS and BACK TO BASE)');

        // Debug: Print all trips
        for (var tripData in trips) {
          print('Trip data: $tripData');
          print('Trip status: ${tripData['status']}');
          print('Driver ID: ${tripData['driver_id']}');
          print('Driver Code: ${tripData['driver_code']}');
        }

        // Process the trips data
        final Map<int, List<Trip>> driverTripsInProgress = {};
        final Map<int, List<Trip>> driverBackToBaseTrips = {};

        // Clear any existing trips to avoid duplicates
        print('Clearing existing trips');

        for (var tripData in trips) {
          final trip = Trip.fromJson(tripData);
          print('Processing trip ${trip.tripCode} with status ${trip.status}');

          // Check if the trip has a driver ID
          if (trip.driverId != null) {
            // Determine which map to use based on trip status
            if (trip.status == 'BACK TO BASE') {
              print('Trip ${trip.tripCode} is a BACK TO BASE trip for driver ${trip.driverId}');

              // Add trip to the driver's list of BACK TO BASE trips
              if (driverBackToBaseTrips.containsKey(trip.driverId!)) {
                // Check if this trip is already in the list
                final bool isDuplicate = driverBackToBaseTrips[trip.driverId!]!.any((existingTrip) =>
                  existingTrip.tripId == trip.tripId || existingTrip.tripCode == trip.tripCode);

                if (!isDuplicate) {
                  driverBackToBaseTrips[trip.driverId!]!.add(trip);
                  print('Added BACK TO BASE trip ${trip.tripCode} for driver ${trip.driverId}');
                } else {
                  print('Skipped duplicate BACK TO BASE trip ${trip.tripCode} for driver ${trip.driverId}');
                }
              } else {
                driverBackToBaseTrips[trip.driverId!] = [trip];
                print('Added first BACK TO BASE trip ${trip.tripCode} for driver ${trip.driverId}');
              }
            } else {
              // Add trip to the driver's list of TRIP IN PROGRESS trips
              if (driverTripsInProgress.containsKey(trip.driverId!)) {
                // Check if this trip is already in the list
                final bool isDuplicate = driverTripsInProgress[trip.driverId!]!.any((existingTrip) =>
                  existingTrip.tripId == trip.tripId || existingTrip.tripCode == trip.tripCode);

                if (!isDuplicate) {
                  driverTripsInProgress[trip.driverId!]!.add(trip);
                  print('Added TRIP IN PROGRESS trip ${trip.tripCode} for driver ${trip.driverId}');
                } else {
                  print('Skipped duplicate TRIP IN PROGRESS trip ${trip.tripCode} for driver ${trip.driverId}');
                }
              } else {
                driverTripsInProgress[trip.driverId!] = [trip];
                print('Added first TRIP IN PROGRESS trip ${trip.tripCode} for driver ${trip.driverId}');
              }
            }
          } else {
            // If no driver ID is present in the trip, check if we can match by driver code
            if (trip.driverCode != null && trip.driverCode!.isNotEmpty) {
              // Find the driver with this driver code
              final matchingDriver = _drivers.firstWhere(
                (driver) => driver.driverCode == trip.driverCode,
                orElse: () => Driver(
                  driverId: -1,
                  driverCode: '',
                  name: AppLocalizations.of(context).unknown,
                  initial: 'U',
                  carCode: '',
                ),
              );

              // If we found a matching driver, associate the trip with that driver
              if (matchingDriver.driverId != -1) {
                // Determine which map to use based on trip status
                if (trip.status == 'BACK TO BASE') {
                  print('Trip ${trip.tripCode} is a BACK TO BASE trip for driver ${matchingDriver.driverId} (matched by driver code)');

                  // Add trip to the driver's list of BACK TO BASE trips
                  if (driverBackToBaseTrips.containsKey(matchingDriver.driverId)) {
                    // Check if this trip is already in the list
                    final bool isDuplicate = driverBackToBaseTrips[matchingDriver.driverId]!.any((existingTrip) =>
                      existingTrip.tripId == trip.tripId || existingTrip.tripCode == trip.tripCode);

                    if (!isDuplicate) {
                      driverBackToBaseTrips[matchingDriver.driverId]!.add(trip);
                      print('Added BACK TO BASE trip ${trip.tripCode} for driver ${matchingDriver.driverId} (matched by driver code)');
                    } else {
                      print('Skipped duplicate BACK TO BASE trip ${trip.tripCode} for driver ${matchingDriver.driverId} (matched by driver code)');
                    }
                  } else {
                    driverBackToBaseTrips[matchingDriver.driverId] = [trip];
                    print('Added first BACK TO BASE trip ${trip.tripCode} for driver ${matchingDriver.driverId} (matched by driver code)');
                  }
                } else {
                  // Add trip to the driver's list of TRIP IN PROGRESS trips
                  if (driverTripsInProgress.containsKey(matchingDriver.driverId)) {
                    // Check if this trip is already in the list
                    final bool isDuplicate = driverTripsInProgress[matchingDriver.driverId]!.any((existingTrip) =>
                      existingTrip.tripId == trip.tripId || existingTrip.tripCode == trip.tripCode);

                    if (!isDuplicate) {
                      driverTripsInProgress[matchingDriver.driverId]!.add(trip);
                      print('Added TRIP IN PROGRESS trip ${trip.tripCode} for driver ${matchingDriver.driverId} (matched by driver code)');
                    } else {
                      print('Skipped duplicate TRIP IN PROGRESS trip ${trip.tripCode} for driver ${matchingDriver.driverId} (matched by driver code)');
                    }
                  } else {
                    driverTripsInProgress[matchingDriver.driverId] = [trip];
                    print('Added first TRIP IN PROGRESS trip ${trip.tripCode} for driver ${matchingDriver.driverId} (matched by driver code)');
                  }
                }
              } else {
                print('Could not find a driver for trip ${trip.tripCode} with driver code ${trip.driverCode}');
              }
            } else {
              print('Trip ${trip.tripCode} has no driver ID or driver code');
            }
          }
        }

        // If we couldn't match any trips to drivers, log a message
        if (driverTripsInProgress.isEmpty && driverBackToBaseTrips.isEmpty && trips.isNotEmpty) {
          print('No trips could be matched to any drivers. Please ensure trips have proper driver_id or driver_code values.');
        }

        // Debug: Print all BACK TO BASE trips before setting state
        print('BACK TO BASE trips before setting state:');
        driverBackToBaseTrips.forEach((driverId, trips) {
          print('Driver $driverId has ${trips.length} BACK TO BASE trips:');
          for (var trip in trips) {
            print('  - Trip ${trip.tripCode}, Status: ${trip.status}');
          }
        });

        if (mounted) {
          setState(() {
            _driverTripsInProgress = driverTripsInProgress;
            _driverBackToBaseTrips = driverBackToBaseTrips;

            // Debug: Print all BACK TO BASE trips after setting state
            print('BACK TO BASE trips after setting state:');
            _driverBackToBaseTrips.forEach((driverId, trips) {
              print('Driver $driverId has ${trips.length} BACK TO BASE trips:');
              for (var trip in trips) {
                print('  - Trip ${trip.tripCode}, Status: ${trip.status}');
              }
            });
          });
        }
      } else {
        print('Failed to load trips: ${response.statusCode}');
      }
    } catch (e) {
      print('Error fetching trips: $e');
    }
  }



  // Calculate the center of the map based on driver locations
  LatLng _calculateMapCenter() {
    // If no driver locations are available, use London as default center
    if (_driverLocations.isEmpty) {
      return LatLng(51.5074, -0.1278); // London center
    }

    // If a driver is selected, center on that driver
    if (_selectedDriverId != null && _driverLocations.containsKey(_selectedDriverId)) {
      return _driverLocations[_selectedDriverId]!;
    }

    // Otherwise, center on the average of all driver locations
    double totalLat = 0;
    double totalLng = 0;

    _driverLocations.forEach((_, location) {
      totalLat += location.latitude;
      totalLng += location.longitude;
    });

    return LatLng(
      totalLat / _driverLocations.length,
      totalLng / _driverLocations.length,
    );
  }

  // Center the map on a specific driver
  void _centerMapOnDriver(int driverId) {
    if (!_driverLocations.containsKey(driverId)) {
      return; // Driver has no location
    }

    // Update the selected driver
    setState(() {
      _selectedDriverId = driverId;
    });

    // Get the driver's location
    final location = _driverLocations[driverId]!;

    // Center the map on the driver's location with animation
    // Use the appropriate map controller based on the platform
    if (!kIsWeb && _isMobilePlatform()) {
      _mobileMapController.move(location, 14.0); // Zoom in closer to the driver on mobile
    } else {
      _mapController.move(location, 14.0); // Zoom in closer to the driver on web
    }

    // Show a snackbar to indicate the map has centered on the driver
    final driver = _drivers.firstWhere(
      (d) => d.driverId == driverId,      orElse: () => Driver(
        driverId: driverId,
        driverCode: AppLocalizations.of(context).unknown,
        name: AppLocalizations.of(context).unknownDriver,
        initial: 'U',
        carCode: '',
        lastUpdated: _driverLastUpdated[driverId],
        isActive: false, // Default to inactive for unknown drivers
      ),
    );

    // Check if the driver has a trip in progress or back to base
    final hasTripInProgress = _driverTripsInProgress.containsKey(driverId);
    final hasBackToBase = _driverBackToBaseTrips.containsKey(driverId);

    // If the driver has any trips, show the trip details dialog
    if (hasTripInProgress || hasBackToBase) {
      _showTripDetailsDialog(driverId);
    } else {
      // Otherwise, just show a snackbar
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context).centeredOnDriver(driver.name)),
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  // Show trip details dialog for a driver with trips in progress or back to base
  void _showTripDetailsDialog(int driverId) {
    print('Showing trip details dialog for driver ID: $driverId');

    // Get the driver
    final driver = _drivers.firstWhere(      (d) => d.driverId == driverId,
      orElse: () => Driver(
        driverId: driverId,
        driverCode: AppLocalizations.of(context).unknown,
        name: AppLocalizations.of(context).unknownDriver,
        initial: 'U',
        carCode: '',
        lastUpdated: _driverLastUpdated[driverId],
      ),
    );

    print('Driver found: ${driver.name} (${driver.driverCode})');

    // Get the trips (both BACK TO BASE and TRIP IN PROGRESS)
    final List<Trip> allTrips = [];

    // Add BACK TO BASE trips first (priority)
    if (_driverBackToBaseTrips.containsKey(driverId)) {
      print('Found ${_driverBackToBaseTrips[driverId]!.length} BACK TO BASE trips for driver $driverId');
      allTrips.addAll(_driverBackToBaseTrips[driverId]!);

      // Debug: Print all BACK TO BASE trips
      for (var trip in _driverBackToBaseTrips[driverId]!) {
        print('BACK TO BASE trip: ${trip.tripCode}, Status: ${trip.status}');
      }
    } else {
      print('No BACK TO BASE trips found for driver $driverId');
    }

    // Add TRIP IN PROGRESS trips
    if (_driverTripsInProgress.containsKey(driverId)) {
      print('Found ${_driverTripsInProgress[driverId]!.length} TRIP IN PROGRESS trips for driver $driverId');
      allTrips.addAll(_driverTripsInProgress[driverId]!);

      // Debug: Print all TRIP IN PROGRESS trips
      for (var trip in _driverTripsInProgress[driverId]!) {
        print('TRIP IN PROGRESS trip: ${trip.tripCode}, Status: ${trip.status}');
      }
    } else {
      print('No TRIP IN PROGRESS trips found for driver $driverId');
    }

    print('Total trips for dialog: ${allTrips.length}');

    // Calculate the dialog height based on the number of trips
    // but cap it at 80% of the screen height
    final double dialogHeight = MediaQuery.of(context).size.height * 0.8;

    // Show the dialog
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              // Show icon based on whether there are BACK TO BASE trips
              _driverBackToBaseTrips.containsKey(driverId)
                ? Icon(Icons.home, color: Colors.deepOrange.shade800)
                : Icon(Icons.directions_car, color: Colors.amber.shade800),
              const SizedBox(width: 8),
              Text(AppLocalizations.of(context).driverTrips(allTrips.length)),
            ],
          ),
          content: SizedBox(
            width: double.maxFinite,
            height: dialogHeight,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Driver information section (fixed at top)
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [                    _buildDetailRow(AppLocalizations.of(context).driver, driver.name),
                    _buildDetailRow(AppLocalizations.of(context).driverCode, driver.driverCode),
                    if (_driverLastUpdated.containsKey(driverId))
                      _buildDetailRow(
                        AppLocalizations.of(context).lastUpdated,
                        _formatLastUpdated(_driverLastUpdated[driverId]!),
                        _isLocationStale(driverId) ? Colors.red : Colors.grey,
                      ),
                    const Divider(),
                  ],
                ),

                // Scrollable trips section
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Display all trips for this driver
                        for (int i = 0; i < allTrips.length; i++) ...[
                          if (i > 0) const Divider(height: 30),                          // Trip header with index if multiple trips
                          if (allTrips.length > 1)
                            Padding(
                              padding: const EdgeInsets.only(bottom: 8),                              child:                                Text(
                                AppLocalizations.of(context).tripNumber('${i + 1}', '${allTrips.length}'),
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                  color: allTrips[i].status == 'BACK TO BASE' ? Colors.deepOrange.shade800 : Colors.amber.shade800,
                                ),
                              ),
                            ),// Trip details
                          _buildDetailRow(AppLocalizations.of(context).tripCode, allTrips[i].tripCode,
                              allTrips[i].status == 'BACK TO BASE' ? Colors.deepOrange.shade800 : Colors.amber.shade800),
                          _buildDetailRow(AppLocalizations.of(context).status, allTrips[i].status,
                              allTrips[i].status == 'BACK TO BASE' ? Colors.deepOrange.shade800 : Colors.amber.shade800),
                          _buildDetailRow(AppLocalizations.of(context).date, allTrips[i].date),
                          _buildDetailRow(AppLocalizations.of(context).time, allTrips[i].time),
                          _buildDetailRow(AppLocalizations.of(context).from, '${allTrips[i].fromDestinationInitial ?? 'OTH'} - ${allTrips[i].fromDestination}'),
                          _buildDetailRow(AppLocalizations.of(context).to, '${allTrips[i].toDestinationInitial ?? 'OTH'} - ${allTrips[i].toDestination}'),
                          _buildDetailRow(AppLocalizations.of(context).requestor, allTrips[i].requestorName),
                          if (allTrips[i].notes != null && allTrips[i].notes!.isNotEmpty)
                            _buildDetailRow(AppLocalizations.of(context).notes, allTrips[i].notes!),
                        ],
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(AppLocalizations.of(context).close),
            ),
          ],
        );
      },
    );
  }

  // Helper method to build a detail row in the dialog
  Widget _buildDetailRow(String label, String value, [Color? valueColor]) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: valueColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to check if a driver's location is stale
  bool _isLocationStale(int driverId) {
    if (_driverLastUpdated.containsKey(driverId)) {
      final lastUpdated = _driverLastUpdated[driverId]!;
      final now = DateTime.now();
      final difference = now.difference(lastUpdated);
      return difference.inMinutes > 10; // Keep 10 minutes as the stale threshold
    }
    return false;
  }

  // Track which driver markers are being hovered over
  Map<int, bool> _hoveredDrivers = {};

  // Method to update hover state for a driver
  void _setDriverHover(int driverId, bool isHovered) {
    setState(() {
      _hoveredDrivers[driverId] = isHovered;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      appBar: CommonAppBar(
        title: 'Driver Tracking',
        onRefresh: () => refreshData(showSnackbar: true),
        showMenuIcon: true,
      ),
      drawer: const SidebarNavigation(),
      body: SafeArea(
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _errorMessage.isNotEmpty
                ? Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error_outline, size: 48, color: Colors.red),
                      const SizedBox(height: 16),
                      Text(
                        'Error: $_errorMessage',
                        style: const TextStyle(fontSize: 16),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () => refreshData(showSnackbar: true),
                        child: Text(AppLocalizations.of(context).retry),
                      ),
                      // Show the map anyway if we have driver locations
                      if (_driverLocations.isNotEmpty) ...[
                        const SizedBox(height: 16),
                        const Text(
                          'Showing last known driver locations:',
                          style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        Expanded(
                          child: LayoutBuilder(
                            builder: (context, constraints) {
                              return !kIsWeb && _isMobilePlatform()
                                ? _buildAndroidMap()
                                : Stack(
                                    children: [
                                      FlutterMap(
                                        options: MapOptions(
                                          initialCenter: _calculateMapCenter(),
                                          initialZoom: 12,
                                          interactionOptions: InteractionOptions(
                                            enableScrollWheel: true,
                                            enableMultiFingerGestureRace: true,
                                          ),
                                          backgroundColor: const Color(0xFFE0E0E0),
                                        ),
                                        mapController: _mapController,
                                        children: [
                                          TileLayer(
                                            urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                                            subdomains: ['a', 'b', 'c'],
                                            userAgentPackageName: 'com.example.fleex',
                                            maxZoom: 19,
                                            minZoom: 1,
                                            panBuffer: 2,
                                            keepBuffer: 5,
                                          ),
                                          MarkerLayer(
                                            markers: _driverLocations.entries.map((entry) {
                                              final driverId = entry.key;
                                              final location = entry.value;                                              // Find the driver with this ID
                                              final driver = _drivers.firstWhere(
                                                (d) => d.driverId == driverId,
                                                orElse: () => Driver(
                                                  driverId: driverId,
                                                  driverCode: AppLocalizations.of(context).unknown,
                                                  name: AppLocalizations.of(context).unknownDriver,
                                                  initial: 'U',
                                                  carCode: '',
                                                  lastUpdated: _driverLastUpdated[driverId],
                                                ),
                                              );

                                              return Marker(
                                                point: location,
                                                width: 40,
                                                height: 40,
                                                child: GestureDetector(
                                                  onTap: () => _centerMapOnDriver(driverId),
                                                  child: Container(
                                                    decoration: BoxDecoration(
                                                      color: Colors.blue,
                                                      shape: BoxShape.circle,
                                                      border: Border.all(color: Colors.white, width: 2),
                                                    ),
                                                    child: Center(
                                                      child: Text(
                                                        driver.initial,
                                                        style: const TextStyle(
                                                          color: Colors.white,
                                                          fontWeight: FontWeight.bold,
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              );
                                            }).toList(),
                                          ),
                                        ],
                                      ),
                                    ],
                                  );
                            },
                          ),
                        ),
                      ],
                    ],
                  )
                : LayoutBuilder(
                  builder: (context, constraints) {
                    return Column(
                      children: [
                        Expanded(
                          child: _driverLocations.isEmpty
                            ? Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const Icon(Icons.location_off, size: 64, color: Colors.grey),
                                    const SizedBox(height: 16),
                                    const Text(
                                      'No driver locations available',
                                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                                    ),
                                    const SizedBox(height: 8),
                                    ElevatedButton(
                                      onPressed: () => _fetchDriverLocations(),
                                      child: Text(AppLocalizations.of(context).refresh),
                                    ),
                                  ],
                                ),
                              )
                            : !kIsWeb && _isMobilePlatform()
                                ? _buildAndroidMap()
                                : Stack(
                                    children: [
                                      FlutterMap(
                                    options: MapOptions(
                                      initialCenter: _calculateMapCenter(),
                                      initialZoom: 12, // Higher zoom level to focus on the city
                                      interactionOptions: InteractionOptions(
                                        enableScrollWheel: true,
                                        enableMultiFingerGestureRace: true,
                                      ),
                                      backgroundColor: const Color(0xFFE0E0E0),
                                    ),
                                    mapController: _mapController,
                                    children: [
                                      // Primary tile layer - OpenStreetMap
                                      TileLayer(
                                        // Use a reliable tile server
                                        urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                                        subdomains: ['a', 'b', 'c'],
                                        userAgentPackageName: 'com.example.fleex',
                                        // Set maximum zoom level
                                        maxZoom: 19,
                                        minZoom: 1,
                                        // Add buffer for smoother panning
                                        panBuffer: 2,
                                        // Tile caching
                                        keepBuffer: 5,
                                      ),                                      MarkerLayer(
                                        markers: _driverLocations.entries.map((entry) {
                                          final driverId = entry.key;
                                          final location = entry.value;

                                          // Find the driver with this ID
                                          final driver = _drivers.firstWhere(
                                            (d) => d.driverId == driverId,
                                            orElse: () => Driver(
                                              driverId: driverId,
                                              driverCode: AppLocalizations.of(context).unknown,
                                              name: AppLocalizations.of(context).unknownDriver,
                                              initial: 'U',
                                              carCode: '',
                                              lastUpdated: _driverLastUpdated[driverId],
                                              isActive: false, // Default to inactive for unknown drivers
                                            ),
                                          );

                                          // Skip inactive drivers
                                          if (!driver.isActive) {
                                            return Marker(
                                              point: const LatLng(0, 0), // Dummy position
                                              width: 0,
                                              height: 0,
                                              child: Container(), // Empty container
                                            );
                                          }

                                          // Check if location is stale (over 10 minutes old)
                                          bool isStale = false;
                                          if (_driverLastUpdated.containsKey(driverId)) {
                                            final lastUpdated = _driverLastUpdated[driverId]!;
                                            final now = DateTime.now();
                                            final difference = now.difference(lastUpdated);
                                            isStale = difference.inMinutes > 10; // Keep 10 minutes as the stale threshold
                                          }

                                          // Check if driver has trips in progress or back to base
                                          final bool hasTripInProgress = _driverTripsInProgress.containsKey(driverId);
                                          final bool hasBackToBase = _driverBackToBaseTrips.containsKey(driverId);

                                          // Check if this driver is being hovered over
                                          final bool isHovered = _hoveredDrivers[driverId] ?? false;

                                          return Marker(
                                            point: location,
                                            width: 130, // Increased width for better text display
                                            height: (hasTripInProgress || hasBackToBase) ? 170 : 90, // Increased height for trip info
                                            child: MouseRegion(
                                              onEnter: (_) => _setDriverHover(driverId, true),
                                              onExit: (_) => _setDriverHover(driverId, false),
                                              child: GestureDetector(
                                                onTap: () {
                                                  // When marker is tapped, center on the driver
                                                  _centerMapOnDriver(driverId);
                                                },
                                                child: Column(
                                                  children: [
                                                    Container(
                                                      width: 40,
                                                      height: 40,
                                                      decoration: BoxDecoration(
                                                        color: isStale
                                                          ? Colors.grey
                                                          : (hasBackToBase
                                                              ? Colors.deepOrange // BACK TO BASE color
                                                              : (hasTripInProgress
                                                                  ? Colors.amber // TRIP IN PROGRESS color
                                                                  : (_selectedDriverId == driverId ? Colors.green : Colors.blue))),
                                                        shape: BoxShape.circle,
                                                        border: Border.all(
                                                          color: _selectedDriverId == driverId ? Colors.yellow : Colors.white,
                                                          width: _selectedDriverId == driverId ? 3 : 2,
                                                        ),
                                                      ),
                                                      child: Center(
                                                        child: Text(
                                                          driver.initial,
                                                          style: const TextStyle(
                                                            color: Colors.white,
                                                            fontWeight: FontWeight.bold,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                    // Only show detailed info when hovered or selected
                                                    if (isHovered || _selectedDriverId == driverId)
                                                      Container(
                                                        padding: const EdgeInsets.all(2),
                                                        decoration: BoxDecoration(
                                                          color: Colors.white,
                                                          borderRadius: BorderRadius.circular(4),
                                                        ),
                                                        child: Column(
                                                          mainAxisSize: MainAxisSize.min,
                                                          children: [
                                                            Text(
                                                              driver.name,
                                                              style: const TextStyle(
                                                                fontSize: 10,
                                                                fontWeight: FontWeight.bold,
                                                              ),
                                                            ),
                                                            if (_driverLastUpdated.containsKey(driverId))
                                                              Text(
                                                                'Updated: ${_formatLastUpdated(_driverLastUpdated[driverId]!)}',
                                                                style: TextStyle(
                                                                  fontSize: 8,
                                                                  color: isStale ? Colors.red : Colors.grey,
                                                                ),
                                                              ),
                                                            // Show BACK TO BASE trips first (priority)
                                                            if (hasBackToBase)
                                                              Container(
                                                                margin: const EdgeInsets.only(top: 4),
                                                                padding: const EdgeInsets.all(4),
                                                                width: 125, // Increased width to prevent overflow
                                                                decoration: BoxDecoration(
                                                                  color: Colors.deepOrange.shade50,
                                                                  border: Border.all(color: Colors.deepOrange.shade200),
                                                                  borderRadius: BorderRadius.circular(4),
                                                                ),
                                                                child: Column(
                                                                  mainAxisSize: MainAxisSize.min,
                                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                                  children: [
                                                                    Row(
                                                                      mainAxisSize: MainAxisSize.min,
                                                                      children: [
                                                                        Icon(Icons.home, size: 10, color: Colors.deepOrange.shade800),
                                                                        const SizedBox(width: 2),
                                                                        Text(
                                                                          _driverBackToBaseTrips[driverId]!.length > 1
                                                                            ? '${_driverBackToBaseTrips[driverId]!.length} BACK TO BASE'
                                                                            : 'BACK TO BASE',
                                                                          style: TextStyle(
                                                                            fontSize: 8,
                                                                            fontWeight: FontWeight.bold,
                                                                            color: Colors.deepOrange.shade800,
                                                                          ),
                                                                        ),
                                                                      ],
                                                                    ),

                                                                    // Show first trip details
                                                                    if (_driverBackToBaseTrips[driverId]!.isNotEmpty) ...[
                                                                      const SizedBox(height: 2),
                                                                      Text(
                                                                        'Trip: ${_driverBackToBaseTrips[driverId]![0].tripCode}',
                                                                        style: const TextStyle(fontSize: 8, fontWeight: FontWeight.bold),
                                                                      ),
                                                                      const SizedBox(height: 1),
                                                                      Text(
                                                                        'From: ${_driverBackToBaseTrips[driverId]![0].fromDestinationInitial ?? 'OTH'}',
                                                                        style: const TextStyle(fontSize: 8),
                                                                        overflow: TextOverflow.ellipsis,
                                                                      ),
                                                                      const SizedBox(height: 1),
                                                                      Text(
                                                                        'To: ${_driverBackToBaseTrips[driverId]![0].toDestinationInitial ?? 'OTH'}',
                                                                        style: const TextStyle(fontSize: 8),
                                                                        overflow: TextOverflow.ellipsis,
                                                                      ),
                                                                    ],

                                                                    // If there are multiple trips, show a message
                                                                    if (_driverBackToBaseTrips[driverId]!.length > 1) ...[
                                                                      const SizedBox(height: 2),
                                                                      Text(
                                                                        '+ ${_driverBackToBaseTrips[driverId]!.length - 1} more trip(s)',
                                                                        style: TextStyle(
                                                                          fontSize: 8,
                                                                          fontStyle: FontStyle.italic,
                                                                          color: Colors.deepOrange.shade800,
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ],
                                                                ),
                                                              ),

                                                            // Show trips in progress information (only if no BACK TO BASE trips)
                                                            if (!hasBackToBase && hasTripInProgress)
                                                              Container(
                                                                margin: const EdgeInsets.only(top: 4),
                                                                padding: const EdgeInsets.all(4),
                                                                width: 125, // Increased width to prevent overflow
                                                                decoration: BoxDecoration(
                                                                  color: Colors.amber.shade50,
                                                                  border: Border.all(color: Colors.amber.shade200),
                                                                  borderRadius: BorderRadius.circular(4),
                                                                ),
                                                                child: Column(
                                                                  mainAxisSize: MainAxisSize.min,
                                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                                  children: [
                                                                    Row(
                                                                      mainAxisSize: MainAxisSize.min,
                                                                      children: [
                                                                        Icon(Icons.directions_car, size: 10, color: Colors.amber.shade800),
                                                                        const SizedBox(width: 2),
                                                                        Text(
                                                                          _driverTripsInProgress[driverId]!.length > 1
                                                                            ? '${_driverTripsInProgress[driverId]!.length} TRIPS IN PROGRESS'
                                                                            : 'TRIP IN PROGRESS',
                                                                          style: TextStyle(
                                                                            fontSize: 8,
                                                                            fontWeight: FontWeight.bold,
                                                                            color: Colors.amber.shade800,
                                                                          ),
                                                                        ),
                                                                      ],
                                                                    ),

                                                                    // Show first trip details
                                                                    if (_driverTripsInProgress[driverId]!.isNotEmpty) ...[
                                                                      const SizedBox(height: 2),
                                                                      Text(
                                                                        'Trip: ${_driverTripsInProgress[driverId]![0].tripCode}',
                                                                        style: const TextStyle(fontSize: 8, fontWeight: FontWeight.bold),
                                                                      ),
                                                                      const SizedBox(height: 1),
                                                                      Text(
                                                                        'From: ${_driverTripsInProgress[driverId]![0].fromDestinationInitial ?? 'OTH'}',
                                                                        style: const TextStyle(fontSize: 8),
                                                                        overflow: TextOverflow.ellipsis,
                                                                      ),
                                                                      const SizedBox(height: 1),
                                                                      Text(
                                                                        'To: ${_driverTripsInProgress[driverId]![0].toDestinationInitial ?? 'OTH'}',
                                                                        style: const TextStyle(fontSize: 8),
                                                                        overflow: TextOverflow.ellipsis,
                                                                      ),
                                                                    ],

                                                                    // If there are multiple trips, show a message
                                                                    if (_driverTripsInProgress[driverId]!.length > 1) ...[
                                                                      const SizedBox(height: 2),
                                                                      Text(
                                                                        '+ ${_driverTripsInProgress[driverId]!.length - 1} more trip(s)',
                                                                        style: TextStyle(
                                                                          fontSize: 8,
                                                                          fontStyle: FontStyle.italic,
                                                                          color: Colors.amber.shade800,
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ],
                                                                ),
                                                              ),
                                                          ],
                                                        ),
                                                      ),
                                                    // When not hovered, show minimal info
                                                    if (!isHovered && _selectedDriverId != driverId)
                                                      Container(
                                                        padding: const EdgeInsets.all(2),
                                                        decoration: BoxDecoration(
                                                          color: Colors.white,
                                                          borderRadius: BorderRadius.circular(4),
                                                        ),
                                                        child: Text(
                                                          driver.name,
                                                          style: const TextStyle(
                                                            fontSize: 10,
                                                            fontWeight: FontWeight.bold,
                                                          ),
                                                        ),
                                                      ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          );
                                        }).toList(),
                                      ),
                                    ],
                                  ),
                                      // Legend overlay
                                      Positioned(
                                        left: 16,
                                        top: 16,
                                        child: Container(
                                          padding: const EdgeInsets.all(8),
                                          decoration: BoxDecoration(
                                            color: Colors.white.withAlpha(204),
                                            borderRadius: BorderRadius.circular(8),
                                          ),
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Text(AppLocalizations.of(context).driversCount(_driverLocations.length)),
                                              Text(AppLocalizations.of(context).inactiveDriversInfo, style: const TextStyle(fontSize: 10, color: Colors.grey)),
                                              Text(AppLocalizations.of(context).updateFrequencyInfo, style: const TextStyle(fontSize: 10, color: Colors.black54)),
                                              Text(
                                                'Amber = Trip in progress (${_driverTripsInProgress.length})',
                                                style: TextStyle(fontSize: 10, color: Colors.amber.shade800)
                                              ),
                                              Text(
                                                'Orange = Back to base (${_driverBackToBaseTrips.length})',
                                                style: TextStyle(fontSize: 10, color: Colors.deepOrange.shade800)
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                        ),                        // Driver list at the bottom (smaller to give more space to the map)
                        // Only show if we have drivers with locations
                        if (_drivers.isNotEmpty && _driverLocations.isNotEmpty)
                          SizedBox(
                            height: 85, // Final height adjustment to fix overflow
                            child: Container(
                              color: Colors.grey[200],
                              child: ListView.builder(
                                scrollDirection: Axis.horizontal,
                                // Only show active drivers
                                itemCount: _drivers.where((driver) => driver.isActive).length,
                                itemBuilder: (context, index) {
                                  final driver = _drivers.where((driver) => driver.isActive).toList()[index];
                                  // Check if this driver has a location
                                  final hasLocation = _driverLocations.containsKey(driver.driverId);

                                  // Check if driver has trips in progress or back to base
                                  final bool hasTripInProgress = _driverTripsInProgress.containsKey(driver.driverId);
                                  final bool hasBackToBase = _driverBackToBaseTrips.containsKey(driver.driverId);

                                  // Check if location is stale (over 10 minutes old)
                                  bool isStale = false;
                                  if (hasLocation && _driverLastUpdated.containsKey(driver.driverId)) {
                                    final lastUpdated = _driverLastUpdated[driver.driverId]!;
                                    final now = DateTime.now();
                                    final difference = now.difference(lastUpdated);
                                    isStale = difference.inMinutes > 10; // Keep 10 minutes as the stale threshold
                                  }

                                  return GestureDetector(
                                    onTap: hasLocation ? () => _centerMapOnDriver(driver.driverId) : null,
                                    child: Container(
                                      width: 100, // Increased width for trip info
                                      margin: const EdgeInsets.symmetric(horizontal: 5, vertical: 4), // Increased vertical margin
                                      decoration: BoxDecoration(
                                        color: _selectedDriverId == driver.driverId
                                            ? Colors.lightBlue.shade50
                                            : (hasBackToBase
                                                ? Colors.deepOrange.shade50 // BACK TO BASE color
                                                : (hasTripInProgress
                                                    ? Colors.amber.shade50 // TRIP IN PROGRESS color
                                                    : (hasLocation ? (isStale ? Colors.grey[200] : Colors.white) : Colors.grey[100]))),
                                        borderRadius: BorderRadius.circular(6), // Smaller radius
                                        boxShadow: [
                                          BoxShadow(
                                            color: _selectedDriverId == driver.driverId
                                                ? Colors.blue.withAlpha(100)
                                                : (hasBackToBase
                                                    ? Colors.deepOrange.withAlpha(100)
                                                    : (hasTripInProgress
                                                        ? Colors.amber.withAlpha(100)
                                                        : Colors.grey.withAlpha(50))),
                                            spreadRadius: 1,
                                            blurRadius: 1, // Minimal blur
                                            offset: const Offset(0, 1),
                                          ),
                                        ],
                                        border: _selectedDriverId == driver.driverId
                                            ? Border.all(color: Colors.blue, width: 1.5)
                                            : (hasBackToBase
                                                ? Border.all(color: Colors.deepOrange, width: 1.0)
                                                : (hasTripInProgress
                                                    ? Border.all(color: Colors.amber, width: 1.0)
                                                    : null)),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Padding(
                                            padding: const EdgeInsets.only(left: 4),
                                            child: CircleAvatar(
                                              radius: 14, // Even smaller avatar
                                              backgroundColor: _selectedDriverId == driver.driverId
                                                  ? Colors.green
                                                  : (hasBackToBase
                                                      ? Colors.deepOrange // BACK TO BASE color
                                                      : (hasTripInProgress
                                                          ? Colors.amber // TRIP IN PROGRESS color
                                                          : (hasLocation ? (isStale ? Colors.grey : Colors.blue) : Colors.grey))),
                                              child: Text(
                                                driver.initial,
                                                style: const TextStyle(
                                                  color: Colors.white,
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 11, // Smaller font
                                                ),
                                              ),
                                            ),
                                          ),
                                          Expanded(
                                            child: Padding(
                                              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                                              child: Column(
                                                mainAxisSize: MainAxisSize.min,
                                                mainAxisAlignment: MainAxisAlignment.center,
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  Row(
                                                    children: [
                                                      Expanded(
                                                        child: Text(
                                                          driver.name,
                                                          style: TextStyle(
                                                            fontSize: 9, // Reduced font size
                                                            fontWeight: FontWeight.bold,
                                                            color: hasLocation ? (isStale ? Colors.grey[700] : Colors.black) : Colors.grey,
                                                          ),
                                                          overflow: TextOverflow.ellipsis,
                                                        ),
                                                      ),
                                                      if (hasBackToBase)
                                                        Icon(
                                                          Icons.home,
                                                          size: 9,
                                                          color: Colors.deepOrange.shade800,
                                                        )
                                                      else if (hasTripInProgress)
                                                        Icon(
                                                          Icons.directions_car,
                                                          size: 9,
                                                          color: Colors.amber.shade800,
                                                        ),
                                                    ],
                                                  ),
                                                  Text(
                                                    driver.driverCode,
                                                    style: const TextStyle(
                                                      fontSize: 7, // Reduced font size
                                                      color: Colors.grey,
                                                    ),
                                                  ),
                                                  // Show BACK TO BASE trips first (priority)
                                                  if (hasBackToBase) ...[
                                                    const SizedBox(height: 1),
                                                    Text(
                                                      'Trip: ${_driverBackToBaseTrips[driver.driverId]![0].tripCode}',
                                                      style: TextStyle(
                                                        fontSize: 7, // Reduced font size
                                                        color: Colors.deepOrange.shade800,
                                                        fontWeight: FontWeight.bold,
                                                      ),
                                                      overflow: TextOverflow.ellipsis,
                                                    ),
                                                    const SizedBox(height: 1),
                                                    Text(
                                                      'From: ${_driverBackToBaseTrips[driver.driverId]![0].fromDestinationInitial ?? 'OTH'}',
                                                      style: TextStyle(
                                                        fontSize: 6, // Reduced font size
                                                        color: Colors.deepOrange.shade700,
                                                      ),
                                                      overflow: TextOverflow.ellipsis,
                                                    ),
                                                    // If multiple trips, show count
                                                    if (_driverBackToBaseTrips[driver.driverId]!.length > 1)
                                                      Text(
                                                        '+ ${_driverBackToBaseTrips[driver.driverId]!.length - 1} more trip(s)',
                                                        style: TextStyle(
                                                          fontSize: 6,
                                                          fontStyle: FontStyle.italic,
                                                          color: Colors.deepOrange.shade800,
                                                        ),
                                                        overflow: TextOverflow.ellipsis,
                                                      ),
                                                  ]
                                                  // Show trips in progress information (only if no BACK TO BASE trips)
                                                  else if (hasTripInProgress) ...[
                                                    const SizedBox(height: 1),
                                                    Text(
                                                      'Trip: ${_driverTripsInProgress[driver.driverId]![0].tripCode}',
                                                      style: TextStyle(
                                                        fontSize: 7, // Reduced font size
                                                        color: Colors.amber.shade800,
                                                        fontWeight: FontWeight.bold,
                                                      ),
                                                      overflow: TextOverflow.ellipsis,
                                                    ),
                                                    const SizedBox(height: 1),
                                                    Text(
                                                      'From: ${_driverTripsInProgress[driver.driverId]![0].fromDestinationInitial ?? 'OTH'}',
                                                      style: TextStyle(
                                                        fontSize: 6, // Reduced font size
                                                        color: Colors.amber.shade700,
                                                      ),
                                                      overflow: TextOverflow.ellipsis,
                                                    ),
                                                    // If multiple trips, show count
                                                    if (_driverTripsInProgress[driver.driverId]!.length > 1)
                                                      Text(
                                                        '+ ${_driverTripsInProgress[driver.driverId]!.length - 1} more trip(s)',
                                                        style: TextStyle(
                                                          fontSize: 6,
                                                          fontStyle: FontStyle.italic,
                                                          color: Colors.amber.shade800,
                                                        ),
                                                        overflow: TextOverflow.ellipsis,
                                                      ),
                                                  ],
                                                  // Always show last updated at the end
                                                  if (hasLocation && _driverLastUpdated.containsKey(driver.driverId))
                                                    Padding(
                                                      padding: const EdgeInsets.only(top: 2),
                                                      child: Text(
                                                        _formatLastUpdated(_driverLastUpdated[driver.driverId]!),
                                                        style: TextStyle(
                                                          fontSize: 6, // Reduced font size
                                                          color: isStale ? Colors.red : Colors.grey,
                                                        ),
                                                      ),
                                                    ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                  ],
                );
              },
            ),
      ),
    );
  }
  // Safe method to check for mobile platforms
  bool _isMobilePlatform() {
    try {
      // Check if the platform is Android or iOS
      return defaultTargetPlatform == TargetPlatform.android ||
             defaultTargetPlatform == TargetPlatform.iOS;
    } catch (e) {
      print('Error detecting platform: $e');
      return false;
    }
  }
  // Map implementation for mobile platforms
  Widget _buildAndroidMap() {
    print('Building mobile map with ${_driverLocations.length} driver locations');

    return Stack(
      children: [
        FlutterMap(
          options: MapOptions(
            initialCenter: _calculateMapCenter(),
            initialZoom: 12, // Higher zoom level to focus on the city
            interactionOptions: const InteractionOptions(
              enableScrollWheel: true,
              enableMultiFingerGestureRace: true,
            ),
            backgroundColor: const Color(0xFFE0E0E0),
          ),
          mapController: _mobileMapController,
          children: [
            // Primary tile layer - OpenStreetMap
            TileLayer(
              // Use a reliable tile server
              urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
              subdomains: const ['a', 'b', 'c'],
              userAgentPackageName: 'com.example.fleex',
              // Set maximum zoom level
              maxZoom: 19,
              minZoom: 1,
              // Add buffer for smoother panning
              panBuffer: 2,
              // Tile caching
              keepBuffer: 5,
            ),            MarkerLayer(
              markers: _driverLocations.entries.map((entry) {
                final driverId = entry.key;
                final location = entry.value;                // Find the driver with this ID
                final driver = _drivers.firstWhere(
                  (d) => d.driverId == driverId,
                  orElse: () => Driver(
                    driverId: driverId,
                    driverCode: AppLocalizations.of(context).unknown,
                    name: AppLocalizations.of(context).unknownDriver,
                    initial: 'U',
                    carCode: '',
                    lastUpdated: _driverLastUpdated[driverId],
                    isActive: false, // Default to inactive for unknown drivers
                  ),
                );
                
                // Skip inactive drivers
                if (!driver.isActive) {
                  return Marker(
                    point: const LatLng(0, 0), // Dummy position
                    width: 0,
                    height: 0,
                    child: Container(), // Empty container
                  );
                }

                // Check if location is stale (over 10 minutes old)
                bool isStale = false;
                if (_driverLastUpdated.containsKey(driverId)) {
                  final lastUpdated = _driverLastUpdated[driverId]!;
                  final now = DateTime.now();
                  final difference = now.difference(lastUpdated);
                  isStale = difference.inMinutes > 10; // Keep 10 minutes as the stale threshold
                }

                // Check if driver has trips in progress or back to base
                final bool hasTripInProgress = _driverTripsInProgress.containsKey(driverId);
                final bool hasBackToBase = _driverBackToBaseTrips.containsKey(driverId);

                // Check if this driver is being hovered over
                final bool isHovered = _hoveredDrivers[driverId] ?? false;

                return Marker(
                  point: location,
                  width: 130, // Increased width for better text display
                  height: (hasTripInProgress || hasBackToBase) ? 170 : 90, // Increased height for trip info
                  child: GestureDetector(
                    onTap: () {
                      // Use the centerMapOnDriver method which will also show the trip details dialog if needed
                      _centerMapOnDriver(driverId);
                    },
                    // For mobile, we'll use a long press to simulate hover since there's no mouse
                    onLongPress: () {
                      _setDriverHover(driverId, !isHovered); // Toggle hover state on long press
                    },
                    child: Column(
                      children: [
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: isStale
                              ? Colors.grey
                              : (hasBackToBase
                                  ? Colors.deepOrange // BACK TO BASE color
                                  : (hasTripInProgress
                                      ? Colors.amber // TRIP IN PROGRESS color
                                      : (_selectedDriverId == driverId ? Colors.green : Colors.blue))),
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: _selectedDriverId == driverId ? Colors.yellow : Colors.white,
                              width: _selectedDriverId == driverId ? 3 : 2,
                            ),
                          ),
                          child: Center(
                            child: Text(
                              driver.initial,
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                        // Only show detailed info when hovered or selected
                        if (isHovered || _selectedDriverId == driverId)
                          Container(
                            padding: const EdgeInsets.all(2),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  driver.name,
                                  style: const TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                if (_driverLastUpdated.containsKey(driverId))
                                  Text(
                                    'Updated: ${_formatLastUpdated(_driverLastUpdated[driverId]!)}',
                                    style: TextStyle(
                                      fontSize: 8,
                                      color: isStale ? Colors.red : Colors.grey,
                                    ),
                                  ),
                                // Show BACK TO BASE trips first (priority)
                                if (hasBackToBase)
                                  Container(
                                    margin: const EdgeInsets.only(top: 4),
                                    padding: const EdgeInsets.all(4),
                                    width: 125, // Increased width to prevent overflow
                                    decoration: BoxDecoration(
                                      color: Colors.deepOrange.shade50,
                                      border: Border.all(color: Colors.deepOrange.shade200),
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(Icons.home, size: 10, color: Colors.deepOrange.shade800),
                                            const SizedBox(width: 2),
                                            Text(
                                              _driverBackToBaseTrips[driverId]!.length > 1
                                                ? '${_driverBackToBaseTrips[driverId]!.length} BACK TO BASE'
                                                : 'BACK TO BASE',
                                              style: TextStyle(
                                                fontSize: 8,
                                                fontWeight: FontWeight.bold,
                                                color: Colors.deepOrange.shade800,
                                              ),
                                            ),
                                          ],
                                        ),

                                        // Show first trip details
                                        if (_driverBackToBaseTrips[driverId]!.isNotEmpty) ...[
                                          const SizedBox(height: 2),
                                          Text(
                                            'Trip: ${_driverBackToBaseTrips[driverId]![0].tripCode}',
                                            style: const TextStyle(fontSize: 8, fontWeight: FontWeight.bold),
                                          ),
                                          const SizedBox(height: 1),
                                          Text(
                                            'From: ${_driverBackToBaseTrips[driverId]![0].fromDestinationInitial ?? 'OTH'}',
                                            style: const TextStyle(fontSize: 8),
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                          const SizedBox(height: 1),
                                          Text(
                                            'To: ${_driverBackToBaseTrips[driverId]![0].toDestinationInitial ?? 'OTH'}',
                                            style: const TextStyle(fontSize: 8),
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ],

                                        // If there are multiple trips, show a message
                                        if (_driverBackToBaseTrips[driverId]!.length > 1) ...[
                                          const SizedBox(height: 2),
                                          Text(
                                            '+ ${_driverBackToBaseTrips[driverId]!.length - 1} more trip(s)',
                                            style: TextStyle(
                                              fontSize: 8,
                                              fontStyle: FontStyle.italic,
                                              color: Colors.deepOrange.shade800,
                                            ),
                                          ),
                                        ],
                                      ],
                                    ),
                                  ),

                                // Show trips in progress information (only if no BACK TO BASE trips)
                                if (!hasBackToBase && hasTripInProgress)
                                  Container(
                                    margin: const EdgeInsets.only(top: 4),
                                    padding: const EdgeInsets.all(4),
                                    width: 125, // Increased width to prevent overflow
                                    decoration: BoxDecoration(
                                      color: Colors.amber.shade50,
                                      border: Border.all(color: Colors.amber.shade200),
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(Icons.directions_car, size: 10, color: Colors.amber.shade800),
                                            const SizedBox(width: 2),
                                            Text(
                                              _driverTripsInProgress[driverId]!.length > 1
                                                ? '${_driverTripsInProgress[driverId]!.length} TRIPS IN PROGRESS'
                                                : 'TRIP IN PROGRESS',
                                              style: TextStyle(
                                                fontSize: 8,
                                                fontWeight: FontWeight.bold,
                                                color: Colors.amber.shade800,
                                              ),
                                            ),
                                          ],
                                        ),

                                        // Show first trip details
                                        if (_driverTripsInProgress[driverId]!.isNotEmpty) ...[
                                          const SizedBox(height: 2),
                                          Text(
                                            'Trip: ${_driverTripsInProgress[driverId]![0].tripCode}',
                                            style: const TextStyle(fontSize: 8, fontWeight: FontWeight.bold),
                                          ),
                                          const SizedBox(height: 1),
                                          Text(
                                            'From: ${_driverTripsInProgress[driverId]![0].fromDestinationInitial ?? 'OTH'}',
                                            style: const TextStyle(fontSize: 8),
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                          const SizedBox(height: 1),
                                          Text(
                                            'To: ${_driverTripsInProgress[driverId]![0].toDestinationInitial ?? 'OTH'}',
                                            style: const TextStyle(fontSize: 8),
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ],

                                        // If there are multiple trips, show a message
                                        if (_driverTripsInProgress[driverId]!.length > 1) ...[
                                          const SizedBox(height: 2),
                                          Text(
                                            '+ ${_driverTripsInProgress[driverId]!.length - 1} more trip(s)',
                                            style: TextStyle(
                                              fontSize: 8,
                                              fontStyle: FontStyle.italic,
                                              color: Colors.amber.shade800,
                                            ),
                                          ),
                                        ],
                                      ],
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        // When not hovered, show minimal info
                        if (!isHovered && _selectedDriverId != driverId)
                          Container(
                            padding: const EdgeInsets.all(2),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              driver.name,
                              style: const TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
        // Map controls
        Positioned(
          right: 16,
          bottom: 16,
          child: Column(
            children: [
              FloatingActionButton(
                mini: true,
                child: const Icon(Icons.add),
                onPressed: () {
                  final currentZoom = _mobileMapController.camera.zoom;
                  _mobileMapController.move(
                    _mobileMapController.camera.center,
                    (currentZoom + 1).clamp(1.0, 19.0)
                  );
                },
              ),
              const SizedBox(height: 8),
              FloatingActionButton(
                mini: true,
                child: const Icon(Icons.remove),
                onPressed: () {
                  final currentZoom = _mobileMapController.camera.zoom;
                  _mobileMapController.move(
                    _mobileMapController.camera.center,
                    (currentZoom - 1).clamp(1.0, 19.0)
                  );
                },
              ),
              const SizedBox(height: 8),
              FloatingActionButton(
                mini: true,
                child: const Icon(Icons.center_focus_strong),
                onPressed: () {
                  _mobileMapController.move(_calculateMapCenter(), 12.0);
                },
              ),
            ],
          ),
        ),
        // Legend overlay
        Positioned(
          left: 16,
          top: 16,
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withAlpha(204),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(AppLocalizations.of(context).driversCount(_driverLocations.length)),
                Text(AppLocalizations.of(context).inactiveDriversInfo, style: const TextStyle(fontSize: 10, color: Colors.grey)),
                Text(
                  'Amber = Trip in progress (${_driverTripsInProgress.length})',
                  style: TextStyle(fontSize: 10, color: Colors.amber.shade800)
                ),
                Text(
                  'Orange = Back to base (${_driverBackToBaseTrips.length})',
                  style: TextStyle(fontSize: 10, color: Colors.deepOrange.shade800)
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

}

// Format the last updated time
String _formatLastUpdated(DateTime timestamp) {
  final now = DateTime.now();
  final difference = now.difference(timestamp);

  if (difference.inMinutes < 1) {
    return 'Just now';
  } else if (difference.inMinutes < 60) {
    return '${difference.inMinutes}m ago';
  } else if (difference.inHours < 24) {
    return '${difference.inHours}h ago';
  } else {
    return '${difference.inDays}d ago';
  }
}


