import 'dart:convert';

import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../generated/l10n/app_localizations.dart';
import '../models/passenger.dart';
import '../models/trip.dart';
import '../models/trip_cargo.dart';
import '../services/api_service.dart';
import '../widgets/common_app_bar.dart';

class DriverTripHistoryScreen extends StatefulWidget {
  final String driverCode;

  const DriverTripHistoryScreen({
    super.key,
    required this.driverCode,
  });

  @override
  State<DriverTripHistoryScreen> createState() => _DriverTripHistoryScreenState();
}

class _DriverTripHistoryScreenState extends State<DriverTripHistoryScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  List<Trip> _trips = [];
  List<Trip> _filteredTrips = [];
  bool _isLoading = false;
  String _errorMessage = '';
  DateTime? _selectedDate;
  String? _selectedStatus;
  final TextEditingController _dateController = TextEditingController();
  final TextEditingController _searchController = TextEditingController();

  // Pagination variables
  int _currentPage = 1;
  int _itemsPerPage = 10; // Default to 10 items per page
  int _totalPages = 1;

  // Options for items per page
  final List<int> _itemsPerPageOptions = [5, 10, 25, 50, 100];

  // List of all possible trip statuses
  final List<String> _statuses = [
    'ASSIGN TO DRIVER',
    'DRIVER CONFIRMATION',
    'TRIP IN PROGRESS',
    'WAITING FOR RATING',
    'COMPLETED',
    'DRIVER REJECTED'
  ];

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
    _fetchTrips();
  }

  @override
  void dispose() {
    _dateController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _fetchTrips() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      // Build the endpoint with optional query parameters
      String endpoint = 'drivers/${widget.driverCode}/trips';
      final List<String> queryParams = [];

      // Add date filter if selected
      if (_selectedDate != null) {
        // Format date as YYYY-MM-DD for API request
        final formattedDate = DateFormat('yyyy-MM-dd').format(_selectedDate!);
        queryParams.add('date=$formattedDate');
      }

      // Add status filter if selected
      if (_selectedStatus != null && _selectedStatus!.isNotEmpty) {
        queryParams.add('status=${Uri.encodeComponent(_selectedStatus!)}');
      }

      // Append query parameters to endpoint if any
      if (queryParams.isNotEmpty) {
        endpoint += '?${queryParams.join('&')}';
      }

      final response = await ApiService.get(endpoint);

      if (!mounted) return;

      if (response.statusCode == 200) {
        final dynamic decodedResponse = jsonDecode(response.body);

        if (!mounted) return;

        if (decodedResponse is Map<String, dynamic>) {
          final List<dynamic> tripsList = List.from(decodedResponse['trips'] ?? []);

          if (mounted) {
            setState(() {
              _trips = tripsList.map((json) => Trip.fromJson(json)).toList();

              // Sort trips by date and time strings (newest first)
              _trips.sort((a, b) {
                // First compare by originalDate (YYYY-MM-DD format)
                final int dateComparison = b.originalDate.compareTo(a.originalDate);
                if (dateComparison != 0) {
                  return dateComparison;
                }
                // If dates are equal, compare by time
                return b.time.compareTo(a.time);
              });

              // Apply filters to create filtered trips list
              _applyFilters();

              // Calculate total pages
              _calculatePagination();
            });
          }
        } else if (decodedResponse is List) {
          // Legacy support for list response format
          if (mounted) {
            setState(() {
              _trips = decodedResponse.map((json) => Trip.fromJson(json)).toList();

              // Sort trips by date and time strings (newest first)
              _trips.sort((a, b) {
                // First compare by originalDate (YYYY-MM-DD format)
                final int dateComparison = b.originalDate.compareTo(a.originalDate);
                if (dateComparison != 0) {
                  return dateComparison;
                }
                // If dates are equal, compare by time
                return b.time.compareTo(a.time);
              });

              // Apply filters to create filtered trips list
              _applyFilters();

              // Calculate total pages
              _calculatePagination();
            });
          }
        } else {
          throw FormatException('Unexpected response format: ${decodedResponse.runtimeType}');
        }
      } else {
        if (mounted) {
          setState(() {
            _errorMessage = 'Failed to load trips: ${response.statusCode}';
          });
        }
      }
    } catch (e, stackTrace) {
      print('Error: $e');
      print('Stack trace: $stackTrace');
      if (mounted) {
        setState(() {
          _errorMessage = 'An error occurred: $e';
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Apply filters to the trips list
  void _applyFilters() {
    _filteredTrips = List.from(_trips);

    // Only apply search filter locally since it's not part of the API call
    final searchText = _searchController.text.trim().toLowerCase();
    if (searchText.isNotEmpty) {
      _filteredTrips = _filteredTrips.where((trip) {
        return trip.tripCode.toLowerCase().contains(searchText) ||
               trip.fromDestination.toLowerCase().contains(searchText) ||
               trip.toDestination.toLowerCase().contains(searchText) ||
               trip.requestorName.toLowerCase().contains(searchText) ||
               trip.status.toLowerCase().contains(searchText) ||
               (trip.notes != null && trip.notes!.toLowerCase().contains(searchText)) ||
               (trip.comments != null && trip.comments!.toLowerCase().contains(searchText)) ||
               (trip.rating != null && trip.rating.toString().contains(searchText));
      }).toList();
    }
  }

  // Calculate pagination variables
  void _calculatePagination() {
    _totalPages = (_filteredTrips.length / _itemsPerPage).ceil();
    if (_totalPages == 0) _totalPages = 1;
    if (_currentPage > _totalPages) _currentPage = _totalPages;
  }

  // Get paginated trips for the current page
  List<Trip> _getPaginatedTrips() {
    final startIndex = (_currentPage - 1) * _itemsPerPage;
    final endIndex = startIndex + _itemsPerPage;

    if (startIndex >= _filteredTrips.length) {
      return [];
    }

    if (endIndex > _filteredTrips.length) {
      return _filteredTrips.sublist(startIndex);
    }

    return _filteredTrips.sublist(startIndex, endIndex);
  }

  // Select a date from the date picker
  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
        _dateController.text = DateFormat('E, dd-MMM-yy').format(picked);
        _searchController.clear(); // Clear search when changing date
        _fetchTrips();
      });
    }
  }

  // Clear the date filter
  void _clearDateFilter() {
    setState(() {
      _selectedDate = null;
      _dateController.clear();
      _searchController.clear(); // Clear search when clearing date
      _fetchTrips();
    });
  }

  // Clear all filters
  void _clearAllFilters() {
    setState(() {
      _selectedDate = null;
      _dateController.clear();
      _selectedStatus = null;
      _searchController.clear();
      _fetchTrips();
    });
  }

  // Handle search text changes
  void _onSearchChanged() {
    setState(() {
      // Apply filters locally without making a new API call
      _applyFilters();
      _calculatePagination();
      _currentPage = 1; // Reset to first page when search changes
    });
  }
  // Show trip details dialog
  void _showTripDetails(Trip trip) async {
    // Fetch trip details with passengers and cargos data
    Trip updatedTrip = trip;
    try {
      final response = await ApiService.get('trips/code/${trip.tripCode}');
      if (response.statusCode == 200) {
        final data = ApiService.parseResponse(response);
        
        // Parse passengers from API response
        List<Passenger>? passengers;
        if (data['passengers'] != null) {
          passengers = (data['passengers'] as List)
              .map((passengerJson) => Passenger.fromJson(passengerJson))
              .toList();
        }

        // Parse cargos from API response
        List<TripCargo>? cargos;
        if (data['cargos'] != null) {
          cargos = (data['cargos'] as List)
              .map((cargoJson) => TripCargo.fromJson(cargoJson))
              .toList();
        }

        // Create updated trip with passengers and cargos
        updatedTrip = Trip(
          tripId: trip.tripId,
          tripCode: trip.tripCode,
          fromDestination: trip.fromDestination,
          toDestination: trip.toDestination,
          date: trip.date,
          originalDate: trip.originalDate,
          time: trip.time,
          status: trip.status,
          requestorId: trip.requestorId,
          requestorName: trip.requestorName,
          driverId: trip.driverId,
          driverCode: trip.driverCode,
          driverName: trip.driverName,
          rating: trip.rating,
          comments: trip.comments,
          rejectionReason: trip.rejectionReason,
          completionNotes: trip.completionNotes,
          completionImagePath: trip.completionImagePath,          totalDuration: trip.totalDuration,
          notes: trip.notes,
          isWaiting: trip.isWaiting,
          passengers: passengers,
          cargos: cargos,
        );
      }
    } catch (e) {
      print('Error fetching trip details: $e');
      // Continue with original trip data if fetch fails
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Trip #${updatedTrip.tripCode}'),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildDetailRow('Status', updatedTrip.status, _getStatusColor(updatedTrip.status)),
                _buildDetailRow('Date', updatedTrip.date),
                _buildDetailRow('Time', updatedTrip.time),
                _buildDetailRow('From', updatedTrip.fromDestination),
                _buildDetailRow('To', updatedTrip.toDestination),
                _buildDetailRow('Requestor', updatedTrip.requestorName),
                if (updatedTrip.totalDuration != null && updatedTrip.totalDuration!.isNotEmpty)
                  _buildDetailRow('Duration', _formatDuration(updatedTrip.getDurationMinutes())),
                if (updatedTrip.notes != null && updatedTrip.notes!.isNotEmpty)
                  _buildDetailRow('Notes', updatedTrip.notes!),
                if (updatedTrip.rejectionReason != null && updatedTrip.rejectionReason!.isNotEmpty)
                  _buildDetailRow('Rejection Reason', updatedTrip.rejectionReason!),
                if (updatedTrip.completionNotes != null && updatedTrip.completionNotes!.isNotEmpty)
                  _buildDetailRow('Completion Notes', updatedTrip.completionNotes!),                if (updatedTrip.rating != null)
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          width: 120,
                          child: Text(
                            'Rating:',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                        ),
                        Expanded(
                          child: _buildStarRating(updatedTrip.rating!, size: 20),
                        ),
                      ],
                    ),
                  ),
                if (updatedTrip.comments != null && updatedTrip.comments!.isNotEmpty)
                  _buildDetailRow('Comments', updatedTrip.comments!),

                // Passengers Section
                if (updatedTrip.passengers != null && updatedTrip.passengers!.isNotEmpty)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 16),
                      const Text(
                        'Passengers:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.blue.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.blue.shade200),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: updatedTrip.passengers!.map((passenger) => Padding(
                            padding: const EdgeInsets.only(bottom: 4),
                            child: Text(
                              '• ${passenger.name}',
                              style: TextStyle(color: Colors.blue.shade800),
                            ),
                          )).toList(),
                        ),
                      ),
                    ],
                  ),

                // Cargo Section
                if (updatedTrip.cargos != null && updatedTrip.cargos!.isNotEmpty)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 16),
                      const Text(
                        'Cargo:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.orange.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.orange.shade200),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: updatedTrip.cargos!.map((cargo) => Padding(
                            padding: const EdgeInsets.only(bottom: 4),
                            child: Text(
                              '• ${cargo.name}',
                              style: TextStyle(color: Colors.orange.shade800),
                            ),
                          )).toList(),
                        ),
                      ),
                    ],
                  ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }

  // Build a detail row for the trip details dialog
  Widget _buildDetailRow(String label, String value, [MaterialColor? valueColor]) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: valueColor?.shade700 ?? Colors.black,
                fontWeight: valueColor != null ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Format duration in minutes to a readable format
  String _formatDuration(int minutes) {
    final hours = minutes ~/ 60;
    final remainingMinutes = minutes % 60;

    if (hours > 0) {
      return '$hours hr ${remainingMinutes > 0 ? '$remainingMinutes min' : ''}';
    } else {
      return '$minutes min';
    }
  }

  // Get color for trip status
  MaterialColor _getStatusColor(String status) {
    switch (status) {
      case 'ASSIGN TO DRIVER':
        return Colors.blue;
      case 'DRIVER CONFIRMATION':
        return Colors.orange;
      case 'TRIP IN PROGRESS':
        return Colors.green;
      case 'WAITING FOR RATING':
        return Colors.purple;
      case 'COMPLETED':
        return Colors.teal;
      case 'DRIVER REJECTED':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  // Build a star rating widget
  Widget _buildStarRating(int rating, {double size = 16}) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(
        5, // Always show 5 stars
        (index) => Icon(
          index < rating ? Icons.star : Icons.star_border,
          color: index < rating ? Colors.amber : Colors.grey,
          size: size,
        ),
      ),
    );
  }

  // Build a mobile-friendly trip card
  Widget _buildTripCard(Trip trip) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: InkWell(
        onTap: () => _showTripDetails(trip),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Trip code and status row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Trip: ${trip.tripCode}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: Color(0xFF0D47A1),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getStatusColor(trip.status).withAlpha(50),
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(
                        color: _getStatusColor(trip.status),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      trip.status,
                      style: TextStyle(
                        color: _getStatusColor(trip.status),
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              // Date and time
              Row(
                children: [
                  const Icon(Icons.calendar_today, size: 14, color: Colors.grey),
                  const SizedBox(width: 4),
                  Text(
                    trip.date,
                    style: const TextStyle(fontSize: 14),
                  ),
                  const SizedBox(width: 12),
                  const Icon(Icons.access_time, size: 14, color: Colors.grey),
                  const SizedBox(width: 4),
                  Text(
                    trip.time,
                    style: const TextStyle(fontSize: 14),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              // From destination
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Icon(Icons.location_on, size: 14, color: Colors.grey),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'From:',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                        Text(
                          trip.fromDestination,
                          style: const TextStyle(fontSize: 14),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              // To destination
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Icon(Icons.location_on, size: 14, color: Colors.grey),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'To:',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                        Text(
                          trip.toDestination,
                          style: const TextStyle(fontSize: 14),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              // Requestor
              Row(
                children: [
                  const Icon(Icons.person, size: 14, color: Colors.grey),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Requestor:',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                        Text(
                          trip.requestorName,
                          style: const TextStyle(fontSize: 14),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              // Show notes if available
              if (trip.notes != null && trip.notes!.isNotEmpty) ...[
                const SizedBox(height: 8),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Icon(Icons.note, size: 14, color: Colors.grey),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Notes:',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey,
                            ),
                          ),
                          Text(
                            trip.notes!,
                            style: const TextStyle(fontSize: 14),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],

              // Show rating if available
              if (trip.rating != null) ...[
                const SizedBox(height: 8),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Icon(Icons.star_rate, size: 14, color: Colors.grey),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Rating:',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey,
                            ),
                          ),
                          _buildStarRating(trip.rating!),
                        ],
                      ),
                    ),
                  ],
                ),
              ],

              // Show comments if available
              if (trip.comments != null && trip.comments!.isNotEmpty) ...[
                const SizedBox(height: 8),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Icon(Icons.comment, size: 14, color: Colors.grey),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Comments:',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey,
                            ),
                          ),
                          Text(
                            trip.comments!,
                            style: const TextStyle(fontSize: 14),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Check if we're on a small screen (mobile)
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600 && !kIsWeb;

    return Scaffold(
      key: _scaffoldKey,      appBar: CommonAppBar(
        title: AppLocalizations.of(context).tripHistory,
        onRefresh: _fetchTrips,
      ),
      body: Column(
        children: [
          // Filter section
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey.shade100,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [                Text(
                  AppLocalizations.of(context).filterTrips,
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                // Search field
                TextField(
                  controller: _searchController,                  decoration: InputDecoration(
                    labelText: AppLocalizations.of(context).search,
                    hintText: AppLocalizations.of(context).searchByTripCode,
                    border: const OutlineInputBorder(),
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchController.text.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () {
                            _searchController.clear();
                            // _onSearchChanged will be called by the listener
                          },
                        )
                      : null,
                  ),
                ),
                const SizedBox(height: 16),
                // Filter controls
                Row(
                  children: [
                    // Date filter
                    Expanded(
                      child: TextField(
                        controller: _dateController,                        decoration: InputDecoration(
                          labelText: AppLocalizations.of(context).date,
                          border: const OutlineInputBorder(),
                          suffixIcon: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              IconButton(
                                icon: const Icon(Icons.calendar_today),
                                onPressed: () => _selectDate(context),
                              ),
                              if (_selectedDate != null)
                                IconButton(
                                  icon: const Icon(Icons.clear),
                                  onPressed: _clearDateFilter,
                                ),
                            ],
                          ),
                        ),
                        readOnly: true,
                        onTap: () => _selectDate(context),
                      ),
                    ),
                    const SizedBox(width: 16),
                    // Status filter
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'Status',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedStatus,                        items: [
                          DropdownMenuItem<String>(
                            value: '',
                            child: Text(AppLocalizations.of(context).allStatuses),
                          ),
                          ..._statuses.map((String status) => DropdownMenuItem<String>(
                            value: status,
                            child: Text(status),
                          )),
                        ],
                        onChanged: (String? newValue) {
                          setState(() {
                            _selectedStatus = newValue == '' ? null : newValue;
                            _searchController.clear(); // Clear search when changing status
                            _fetchTrips();
                          });
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                // Clear filters button
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [                    TextButton.icon(
                      icon: const Icon(Icons.clear_all),
                      label: Text(AppLocalizations.of(context).clearAllFilters),
                      onPressed: _clearAllFilters,
                    ),
                    const SizedBox(width: 16),
                    TextButton.icon(
                      icon: const Icon(Icons.refresh),
                      label: const Text('Refresh'),
                      onPressed: _fetchTrips,
                    ),
                  ],
                ),
              ],
            ),
          ),
          // Trips list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _errorMessage.isNotEmpty
                    ? Center(child: Text(_errorMessage))
                    : _filteredTrips.isEmpty
                        ? Center(child: Text(AppLocalizations.of(context).noTripsMatchFilters))
                        : isSmallScreen
                            // Mobile layout - vertical list of cards
                            ? RefreshIndicator(
                                onRefresh: () async {
                                  await _fetchTrips();
                                },
                                child: ListView.builder(
                                  padding: const EdgeInsets.all(8.0),
                                  // For mobile view, we'll show all items without pagination
                                  itemCount: _filteredTrips.length,
                                  itemBuilder: (context, index) {
                                    return _buildTripCard(_filteredTrips[index]);
                                  },
                                ),
                              )
                            // Desktop layout - data table with pagination
                            : Column(
                                children: [
                                  Expanded(
                                    child: SingleChildScrollView(
                                      child: Padding(
                                        padding: const EdgeInsets.all(16.0),
                                        child: Theme(
                                          data: Theme.of(context).copyWith(
                                            dataTableTheme: DataTableThemeData(
                                              columnSpacing: 16,
                                              horizontalMargin: 16,
                                              headingTextStyle: const TextStyle(
                                                fontWeight: FontWeight.bold,
                                                color: Color(0xFF0D47A1),
                                              ),
                                            ),
                                          ),
                                          child: DataTable(
                                            showCheckboxColumn: false,                                            columns: [
                                              DataColumn(label: Text(AppLocalizations.of(context).tripCode)),
                                              DataColumn(label: Text('From')),
                                              DataColumn(label: Text('To')),
                                              DataColumn(label: Text('Date')),
                                              DataColumn(label: Text('Time')),
                                              DataColumn(label: Text(AppLocalizations.of(context).requestor)),
                                              DataColumn(label: Text('Status')),
                                              DataColumn(label: Text(AppLocalizations.of(context).rating)),
                                              DataColumn(label: Text(AppLocalizations.of(context).comments)),
                                            ],
                                            rows: _getPaginatedTrips().map((trip) {
                                              return DataRow(
                                                onSelectChanged: (_) => _showTripDetails(trip),
                                                cells: [
                                                  DataCell(Text(trip.tripCode)),
                                                  DataCell(Text(trip.fromDestination)),
                                                  DataCell(Text(trip.toDestination)),
                                                  DataCell(Text(trip.date)),
                                                  DataCell(Text(trip.time)),
                                                  DataCell(Text(trip.requestorName)),
                                                  DataCell(
                                                    Container(
                                                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                                      decoration: BoxDecoration(
                                                        color: _getStatusColor(trip.status).withAlpha(50),
                                                        borderRadius: BorderRadius.circular(4),
                                                      ),
                                                      child: Text(
                                                        trip.status,
                                                        style: TextStyle(
                                                          color: _getStatusColor(trip.status).shade800,
                                                          fontWeight: FontWeight.bold,
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  // Rating cell
                                                  DataCell(
                                                    trip.rating != null
                                                      ? _buildStarRating(trip.rating!)
                                                      : const Text('-'),
                                                  ),
                                                  // Comments cell
                                                  DataCell(
                                                    trip.comments != null && trip.comments!.isNotEmpty
                                                      ? Text(
                                                          trip.comments!,
                                                          overflow: TextOverflow.ellipsis,
                                                          maxLines: 1,
                                                        )
                                                      : const Text('-'),
                                                  ),
                                                ],
                                              );
                                            }).toList(),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  // Pagination controls
                                  Container(
                                    padding: const EdgeInsets.all(16.0),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        // Items per page dropdown
                                        Row(
                                          children: [
                                            const Text('Items per page: '),
                                            const SizedBox(width: 8),
                                            DropdownButton<int>(
                                              value: _itemsPerPage,
                                              items: _itemsPerPageOptions.map((int value) {
                                                return DropdownMenuItem<int>(
                                                  value: value,
                                                  child: Text('$value'),
                                                );
                                              }).toList(),
                                              onChanged: (int? newValue) {
                                                if (newValue != null) {
                                                  setState(() {
                                                    _itemsPerPage = newValue;
                                                    _currentPage = 1;
                                                    _calculatePagination();
                                                  });
                                                }
                                              },
                                            ),
                                          ],
                                        ),
                                        // Page navigation
                                        Row(
                                          children: [
                                            IconButton(
                                              icon: const Icon(Icons.first_page),
                                              onPressed: _currentPage > 1
                                                  ? () {
                                                      setState(() {
                                                        _currentPage = 1;
                                                      });
                                                    }
                                                  : null,
                                            ),
                                            IconButton(
                                              icon: const Icon(Icons.navigate_before),
                                              onPressed: _currentPage > 1
                                                  ? () {
                                                      setState(() {
                                                        _currentPage--;
                                                      });
                                                    }
                                                  : null,
                                            ),
                                            Text('$_currentPage of $_totalPages'),
                                            IconButton(
                                              icon: const Icon(Icons.navigate_next),
                                              onPressed: _currentPage < _totalPages
                                                  ? () {
                                                      setState(() {
                                                        _currentPage++;
                                                      });
                                                    }
                                                  : null,
                                            ),
                                            IconButton(
                                              icon: const Icon(Icons.last_page),
                                              onPressed: _currentPage < _totalPages
                                                  ? () {
                                                      setState(() {
                                                        _currentPage = _totalPages;
                                                      });
                                                    }
                                                  : null,
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
          ),
        ],
      ),
    );
  }
}


