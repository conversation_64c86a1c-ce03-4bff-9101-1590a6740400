import 'dart:convert';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import '../generated/l10n/app_localizations.dart';
import '../models/driver.dart';
import '../services/api_service.dart';
import '../sidebar.dart';
import '../widgets/common_app_bar.dart';
import 'add_driver_screen.dart';
import 'edit_driver_screen.dart';

class DriverSettingsScreen extends StatefulWidget {
  const DriverSettingsScreen({super.key});

  @override
  State<DriverSettingsScreen> createState() => DriverSettingsScreenState();
}

class DriverSettingsScreenState extends State<DriverSettingsScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  List<Driver> _drivers = [];
  List<Driver> _filteredDrivers = [];
  bool _isLoading = false;
  String _errorMessage = '';
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _fetchDrivers();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterDrivers(String query) {
    setState(() {
      _searchQuery = query.toLowerCase();
      _filteredDrivers = _drivers.where((driver) {
        return driver.driverCode.toLowerCase().contains(_searchQuery) ||
            driver.initial.toLowerCase().contains(_searchQuery) ||
            driver.name.toLowerCase().contains(_searchQuery);
      }).toList();
    });
  }

  Future<void> _fetchDrivers() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final response = await ApiService.get('drivers');

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = jsonDecode(response.body);
        setState(() {
          _drivers = (data['drivers'] as List)
              .map((json) => Driver.fromJson(json))
              .toList();
          _filteredDrivers = _drivers; // Initialize filtered list
        });      } else {
        setState(() {
          _errorMessage = AppLocalizations.of(context).failedToLoadDrivers(response.statusCode.toString());
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'An error occurred: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _deleteDriver(Driver driver) async {
    // Store context reference before async gap
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    try {
      final response = await ApiService.delete('drivers/${driver.driverCode}');

      // Check if widget is still mounted before updating UI
      if (!mounted) return;      if (response.statusCode == 200) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).driverDeletedSuccessfully),
            backgroundColor: Colors.green,
          ),
        );
        _fetchDrivers(); // Refresh the list
      } else {        final errorData = jsonDecode(response.body);
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(errorData['error'] ?? AppLocalizations.of(context).failedToDeleteDriver),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      // Check if widget is still mounted before updating UI
      if (!mounted) return;      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('${AppLocalizations.of(context).anErrorOccurred}: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
  void _showDeleteConfirmation(Driver driver) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(AppLocalizations.of(context).confirmDelete),
          content: Text(AppLocalizations.of(context).areYouSureDeleteDriver(driver.name)),
          actions: [
            TextButton(
              child: Text(AppLocalizations.of(context).cancel),
              onPressed: () => Navigator.of(context).pop(),
            ),
            TextButton(
              child: Text(
                AppLocalizations.of(context).delete,
                style: const TextStyle(color: Colors.red),
              ),
              onPressed: () {
                Navigator.of(context).pop();
                _deleteDriver(driver);
              },
            ),
          ],
        );
      },
    );
  }

  // Build a mobile-friendly card for each driver
  Widget _buildDriverCard(Driver driver) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Driver name and initial
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    driver.name,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: Color(0xFF0D47A1),
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: const Color(0xFF0D47A1).withAlpha(25), // 0.1 opacity = ~25 alpha
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    driver.initial,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF0D47A1),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            // Driver code and car code
            Row(
              children: [
                const Icon(Icons.badge, size: 16, color: Colors.grey),
                const SizedBox(width: 4),                Text('${AppLocalizations.of(context).driverCodeLabel} ${driver.driverCode}'),
                const Spacer(),
                const Icon(Icons.directions_car, size: 16, color: Colors.grey),
                const SizedBox(width: 4),
                Text('${AppLocalizations.of(context).carLabel} ${driver.carCode}'),
              ],
            ),
            const SizedBox(height: 12),
            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [                TextButton.icon(
                  icon: const Icon(Icons.edit, size: 18),
                  label: Text(AppLocalizations.of(context).edit),
                  style: TextButton.styleFrom(
                    foregroundColor: const Color(0xFF0D47A1),
                  ),
                  onPressed: () async {
                    final result = await Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => EditDriverScreen(driver: driver),
                      ),
                    );
                    if (result == true) {
                      _fetchDrivers();
                    }
                  },
                ),
                const SizedBox(width: 8),                TextButton.icon(
                  icon: const Icon(Icons.delete, size: 18),
                  label: Text(AppLocalizations.of(context).delete),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.red,
                  ),
                  onPressed: () => _showDeleteConfirmation(driver),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Check if we're on a small screen (mobile)
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600 && !kIsWeb;

    return Scaffold(
      key: _scaffoldKey,
      drawer: const SidebarNavigation(),      appBar: CommonAppBar(
        title: AppLocalizations.of(context).driverSettingsTitle,
        onRefresh: _fetchDrivers,
        showMenuIcon: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage.isNotEmpty
              ? Center(child: Text(_errorMessage))
              : Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: TextField(
                        controller: _searchController,                        decoration: InputDecoration(
                          labelText: AppLocalizations.of(context).search,
                          hintText: AppLocalizations.of(context).searchDrivers,
                          prefixIcon: const Icon(Icons.search),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          filled: true,
                          fillColor: Colors.white,
                          suffixIcon: _searchQuery.isNotEmpty
                              ? IconButton(
                                  icon: const Icon(Icons.clear),
                                  onPressed: () {
                                    _searchController.clear();
                                    _filterDrivers('');
                                  },
                                )
                              : null,
                        ),
                        onChanged: _filterDrivers,
                      ),
                    ),
                    Expanded(                      child: _filteredDrivers.isEmpty
                          ? Center(
                              child: Text(
                                _searchQuery.isEmpty
                                    ? AppLocalizations.of(context).noDriversFound
                                    : AppLocalizations.of(context).noMatchingDriversFound,
                              ),
                            )
                          : isSmallScreen
                              // Mobile layout - card list
                              ? RefreshIndicator(
                                  onRefresh: () async {
                                    await _fetchDrivers();
                                  },
                                  child: ListView.builder(
                                    padding: const EdgeInsets.all(8.0),
                                    itemCount: _filteredDrivers.length,
                                    itemBuilder: (context, index) {
                                      return _buildDriverCard(_filteredDrivers[index]);
                                    },
                                  ),
                                )
                              // Desktop layout - data table with horizontal scroll
                              : SingleChildScrollView(
                                  scrollDirection: Axis.horizontal,
                                  child: SingleChildScrollView(
                                    child: Padding(
                                      padding: const EdgeInsets.all(16.0),
                                      child: Theme(
                                        data: Theme.of(context).copyWith(
                                          dataTableTheme: DataTableThemeData(
                                            columnSpacing: 16,
                                            horizontalMargin: 16,
                                            headingTextStyle: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                              color: Color(0xFF0D47A1),
                                            ),
                                          ),
                                        ),
                                        child: DataTable(
                                          headingRowColor: WidgetStateProperty.all(
                                            const Color(0xFF0D47A1).withAlpha(25),
                                          ),                                          columns: [
                                            DataColumn(label: Text(AppLocalizations.of(context).initial)),
                                            DataColumn(label: Text(AppLocalizations.of(context).driverName)),
                                            DataColumn(label: Text(AppLocalizations.of(context).driverCode)),
                                            DataColumn(label: Text(AppLocalizations.of(context).carCode)),
                                            DataColumn(label: Text(AppLocalizations.of(context).actions)),
                                          ],
                                          rows: _filteredDrivers.map((driver) {
                                            return DataRow(
                                              cells: [
                                                DataCell(Text(driver.initial)),
                                                DataCell(Text(driver.name)),
                                                DataCell(Text(driver.driverCode)),
                                                DataCell(Text(driver.carCode)),
                                                DataCell(
                                                  Row(
                                                    mainAxisSize: MainAxisSize.min,
                                                    children: [
                                                      IconButton(
                                                        icon: const Icon(Icons.edit),
                                                        onPressed: () async {
                                                          final result = await Navigator.push(
                                                            context,
                                                            MaterialPageRoute(
                                                              builder: (context) => EditDriverScreen(driver: driver),
                                                            ),
                                                          );
                                                          if (result == true) {
                                                            _fetchDrivers();
                                                          }
                                                        },
                                                      ),
                                                      IconButton(
                                                        icon: const Icon(Icons.delete),
                                                        color: Colors.red,
                                                        onPressed: () => _showDeleteConfirmation(driver),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            );
                                          }).toList(),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                    ),
                  ],
                ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: const Color(0xFF0D47A1),
        child: const Icon(Icons.add),
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const AddDriverScreen()),
          );

          if (result == true) {
            _fetchDrivers(); // Refresh the list after adding a new driver
          }
        },
      ),
    );
  }
}






