import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../generated/l10n/app_localizations.dart';
import '../models/car.dart';
import '../services/api_service.dart';

class EditCarScreen extends StatefulWidget {
  final Car car;

  const EditCarScreen({super.key, required this.car});

  @override
  EditCarScreenState createState() => EditCarScreenState();
}

// Custom formatter to convert text to uppercase
class UpperCaseTextFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    return TextEditingValue(
      text: newValue.text.toUpperCase(),
      selection: newValue.selection,
    );
  }
}

class EditCarScreenState extends State<EditCarScreen> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _carCodeController;
  late final TextEditingController _manufacturerController;
  late final TextEditingController _modelNameController;
  late final TextEditingController _odometerController;
  late final TextEditingController _typeController;
  late final TextEditingController _plateNumberController;
  late final TextEditingController _colorController;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _carCodeController = TextEditingController(text: widget.car.carCode);
    _manufacturerController = TextEditingController(text: widget.car.manufacturer.toUpperCase());
    _modelNameController = TextEditingController(text: widget.car.modelName.toUpperCase());
    _odometerController = TextEditingController(text: widget.car.odometer.toString());
    _typeController = TextEditingController(text: widget.car.type.toUpperCase());
    _plateNumberController = TextEditingController(text: widget.car.plateNumber.toUpperCase());
    _colorController = TextEditingController(text: widget.car.color.toUpperCase());
  }

  @override
  void dispose() {
    _carCodeController.dispose();
    _manufacturerController.dispose();
    _modelNameController.dispose();
    _odometerController.dispose();
    _typeController.dispose();
    _plateNumberController.dispose();
    _colorController.dispose();
    super.dispose();
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final response = await ApiService.put(
        'cars/${widget.car.carCode}',
        {
          'manufacturer': _manufacturerController.text.toUpperCase(),
          'model_name': _modelNameController.text.toUpperCase(),
          'odometer': int.parse(_odometerController.text),
          'type': _typeController.text.toUpperCase(),
          'plate_number': _plateNumberController.text.toUpperCase(),
          'color': _colorController.text.toUpperCase(),
        },
      );

      if (response.statusCode == 200) {
        if (mounted) {
          Navigator.pop(context, true);
        }
      } else {
        final errorData = jsonDecode(response.body);        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(errorData['error'] ?? AppLocalizations.of(context).failedToUpdateCar),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${AppLocalizations.of(context).anErrorOccurred}: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(      appBar: AppBar(
        backgroundColor: Color(0xFF0D47A1),
        foregroundColor: Colors.white,
        title: Text(AppLocalizations.of(context).editCar),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      TextFormField(
                        controller: _carCodeController,                        decoration: InputDecoration(
                          labelText: AppLocalizations.of(context).carCode,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        enabled: false,
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _manufacturerController,                        decoration: InputDecoration(
                          labelText: AppLocalizations.of(context).manufacturer,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        textCapitalization: TextCapitalization.characters,
                        inputFormatters: [
                          UpperCaseTextFormatter(),
                        ],                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return AppLocalizations.of(context).pleaseEnterManufacturer;
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _modelNameController,                        decoration: InputDecoration(
                          labelText: AppLocalizations.of(context).modelName,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        textCapitalization: TextCapitalization.characters,
                        inputFormatters: [
                          UpperCaseTextFormatter(),
                        ],                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return AppLocalizations.of(context).pleaseEnterModelName;
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _odometerController,                        decoration: InputDecoration(
                          labelText: AppLocalizations.of(context).odometer,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                        ],                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return AppLocalizations.of(context).pleaseEnterOdometerReading;
                          }
                          if (int.tryParse(value) == null) {
                            return AppLocalizations.of(context).pleaseEnterValidNumber;
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _typeController,                        decoration: InputDecoration(
                          labelText: AppLocalizations.of(context).type,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        textCapitalization: TextCapitalization.characters,
                        inputFormatters: [
                          UpperCaseTextFormatter(),
                        ],                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return AppLocalizations.of(context).pleaseEnterCarType;
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _plateNumberController,                        decoration: InputDecoration(
                          labelText: AppLocalizations.of(context).plateNumber,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        textCapitalization: TextCapitalization.characters,
                        inputFormatters: [
                          UpperCaseTextFormatter(),
                        ],                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return AppLocalizations.of(context).pleaseEnterPlateNumber;
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _colorController,                        decoration: InputDecoration(
                          labelText: AppLocalizations.of(context).color,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        textCapitalization: TextCapitalization.characters,
                        inputFormatters: [
                          UpperCaseTextFormatter(),
                        ],                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return AppLocalizations.of(context).pleaseEnterColor;
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton(
                        onPressed: _submitForm,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Color(0xFF0D47A1),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        child: Text(AppLocalizations.of(context).updateCar),
                      ),
                    ],
                  ),
                ),
              ),
            ),
    );
  }
}




