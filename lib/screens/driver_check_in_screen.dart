import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../generated/l10n/app_localizations.dart';
import '../main.dart';
import '../services/api_service.dart';
import '../services/auth_service.dart';
import '../services/state_persistence_service.dart';

class DriverCheckInScreen extends StatefulWidget {
  final String driverCode;

  const DriverCheckInScreen({
    super.key,
    required this.driverCode,
  });

  @override
  State<DriverCheckInScreen> createState() => _DriverCheckInScreenState();
}

class _DriverCheckInScreenState extends State<DriverCheckInScreen> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  // Perform logout with proper cleanup and navigation
  Future<void> _performLogout() async {
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,      builder: (BuildContext context) {
        return Dialog(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 20),
                Text(AppLocalizations.of(context).loggingOut),
              ],
            ),
          ),
        );
      },
    );

    try {
      print('Driver check-in: Starting logout process');

      // Get the app state
      final appState = Provider.of<MyAppState>(context, listen: false);

      // First call the AuthService logout method to clear tokens
      await AuthService.logout();
      print('Driver check-in: AuthService.logout completed');

      // Then clear user data in the app state
      appState.logout();
      print('Driver check-in: App state cleared');

      // Double-check that all persisted data is cleared
      final persistenceService = StatePersistenceService();
      await persistenceService.clearAll();
      print('Driver check-in: Additional clearAll completed');

      // Verify data was cleared
      final hasUserData = await persistenceService.hasUserData();
      final hasToken = await persistenceService.loadAccessToken() != null;
      print('Driver check-in: Verification - User data exists: $hasUserData, Token exists: $hasToken');

      // Close the dialog and navigate to check-in screen
      if (mounted) {
        Navigator.of(context).pop(); // Close dialog
        print('Driver check-in: Navigating to login screen');

        // Navigate to login screen
        Navigator.of(context).pushNamedAndRemoveUntil(
          '/',
          (route) => false, // Remove all previous routes
        );
      }
    } catch (e) {
      print('Driver check-in: Error during logout: $e');

      // Close dialog if still showing
      if (mounted && Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }

      // Still try to navigate to login screen even if there was an error
      if (mounted) {
        Navigator.of(context).pushNamedAndRemoveUntil(
          '/',
          (route) => false,
        );
      }
    }
  }  Future<void> _submitCheckIn() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Create the request body
      final Map<String, dynamic> requestBody = {
        'driver_code': widget.driverCode,
      };

      // Make the API request
      final response = await ApiService.post(
        'drivers/check-in',
        requestBody,
      );

      if (response.statusCode == 201) {
        // Parse response to check for auto-checkout message
        try {
          final responseData = jsonDecode(response.body);
          final autoCheckoutPrevious = responseData['auto_checkout_previous'] ?? false;
          
          if (mounted) {
            // Show success message with additional info if previous check-in was auto-checked out
            if (autoCheckoutPrevious) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Previous day\'s check-in was automatically checked out'),
                  backgroundColor: Colors.orange,
                  duration: Duration(seconds: 3),
                ),
              );
            }

            // Navigate to driver trips screen
            Navigator.pushReplacementNamed(
              context,
              '/driver-trips',
              arguments: {'driverCode': widget.driverCode},
            );
          }
        } catch (e) {
          // If parsing fails, still navigate (for backward compatibility)
          if (mounted) {
            Navigator.pushReplacementNamed(
              context,
              '/driver-trips',
              arguments: {'driverCode': widget.driverCode},
            );
          }
        }
      } else {
        String errorMessage;
        try {
          final errorData = jsonDecode(response.body);
          errorMessage = errorData['error'] ?? 'Failed to check in';
          
          // Handle specific error cases
          if (response.statusCode == 409) {
            // Conflict - already checked in
            errorMessage = 'You are already checked in for today. Please check out first before checking in again.';
          }
        } catch (e) {
          errorMessage = 'Failed to check in: ${response.statusCode} ${response.reasonPhrase}';
        }

        setState(() {
          _errorMessage = errorMessage;
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error during check-in: $e');
      setState(() {
        _errorMessage = 'An error occurred: $e';
        _isLoading = false;
      });
    }
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context).driverCheckIn),
        backgroundColor: const Color(0xFF0D47A1),
        foregroundColor: Colors.white,
        actions: [
          // Add logout button to the app bar
          TextButton.icon(
            icon: const Icon(Icons.logout, color: Colors.white),
            label: Text(AppLocalizations.of(context).logout, style: const TextStyle(color: Colors.white)),
            onPressed: _performLogout,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Driver code display
                      Card(
                        elevation: 2,
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [                              Text(
                                AppLocalizations.of(context).driverInformation,
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),                              Text(
                                '${AppLocalizations.of(context).driverCodeColon} ${widget.driverCode}',
                                style: const TextStyle(fontSize: 16),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),

                      // Information about removed features
                      Card(
                        elevation: 2,
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,                            children: [
                              Text(
                                AppLocalizations.of(context).checkInInformation,
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),
                              Text(
                                AppLocalizations.of(context).clickButtonCompleteCheckIn,
                                style: const TextStyle(fontSize: 16),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),

                      // Error message
                      if (_errorMessage != null)
                        Container(
                          padding: const EdgeInsets.all(8),
                          color: Colors.red.shade100,
                          child: Text(
                            _errorMessage!,
                            style: TextStyle(color: Colors.red.shade900),
                          ),
                        ),

                      const SizedBox(height: 24),

                      // Submit button
                      SizedBox(
                        height: 50,
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF0D47A1),
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                          onPressed: _submitCheckIn,                          child: Text(
                            AppLocalizations.of(context).completeCheckIn,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
    );
  }
}


