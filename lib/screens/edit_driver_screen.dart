import 'dart:convert';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import '../generated/l10n/app_localizations.dart';
import '../models/car.dart';
import '../models/driver.dart';
import '../services/api_service.dart';

class EditDriverScreen extends StatefulWidget {
  final Driver driver;

  const EditDriverScreen({super.key, required this.driver});

  @override
  State<EditDriverScreen> createState() => _EditDriverScreenState();
}

class _EditDriverScreenState extends State<EditDriverScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _initialController;
  late TextEditingController _passwordController;
  bool _isLoading = false;
  bool _isLoadingCars = true;
  List<Car> _cars = [];
  String? _selectedCarCode;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.driver.name);
    _initialController = TextEditingController(text: widget.driver.initial);
    _passwordController = TextEditingController();
    _selectedCarCode = widget.driver.carCode.isNotEmpty ? widget.driver.carCode : null;
    _fetchCars();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _initialController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _fetchCars() async {
    setState(() => _isLoadingCars = true);
    try {
      // Use the available cars endpoint to get only cars that are not assigned to any driver
      // Include the current driver's car in the results by passing the driver code
      final response = await ApiService.get('available-cars?current_driver_code=${widget.driver.driverCode}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        setState(() {
          _cars = (data['cars'] as List).map((json) => Car.fromJson(json)).toList();
          _isLoadingCars = false;
        });      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.of(context).failedToLoadCars),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).anErrorOccurred(e.toString())),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final response = await ApiService.put(
        'drivers/${widget.driver.driverCode}',
        {
          'name': _nameController.text.toUpperCase(),
          'initial': _initialController.text.toUpperCase(),
          'car_code': _selectedCarCode,
          if (_passwordController.text.isNotEmpty)
            'password': _passwordController.text,
        },
      );

      if (mounted) {
        if (response.statusCode == 200) {
          Navigator.pop(context, true);        } else {
          final errorData = jsonDecode(response.body);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(errorData['error'] ?? AppLocalizations.of(context).failedToUpdateDriver),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).anErrorOccurred(e.toString())),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(      appBar: AppBar(
        backgroundColor: Color(0xFF0D47A1),
        foregroundColor: Colors.white,
        title: Text(AppLocalizations.of(context).editDriver),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            children: [              TextFormField(
                controller: _nameController,
                decoration: InputDecoration(
                  labelText: AppLocalizations.of(context).name,
                  helperText: AppLocalizations.of(context).nameWillBeUppercase,
                ),
                textCapitalization: TextCapitalization.characters,
                onChanged: (value) {
                  // Convert to uppercase as user types
                  final cursorPos = _nameController.selection;
                  _nameController.text = value.toUpperCase();
                  // Maintain cursor position after text change
                  _nameController.selection = cursorPos;
                },
                validator: (value) =>
                    value?.isEmpty ?? true ? AppLocalizations.of(context).nameIsRequired : null,
              ),
              const SizedBox(height: 16),              TextFormField(
                controller: _initialController,
                decoration: InputDecoration(
                  labelText: AppLocalizations.of(context).initial,
                  helperText: AppLocalizations.of(context).initialHelperText,
                ),
                textCapitalization: TextCapitalization.characters,
                maxLength: 3,
                onChanged: (value) {
                  // Convert to uppercase as user types
                  final cursorPos = _initialController.selection;
                  _initialController.text = value.toUpperCase();
                  // Maintain cursor position after text change
                  _initialController.selection = cursorPos;
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return AppLocalizations.of(context).initialIsRequired;
                  }
                  if (value.length != 3) {
                    return AppLocalizations.of(context).initialMustBe3Characters;
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownSearch<Car>(
                popupProps: PopupProps.menu(
                  showSearchBox: true,                  searchFieldProps: TextFieldProps(
                    decoration: InputDecoration(
                      hintText: AppLocalizations.of(context).searchForCar,
                      prefixIcon: const Icon(Icons.search),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                  ),
                  itemBuilder: (context, car, isSelected) {
                    return ListTile(
                      selected: isSelected,
                      title: Text('${car.carCode} - ${car.manufacturer} ${car.modelName}'),
                      subtitle: Text('Type: ${car.type}, Plate: ${car.plateNumber}'),
                    );
                  },
                ),                dropdownDecoratorProps: DropDownDecoratorProps(
                  dropdownSearchDecoration: InputDecoration(
                    labelText: AppLocalizations.of(context).carCode,
                    hintText: _isLoadingCars ? AppLocalizations.of(context).loadingCars : AppLocalizations.of(context).selectACar,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                ),
                enabled: !_isLoadingCars,
                items: _cars,
                itemAsString: (Car car) => '${car.carCode} - ${car.manufacturer} ${car.modelName}',
                onChanged: (Car? car) {
                  setState(() {
                    _selectedCarCode = car?.carCode;
                  });
                },
                selectedItem: _selectedCarCode != null
                  ? _cars.firstWhere(
                      (car) => car.carCode == _selectedCarCode,
                      orElse: () => Car(
                        carCode: '',
                        manufacturer: '',
                        modelName: '',
                        odometer: 0,
                        type: '',
                        plateNumber: '',
                        color: '',
                      ),
                    )
                  : null,                validator: (value) {
                  if (value == null) {
                    return AppLocalizations.of(context).pleaseSelectCar;
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),              TextFormField(
                controller: _passwordController,
                decoration: InputDecoration(
                  labelText: AppLocalizations.of(context).newPasswordOptional,
                  helperText: AppLocalizations.of(context).leaveEmptyToKeepPassword,
                ),
                obscureText: true,
              ),
              const SizedBox(height: 24),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Color(0xFF0D47A1),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                  ),
                  onPressed: _isLoading ? null : _submitForm,
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Text(AppLocalizations.of(context).saveChanges),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}





