import 'package:flutter/foundation.dart' show kIsWeb;

/// Utility class for web-specific functionality
class WebUtils {
  /// Updates the URL in the browser without reloading the page
  /// Only works on web platforms
  static void updateUrl(String url) {
    if (kIsWeb) {
      // Use JS interop to update the URL
      // This is done in a way that works with <PERSON><PERSON><PERSON>'s web compilation
      _updateUrlJS(url);
    }
  }

  /// Stores the user role in localStorage for use in navigation
  /// Only works on web platforms
  static void storeUserRole(String role) {
    if (kIsWeb) {
      print('WebUtils - Storing user role in localStorage: $role');

      // Use JS interop to store the user role
      final js = """
        console.log('JS - Storing user role in localStorage: $role');
        localStorage.setItem('user_role', '$role');
        console.log('JS - User role stored. Current value: ' + localStorage.getItem('user_role'));
      """;

      // Execute the JS
      _evalJS(js);

      // Verify the role was stored correctly
      final verifyJs = """
        console.log('JS - Verifying user role in localStorage: ' + localStorage.getItem('user_role'));
        return localStorage.getItem('user_role');
      """;

      final result = _evalJSWithResult(verifyJs);
      print('WebUtils - Verification - User role in localStorage: $result');
    }
  }

  // JS interop method to update URL
  static void _updateUrlJS(String url) {
    // Using JS eval through dart:js is not recommended, but this is a simple case
    // and we're just updating the URL history
    final js = """
      window.history.pushState({}, '', '$url');
    """;

    // Execute the JS
    _evalJS(js);
  }

  // JS eval method - abstracted to make it easier to replace later
  static void _evalJS(String js) {
    if (kIsWeb) {
      // This will be replaced by the compiler with the actual JS eval
      // when compiled to web
      // ignore: undefined_function
      _jsEval(js);
    }
  }

  // This is a placeholder that will be replaced by the actual JS eval
  // when compiled to web
  static void _jsEval(String js) {
    // This is intentionally left empty as it will be replaced
    // by the compiler when compiled to web
  }

  // JS eval method with result - abstracted to make it easier to replace later
  static String? _evalJSWithResult(String js) {
    if (kIsWeb) {
      // This will be replaced by the compiler with the actual JS eval
      // when compiled to web
      // ignore: undefined_function
      return _jsEvalWithResult(js);
    }
    return null;
  }

  // This is a placeholder that will be replaced by the actual JS eval with result
  // when compiled to web
  static String? _jsEvalWithResult(String js) {
    // This is intentionally left empty as it will be replaced
    // by the compiler when compiled to web
    return null;
  }
}


