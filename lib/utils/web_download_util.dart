import 'dart:html' as html;
import 'dart:typed_data';

class WebDownloadUtil {
  static bool _isDownloading = false;
  
  static void downloadFile(List<int> bytes, String fileName, String mimeType) {
    // Prevent multiple simultaneous downloads
    if (_isDownloading) {
      print('Download already in progress, skipping duplicate request');
      return;
    }
    
    _isDownloading = true;
    
    try {
      final blob = html.Blob([Uint8List.fromList(bytes)], mimeType);
      final url = html.Url.createObjectUrlFromBlob(blob);
      final anchor = html.document.createElement('a') as html.AnchorElement
        ..href = url
        ..style.display = 'none'
        ..download = fileName;
      html.document.body?.children.add(anchor);
      anchor.click();
      html.document.body?.children.remove(anchor);
      html.Url.revokeObjectUrl(url);
      
      print('Download initiated for: $fileName');
    } finally {
      // Reset the flag after a short delay to allow the download to start
      Future.delayed(const Duration(milliseconds: 500), () {
        _isDownloading = false;
      });
    }
  }
  
  static void downloadExcel(List<int> bytes, String fileName) {
    downloadFile(bytes, fileName, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
  }
}


