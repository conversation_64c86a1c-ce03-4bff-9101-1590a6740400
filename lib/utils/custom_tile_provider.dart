import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';

/// A custom tile provider that uses IP addresses instead of hostnames
/// to work around DNS resolution issues in Android emulators
class CustomTileProvider extends TileProvider {
  final String urlTemplate;
  final String? fallbackUrlTemplate;
  final Map<String, String> _customHeaders;

  CustomTileProvider({
    required this.urlTemplate,
    this.fallbackUrlTemplate,
    Map<String, String> headers = const {},
  }) : _customHeaders = headers;

  @override
  Map<String, String> get headers => _customHeaders;

  @override
  void dispose() {
    // No need to close any client as we're using the shared http client
    super.dispose();
  }

  @override
  ImageProvider getImage(TileCoordinates coordinates, TileLayer options) {
    // For Android, use a direct network image with IP address
    if (Platform.isAndroid) {
      final url = _getUrlWithIpAddress(
        urlTemplate
            .replaceAll('{z}', coordinates.z.toString())
            .replaceAll('{x}', coordinates.x.toString())
            .replaceAll('{y}', coordinates.y.toString()),
      );

      return NetworkImage(url, headers: headers);
    }

    // For other platforms, use the default implementation
    return NetworkImage(
      urlTemplate
          .replaceAll('{z}', coordinates.z.toString())
          .replaceAll('{x}', coordinates.x.toString())
          .replaceAll('{y}', coordinates.y.toString()),
      headers: headers,
    );
  }

  // Helper method to replace hostnames with IP addresses for Android
  String _getUrlWithIpAddress(String url) {
    // For Android emulator, use a direct IP address for OpenStreetMap
    // This is a Cloudflare IP that serves OpenStreetMap tiles
    url = url.replaceAll('tile.openstreetmap.org', '************');

    // Use HTTPS for security
    if (url.startsWith('http://')) {
      url = url.replaceFirst('http://', 'https://');
    }

    return url;
  }
}




