import 'dart:async';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// A mixin that provides auto-refresh functionality for Flutter screens.
/// 
/// This mixin adds:
/// - Auto-refresh timer that refreshes data at specified intervals
/// - Last refresh time tracking and formatting
/// - UI updates for the refresh time display
/// 
/// Usage:
/// 1. Add this mixin to your StatefulWidget's State class
/// 2. Call initAutoRefresh() in initState()
/// 3. Call disposeAutoRefresh() in dispose()
/// 4. Implement the refreshData() method to fetch your data
/// 5. Use the provided UI components in your build method
mixin AutoRefreshMixin<T extends StatefulWidget> on State<T> {
  /// Timer for auto-refreshing data
  Timer? _refreshTimer;
  
  /// Timer for updating the "last refreshed" text display
  Timer? _refreshTimeDisplayTimer;
  
  /// Whether auto-refresh is enabled
  bool autoRefreshEnabled = true;
  
  /// Interval in minutes between auto-refreshes
  final int refreshIntervalMinutes = 5;
  
  /// When the data was last refreshed
  DateTime? lastRefreshTime;

  /// Initialize auto-refresh functionality
  void initAutoRefresh() {
    // Initialize last refresh time
    lastRefreshTime = DateTime.now();
    
    // Start auto-refresh timer if enabled by default
    if (autoRefreshEnabled) {
      startAutoRefreshTimer();
    }
    
    // Start timer to update the "last refreshed" text every second
    startRefreshTimeDisplayTimer();
  }
  
  /// Clean up timers when disposing
  void disposeAutoRefresh() {
    _refreshTimer?.cancel();
    _refreshTimeDisplayTimer?.cancel();
  }
  
  /// Toggle auto-refresh on/off
  void toggleAutoRefresh() {
    setState(() {
      autoRefreshEnabled = !autoRefreshEnabled;
      
      if (autoRefreshEnabled) {
        // Start the timer for auto-refresh
        startAutoRefreshTimer();
        
        // Show a message that auto-refresh is enabled
        showRefreshSnackbar('Auto-refresh enabled (every $refreshIntervalMinutes minutes)');
      } else {
        // Cancel the timer
        _refreshTimer?.cancel();
        _refreshTimer = null;
        
        // Show a message that auto-refresh is disabled
        showRefreshSnackbar('Auto-refresh disabled');
      }
    });
  }
  
  /// Start the auto-refresh timer
  void startAutoRefreshTimer() {
    // Cancel any existing timer
    _refreshTimer?.cancel();
    
    // Create a new timer that fires at the specified interval
    _refreshTimer = Timer.periodic(
      Duration(minutes: refreshIntervalMinutes),
      (timer) {
        // Only refresh if the widget is still mounted
        if (mounted) {
          print('Auto-refresh triggered at ${DateTime.now()}');
          // Update the last refresh time in the UI immediately
          setState(() {
            lastRefreshTime = DateTime.now();
          });
          refreshData(showSnackbar: false);
        }
      },
    );
  }
  
  /// Start timer to update the "last refreshed" text every second
  void startRefreshTimeDisplayTimer() {
    // Cancel any existing timer
    _refreshTimeDisplayTimer?.cancel();
    
    // Create a new timer that fires every second
    _refreshTimeDisplayTimer = Timer.periodic(
      const Duration(seconds: 1),
      (timer) {
        // Only update if the widget is still mounted and we have a last refresh time
        if (mounted && lastRefreshTime != null) {
          // Force UI update to refresh the time display
          setState(() {
            // No need to change any state variables, just trigger a rebuild
          });
        }
      },
    );
  }
  
  /// Get a user-friendly string for the last refresh time
  String getLastRefreshTimeString() {
    if (lastRefreshTime == null) {
      return 'Never';
    }
    
    final now = DateTime.now();
    final difference = now.difference(lastRefreshTime!);
    
    // Total seconds
    final int totalSeconds = difference.inSeconds;
    
    if (totalSeconds < 60) {
      // Less than a minute
      return '${totalSeconds}s ago';
    } else if (difference.inMinutes < 60) {
      // Less than an hour
      final int minutes = difference.inMinutes;
      final int seconds = totalSeconds - (minutes * 60);
      return '${minutes}m ${seconds}s ago';
    } else {
      // More than an hour
      return DateFormat('HH:mm:ss').format(lastRefreshTime!);
    }
  }
  
  /// Show a snackbar message safely (without BuildContext across async gaps)
  void showRefreshSnackbar(String message, {bool isError = false}) {
    // Use a post-frame callback to ensure we're not in the middle of a build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: isError ? Colors.red : Colors.green,
          duration: Duration(seconds: isError ? 3 : 2),
        ),
      );
    });
  }
  
  /// Build the auto-refresh toggle button for the AppBar
  Widget buildAutoRefreshToggleButton() {
    return IconButton(
      icon: Icon(autoRefreshEnabled ? Icons.timer : Icons.timer_off),
      tooltip: autoRefreshEnabled 
          ? 'Disable auto-refresh' 
          : 'Enable auto-refresh (every $refreshIntervalMinutes min)',
      onPressed: toggleAutoRefresh,
    );
  }
  
  /// Build the last refresh time indicator for the AppBar
  Widget buildLastRefreshTimeIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      constraints: const BoxConstraints(minWidth: 120),
      child: Center(
        child: Text(
          'Last: ${lastRefreshTime != null ? getLastRefreshTimeString() : "Never"}',
          style: const TextStyle(fontSize: 12, color: Colors.white70),
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }
  
  /// Build the manual refresh button for the AppBar
  Widget buildManualRefreshButton() {
    return IconButton(
      icon: const Icon(Icons.refresh),
      tooltip: 'Refresh now',
      onPressed: () {
        // Force UI update for last refresh time
        setState(() {
          lastRefreshTime = DateTime.now();
        });
        refreshData();
      },
    );
  }
  
  /// Method that must be implemented by the class using this mixin
  /// This is where you should fetch your data
  Future<void> refreshData({bool showSnackbar = true});
}


