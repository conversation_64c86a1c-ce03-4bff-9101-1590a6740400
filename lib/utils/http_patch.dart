import 'dart:convert';
import 'package:http/http.dart' as http;
import '../services/api_service.dart';

/// Utility class for HTTP requests with URL conversion
/// This is a compatibility layer for code that uses http package directly
/// It's recommended to use ApiService instead for new code
class HttpPatch {
  /// Debug mode flag - set to false in production
  static bool debugMode = true;

  /// Log a message if debug mode is enabled
  static void _log(String message) {
    if (debugMode) {
      print('[HttpPatch] $message');
    }
  }

  /// Initialize the HTTP patch
  /// This is a no-op now as we're using ApiService instead of patching http directly
  static void apply() {
    _log('HTTP patch initialized - using ApiService for all requests');
  }

  /// Convert a URL for HTTP requests
  static Uri convertUrl(Uri url) {
    // Extract the path from the URL
    String path = url.path;
    if (path.startsWith('/api/')) {
      path = path.substring(5); // Remove '/api/' prefix
    }

    // Add query parameters if present
    if (url.hasQuery) {
      path = '$path?${url.query}';
    }

    // Get the API base URL first, then use it in string interpolation
    final apiBaseUrl = ApiService.getApiBaseUrl();
    return Uri.parse('$apiBaseUrl/$path');
  }

  /// Send a GET request with URL conversion
  static Future<http.Response> get(Uri url, {Map<String, String>? headers}) async {
    _log('GET request: $url (using ApiService)');
    // Extract the path from the URL
    String path = url.path;
    if (path.startsWith('/api/')) {
      path = path.substring(5); // Remove '/api/' prefix
    }

    // Add query parameters if present
    if (url.hasQuery) {
      path = '$path?${url.query}';
    }

    return ApiService.get(path, headers: headers);
  }

  /// Send a POST request with URL conversion
  static Future<http.Response> post(Uri url, {Map<String, String>? headers, Object? body, Encoding? encoding}) async {
    _log('POST request: $url (using ApiService)');
    // Extract the path from the URL
    String path = url.path;
    if (path.startsWith('/api/')) {
      path = path.substring(5); // Remove '/api/' prefix
    }

    // Add query parameters if present
    if (url.hasQuery) {
      path = '$path?${url.query}';
    }

    return ApiService.post(
      path,
      body,
      headers: headers,
      encoding: encoding
    );
  }

  /// Send a PUT request with URL conversion
  static Future<http.Response> put(Uri url, {Map<String, String>? headers, Object? body, Encoding? encoding}) async {
    _log('PUT request: $url (using ApiService)');
    // Extract the path from the URL
    String path = url.path;
    if (path.startsWith('/api/')) {
      path = path.substring(5); // Remove '/api/' prefix
    }

    // Add query parameters if present
    if (url.hasQuery) {
      path = '$path?${url.query}';
    }

    return ApiService.put(
      path,
      body,
      headers: headers,
      encoding: encoding
    );
  }

  /// Send a DELETE request with URL conversion
  static Future<http.Response> delete(Uri url, {Map<String, String>? headers, Object? body, Encoding? encoding}) async {
    _log('DELETE request: $url (using ApiService)');
    // Extract the path from the URL
    String path = url.path;
    if (path.startsWith('/api/')) {
      path = path.substring(5); // Remove '/api/' prefix
    }

    // Add query parameters if present
    if (url.hasQuery) {
      path = '$path?${url.query}';
    }

    return ApiService.delete(
      path,
      body: body,
      headers: headers,
      encoding: encoding
    );
  }
}


