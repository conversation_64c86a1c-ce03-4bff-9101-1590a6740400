import '../config/app_config.dart';

/// Logger utility for consistent logging across the application
class Logger {
  /// The tag to identify the source of the log
  final String tag;

  /// Create a new logger with the specified tag
  Logger(this.tag);

  /// Log a debug message
  void debug(String message) {
    if (AppConfig.logging.enableVerboseLogging) {
      print('[$tag] 🔍 $message');
    }
  }

  /// Log an info message
  void info(String message) {
    print('[$tag] ℹ️ $message');
  }

  /// Log a warning message
  void warning(String message) {
    print('[$tag] ⚠️ $message');
  }

  /// Log an error message
  void error(String message, [Object? error, StackTrace? stackTrace]) {
    print('[$tag] ❌ $message');
    if (error != null) {
      print('[$tag] Error details: $error');
    }
    if (stackTrace != null) {
      print('[$tag] Stack trace: $stackTrace');
    }
  }

  /// Log an API request
  void logRequest(String method, String url, {Map<String, String>? headers, Object? body}) {
    if (AppConfig.logging.logHttpRequests) {
      debug('$method request: $url');
      if (headers != null) {
        debug('Headers: ${_sanitizeHeaders(headers)}');
      }
      if (body != null) {
        debug('Body: ${_truncateBody(body.toString())}');
      }
    }
  }

  /// Log an API response
  void logResponse(String url, int statusCode, String body, {Map<String, String>? headers}) {
    if (AppConfig.logging.logHttpRequests) {
      if (statusCode >= 200 && statusCode < 300) {
        debug('Response from $url: $statusCode');
      } else {
        warning('Error response from $url: $statusCode');
      }
      if (headers != null) {
        debug('Response headers: $headers');
      }
      debug('Response body: ${_truncateBody(body)}');
    }
  }

  /// Sanitize headers to remove sensitive information
  Map<String, String> _sanitizeHeaders(Map<String, String> headers) {
    final sanitized = Map<String, String>.from(headers);
    if (sanitized.containsKey('Authorization')) {
      sanitized['Authorization'] = 'Bearer [REDACTED]';
    }
    return sanitized;
  }

  /// Truncate a long body for logging
  String _truncateBody(String body) {
    const maxLength = 500;
    if (body.length <= maxLength) {
      return body;
    }
    return '${body.substring(0, maxLength)}... [truncated]';
  }
}


