import 'dart:convert';
import 'package:intl/intl.dart';
import '../models/driver_availability.dart';
import '../services/api_service.dart';

class DriverAvailabilityService {
  // Singleton pattern
  static final DriverAvailabilityService _instance = DriverAvailabilityService._internal();
  factory DriverAvailabilityService() => _instance;
  DriverAvailabilityService._internal();

  // Cache for availability data to reduce API calls
  final Map<String, DriverAvailability> _availabilityCache = {};

  // Cache expiration time (5 minutes)
  static const int _cacheExpirationMinutes = 5;
  final Map<String, DateTime> _cacheTimestamps = {};

  // Get driver availability for a specific date and driver ID
  Future<DriverAvailability?> getDriverAvailability(int driverId, DateTime date) async {
    try {
      // Format date for API request and cache key
      final String formattedDate = DateFormat('yyyy-MM-dd').format(date.toLocal());
      final String cacheKey = '${driverId}_$formattedDate';

      // Check if we have a valid cached result
      if (_availabilityCache.containsKey(cacheKey) && _cacheTimestamps.containsKey(cacheKey)) {
        final DateTime timestamp = _cacheTimestamps[cacheKey]!;
        final DateTime now = DateTime.now();
        final Duration difference = now.difference(timestamp);

        // If cache is still valid, return cached result
        if (difference.inMinutes < _cacheExpirationMinutes) {
          return _availabilityCache[cacheKey];
        }
      }

      // Make API call to get driver availabilities for the date
      final response = await ApiService.get(
        'driver-availabilities?start_date=$formattedDate&end_date=$formattedDate',
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final availabilities = data['availabilities'] as List;

        // Find the availability for this specific driver
        for (var avail in availabilities) {
          final int availDriverId = avail['driver_id'] is String
              ? int.tryParse(avail['driver_id']) ?? 0
              : avail['driver_id'] ?? 0;

          if (availDriverId == driverId) {
            // Create a DriverAvailability object
            final availability = DriverAvailability.fromJson(avail);

            // Cache the result
            _availabilityCache[cacheKey] = availability;
            _cacheTimestamps[cacheKey] = DateTime.now();

            return availability;
          }
        }

        // If no specific availability found, create a default one based on weekday/weekend
        final defaultStatus = DriverAvailability.getDefaultStatusForDate(date);
        final defaultAvailability = DriverAvailability(
          driverId: driverId,
          driverName: 'Unknown', // We don't have the name here
          date: date,
          status: defaultStatus,
        );

        // Cache the default result
        _availabilityCache[cacheKey] = defaultAvailability;
        _cacheTimestamps[cacheKey] = DateTime.now();

        return defaultAvailability;
      } else {
        print('Failed to fetch driver availability: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      print('Error fetching driver availability: $e');
      return null;
    }
  }

  // Clear the cache
  void clearCache() {
    _availabilityCache.clear();
    _cacheTimestamps.clear();
  }

  // Check if driver should be tracked based on availability status and day of week
  bool shouldTrackDriver(DriverAvailability? availability) {
    if (availability == null) {
      return false; // No availability data, don't track
    }

    final String status = availability.status.toUpperCase();

    // Apply the tracking rules:
    // 1. If AVAILABLE - track the driver
    // 2. If UNAVAILABLE - don't track the driver

    if (status == 'AVAILABLE') {
      return true;
    } else {
      return false; // UNAVAILABLE or any other status
    }
  }

  // Get availability status considering default values for weekends
  Future<String> getEffectiveAvailabilityStatus(int driverId, DateTime date) async {
    // Try to get the explicit availability from the database
    final DriverAvailability? availability = await getDriverAvailability(driverId, date);

    if (availability != null) {
      // If we have an explicit entry, use it
      return availability.status;
    } else {
      // If no explicit entry, use the default status based on weekday/weekend
      return DriverAvailability.getDefaultStatusForDate(date);
    }
  }

  // Check if driver should be tracked based on effective availability status
  Future<bool> shouldTrackDriverByDate(int driverId, DateTime date) async {
    // Get the effective status
    final String status = await getEffectiveAvailabilityStatus(driverId, date);

    // Apply the tracking rules
    if (status.toUpperCase() == 'AVAILABLE') {
      return true;
    } else {
      return false; // UNAVAILABLE or any other status
    }
  }


}


