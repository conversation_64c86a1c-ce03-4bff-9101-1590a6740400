import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:http/http.dart' as http;

import '../config/app_config.dart';
import '../utils/logger.dart';
import 'auth_service.dart';

/// HTTP request exception
class HttpRequestException implements Exception {
  final String message;
  final int? statusCode;
  final String? body;
  final Object? originalException;

  HttpRequestException(
    this.message, {
    this.statusCode,
    this.body,
    this.originalException,
  });

  @override
  String toString() {
    return 'HttpRequestException: $message${statusCode != null ? ' (Status code: $statusCode)' : ''}';
  }
}

/// API request result
class ApiResult<T> {
  final bool success;
  final T? data;
  final String? errorMessage;
  final int? statusCode;

  ApiResult({
    required this.success,
    this.data,
    this.errorMessage,
    this.statusCode,
  });

  factory ApiResult.success(T data) {
    return ApiResult(
      success: true,
      data: data,
    );
  }

  factory ApiResult.error(String errorMessage, {int? statusCode}) {
    return ApiResult(
      success: false,
      errorMessage: errorMessage,
      statusCode: statusCode,
    );
  }

  @override
  String toString() {
    if (success) {
      return 'ApiResult.success(data: $data)';
    } else {
      return 'ApiResult.error(message: $errorMessage, statusCode: $statusCode)';
    }
  }
}

/// Service for making authenticated API requests
class ApiService {
  static final Logger _logger = Logger('ApiService');
  static final http.Client _client = http.Client();

  /// Maximum number of retries for failed requests
  static const int _maxRetries = 3;

  /// Timeout duration for requests
  static const Duration _timeout = Duration(seconds: 30);

  /// Helper method to get authenticated headers
  static Future<Map<String, String>> getAuthHeaders() async {
    final token = await AuthService.getAccessToken();
    _logger.debug('Access token: ${token != null ? 'token exists' : 'token is null'}');

    final headers = {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ${token ?? ''}',
    };

    return headers;
  }

  /// Build a full API URL from an endpoint
  static String _buildApiUrl(String endpoint) {
    // Remove leading slash if present
    if (endpoint.startsWith('/')) {
      endpoint = endpoint.substring(1);
    }

    // If the endpoint already contains the full URL, return it as is
    if (endpoint.startsWith('http://') || endpoint.startsWith('https://')) {
      return endpoint;
    }

    // Get the API base URL first, then use it in string interpolation
    final apiBaseUrl = getApiBaseUrl();
    return '$apiBaseUrl/$endpoint';
  }

  /// Process the body for a request
  static Object? _processBody(Object? body) {
    // Convert body to JSON string if it's a Map or List
    if (body is Map || body is List) {
      return jsonEncode(body);
    }
    return body;
  }

  /// Execute a request with retry logic and error handling
  static Future<http.Response> _executeRequest(
    String method,
    String url,
    Future<http.Response> Function() requestFn,
  ) async {
    int retryCount = 0;

    while (true) {
      try {
        _logger.debug('$method request to $url');

        final response = await requestFn().timeout(
          _timeout,
          onTimeout: () {
            throw TimeoutException('Request timed out after ${_timeout.inSeconds} seconds');
          },
        );

        _logger.debug('Response status code: ${response.statusCode}');

        return response;
      } catch (e) {
        // Don't retry if we've reached the maximum number of retries
        if (retryCount >= _maxRetries) {
          if (e is SocketException) {
            throw HttpRequestException(
              'Network error: Unable to connect to server. Please check your network connection.',
              originalException: e,
            );
          } else if (e is TimeoutException) {
            throw HttpRequestException(
              'Request timed out. Please try again later.',
              originalException: e,
            );
          } else if (e is FormatException) {
            throw HttpRequestException(
              'Invalid response format from server.',
              originalException: e,
            );
          } else {
            throw HttpRequestException(
              'Error during HTTP request: ${e.toString()}',
              originalException: e,
            );
          }
        }

        // Exponential backoff for retries
        final waitTime = Duration(milliseconds: 200 * (1 << retryCount));
        _logger.warning('Request failed, retrying in ${waitTime.inMilliseconds}ms: $e');
        await Future.delayed(waitTime);
        retryCount++;
      }
    }
  }

  /// Generic GET request with authentication
  static Future<http.Response> get(String endpoint, {Map<String, String>? headers}) async {
    final authHeaders = await getAuthHeaders();

    // Merge custom headers with auth headers
    if (headers != null) {
      authHeaders.addAll(headers);
    }

    final url = _buildApiUrl(endpoint);

    return _executeRequest(
      'GET',
      url,
      () => _client.get(Uri.parse(url), headers: authHeaders),
    );
  }

  /// Generic POST request with authentication
  static Future<http.Response> post(String endpoint, dynamic body, {Map<String, String>? headers, Encoding? encoding}) async {
    final authHeaders = await getAuthHeaders();

    // Merge custom headers with auth headers
    if (headers != null) {
      authHeaders.addAll(headers);
    }

    final url = _buildApiUrl(endpoint);
    final processedBody = _processBody(body);

    return _executeRequest(
      'POST',
      url,
      () => _client.post(
        Uri.parse(url),
        headers: authHeaders,
        body: processedBody,
        encoding: encoding,
      ),
    );
  }

  /// Generic PUT request with authentication
  static Future<http.Response> put(String endpoint, dynamic body, {Map<String, String>? headers, Encoding? encoding}) async {
    final authHeaders = await getAuthHeaders();

    // Merge custom headers with auth headers
    if (headers != null) {
      authHeaders.addAll(headers);
    }

    final url = _buildApiUrl(endpoint);
    final processedBody = _processBody(body);

    return _executeRequest(
      'PUT',
      url,
      () => _client.put(
        Uri.parse(url),
        headers: authHeaders,
        body: processedBody,
        encoding: encoding,
      ),
    );
  }

  /// Generic DELETE request with authentication
  static Future<http.Response> delete(String endpoint, {dynamic body, Map<String, String>? headers, Encoding? encoding}) async {
    final authHeaders = await getAuthHeaders();

    // Merge custom headers with auth headers
    if (headers != null) {
      authHeaders.addAll(headers);
    }

    final url = _buildApiUrl(endpoint);
    final processedBody = _processBody(body);

    return _executeRequest(
      'DELETE',
      url,
      () => _client.delete(
        Uri.parse(url),
        headers: authHeaders,
        body: processedBody,
        encoding: encoding,
      ),
    );
  }

  /// Close the client when it's no longer needed
  static void close() {
    _client.close();
  }

  /// Parse JSON response and handle errors
  static dynamic parseResponse(http.Response response) {
    if (response.statusCode >= 200 && response.statusCode < 300) {
      // Success response
      if (response.body.isEmpty) {
        return null;
      }

      try {
        return jsonDecode(response.body);
      } catch (e) {
        _logger.error('Error parsing JSON response', e);
        throw HttpRequestException(
          'Invalid JSON response from server',
          statusCode: response.statusCode,
          body: response.body,
          originalException: e,
        );
      }
    } else {
      // Error response
      String errorMessage = 'Server error: ${response.statusCode}';

      try {
        final errorData = jsonDecode(response.body);
        if (errorData['error'] != null) {
          errorMessage = errorData['error'];
        }
      } catch (e) {
        // If we can't parse the error JSON, use the response body as the error message
        if (response.body.isNotEmpty) {
          errorMessage = response.body;
        }
      }

      throw HttpRequestException(
        errorMessage,
        statusCode: response.statusCode,
        body: response.body,
      );
    }
  }

  /// Execute an API request and return a typed result
  static Future<ApiResult<T>> execute<T>(
    Future<http.Response> Function() requestFn,
    T Function(dynamic) mapper,
  ) async {
    try {
      final response = await requestFn();
      final data = parseResponse(response);
      return ApiResult.success(mapper(data));
    } on HttpRequestException catch (e) {
      return ApiResult.error(e.message, statusCode: e.statusCode);
    } catch (e) {
      _logger.error('Error executing API request', e);
      return ApiResult.error('An unexpected error occurred: ${e.toString()}');
    }
  }

  /// Execute a GET request and return a typed result
  static Future<ApiResult<T>> getTyped<T>(
    String endpoint,
    T Function(dynamic) mapper,
  ) async {
    return execute(() => get(endpoint), mapper);
  }

  /// Execute a POST request and return a typed result
  static Future<ApiResult<T>> postTyped<T>(
    String endpoint,
    dynamic body,
    T Function(dynamic) mapper,
  ) async {
    return execute(() => post(endpoint, body), mapper);
  }

  /// Execute a PUT request and return a typed result
  static Future<ApiResult<T>> putTyped<T>(
    String endpoint,
    dynamic body,
    T Function(dynamic) mapper,
  ) async {
    return execute(() => put(endpoint, body), mapper);
  }

  /// Execute a DELETE request and return a typed result
  static Future<ApiResult<T>> deleteTyped<T>(
    String endpoint, {
    dynamic body,
    required T Function(dynamic) mapper,
  }) async {
    return execute(() => delete(endpoint, body: body), mapper);
  }

  /// Get the base URL for API requests
  static String getBaseUrl() {
    return AppConfig.api.baseUrl;
  }

  /// Get the API base URL (includes /api path)
  static String getApiBaseUrl() {
    return AppConfig.api.apiBaseUrl;
  }

  /// Get the full URL for an image or other static resource
  static String getImageUrl(String path) {
    // Remove leading slash if present
    if (path.startsWith('/')) {
      path = path.substring(1);
    }

    // If the path already contains the full URL, return it as is
    if (path.startsWith('http://') || path.startsWith('https://')) {
      return path;
    }

    // Get the base URL first, then use it in string interpolation
    final baseUrl = getBaseUrl();
    return '$baseUrl/$path';
  }
}


