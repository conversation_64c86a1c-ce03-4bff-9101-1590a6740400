import 'dart:async';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// A bridge class to handle communication between web content and native app
class WebViewBridge {
  static const MethodChannel _channel = MethodChannel('com.example.fleex/webview_bridge');

  /// Initialize the bridge
  static Future<void> initialize() async {
    if (!kIsWeb) {
      try {
        await _channel.invokeMethod('initialize');
      } on PlatformException catch (e) {
        debugPrint('Failed to initialize WebViewBridge: ${e.message}');
      }
    }
  }

  /// Send data from native to web
  static Future<void> sendToWeb(String event, dynamic data) async {
    if (!kIsWeb) {
      try {
        await _channel.invokeMethod('sendToWeb', {
          'event': event,
          'data': data,
        });
      } on PlatformException catch (e) {
        debugPrint('Failed to send data to web: ${e.message}');
      }
    }
  }

  /// Register a callback to receive data from web
  static void registerCallback(String event, Function(dynamic) callback) {
    debugPrint('WebViewBridge: Registering callback for event: $event');
    if (!kIsWeb) {
      _channel.setMethodCallHandler((call) async {
        debugPrint('WebViewBridge: Received method call: ${call.method}');
        if (call.method == 'fromWeb') {
          debugPrint('WebViewBridge: Received fromWeb event: ${call.arguments['event']}');
          if (call.arguments['event'] == event) {
            debugPrint('WebViewBridge: Calling callback with data: ${call.arguments['data']}');
            callback(call.arguments['data']);
          }
        }
        return null;
      });
      debugPrint('WebViewBridge: Callback registered successfully');
    } else {
      debugPrint('WebViewBridge: Not registering callback in web mode');
    }
  }

  /// Handle deep links from web to app
  static Future<bool> handleDeepLink(String url) async {
    debugPrint('WebViewBridge: handleDeepLink called with URL: $url');
    if (!kIsWeb) {
      try {
        // Special handling for SSO callback URLs
        if (url.startsWith('com.example.fleex://auth/callback')) {
          debugPrint('WebViewBridge: Detected SSO callback URL, handling directly');
          // For SSO callbacks, we'll handle them directly in Flutter
          // This avoids the circular reference issue
          _channel.invokeMethod('fromWeb', {
            'event': 'deepLink',
            'data': url
          });
          return true;
        }

        // For other URLs, use the native handler
        debugPrint('WebViewBridge: Invoking native handleDeepLink method');
        final bool handled = await _channel.invokeMethod('handleDeepLink', {
          'url': url,
        });
        debugPrint('WebViewBridge: Native handleDeepLink result: $handled');
        return handled;
      } on PlatformException catch (e) {
        debugPrint('WebViewBridge: Failed to handle deep link: ${e.message}');
        return false;
      } catch (e) {
        debugPrint('WebViewBridge: Unexpected error handling deep link: $e');
        return false;
      }
    }
    debugPrint('WebViewBridge: Not handling deep link in web mode');
    return false;
  }

  /// Check if running in WebView
  static Future<bool> isRunningInWebView() async {
    if (kIsWeb) return false;

    try {
      final bool isWebView = await _channel.invokeMethod('isWebView');
      return isWebView;
    } on PlatformException catch (e) {
      debugPrint('Failed to check if running in WebView: ${e.message}');
      return false;
    }
  }
}


