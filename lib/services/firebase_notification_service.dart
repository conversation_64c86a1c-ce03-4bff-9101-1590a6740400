import 'dart:convert';
import 'dart:io';
import 'dart:async';

import 'package:FLEEX/firebase_options.dart';
import 'package:FLEEX/services/api_service.dart';
import 'package:FLEEX/services/notification_translation_service.dart';
import 'package:FLEEX/services/localization_service.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

class FirebaseNotificationService {
  static final FirebaseNotificationService _instance =
      FirebaseNotificationService._internal();
  factory FirebaseNotificationService() => _instance;
  FirebaseNotificationService._internal();

  FirebaseMessaging? _firebaseMessaging;
  final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();  bool _initialized = false;
  bool _handlersSetup = false;
  String? _fcmToken;
  
  // Store subscriptions to cancel them if needed
  StreamSubscription<RemoteMessage>? _foregroundSubscription;
  StreamSubscription<RemoteMessage>? _backgroundOpenedSubscription;
  StreamSubscription<String>? _tokenRefreshSubscription;

  // Getters
  String? get fcmToken => _fcmToken;
  bool get isInitialized => _initialized;  /// Initialize Firebase and notification services
  Future<void> initialize() async {
    if (_initialized) {
      print('✅ Firebase already initialized');
      return;
    }

    // Skip Firebase initialization on web platform
    if (kIsWeb) {
      print('Skipping Firebase initialization on web platform');
      _initialized = true;
      return;
    }

    print('🔄 Starting Firebase initialization...');

    try {
      // Initialize Firebase with detailed error handling
      try {
        print('🔄 Calling Firebase.initializeApp()...');
        await Firebase.initializeApp(
          options: DefaultFirebaseOptions.currentPlatform,
        );
        print('✅ Firebase.initializeApp() completed successfully');
        
        // Initialize FirebaseMessaging instance after Firebase is initialized
        _firebaseMessaging = FirebaseMessaging.instance;
        print('✅ FirebaseMessaging instance created');
        
      } catch (e) {
        print('⚠️ Firebase.initializeApp() error: $e');
        
        // Check if this is just a "already initialized" error
        String errorMsg = e.toString().toLowerCase();
        if (errorMsg.contains('already') || 
            errorMsg.contains('duplicate') || 
            errorMsg.contains('initialized')) {
          print('ℹ️ Firebase was already initialized, creating FirebaseMessaging instance...');
          _firebaseMessaging = FirebaseMessaging.instance;
          print('✅ FirebaseMessaging instance created from existing Firebase app');
        } else {
          print('❌ Critical Firebase initialization error: $e');
          // For other errors, still try to continue but log the error
          print('⚠️ Continuing with Firebase initialization despite error...');
          // Still try to create FirebaseMessaging instance
          try {
            _firebaseMessaging = FirebaseMessaging.instance;
            print('✅ FirebaseMessaging instance created despite initialization error');
          } catch (msgError) {
            print('❌ Failed to create FirebaseMessaging instance: $msgError');
            return; // Cannot continue without FirebaseMessaging
          }
        }
      }

      // Initialize local notifications
      print('🔄 Initializing local notifications...');
      await _initializeLocalNotifications();
      print('✅ Local notifications initialized');

      // Request notification permissions
      print('🔄 Requesting notification permissions...');
      await _requestPermissions();
      print('✅ Notification permissions handled');

      // Get FCM token
      print('🔄 Getting FCM token...');
      await _getFCMToken();
      print('✅ FCM token retrieval completed');

      // Set up message handlers
      print('🔄 Setting up message handlers...');
      _setupMessageHandlers();
      print('✅ Message handlers set up');

      _initialized = true;
      print('✅ Firebase Notification Service initialized successfully');
    } catch (e) {
      print('❌ Error initializing Firebase Notification Service: $e');
      print('❌ Stack trace: ${e.toString()}');
      // Mark as initialized to prevent infinite retry loops
      _initialized = true;
    }
  }  /// Initialize local notifications
  Future<void> _initializeLocalNotifications() async {
    // Skip local notifications on web platform
    if (kIsWeb) {
      return;
    }

    const androidSettings = AndroidInitializationSettings('@drawable/ic_fleex_notification');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Create notification channel for Android
    if (Platform.isAndroid) {
      const androidChannel = AndroidNotificationChannel(
        'FLEEX_NOTIFICATIONS',
        'FLEEX Notifications',
        description: 'Notifications for FLEEX app',
        importance: Importance.high,
      );

      await _localNotifications
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(androidChannel);
    }
  }  /// Request notification permissions
  Future<void> _requestPermissions() async {
    // Skip on web platform
    if (kIsWeb) {
      return;
    }

    if (_firebaseMessaging == null) {
      print('❌ FirebaseMessaging not initialized, cannot request permissions');
      return;
    }

    final settings = await _firebaseMessaging!.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    print('Notification permission status: ${settings.authorizationStatus}');
  }  /// Get FCM token
  Future<void> _getFCMToken() async {
    // Skip on web platform
    if (kIsWeb) {
      return;
    }

    if (_firebaseMessaging == null) {
      print('❌ FirebaseMessaging not initialized, cannot get FCM token');
      return;
    }

    try {
      print('🔄 Requesting FCM token from Firebase...');
      _fcmToken = await _firebaseMessaging!.getToken();
      print('✅ FCM Token obtained: ${_fcmToken?.substring(0, 50)}...');

      // Note: We don't send the token to server here because we don't have user ID yet
      // The token will be sent when updateTokenForUser() is called after login
    } catch (e) {
      print('❌ Error getting FCM token: $e');
    }
  }
  /// Send FCM token to server
  Future<void> _sendTokenToServer(String token) async {
    // Skip on web platform
    if (kIsWeb) {
      return;
    }

    try {
      // TODO: Replace with your user ID logic
      // You might want to get this from your auth service
      final response = await ApiService.post('notifications/fcm-token', {
        'token': token,
        'platform': Platform.isAndroid ? 'android' : 'ios',
      });

      if (response.statusCode == 200) {
        print('FCM token sent to server successfully');
      } else {
        print('Failed to send FCM token to server: ${response.statusCode}');
      }
    } catch (e) {
      print('Error sending FCM token to server: $e');
    }
  }

  /// Remove FCM token from server (called during logout)
  Future<void> removeTokenFromServer() async {
    // Skip on web platform
    if (kIsWeb) {
      return;
    }

    if (_fcmToken == null) {
      print('No FCM token to remove');
      return;
    }    try {
      final response = await ApiService.delete('notifications/fcm-token', body: {
        'token': _fcmToken!,
      });

      if (response.statusCode == 200) {
        print('FCM token removed from server successfully');
        _fcmToken = null; // Clear local token reference
      } else {
        print('Failed to remove FCM token from server: ${response.statusCode}');
      }
    } catch (e) {
      print('Error removing FCM token from server: $e');
    }
  }  /// Set up Firebase message handlers
  void _setupMessageHandlers() {
    // Skip on web platform
    if (kIsWeb) {
      return;
    }

    if (_firebaseMessaging == null) {
      print('❌ FirebaseMessaging not initialized, cannot set up message handlers');
      return;
    }

    // Prevent multiple handler setups
    if (_handlersSetup) {
      print('⚠️ Message handlers already set up, skipping');
      return;
    }

    // Cancel any existing subscriptions first
    _foregroundSubscription?.cancel();
    _backgroundOpenedSubscription?.cancel();
    _tokenRefreshSubscription?.cancel();

    print('🔧 Setting up Firebase message handlers...');

    // Handle messages when app is in foreground
    _foregroundSubscription = FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle messages when app is opened from background
    _backgroundOpenedSubscription = FirebaseMessaging.onMessageOpenedApp.listen(_handleBackgroundMessage);

    // Handle token refresh
    _tokenRefreshSubscription = _firebaseMessaging!.onTokenRefresh.listen((token) {
      _fcmToken = token;
      _sendTokenToServer(token);
    });
    
    _handlersSetup = true;
    print('✅ Firebase message handlers set up');
  }/// Handle messages when app is in foreground
  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    print('🔔 FOREGROUND MESSAGE: ${message.messageId}');
    print('🔔 Notification: ${message.notification?.toMap()}');
    print('🔔 Data: ${message.data}');

    // Handle data messages (our primary method now)
    if (message.data.isNotEmpty && message.data.containsKey('title') && message.data.containsKey('body')) {
      print('🔔 FOREGROUND: Showing local notification for data message');
      await _showDataNotification(message.data);
    }
    // Handle traditional notification messages (fallback)
    else if (message.notification != null) {
      print('🔔 FOREGROUND: Showing local notification for traditional notification message');
      await _showLocalNotification(message);
    }
    
    // Handle any additional actions based on data
    if (message.data.isNotEmpty) {
      await _handleNotificationAction(message.data);
    }
  }
  /// Handle messages when app is opened from background
  Future<void> _handleBackgroundMessage(RemoteMessage message) async {
    print('App opened from background message: ${message.messageId}');
    print('Data: ${message.data}');

    // Handle navigation or other actions when notification is tapped
    await _handleNotificationAction(message.data);
  }  /// Translate push notification content if localization data is available
  Future<Map<String, String>?> _translatePushNotification(Map<String, dynamic> data) async {
    try {
      // Get the app's current language preference (from flag selection)
      String appLocale = 'en'; // Default to English
      try {
        final localizationService = LocalizationService();
        final savedLocale = await localizationService.getSavedLocale();
        appLocale = savedLocale.languageCode;
        print('🌍 App locale from flag selection: $appLocale');
      } catch (e) {
        print('🌍 Could not get app locale, using English fallback: $e');
      }

      // First, try to use localization data if available
      final localizationDataString = data['localization_data'] as String?;
      if (localizationDataString != null && localizationDataString.isNotEmpty) {
        try {
          // Parse localization data
          final localizationData = jsonDecode(localizationDataString) as Map<String, dynamic>;
          print('🌍 Found localization data: $localizationData');

          // Use the context-free translation method from NotificationTranslationService
          final translatedContent = NotificationTranslationService.translatePushNotification(
            localizationData,
            locale: appLocale, // Use the app's selected language
          );

          if (translatedContent != null) {
            print('🌍 Successfully translated push notification ($appLocale): ${translatedContent['title']} / ${translatedContent['body']}');
            return translatedContent;
          }
        } catch (e) {
          print('🌍 Error parsing localization data: $e, falling back to pattern matching');
        }
      }

      // If no localization data or translation failed, try pattern matching as fallback
      if (appLocale != 'en') {
        print('🌍 No localization data found, attempting pattern matching fallback');
        final title = data['title'] as String? ?? '';
        final body = data['body'] as String? ?? '';
          if (title.isNotEmpty && body.isNotEmpty) {
          // Try to parse using the same pattern matching logic as in-app notifications
          final parsedData = NotificationTranslationService.parseNotificationContent(
            title: title,
            message: body,
          );
          
          if (parsedData != null) {
            final translatedContent = NotificationTranslationService.translatePushNotification(
              parsedData,
              locale: appLocale,
            );
            
            if (translatedContent != null) {
              print('🌍 Successfully translated push notification via pattern matching ($appLocale): ${translatedContent['title']} / ${translatedContent['body']}');
              return translatedContent;
            }
          }
        }
      }

      print('🌍 No translation available, using original content');
      return null;
      
    } catch (e) {
      print('❌ Error translating push notification: $e');
      return null;
    }
  }/// Show local notification
  Future<void> _showLocalNotification(RemoteMessage message) async {
    // Skip on web platform
    if (kIsWeb) {
      return;
    }

    final notification = message.notification;
    final data = message.data;

    if (notification != null) {
      String title = notification.title ?? '';
      String body = notification.body ?? '';

      // Try to translate notification if localization data is available
      final translatedContent = await _translatePushNotification(data);
      if (translatedContent != null) {
        title = translatedContent['title'] ?? title;
        body = translatedContent['body'] ?? body;
        print('🌍 Using translated traditional notification: $title / $body');
      }

      const androidDetails = AndroidNotificationDetails(
        'FLEEX_NOTIFICATIONS',
        'FLEEX Notifications',
        channelDescription: 'Notifications for FLEEX app',
        importance: Importance.high,
        priority: Priority.high,
        showWhen: true,
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications.show(
        message.hashCode,
        title,
        body,
        notificationDetails,
        payload: jsonEncode(data),
      );
    }
  }/// Show local notification from data payload
  Future<void> _showDataNotification(Map<String, dynamic> data) async {
    // Skip on web platform
    if (kIsWeb) {
      return;
    }

    String title = data['title'] ?? '';
    String body = data['body'] ?? '';

    print('🔔 _showDataNotification called with title: $title, body: $body');

    // Try to translate notification if localization data is available
    final translatedContent = await _translatePushNotification(data);
    if (translatedContent != null) {
      title = translatedContent['title'] ?? title;
      body = translatedContent['body'] ?? body;
      print('🌍 Using translated notification: $title / $body');
    }

    if (title.isNotEmpty && body.isNotEmpty) {
      const androidDetails = AndroidNotificationDetails(
        'FLEEX_NOTIFICATIONS',
        'FLEEX Notifications',
        channelDescription: 'Notifications for FLEEX app',
        importance: Importance.high,
        priority: Priority.high,
        showWhen: true,
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      final notificationId = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      print('🔔 Showing local notification with ID: $notificationId');

      await _localNotifications.show(
        notificationId,
        title,
        body,
        notificationDetails,
        payload: jsonEncode(data),
      );
      
      print('🔔 Local notification shown successfully');
    } else {
      print('❌ Cannot show notification - missing title or body');
    }
  }

  /// Handle notification tap
  void _onNotificationTapped(NotificationResponse response) {
    print('Notification tapped with payload: ${response.payload}');

    if (response.payload != null) {
      try {
        final data = jsonDecode(response.payload!) as Map<String, dynamic>;
        _handleNotificationAction(data);
      } catch (e) {
        print('Error parsing notification payload: $e');
      }
    }
  }

  /// Handle notification actions (navigation, etc.)
  Future<void> _handleNotificationAction(Map<String, dynamic> data) async {
    final type = data['type'];
    final entityId = data['entity_id'];

    print('Handling notification action - Type: $type, Entity ID: $entityId');

    // TODO: Implement navigation logic based on notification type
    switch (type) {
      case 'TRIP_ASSIGNED':
      case 'TRIP_STATUS_CHANGED':
      case 'TRIP_APPROVED':
      case 'TRIP_REJECTED':
        // Navigate to trip details or trip screen
        break;
      case 'DRIVER_REJECTED':
        // Navigate to trip management screen
        break;
      default:
        // Navigate to notifications screen
        break;
    }
  }
  /// Subscribe to topic
  Future<void> subscribeToTopic(String topic) async {
    // Skip on web platform
    if (kIsWeb) {
      return;
    }    try {
      if (_firebaseMessaging == null) {
        print('❌ FirebaseMessaging not initialized, cannot subscribe to topic');
        return;
      }
      await _firebaseMessaging!.subscribeToTopic(topic);
      print('Subscribed to topic: $topic');
    } catch (e) {
      print('Error subscribing to topic $topic: $e');
    }
  }
  /// Unsubscribe from topic
  Future<void> unsubscribeFromTopic(String topic) async {
    // Skip on web platform
    if (kIsWeb) {
      return;
    }    try {
      if (_firebaseMessaging == null) {
        print('❌ FirebaseMessaging not initialized, cannot unsubscribe from topic');
        return;
      }
      await _firebaseMessaging!.unsubscribeFromTopic(topic);
      print('Unsubscribed from topic: $topic');
    } catch (e) {
      print('Error unsubscribing from topic $topic: $e');
    }
  }  /// Update FCM token for a user
  Future<void> updateTokenForUser(dynamic userId) async {
    // Skip on web platform
    if (kIsWeb) {
      print('Skipping FCM token update on web platform for user $userId');
      return;
    }
    
    print('🔥 Starting FCM token update for user: $userId');
    
    // Check if Firebase is initialized
    if (!_initialized) {
      print('❌ Firebase not initialized, cannot update FCM token for user $userId');
      return;
    }
      // If we don't have an FCM token yet, get it
    if (_fcmToken == null) {
      print('🔄 No cached FCM token, retrieving new token for user $userId');
      try {
        if (_firebaseMessaging == null) {
          print('❌ FirebaseMessaging not initialized, cannot get FCM token for user $userId');
          return;
        }
        _fcmToken = await _firebaseMessaging!.getToken();
        print('✅ FCM Token retrieved for user $userId: ${_fcmToken?.substring(0, 50)}...');
      } catch (e) {
        print('❌ Error getting FCM token for user $userId: $e');
        return;
      }
    } else {
      print('✅ Using cached FCM token for user $userId: ${_fcmToken?.substring(0, 50)}...');
    }
    
    if (_fcmToken != null) {
      try {
        print('📤 Sending FCM token to server for user $userId');
        final requestBody = {
          'user_id': userId.toString(),
          'token': _fcmToken,
          'platform': Platform.isAndroid ? 'android' : 'ios',
        };
        print('📝 Request body: $requestBody');
        
        final response = await ApiService.post('notifications/fcm-token', requestBody);

        print('📥 Server response - Status: ${response.statusCode}');
        print('📥 Server response - Body: ${response.body}');

        if (response.statusCode == 200) {
          print('✅ FCM token updated successfully for user $userId');
        } else {
          print('❌ Failed to update FCM token for user $userId: ${response.statusCode}');
          print('Response body: ${response.body}');
        }
      } catch (e) {
        print('❌ Error updating FCM token for user $userId: $e');
        print('Stack trace: ${e.toString()}');
      }
    } else {
      print('❌ No FCM token available for user $userId');
    }
  }
  /// Clear all notifications
  Future<void> clearAllNotifications() async {
    // Skip on web platform
    if (kIsWeb) {
      return;
    }
    await _localNotifications.cancelAll();
  }
  /// Clear notification by ID
  Future<void> clearNotification(int id) async {
    // Skip on web platform
    if (kIsWeb) {
      return;
    }
    await _localNotifications.cancel(id);
  }

  /// Dispose of all subscriptions
  void dispose() {
    _foregroundSubscription?.cancel();
    _backgroundOpenedSubscription?.cancel();
    _tokenRefreshSubscription?.cancel();
    _handlersSetup = false;
    print('🧹 Firebase notification service disposed');
  }
}

/// Handle background messages (top-level function required)
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // Skip on web platform
  if (kIsWeb) {
    return;
  }
  
  try {
    await Firebase.initializeApp();
    print('🔔 BACKGROUND: Background message received: ${message.messageId}');
    print('🔔 BACKGROUND: Data: ${message.data}');
    
    // On Android, let the native FirebaseMessagingService handle background notifications
    // On iOS, we need to handle them here since iOS doesn't have a native service
    if (Platform.isIOS) {
      print('🔔 BACKGROUND: iOS detected - showing notification from Flutter');
      
      // Show notification for background messages on iOS
      final FlutterLocalNotificationsPlugin localNotifications = FlutterLocalNotificationsPlugin();
        // Initialize local notifications
      const androidSettings = AndroidInitializationSettings('@drawable/ic_fleex_notification');
      const iosSettings = DarwinInitializationSettings();
      const initSettings = InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
      );
      
      await localNotifications.initialize(initSettings);
      
      // Extract notification data
      String? title;
      String? body;
      
      // Check if it's a data message
      if (message.data.isNotEmpty && message.data.containsKey('title') && message.data.containsKey('body')) {
        title = message.data['title'];
        body = message.data['body'];
      }
      // Check if it's a traditional notification
      else if (message.notification != null) {
        title = message.notification!.title;
        body = message.notification!.body;
      }
      
      // Show notification if we have title and body
      if (title != null && body != null) {
        const iosDetails = DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
        );

        const notificationDetails = NotificationDetails(
          iOS: iosDetails,
        );

        await localNotifications.show(
          DateTime.now().millisecondsSinceEpoch ~/ 1000,
          title,
          body,
          notificationDetails,
          payload: jsonEncode(message.data),
        );
        
        print('🔔 BACKGROUND: iOS notification shown: $title');
      }
    } else {
      print('🔔 BACKGROUND: Android detected - letting native service handle notification');
    }
    
  } catch (e) {
    print('❌ BACKGROUND: Error in background message handler: $e');
  }
}
