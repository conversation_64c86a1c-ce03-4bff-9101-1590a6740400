import 'dart:io';
import 'package:excel/excel.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import '../models/trip.dart';

class ExcelExportService {  static Future<String> exportTripsToExcel(List<Trip> trips) async {
    // Generate file name with timestamp
    final timestamp = DateFormat('yyyy-MM-dd_HH-mm-ss').format(DateTime.now());
    final fileName = 'trip_history_$timestamp.xlsx';
    
    // For web, we only generate bytes and return filename
    // The actual download is handled by the calling code
    if (kIsWeb) {
      return fileName;
    } else {
      // For mobile/desktop, create the Excel file and save it
      final excel = _createExcelFile(trips);
      final bytes = excel.save();
      return await _saveFileMobile(bytes!, fileName);
    }
  }
  
  // Helper method to create Excel file with data
  static Excel _createExcelFile(List<Trip> trips) {
    final excel = Excel.createExcel();
    final sheet = excel['Trip History'];
    
    // Remove default sheet
    excel.delete('Sheet1');
    
    // Add headers
    final headers = [
      'Trip Code',
      'Requestor',
      'From Destination', 
      'To Destination',
      'Date',
      'Time',
      'Status',
      'Driver Name',
      'Rating',
      'Comments',
      'Completion Notes',
      'Rejection Reason',
      'Total Duration',
      'Passengers',
      'Cargo Items'
    ];
    
    // Set header row
    for (int i = 0; i < headers.length; i++) {
      final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0));
      cell.value = TextCellValue(headers[i]);
      
      // Style the header
      cell.cellStyle = CellStyle(
        bold: true,
        backgroundColorHex: ExcelColor.blue,
        fontColorHex: ExcelColor.white,
      );
    }
    
    // Add data rows
    for (int rowIndex = 0; rowIndex < trips.length; rowIndex++) {
      final trip = trips[rowIndex];
      final row = rowIndex + 1; // +1 because row 0 is header
      
      // Helper function to set cell value
      void setCellValue(int colIndex, String value) {
        final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: colIndex, rowIndex: row));
        cell.value = TextCellValue(value);
      }
      
      setCellValue(0, trip.tripCode);
      setCellValue(1, trip.requestorName);
      setCellValue(2, trip.fromDestination);
      setCellValue(3, trip.toDestination);
      setCellValue(4, trip.date);
      setCellValue(5, trip.time);
      setCellValue(6, trip.status);
      setCellValue(7, trip.driverName ?? 'Unassigned');
      setCellValue(8, trip.rating?.toString() ?? 'Not rated');
      setCellValue(9, trip.comments ?? '');
      setCellValue(10, trip.completionNotes ?? '');
      setCellValue(11, trip.rejectionReason ?? '');
      setCellValue(12, trip.totalDuration ?? '');
      
      // Format passengers list
      String passengersList = '';
      if (trip.passengers != null && trip.passengers!.isNotEmpty) {
        passengersList = trip.passengers!.map((p) => p.name).join(', ');
      }
      setCellValue(13, passengersList);
      
      // Format cargo list
      String cargoList = '';
      if (trip.cargos != null && trip.cargos!.isNotEmpty) {
        cargoList = trip.cargos!.map((c) => c.name).join(', ');
      }
      setCellValue(14, cargoList);
    }
    
    // Auto-fit columns (approximate)
    for (int i = 0; i < headers.length; i++) {
      sheet.setColumnWidth(i, 20.0);
    }
      return excel;
  }
  
  static Future<String> _saveFileMobile(List<int> bytes, String fileName) async {
    try {
      Directory? directory;
      
      if (Platform.isAndroid) {
        // For Android, try to save to Downloads folder
        directory = Directory('/storage/emulated/0/Download');
        if (!await directory.exists()) {
          directory = await getExternalStorageDirectory();
        }
      } else if (Platform.isIOS) {
        directory = await getApplicationDocumentsDirectory();
      } else {
        directory = await getDownloadsDirectory() ?? await getApplicationDocumentsDirectory();
      }
      
      final file = File('${directory!.path}/$fileName');
      await file.writeAsBytes(bytes);
      
      return file.path;
    } catch (e) {
      throw Exception('Failed to save Excel file: $e');
    }
  }
  
  // Helper method to get file bytes for web download
  static List<int>? getExcelBytes(List<Trip> trips) {
    final excel = _createExcelFile(trips);
    // Use encode() instead of save() to avoid automatic web download
    return excel.encode();
  }
}


