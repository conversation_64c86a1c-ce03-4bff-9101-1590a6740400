import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../config/app_config.dart';
import '../models/user.dart';
import '../utils/logger.dart';

/// Service for persisting application state across app restarts
class StatePersistenceService {
  // Singleton pattern
  static final StatePersistenceService _instance = StatePersistenceService._internal();
  factory StatePersistenceService() => _instance;
  StatePersistenceService._internal();

  // Logger instance
  static final Logger _logger = Logger('StatePersistenceService');

  // Constants for storage keys
  static String get _userKey => AppConfig.auth.userDataKey;
  static String get _accessTokenKey => AppConfig.auth.accessTokenKey;
  static String get _idTokenKey => AppConfig.auth.idTokenKey;
  static String get _lastLoginTimeKey => AppConfig.auth.lastLoginTimeKey;

  // Secure storage for sensitive data
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage();

  // Flag to track initialization status
  bool _isInitialized = false;

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    // Check if we're running on an emulator
    bool isEmulator = false;
    if (!kIsWeb) {
      try {
        if (Platform.isAndroid) {
          // Check for common emulator properties
          isEmulator = await _isRunningOnEmulator();
          _logger.debug('Running on ${isEmulator ? 'emulator' : 'physical device'}');
        }
      } catch (e) {
        _logger.error('Error checking emulator status', e);
      }
    }

    // Any initialization logic can go here
    _isInitialized = true;
    _logger.info('Service initialized');

    // Print the paths where data will be stored
    _printStoragePaths();
  }

  /// Check if running on an emulator
  Future<bool> _isRunningOnEmulator() async {
    try {
      // This is a simple check that might not be 100% accurate
      // For a more accurate check, you would need to use platform-specific code
      final manufacturer = await _getDeviceProperty('ro.product.manufacturer');
      final model = await _getDeviceProperty('ro.product.model');
      final brand = await _getDeviceProperty('ro.product.brand');

      _logger.debug('Device info:');
      _logger.debug('  Manufacturer: $manufacturer');
      _logger.debug('  Model: $model');
      _logger.debug('  Brand: $brand');

      // Common emulator indicators
      final isEmulator =
        manufacturer?.toLowerCase().contains('genymotion') == true ||
        model?.toLowerCase().contains('sdk') == true ||
        model?.toLowerCase().contains('emulator') == true ||
        brand?.toLowerCase().contains('google') == true && model?.toLowerCase().contains('sdk') == true;

      return isEmulator;
    } catch (e) {
      _logger.error('Error detecting emulator', e);
      return false;
    }
  }

  /// Get a device property using getprop
  Future<String?> _getDeviceProperty(String property) async {
    try {
      final process = await Process.run('getprop', [property]);
      return process.stdout.toString().trim();
    } catch (e) {
      _logger.error('Error getting device property $property', e);
      return null;
    }
  }

  /// Print storage paths for debugging
  void _printStoragePaths() async {
    try {
      if (kIsWeb) {
        _logger.debug('Web platform, no local storage paths');
        return;
      }

      // Get application documents directory
      final appDir = Directory.systemTemp;
      _logger.debug('App directory: ${appDir.path}');

      // Get shared preferences path (approximate)
      final prefs = await SharedPreferences.getInstance();
      _logger.debug('SharedPreferences instance: $prefs');

      // List all files in the app directory
      _logger.debug('Files in app directory:');
      try {
        final files = appDir.listSync();
        for (var file in files) {
          _logger.debug('  ${file.path}');
        }
      } catch (e) {
        _logger.error('Error listing files', e);
      }
    } catch (e) {
      _logger.error('Error printing storage paths', e);
    }
  }

  /// Save user data
  Future<void> saveUser(User user) async {
    try {
      final userData = user.toJson();
      final userDataString = jsonEncode(userData);

      _logger.info('Saving user data for ${user.name}, role: ${user.role}');
      _logger.debug('User data JSON: $userDataString');

      if (kIsWeb) {
        // For web, we don't use shared_preferences
        _logger.debug('Web platform detected, user data not persisted locally');
        return;
      }

      // Use a try-catch block specifically for SharedPreferences operations
      try {
        final prefs = await SharedPreferences.getInstance();

        // Try to clear any existing data first to avoid conflicts
        if (prefs.containsKey(_userKey)) {
          await prefs.remove(_userKey);
          _logger.debug('Cleared existing user data');
        }

        // Save the new data
        final success = await prefs.setString(_userKey, userDataString);
        _logger.debug('Data save operation result: $success');

        // SharedPreferences automatically commits changes, no need to call commit()
        // Let's add a small delay to ensure the write completes
        await Future.delayed(const Duration(milliseconds: 100));
        _logger.debug('Added delay to ensure data is written');

        // Verify the data was saved
        final savedData = prefs.getString(_userKey);
        _logger.debug('Verification - data saved: ${savedData != null}');
        if (savedData == null) {
          _logger.warning('WARNING - Data verification failed, data not saved!');
        } else if (savedData != userDataString) {
          _logger.warning('WARNING - Saved data does not match original data!');
        }

        // Update last login time
        final now = DateTime.now().toIso8601String();
        await prefs.setString(_lastLoginTimeKey, now);
        _logger.debug('Last login time updated to $now');

        // Also save to secure storage as a backup
        await _secureStorage.write(key: 'user_data_backup', value: userDataString);
        _logger.debug('User data also saved to secure storage as backup');

        _logger.info('User data saved to persistent storage');
      } catch (prefsError) {
        _logger.error('SharedPreferences error', prefsError);

        // Fallback to secure storage if SharedPreferences fails
        try {
          await _secureStorage.write(key: 'user_data_backup', value: userDataString);
          _logger.info('Fallback to secure storage successful');
        } catch (secureError) {
          _logger.error('Secure storage fallback also failed', secureError);
          rethrow; // Re-throw to be caught by outer catch
        }
      }
    } catch (e) {
      _logger.error('Error saving user data', e);
    }
  }

  /// Load user data
  Future<User?> loadUser() async {
    try {
      if (kIsWeb) {
        // For web, we don't use shared_preferences
        _logger.debug('Web platform detected, no persisted user data to load');
        return null;
      }

      _logger.debug('Attempting to load user data');

      // Try to load from SharedPreferences first
      String? userDataString;
      try {
        final prefs = await SharedPreferences.getInstance();
        userDataString = prefs.getString(_userKey);

        if (userDataString != null) {
          _logger.debug('Found user data in SharedPreferences');
        } else {
          _logger.debug('No user data found in SharedPreferences');
        }
      } catch (prefsError) {
        _logger.error('Error accessing SharedPreferences', prefsError);
      }

      // If SharedPreferences failed, try to load from secure storage backup
      if (userDataString == null) {
        try {
          _logger.debug('Trying to load from secure storage backup');
          userDataString = await _secureStorage.read(key: 'user_data_backup');

          if (userDataString != null) {
            _logger.debug('Found user data in secure storage backup');
          } else {
            _logger.debug('No user data found in secure storage backup');
          }
        } catch (secureError) {
          _logger.error('Error accessing secure storage', secureError);
        }
      }

      // If we still don't have user data, return null
      if (userDataString == null) {
        _logger.debug('No saved user data found in any storage');
        return null;
      }

      // Print a truncated version of the user data string to avoid flooding the logs
      final previewLength = userDataString.length > 100 ? 100 : userDataString.length;
      _logger.debug('Found user data string: ${userDataString.substring(0, previewLength)}...');

      try {
        final userData = jsonDecode(userDataString) as Map<String, dynamic>;
        final user = User.fromJson(userData);

        _logger.info('User data loaded successfully for ${user.name}, role: ${user.role}');

        // If we loaded from secure storage but not from SharedPreferences, save back to SharedPreferences
        try {
          final prefs = await SharedPreferences.getInstance();
          if (!prefs.containsKey(_userKey)) {
            await prefs.setString(_userKey, userDataString);
            _logger.debug('Restored SharedPreferences data from secure storage backup');
          }
        } catch (e) {
          _logger.error('Failed to restore SharedPreferences data', e);
        }

        return user;
      } catch (parseError) {
        _logger.error('Error parsing user data', parseError);

        // If there's an error parsing the data, clear it to prevent future errors
        try {
          final prefs = await SharedPreferences.getInstance();
          await prefs.remove(_userKey);
          await _secureStorage.delete(key: 'user_data_backup');
          _logger.debug('Cleared invalid user data from all storage');
        } catch (clearError) {
          _logger.error('Error clearing invalid data', clearError);
        }

        return null;
      }
    } catch (e) {
      _logger.error('Error loading user data', e);
      return null;
    }
  }

  /// Save tokens
  Future<void> saveTokens(String accessToken, String idToken) async {
    try {
      _logger.debug('Saving tokens');

      if (kIsWeb) {
        // For web, we don't use secure storage
        _logger.debug('Web platform detected, tokens not persisted locally');
        return;
      }

      await _secureStorage.write(key: _accessTokenKey, value: accessToken);
      _logger.debug('Access token saved');

      await _secureStorage.write(key: _idTokenKey, value: idToken);
      _logger.debug('ID token saved');

      // Verify tokens were saved
      final savedAccessToken = await _secureStorage.read(key: _accessTokenKey);
      _logger.debug('Verification - access token saved: ${savedAccessToken != null}');

      _logger.info('Tokens saved to secure storage');
    } catch (e) {
      _logger.error('Error saving tokens', e);
    }
  }

  /// Load access token
  Future<String?> loadAccessToken() async {
    try {
      if (kIsWeb) {
        // For web, we don't use secure storage
        _logger.debug('Web platform detected, no persisted tokens to load');
        return null;
      }

      _logger.debug('Attempting to load access token');
      final token = await _secureStorage.read(key: _accessTokenKey);

      if (token == null) {
        _logger.debug('No access token found');
      } else {
        _logger.debug('Access token found');
      }

      return token;
    } catch (e) {
      _logger.error('Error loading access token', e);
      return null;
    }
  }

  /// Load ID token
  Future<String?> loadIdToken() async {
    try {
      if (kIsWeb) {
        // For web, we don't use secure storage
        _logger.debug('Web platform detected, no persisted tokens to load');
        return null;
      }

      final token = await _secureStorage.read(key: _idTokenKey);
      return token;
    } catch (e) {
      _logger.error('Error loading ID token', e);
      return null;
    }
  }

  /// Clear all persisted data
  Future<void> clearAll() async {
    try {
      _logger.info('Clearing all persisted data');

      if (kIsWeb) {
        // For web, we don't use shared_preferences or secure storage
        _logger.debug('Web platform detected, no persisted data to clear');
        return;
      }

      // Clear shared preferences - first try specific keys
      final prefs = await SharedPreferences.getInstance();

      // Check if data exists before clearing
      final hasUserData = prefs.containsKey(_userKey);
      _logger.debug('User data exists before clearing: $hasUserData');

      // Remove specific keys
      await prefs.remove(_userKey);
      _logger.debug('User data removed');

      await prefs.remove(_lastLoginTimeKey);
      _logger.debug('Last login time removed');

      // Also remove backup user data
      await prefs.remove('user_data_backup');
      _logger.debug('User data backup removed');

      // Clear ALL shared preferences as a fallback
      await prefs.clear();
      _logger.debug('All SharedPreferences cleared');

      // Clear secure storage - specific keys
      final hasAccessToken = (await _secureStorage.read(key: _accessTokenKey)) != null;
      _logger.debug('Access token exists before clearing: $hasAccessToken');

      await _secureStorage.delete(key: _accessTokenKey);
      _logger.debug('Access token deleted');

      await _secureStorage.delete(key: _idTokenKey);
      _logger.debug('ID token deleted');

      // Also clear backup user data in secure storage
      await _secureStorage.delete(key: 'user_data_backup');
      _logger.debug('User data backup deleted from secure storage');

      // Clear ALL secure storage as a fallback
      try {
        await _secureStorage.deleteAll();
        _logger.debug('All secure storage deleted');
      } catch (e) {
        _logger.error('Error deleting all secure storage', e);
        // Continue with verification even if deleteAll fails
      }

      // Verify data was cleared
      final userDataAfter = prefs.containsKey(_userKey);
      final accessTokenAfter = (await _secureStorage.read(key: _accessTokenKey)) != null;

      _logger.debug('Verification - User data exists after clearing: $userDataAfter');
      _logger.debug('Verification - Access token exists after clearing: $accessTokenAfter');

      if (userDataAfter || accessTokenAfter) {
        _logger.warning('WARNING - Some data still exists after clearing!');

        // Try one more time with a delay
        await Future.delayed(const Duration(milliseconds: 500));

        if (userDataAfter) {
          await prefs.remove(_userKey);
          _logger.debug('Second attempt to remove user data');
        }

        if (accessTokenAfter) {
          await _secureStorage.delete(key: _accessTokenKey);
          _logger.debug('Second attempt to remove access token');
        }
      }

      _logger.info('All persisted data cleared');
    } catch (e) {
      _logger.error('Error clearing persisted data', e);
    }
  }

  /// Check if user data exists
  Future<bool> hasUserData() async {
    try {
      if (kIsWeb) {
        // For web, we don't use shared_preferences
        _logger.debug('Web platform detected, returning false');
        return false;
      }

      final prefs = await SharedPreferences.getInstance();
      final hasData = prefs.containsKey(_userKey);
      _logger.debug('User data exists: $hasData');

      // If we have user data, also check if we have tokens
      if (hasData) {
        final accessToken = await _secureStorage.read(key: _accessTokenKey);
        _logger.debug('Access token exists: ${accessToken != null}');
      }

      return hasData;
    } catch (e) {
      _logger.error('Error checking for user data', e);
      return false;
    }
  }

  /// Get last login time
  Future<DateTime?> getLastLoginTime() async {
    try {
      if (kIsWeb) {
        // For web, we don't use shared_preferences
        _logger.debug('Web platform detected, no persisted login time');
        return null;
      }

      final prefs = await SharedPreferences.getInstance();
      final timeString = prefs.getString(_lastLoginTimeKey);

      if (timeString == null) {
        _logger.debug('No last login time found');
        return null;
      }

      _logger.debug('Last login time found: $timeString');
      return DateTime.parse(timeString);
    } catch (e) {
      _logger.error('Error getting last login time', e);
      return null;
    }
  }
}


