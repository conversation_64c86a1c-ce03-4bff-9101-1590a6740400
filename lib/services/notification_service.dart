import 'dart:convert';

import 'package:FLEEX/models/notification.dart' as app_notification;
import 'package:FLEEX/services/api_service.dart';
import 'package:FLEEX/services/firebase_notification_service.dart';
import 'package:flutter/foundation.dart';

class NotificationService with ChangeNotifier {
  // Debug flag to control logging
  static const bool debugMode = false;

  List<app_notification.Notification> _notifications = [];
  int _unreadCount = 0;
  bool _isLoading = false;
  String _error = '';

  // Firebase notification service instance - only initialize on non-web platforms
  FirebaseNotificationService? _firebaseNotificationService;

  List<app_notification.Notification> get notifications => _notifications;
  int get unreadCount => _unreadCount;
  bool get isLoading => _isLoading;
  String get error => _error;  /// Initialize the notification service with Firebase integration
  Future<void> initialize(dynamic userId) async {
    // Initialize Firebase notifications if not on web
    if (!kIsWeb) {
      try {
        // Get the singleton Firebase service instance (should already be initialized in main.dart)
        _firebaseNotificationService ??= FirebaseNotificationService();
        
        // Only initialize if it's not already initialized (this should rarely happen)
        if (!(_firebaseNotificationService?.isInitialized ?? false)) {
          print('⚠️ Firebase notification service not initialized in main.dart, initializing now...');
          await _firebaseNotificationService?.initialize();
        }
        
        // Update FCM token for the current user
        if (userId != null) {
          await _firebaseNotificationService?.updateTokenForUser(userId);
        }
      } catch (e) {
        print('Error initializing Firebase notification service: $e');
      }
    }
  }
  // Fetch notifications for a user
  Future<void> fetchNotifications(dynamic userId, {bool includeRead = false}) async {
    try {
      _isLoading = true;
      notifyListeners();

      // Initialize Firebase notifications if not already done
      await initialize(userId);

      // Ensure userId is a string for the query parameter
      final userIdStr = userId.toString();

      // Convert boolean to string 'true'/'false' for query parameter
      final includeReadStr = includeRead.toString();
      final response = await ApiService.get(
        'notifications?user_id=$userIdStr&include_read=$includeReadStr',
      );

      // Log the response for debugging if debug mode is enabled
      if (debugMode) {
        print('Fetching notifications with user_id: $userIdStr');
        print('Notification API response: ${response.statusCode}');
        print('Response body: ${response.body}');
      }

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        // Debug: Print the raw notifications data
        if (debugMode) {
          print('Raw notifications data: $data');
          print('Notifications count in response: ${(data['notifications'] as List).length}');
        }

        try {
          // Debug: Print each notification JSON before parsing
          if (debugMode) {
            print('Parsing notifications:');
            for (var notif in data['notifications'] as List) {
              print('  Notification: $notif');
            }
          }

          _notifications = (data['notifications'] as List)
              .map((json) => app_notification.Notification.fromJson(json as Map<String, dynamic>))
              .toList();

          if (debugMode) {
            print('Successfully parsed ${_notifications.length} notifications');
          }
        } catch (e) {
          if (debugMode) {
            print('Error parsing notifications list: $e');
          }
          _notifications = [];
          _error = 'Error parsing notifications: $e';
        }

        // Also update the unread count
        await fetchUnreadCount(userId);

        _error = '';
      } else {
        final error = jsonDecode(response.body);
        _error = error['error'] ?? 'Failed to fetch notifications';
      }
    } catch (e) {
      _error = 'Error: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Fetch unread notification count
  Future<void> fetchUnreadCount(dynamic userId) async {
    try {
      // Ensure userId is a string for the query parameter
      final userIdStr = userId.toString();

      if (debugMode) {
        print('Fetching unread count for user ID: $userIdStr (original type: ${userId.runtimeType})');
      }

      final response = await ApiService.get(
        'notifications/count?user_id=$userIdStr',
      );

      if (debugMode) {
        print('Unread count API response: ${response.statusCode}');
        print('Unread count response body: ${response.body}');
      }

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        _unreadCount = data['count'];

        if (debugMode) {
          print('Updated unread count: $_unreadCount');
        }

        notifyListeners();
      }
    } catch (e) {
      if (debugMode) {
        print('Error fetching unread count: $e');
      }
    }
  }

  // Mark a notification as read
  Future<bool> markAsRead(int notificationId) async {
    try {
      final response = await ApiService.put(
        'notifications/$notificationId/read',
        null,
      );

      if (response.statusCode == 200) {
        // Update the local notification
        final index = _notifications.indexWhere((n) => n.notificationId == notificationId);
        if (index != -1) {
          final updatedNotification = app_notification.Notification(
            notificationId: _notifications[index].notificationId,
            userId: _notifications[index].userId,
            title: _notifications[index].title,
            message: _notifications[index].message,
            isRead: true,
            createdAt: _notifications[index].createdAt,
          );

          _notifications[index] = updatedNotification;
          _unreadCount = _unreadCount > 0 ? _unreadCount - 1 : 0;
          notifyListeners();
        }
        return true;
      }
      return false;
    } catch (e) {
      if (debugMode) {
        print('Error marking notification as read: $e');
      }
      return false;
    }
  }

  // Mark all notifications as read
  Future<bool> markAllAsRead(dynamic userId) async {
    try {
      // Ensure userId is converted to string for logging
      final userIdStr = userId.toString();
      if (debugMode) {
        print('Marking all notifications as read for user ID: $userIdStr (original type: ${userId.runtimeType})');
      }

      final response = await ApiService.put(
        'notifications/read-all',
        {'user_id': userId},
      );

      if (response.statusCode == 200) {
        // Update all local notifications
        _notifications = _notifications.map((notification) {
          return app_notification.Notification(
            notificationId: notification.notificationId,
            userId: notification.userId,
            title: notification.title,
            message: notification.message,
            isRead: true,
            createdAt: notification.createdAt,
          );
        }).toList();

        _unreadCount = 0;
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      if (debugMode) {
        print('Error marking all notifications as read: $e');
      }
      return false;
    }
  }

  // Delete a notification
  Future<bool> deleteNotification(int notificationId) async {
    try {
      final response = await ApiService.delete(
        'notifications/$notificationId',
      );

      if (response.statusCode == 200) {
        // Find the notification index
        final index = _notifications.indexWhere((n) => n.notificationId == notificationId);

        if (index != -1) {
          // Check if it was unread before removing
          final wasUnread = !_notifications[index].isRead;

          // Remove the notification
          _notifications.removeAt(index);

          // Update unread count if the notification was unread
          if (wasUnread) {
            _unreadCount = _unreadCount > 0 ? _unreadCount - 1 : 0;
          }

          notifyListeners();
        }
        return true;
      }
      return false;
    } catch (e) {
      if (debugMode) {
        print('Error deleting notification: $e');
      }
      return false;
    }
  }

  // Test notification functionality removed
}


