import 'dart:async';
import 'dart:io';
import 'dart:ui';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:geolocator/geolocator.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../services/api_service.dart';

@pragma('vm:entry-point')
class BackgroundLocationService {
  // Singleton pattern
  static final BackgroundLocationService _instance = BackgroundLocationService._internal();
  factory BackgroundLocationService() => _instance;
  @pragma('vm:entry-point')
  BackgroundLocationService._internal();

  // Service instance
  final FlutterBackgroundService _service = FlutterBackgroundService();

  // Notification channel ID
  static const String notificationChannelId = 'fleex_location_tracking';
  static const String notificationId = 'fleex_location_tracking_service';

  // Shared preferences keys
  static const String prefKeyUserId = 'background_tracking_user_id';
  static const String prefKeyDriverId = 'background_tracking_driver_id';
  static const String prefKeyIsEnabled = 'background_tracking_enabled';
  static const String prefKeyLastUpdateTime = 'background_tracking_last_update';

  // Initialize the background service
  Future<bool> initialize() async {
    // Configure the service
    await _service.configure(
      androidConfiguration: AndroidConfiguration(
        onStart: _onStart,
        autoStart: false,
        isForegroundMode: true,
        notificationChannelId: notificationChannelId,
        initialNotificationTitle: 'FLEEX Driver Tracking',
        initialNotificationContent: 'Initializing...',
        foregroundServiceNotificationId: 888,
      ),
      iosConfiguration: IosConfiguration(
        autoStart: false,
        onForeground: _onStart,
        onBackground: _onIosBackground,
      ),
    );

    return true;
  }

  // Check if the service is running
  Future<bool> isRunning() async {
    return await _service.isRunning();
  }

  // Start the background service
  Future<bool> startService({
    required int userId,
    int? driverId,
  }) async {
    try {
      // Store user and driver IDs in shared preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(prefKeyUserId, userId);
      if (driverId != null) {
        await prefs.setInt(prefKeyDriverId, driverId);
      }
      await prefs.setBool(prefKeyIsEnabled, true);

      // Start the service
      await _service.startService();

      // Send initial data to the service
      _service.invoke('updateData', {
        'userId': userId,
        'driverId': driverId,
        'isEnabled': true,
      });

      return true;
    } catch (e) {
      print('Error starting background service: $e');
      return false;
    }
  }

  // Stop the background service
  Future<bool> stopService() async {
    try {
      print('Stopping background location service');

      // Update shared preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(prefKeyIsEnabled, false);

      // Send stop command to service which will call stopSelf() internally
      _service.invoke('stop');

      // Wait a moment to allow the service to process the stop command
      await Future.delayed(const Duration(milliseconds: 500));

      // Double-check if service is still running and force stop if needed
      if (await _service.isRunning()) {
        print('Service still running after stop command, forcing stop');
        _service.invoke('stop');

        // Wait a bit longer and check again
        await Future.delayed(const Duration(seconds: 1));

        // If still running, try to use the internal method to stop
        if (await _service.isRunning()) {
          print('Service still running, attempting to force close');
        }
      }

      print('Background location service stopped successfully');
      return true;
    } catch (e) {
      print('Error stopping background service: $e');
      return false;
    }
  }

  // Force a location update
  Future<void> forceLocationUpdate() async {
    _service.invoke('forceLocationUpdate');
  }



  // Background service entry point for iOS
  @pragma('vm:entry-point')
  static Future<bool> _onIosBackground(ServiceInstance service) async {
    WidgetsFlutterBinding.ensureInitialized();
    DartPluginRegistrant.ensureInitialized();

    // Get data from shared preferences
    final prefs = await SharedPreferences.getInstance();
    final isEnabled = prefs.getBool(prefKeyIsEnabled) ?? false;

    // If tracking is disabled, stop the service
    if (!isEnabled) {
      print('iOS background service: isEnabled is false, stopping service');
      return false;
    }

    // Set up event handlers for iOS background service
    service.on('stop').listen((event) {
      print('iOS background service received stop command');
      // Update shared preferences to indicate service should stop
      prefs.setBool(prefKeyIsEnabled, false);
      // Return false from _onIosBackground on next call to stop the service
    });

    // Force a location update if enabled
    if (isEnabled) {
      final userId = prefs.getInt(prefKeyUserId);
      final driverId = prefs.getInt(prefKeyDriverId);

      if (userId != null) {
        try {
          await _getCurrentLocationAndSend(userId, driverId);

          // Update last update time
          await prefs.setString(prefKeyLastUpdateTime, DateTime.now().toIso8601String());
        } catch (e) {
          print('Error updating location in iOS background: $e');
        }
      }
    }

    return true;
  }

  // Main background service entry point
  @pragma('vm:entry-point')
  static void _onStart(ServiceInstance service) async {
    WidgetsFlutterBinding.ensureInitialized();
    DartPluginRegistrant.ensureInitialized();

    // For Android, we need to set the service as a foreground service
    if (service is AndroidServiceInstance) {
      // Set as foreground service with notification
      service.setForegroundNotificationInfo(
        title: 'FLEEX Driver Tracking',
        content: 'Your location is being tracked even when app is closed',
      );
    }

    // Get data from shared preferences
    final prefs = await SharedPreferences.getInstance();
    int? userId = prefs.getInt(prefKeyUserId);
    int? driverId = prefs.getInt(prefKeyDriverId);
    bool isEnabled = prefs.getBool(prefKeyIsEnabled) ?? false;

    // Initialize location tracking variables
    DateTime? lastUpdateTime;
    Timer? locationUpdateTimer;
    bool isTracking = false;

    // Load last update time if available
    final lastUpdateTimeStr = prefs.getString(prefKeyLastUpdateTime);
    if (lastUpdateTimeStr != null) {
      lastUpdateTime = DateTime.parse(lastUpdateTimeStr);
    }

    // Set up a static notification that won't change with every update
    final deviceInfo = await _getDeviceInfo();
    if (service is AndroidServiceInstance) {
      service.setForegroundNotificationInfo(
        title: 'FLEEX Driver Tracking',
        content: 'Tracking active - updates every 5 min or 500m (${deviceInfo['model']})',
      );
    }

    // Handle service events
    service.on('updateData').listen((event) {
      if (event != null) {
        // Update tracking data
        userId = event['userId'];
        driverId = event['driverId'];
        isEnabled = event['isEnabled'] ?? isEnabled;

        // Store updated values
        prefs.setInt(prefKeyUserId, userId!);
        if (driverId != null) {
          prefs.setInt(prefKeyDriverId, driverId!);
        }
        prefs.setBool(prefKeyIsEnabled, isEnabled);

        // Update tracking state
        if (isEnabled && !isTracking) {
          _startLocationTracking(service, locationUpdateTimer, userId!, driverId);
        } else if (!isEnabled && isTracking) {
          _stopLocationTracking(locationUpdateTimer);
          isTracking = false;
        }
      }
    });

    // Handle stop tracking command
    service.on('stopTracking').listen((event) {
      _stopLocationTracking(locationUpdateTimer);
      isTracking = false;

      // Update notification
      if (service is AndroidServiceInstance) {
        service.setForegroundNotificationInfo(
          title: 'FLEEX Driver Tracking',
          content: 'Tracking stopped',
        );
      }
    });

    // Handle complete service stop command
    service.on('stop').listen((event) {
      print('Background service received stop command');

      // Stop tracking if active
      _stopLocationTracking(locationUpdateTimer);
      isTracking = false;

      // Update shared preferences
      prefs.setBool(prefKeyIsEnabled, false);

      // Update notification before stopping
      if (service is AndroidServiceInstance) {
        service.setForegroundNotificationInfo(
          title: 'FLEEX Driver Tracking',
          content: 'Service stopping...',
        );

        // Stop the service completely
        print('Stopping background service');
        service.stopSelf();
      }
    });

    // Handle force location update command
    service.on('forceLocationUpdate').listen((event) async {
      if (isEnabled && userId != null) {
        await _getCurrentLocationAndSend(userId!, driverId);

        // Update last update time without updating notification
        lastUpdateTime = DateTime.now();
        await prefs.setString(prefKeyLastUpdateTime, lastUpdateTime!.toIso8601String());
      }
    });

    // Start location tracking if enabled
    if (isEnabled && userId != null) {
      _startLocationTracking(service, locationUpdateTimer, userId!, driverId);
      isTracking = true;
    }

    // Periodic service check (every 15 minutes)
    Timer.periodic(const Duration(minutes: 15), (timer) async {
      // Check if service should still be running
      final isStillEnabled = prefs.getBool(prefKeyIsEnabled) ?? false;
      if (!isStillEnabled) {
        print('Periodic check found service should be stopped');
        timer.cancel(); // Cancel this timer

        // Stop tracking
        _stopLocationTracking(locationUpdateTimer);
        isTracking = false;

        // Stop the service
        if (service is AndroidServiceInstance) {
          service.setForegroundNotificationInfo(
            title: 'FLEEX Driver Tracking',
            content: 'Service stopping due to logout',
          );

          print('Stopping background service from periodic check');
          service.stopSelf();
        }
        return;
      }

      // Update tracking state based on enabled status
      if (isEnabled && !isTracking && userId != null) {
        _startLocationTracking(service, locationUpdateTimer, userId!, driverId);
        isTracking = true;
      } else if (isTracking && !isEnabled) {
        _stopLocationTracking(locationUpdateTimer);
        isTracking = false;
      }

      // Update notification with status (only status, not time)
      if (service is AndroidServiceInstance) {
        final statusText = isTracking ? 'Active' : (isEnabled ? 'Standby' : 'Disabled');

        service.setForegroundNotificationInfo(
          title: 'FLEEX Driver Tracking ($statusText)',
          content: 'Tracking active - updates every 5 min or 500m',
        );
      }

      // Keep the service alive
      service.invoke('keepAlive');
    });
  }

  // Start location tracking
  static void _startLocationTracking(
    ServiceInstance service,
    Timer? timer,
    int userId,
    int? driverId
  ) async {
    // Cancel existing timer if any
    timer?.cancel();

    // Check location permissions
    final permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      // We can't request permission in the background, so just log the error
      print('Location permission denied');
      return;
    }

    // Start periodic location updates
    timer = Timer.periodic(const Duration(minutes: 5), (timer) async {
      // Update location every 5 minutes
      await _getCurrentLocationAndSend(userId, driverId);

      // Store last update time without updating notification
      final now = DateTime.now();
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(prefKeyLastUpdateTime, now.toIso8601String());
    });

    // Force an immediate update
    await _getCurrentLocationAndSend(userId, driverId);
  }

  // Stop location tracking
  static void _stopLocationTracking(Timer? timer) {
    timer?.cancel();
  }

  // Get current location and send to server
  static Future<bool> _getCurrentLocationAndSend(int userId, int? driverId) async {
    try {
      // Check if location services are enabled
      final bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        print('Location services are disabled');
        return false;
      }

      // Get current position with high accuracy
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      // Format location data
      final String latLngStr = '${position.latitude},${position.longitude}';

      // Send location to server
      final response = await ApiService.post(
        'driver-locations',
        {
          'user_id': userId,
          'trip_id': null, // We don't track specific trips in background mode
          'latitude_longitude': latLngStr,
        },
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        print('Location sent successfully: $latLngStr');
        return true;
      } else {
        print('Failed to send location: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      print('Error getting or sending location: $e');
      return false;
    }
  }



  // Get device info for notification
  static Future<Map<String, String>> _getDeviceInfo() async {
    final deviceInfo = <String, String>{};
    final deviceInfoPlugin = DeviceInfoPlugin();

    try {
      if (Platform.isAndroid) {
        final androidInfo = await deviceInfoPlugin.androidInfo;
        deviceInfo['model'] = androidInfo.model;
        deviceInfo['manufacturer'] = androidInfo.manufacturer;
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfoPlugin.iosInfo;
        deviceInfo['model'] = iosInfo.model;
        deviceInfo['name'] = iosInfo.name;
      }
    } catch (e) {
      print('Error getting device info: $e');
      deviceInfo['model'] = 'Unknown device';
    }

    return deviceInfo;
  }
}


