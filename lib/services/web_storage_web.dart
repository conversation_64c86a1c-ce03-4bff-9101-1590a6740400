import 'dart:convert';
import 'dart:js' as js;

class AuthDataStorage {
  static bool hasAuthData() {
    return js.context.callMethod('eval', ['localStorage.getItem("auth_data") != null']);
  }

  static Map<String, dynamic>? getAuthData() {
    final authDataStr = js.context.callMethod('eval', ['localStorage.getItem("auth_data")']);
    if (authDataStr != null) {
      return jsonDecode(authDataStr);
    }
    return null;
  }

  static void clearAuthData() {
    js.context.callMethod('eval', ['localStorage.removeItem("auth_data")']);
  }

  // New methods for auth token
  static bool hasAuthToken() {
    try {
      // Simplified approach to check for auth token
      final result = js.context.callMethod('eval', [
        '''
        (function() {
          try {
            // Check localStorage
            var localToken = localStorage.getItem("auth_token");
            if (localToken && localToken !== "null" && localToken !== "") {
              console.log("Found token in localStorage");
              return true;
            }

            // Check sessionStorage
            var sessionToken = sessionStorage.getItem("auth_token");
            if (sessionToken && sessionToken !== "null" && sessionToken !== "") {
              console.log("Found token in sessionStorage");
              return true;
            }

            // Check if this is a login redirect from SSO
            var isLoginRedirect = localStorage.getItem("login_redirect") === "true";
            if (isLoginRedirect) {
              console.log("Detected login redirect from SSO");

              // Clear the login redirect flag
              localStorage.removeItem("login_redirect");

              // Get the user role from localStorage
              var role = localStorage.getItem("user_role");
              var targetRoute = window.location.pathname;

              console.log("User role from localStorage: " + role);

              // Convert role to lowercase for case-insensitive comparison
              var roleLower = role ? role.toLowerCase() : '';
              console.log("Role lowercase: '" + roleLower + "'");

              // Determine the correct dashboard based on role
              if (role === 'Trip Manager' || roleLower === 'trip manager' ||
                  roleLower.indexOf('trip') !== -1 && roleLower.indexOf('manager') !== -1 ||
                  roleLower === 'super admin') {
                console.log("Detected Trip Manager or Super Admin role");
                targetRoute = window.location.origin + window.location.pathname + '#/trip-manager-dashboard';
              } else if (roleLower === 'driver') {
                console.log("Detected Driver role");
                targetRoute = window.location.origin + window.location.pathname + '#/driver-trips';
              } else {
                // Default to requestor dashboard
                console.log("Defaulting to Requestor role");
                targetRoute = window.location.origin + window.location.pathname + '#/requestor-dashboard';
              }

              console.log("Redirecting to dashboard: " + targetRoute);
              window.location.href = targetRoute;
              return true;
            }

            // Check URL fragment (for backward compatibility)
            var hash = window.location.hash;
            if (hash && hash.indexOf("auth_token=") !== -1) {
              console.log("Found token in URL fragment");
              // Extract the token
              var tokenStart = hash.indexOf("auth_token=") + "auth_token=".length;
              var tokenEnd = hash.indexOf("&", tokenStart);
              if (tokenEnd === -1) tokenEnd = hash.length;
              var token = hash.substring(tokenStart, tokenEnd);

              // Store it in localStorage for future use
              localStorage.setItem("auth_token", token);

              // Clean the URL by removing the token
              history.replaceState(null, null, window.location.pathname + window.location.search);

              return true;
            }

            // Check cookies
            var cookies = document.cookie.split(';');
            for (var i = 0; i < cookies.length; i++) {
              var cookie = cookies[i].trim();
              if (cookie.indexOf('auth_token=') === 0) {
                var cookieToken = cookie.substring('auth_token='.length, cookie.length);
                if (cookieToken && cookieToken !== "null" && cookieToken !== "") {
                  console.log("Found token in cookie");
                  // Store it in localStorage for future use
                  localStorage.setItem("auth_token", cookieToken);
                  return true;
                }
              }
            }

            console.log("No auth token found in any storage location");
            return false;
          } catch(e) {
            console.error("Error in hasAuthToken:", e);
            return false;
          }
        })()
        '''
      ]);
      print('hasAuthToken check result: $result');
      return result == true;
    } catch (e) {
      print('Error checking for auth token: $e');
      return false;
    }
  }

  static String? getAuthToken() {
    try {
      // Simplified approach to get the auth token
      final token = js.context.callMethod('eval', [
        '''
        (function() {
          try {
            // Check localStorage first
            var localToken = localStorage.getItem("auth_token");
            console.log("Raw token from localStorage:", localToken);
            if (localToken && localToken !== "null" && localToken !== "") {
              return localToken;
            }

            // Check sessionStorage
            var sessionToken = sessionStorage.getItem("auth_token");
            console.log("Raw token from sessionStorage:", sessionToken);
            if (sessionToken && sessionToken !== "null" && sessionToken !== "") {
              // Store in localStorage for future use
              localStorage.setItem("auth_token", sessionToken);
              return sessionToken;
            }

            // Check if this is a login redirect from SSO
            var isLoginRedirect = localStorage.getItem("login_redirect") === "true";
            if (isLoginRedirect) {
              console.log("Detected login redirect in getAuthToken");
              // The token should already be in localStorage from the redirect page
              var token = localStorage.getItem("auth_token");
              if (token) {
                console.log("Found token from SSO redirect");
                return token;
              }
            }

            // Check URL fragment (for backward compatibility)
            var hash = window.location.hash;
            if (hash && hash.indexOf("auth_token=") !== -1) {
              // Extract the token
              var tokenStart = hash.indexOf("auth_token=") + "auth_token=".length;
              var tokenEnd = hash.indexOf("&", tokenStart);
              if (tokenEnd === -1) tokenEnd = hash.length;
              var token = hash.substring(tokenStart, tokenEnd);

              console.log("Raw token from URL fragment:", token);
              // Store it in localStorage for future use
              localStorage.setItem("auth_token", token);

              // Clean the URL by removing the token
              history.replaceState(null, null, window.location.pathname + window.location.search);
              return token;
            }

            // Check cookies
            var cookies = document.cookie.split(';');
            for (var i = 0; i < cookies.length; i++) {
              var cookie = cookies[i].trim();
              if (cookie.indexOf('auth_token=') === 0) {
                var cookieToken = cookie.substring('auth_token='.length, cookie.length);
                console.log("Raw token from cookie:", cookieToken);
                if (cookieToken && cookieToken !== "null" && cookieToken !== "") {
                  // Store it in localStorage for future use
                  localStorage.setItem("auth_token", cookieToken);
                  return cookieToken;
                }
              }
            }

            console.log("No auth token found in any storage location");
            return null;
          } catch(e) {
            console.error("Error in getAuthToken:", e);
            return null;
          }
        })()
        '''
      ]);

      print('Retrieved auth token: ${token != null ? 'token exists' : 'token is null'}');
      if (token == null) {
        return null;
      }
      return token.toString();
    } catch (e) {
      print('Error getting auth token: $e');
      return null;
    }
  }

  static void clearAuthToken() {
    try {
      js.context.callMethod('eval', [
        '''
        (function() {
          try {
            // Clear from localStorage
            localStorage.removeItem("auth_token");
            console.log("Auth token cleared from localStorage");

            // Clear from sessionStorage
            sessionStorage.removeItem("auth_token");
            console.log("Auth token cleared from sessionStorage");

            // Clear from cookies
            document.cookie = "auth_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
            console.log("Auth token cleared from cookies");

            // Clear from URL fragment if present
            if (window.location.hash.indexOf("auth_token=") !== -1) {
              history.replaceState(null, null, window.location.pathname + window.location.search);
              console.log("Auth token cleared from URL fragment");
            }
          } catch(e) {
            console.error("Error clearing auth token:", e);
          }
        })()
        '''
      ]);
      print('Auth token cleared from all storage locations');
    } catch (e) {
      print('Error clearing auth token: $e');
    }
  }

  // Store access token in localStorage for web
  static void storeAccessToken(String token) {
    try {
      js.context.callMethod('eval', [
        '''
        (function() {
          try {
            localStorage.setItem("access_token", "$token");
            console.log("Access token stored in localStorage");
            return true;
          } catch (e) {
            console.error("Error storing access token: " + e);
            return false;
          }
        })();
        '''
      ]);
    } catch (e) {
      print('Error storing access token: $e');
    }
  }

  // Get access token from localStorage for web
  static String? getAccessToken() {
    try {
      final result = js.context.callMethod('eval', [
        '''
        (function() {
          try {
            var token = localStorage.getItem("access_token");
            console.log("Access token retrieved from localStorage: " + (token ? "exists" : "null"));
            return token;
          } catch (e) {
            console.error("Error getting access token: " + e);
            return null;
          }
        })();
        '''
      ]);

      return result?.toString();
    } catch (e) {
      print('Error getting access token: $e');
      return null;
    }
  }
}


