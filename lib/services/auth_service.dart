import 'dart:convert';

import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';

import '../config/app_config.dart';
import '../models/user.dart';
import '../utils/logger.dart';
import 'api_service.dart';
import 'state_persistence_service.dart';
import 'web_storage.dart'
    if (dart.library.html) 'web_storage_web.dart';

/// HTTP request exception
class HttpRequestException implements Exception {
  final String message;
  final int? statusCode;
  final String? body;
  final Object? originalException;

  HttpRequestException(
    this.message, {
    this.statusCode,
    this.body,
    this.originalException,
  });

  @override
  String toString() {
    return 'HttpRequestException: $message${statusCode != null ? ' (Status code: $statusCode)' : ''}';
  }
}

/// Authentication service for handling user login, logout, and token management
class AuthService {
  static final Logger _logger = Logger('AuthService');
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage();
  static final StatePersistenceService _persistenceService = StatePersistenceService();

  /// Get the base URL for API requests
  static String get baseUrl => AppConfig.api.baseUrl;

  /// Traditional login with username and password
  static Future<User> login(String username, String password) async {
    if (username.isEmpty || password.isEmpty) {
      throw Exception('Username and password are required');
    }

    try {
      _logger.info('Attempting login for user: $username');

      // Connect to the server using ApiService
      final response = await ApiService.post(
        'login',
        {
          'username': username,
          'password': password,
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final user = User.fromJson(data['user']);

        _logger.info('Login successful for ${user.name}, role: ${user.role}');

        // For mobile, save user data and tokens for persistence
        if (!kIsWeb) {
          _logger.debug('Saving user data for persistence');
          await _persistenceService.saveUser(user);

          // Generate a dummy token for testing if not provided by the server
          // In a real app, you would use the actual tokens from the server
          final accessToken = data['access_token'] ?? 'dummy_access_token_${DateTime.now().millisecondsSinceEpoch}';
          final idToken = data['id_token'] ?? accessToken;

          // Save tokens to both secure storage and persistence service
          await saveTokens(accessToken, idToken);

          _logger.debug('User data and tokens saved for persistence');
        }

        return user;
      } else {
        final error = jsonDecode(response.body);
        final errorMessage = error['error'] ?? 'Failed to login';
        _logger.error('Login failed: $errorMessage');
        throw HttpRequestException(
          errorMessage,
          statusCode: response.statusCode,
          body: response.body,
        );
      }
    } catch (e) {
      _logger.error('Error during login', e);
      if (e is HttpRequestException) {
        rethrow;
      } else {
        throw HttpRequestException(
          'Connection error: Unable to connect to server. Please check your network connection and try again.',
          originalException: e,
        );
      }
    }
  }

  /// Microsoft Entra ID (Azure AD) login
  static Future<User> loginWithMicrosoftEntra() async {
    try {
      _logger.info('Starting Microsoft Entra login process');

      // Microsoft Entra login should work on both web and mobile
      if (!kIsWeb) {
        _logger.debug('Starting Microsoft Entra login process on mobile');
      }

      // First check if we have auth token in localStorage (from our secure redirect)
      if (kIsWeb && AuthDataStorage.hasAuthToken()) {
        _logger.debug('Found auth token in localStorage, attempting to exchange it');
        final token = AuthDataStorage.getAuthToken()!;

        try {
          // Exchange the token for auth data
          final authData = await exchangeToken(token);
          final user = User.fromJson(authData['user']);

          // Save tokens to secure storage
          await _secureStorage.write(key: AppConfig.auth.accessTokenKey, value: authData['access_token']);
          await _secureStorage.write(key: AppConfig.auth.idTokenKey, value: authData['id_token']);

          // Clear the auth token from localStorage
          AuthDataStorage.clearAuthToken();
          _logger.info('Successfully authenticated with token from localStorage');
          return user;
        } catch (e) {
          _logger.error('Error exchanging token from localStorage', e);
          // Continue with the login flow
        }
      }

      // Get the current URL to pass to the server
      String currentUrl = '';
      if (kIsWeb) {
        currentUrl = Uri.base.toString().split('?')[0]; // Remove any query parameters
        _logger.debug('Current URL: $currentUrl');
      }

      // Get the login URL from the server, passing the current URL as frontendUrl
      // For mobile, add the platform parameter
      _logger.debug('Requesting login URL from server');
      final urlParams = kIsWeb
          ? 'frontendUrl=$currentUrl'
          : 'frontendUrl=$baseUrl&platform=mobile';

      final urlResponse = await ApiService.get(
        'auth/login?$urlParams',
      );

      if (urlResponse.statusCode != 200) {
        throw HttpRequestException(
          'Failed to get login URL',
          statusCode: urlResponse.statusCode,
          body: urlResponse.body,
        );
      }

      final urlData = jsonDecode(urlResponse.body);
      final loginUrl = urlData['loginUrl'];
      _logger.debug('Received login URL: $loginUrl');

      // For web, we'll use a direct URL approach
      if (kIsWeb) {
        // The web=true parameter is now set on the server side
        // No need to add it here to avoid duplicate parameters
        final uri = Uri.parse(loginUrl);

        // As a fallback, check if we have auth_token in the URL (for backward compatibility)
        final currentUri = Uri.parse(Uri.base.toString());
        if (currentUri.queryParameters.containsKey('auth_token')) {
          _logger.debug('Found auth token in URL, attempting to exchange it');
          final token = currentUri.queryParameters['auth_token']!;

          // Exchange the token for auth data
          final authData = await exchangeToken(token);
          final user = User.fromJson(authData['user']);

          // Save tokens to secure storage
          await _secureStorage.write(key: AppConfig.auth.accessTokenKey, value: authData['access_token']);
          await _secureStorage.write(key: AppConfig.auth.idTokenKey, value: authData['id_token']);

          _logger.info('Successfully authenticated with token from URL');
          return user;
        }

        _logger.debug('Launching Microsoft login URL');
        // Launch the login URL
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, webOnlyWindowName: '_self');
          // We'll never reach here because the page will navigate away
          throw Exception('Navigation to Microsoft login URL in progress');
        } else {
          throw Exception('Could not launch Microsoft login URL');
        }
      } else {
        // For mobile platforms, use url_launcher
        final Uri url = Uri.parse(loginUrl);

        if (!await launchUrl(
          url,
          mode: LaunchMode.externalApplication,
        )) {
          throw Exception('Could not launch $url');
        }

        // For mobile, we need to wait for the callback to be handled by the deep link handler
        // The deep link handler will receive a URL like: com.example.fleex://auth/callback?token=xyz
        // The token will be used to exchange for auth data
        // This is handled in the UrlHandlerService

        // We'll throw an exception here to indicate that the login process is in progress
        // The exception will be caught by the login screen and displayed to the user
        throw Exception('Microsoft login in progress. Please complete the authentication in your browser.');
      }
    } catch (e) {
      _logger.error('Microsoft Entra login failed', e);
      if (e is HttpRequestException) {
        rethrow;
      } else {
        throw Exception('Microsoft Entra login failed: ${e.toString()}');
      }
    }
  }  /// Logout the current user
  static Future<void> logout() async {
    try {
      _logger.info('Starting logout process');      // Remove all FCM tokens from server before clearing local data
      try {
        // Get current user ID for FCM token cleanup
        final user = await _persistenceService.loadUser();
        if (user != null) {
          // Remove all FCM tokens for this user from the server
          final response = await ApiService.delete('notifications/fcm-tokens/all', body: {
            'user_id': user.userId,
          });
          
          if (response.statusCode == 200) {
            _logger.debug('All FCM tokens removed from server successfully');
          } else {
            _logger.warning('Failed to remove FCM tokens from server: ${response.statusCode}');
          }
        } else {
          _logger.debug('No user data found, skipping FCM token cleanup');
        }
      } catch (e) {
        _logger.error('Error removing FCM tokens from server during logout', e);
        // Continue with logout even if FCM token removal fails
      }

      // Clear tokens from secure storage
      await _secureStorage.delete(key: AppConfig.auth.accessTokenKey);
      _logger.debug('Access token deleted');

      await _secureStorage.delete(key: AppConfig.auth.idTokenKey);
      _logger.debug('ID token deleted');

      // For mobile, also clear all persisted data
      if (!kIsWeb) {
        // First clear all persisted data through our service
        await _persistenceService.clearAll();
        _logger.debug('All persisted data cleared through persistence service');

        // Also try to clear all secure storage directly
        try {
          await _secureStorage.deleteAll();
          _logger.debug('All secure storage deleted directly');
        } catch (e) {
          _logger.error('Error deleting all secure storage', e);
        }

        // Try to clear SharedPreferences directly
        try {
          final prefs = await SharedPreferences.getInstance();
          await prefs.clear();
          _logger.debug('All SharedPreferences cleared directly');
        } catch (e) {
          _logger.error('Error clearing SharedPreferences', e);
        }

        // Verify everything is cleared
        bool anyDataRemaining = false;

        try {
          final accessToken = await _secureStorage.read(key: AppConfig.auth.accessTokenKey);
          if (accessToken != null) {
            _logger.warning('WARNING - Access token still exists after clearing!');
            anyDataRemaining = true;
          }

          final hasUserData = await _persistenceService.hasUserData();
          if (hasUserData) {
            _logger.warning('WARNING - User data still exists after clearing!');
            anyDataRemaining = true;
          }

          if (!anyDataRemaining) {
            _logger.debug('Verification successful - all data cleared');
          }
        } catch (e) {
          _logger.error('Error during verification', e);
        }
      }

      // For web, we'll try to call the Microsoft logout endpoint
      if (kIsWeb) {
        try {
          // Get the logout URL from the server
          final urlResponse = await ApiService.get('auth/logout');

          if (urlResponse.statusCode == 200) {
            final urlData = jsonDecode(urlResponse.body);
            final logoutUrl = urlData['logoutUrl'];

            // Launch the logout URL
            final uri = Uri.parse(logoutUrl);
            if (await canLaunchUrl(uri)) {
              await launchUrl(uri);
            }
          }
        } catch (e) {
          _logger.error('Web logout error', e);
          // Continue with local logout even if server logout fails
        }
      } else {
        // For mobile, we'll just clear the tokens and not try to launch the browser
        // This avoids the 'route not found' error
        _logger.info('Mobile logout completed successfully');
      }
    } catch (e) {
      _logger.error('Error during logout', e);
      // Continue with local logout even if server logout fails
    }
  }

  /// Get the current access token
  static Future<String?> getAccessToken() async {
    if (kIsWeb) {
      // For web, use localStorage
      final token = AuthDataStorage.getAccessToken();
      _logger.debug('getAccessToken (Web): ${token != null ? 'token exists' : 'token is null'}');
      return token;
    } else {
      // For mobile, use secure storage
      final token = await _secureStorage.read(key: AppConfig.auth.accessTokenKey);
      _logger.debug('getAccessToken (Mobile): ${token != null ? 'token exists' : 'token is null'}');
      return token;
    }
  }

  /// Verify token with server
  static Future<bool> verifyToken(String token) async {
    _logger.debug('Verifying token with server');
    try {
      // Make a request to a protected endpoint to verify the token
      final response = await http.get(
        Uri.parse('$baseUrl/api/verify-token'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      _logger.debug('Verify token response status code: ${response.statusCode}');

      // If the response is 200, the token is valid
      return response.statusCode == 200;
    } catch (e) {
      _logger.error('Error verifying token', e);
      return false;
    }
  }

  /// Check for persisted user data and restore session if available
  static Future<User?> checkPersistedUserData() async {
    if (kIsWeb) {
      // For web, we don't use persistence
      _logger.debug('Web platform detected, no persistence');
      return null;
    }

    try {
      _logger.debug('Checking for persisted user data');
      // Check if we have persisted user data
      if (await _persistenceService.hasUserData()) {
        // Load the user data
        final user = await _persistenceService.loadUser();

        if (user != null) {
          _logger.info('Found persisted user data for ${user.name}, role: ${user.role}');

          // Check if we have valid tokens
          final accessToken = await _persistenceService.loadAccessToken();

          if (accessToken != null) {
            _logger.debug('Found persisted access token');

            // Try to verify the token with the server
            try {
              final isValid = await verifyToken(accessToken);

              if (isValid) {
                _logger.info('Token verified with server');
                return user;
              } else {
                _logger.warning('Token is invalid or expired');
                // Clear invalid data
                await _persistenceService.clearAll();
                return null;
              }
            } catch (e) {
              _logger.error('Error verifying token', e);
              // If we can't verify the token (e.g., no internet), still return the user
              // This allows offline login
              _logger.info('Returning user without token verification');
              return user;
            }
          } else {
            _logger.debug('No persisted access token found');
          }
        }
      } else {
        _logger.debug('No persisted user data found');
      }

      return null;
    } catch (e) {
      _logger.error('Error checking persisted data', e);
      return null;
    }
  }

  /// Save tokens to secure storage
  static Future<void> saveTokens(String accessToken, String idToken) async {
    _logger.debug('Saving access token and ID token');

    if (kIsWeb) {
      // For web, use localStorage
      AuthDataStorage.storeAccessToken(accessToken);
      _logger.debug('Access token saved to localStorage');
    } else {
      // For mobile, use both secure storage and our persistence service
      await _secureStorage.write(key: AppConfig.auth.accessTokenKey, value: accessToken);
      await _secureStorage.write(key: AppConfig.auth.idTokenKey, value: idToken);

      // Also save to our persistence service
      await _persistenceService.saveTokens(accessToken, idToken);

      _logger.debug('Tokens saved to secure storage');

      // Verify the token was saved correctly (mobile only)
      final savedToken = await _secureStorage.read(key: AppConfig.auth.accessTokenKey);
      _logger.debug('Verification - ${savedToken != null ? 'token exists' : 'token is null'}');
    }

    _logger.info('Tokens saved successfully');
  }

  /// Exchange a token or code for auth data
  static Future<Map<String, dynamic>> exchangeToken(String token) async {
    // Check if the token contains HTML or invalid characters
    if (token.contains('<') || token.contains('>') || token.contains('DOCTYPE')) {
      _logger.error('Invalid token format detected - contains HTML');
      throw Exception('Invalid token format - contains HTML content');
    }

    // Clean the token - remove any trailing characters that might cause issues
    token = token.trim();

    // Log token details for debugging
    _logger.debug('Token length: ${token.length}');
    if (token.length > 20) {
      _logger.debug('Token preview: ${token.substring(0, 20)}...');
    } else {
      _logger.debug('Full token: $token');
    }

    // Determine if this is a Microsoft Entra code or our standard token
    // Microsoft Entra codes are typically much longer
    final bool isMicrosoftEntraCode = token.length > 100;
    _logger.debug('Identified as ${isMicrosoftEntraCode ? 'Microsoft Entra code' : 'standard token'}');

    // We'll use different endpoints based on the token type
    _logger.debug('Using endpoint: ${isMicrosoftEntraCode ? 'auth/code' : 'auth/token/$token'}');

    try {
      http.Response response;

      if (isMicrosoftEntraCode) {
        // For Microsoft Entra codes, use POST to avoid URL length limitations
        _logger.debug('Using POST method for Microsoft Entra code');
        response = await ApiService.post(
          'auth/code',
          {'code': token},
        );
      } else {
        // For standard tokens, use GET
        _logger.debug('Using GET method for standard token');
        response = await ApiService.get(
          'auth/token/$token',
        );
      }

      _logger.debug('Response status code: ${response.statusCode}');
      _logger.debug('Response content type: ${response.headers['content-type']}');

      // Log the first 500 characters of the response body for debugging
      final previewLength = response.body.length > 500 ? 500 : response.body.length;
      _logger.debug('Response body preview: ${response.body.substring(0, previewLength)}');

      if (response.statusCode == 200) {
        // Check if the response is JSON
        if (response.headers['content-type']?.contains('application/json') ?? false) {
          try {
            return jsonDecode(response.body);
          } catch (e) {
            _logger.error('Error decoding JSON', e);
            throw Exception('Failed to parse server response as JSON');
          }
        } else {
          // If we got HTML or other non-JSON response
          _logger.error('Received non-JSON response with content type: ${response.headers['content-type']}');
          throw Exception('Received non-JSON response from server');
        }
      } else {
        // Try to parse as JSON, but handle the case where it's not JSON
        try {
          final error = jsonDecode(response.body);
          throw HttpRequestException(
            error['error'] ?? 'Failed to exchange token/code',
            statusCode: response.statusCode,
            body: response.body,
          );
        } catch (e) {
          // Check if the response contains HTML
          if (response.body.contains('<!DOCTYPE') || response.body.contains('<html')) {
            _logger.error('Received HTML error response');
            throw Exception('Authentication failed: Server returned an HTML error page');
          }
          // If we can't parse as JSON, return the raw error
          throw HttpRequestException(
            'Server error: ${response.statusCode}',
            statusCode: response.statusCode,
            body: response.body,
          );
        }
      }
    } catch (e) {
      _logger.error('Error exchanging token', e);
      if (e is HttpRequestException) {
        rethrow;
      } else {
        throw Exception('Error exchanging token: ${e.toString()}');
      }
    }
  }
}


