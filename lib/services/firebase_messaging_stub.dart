// Stub implementation for Firebase Messaging on web platform
// This prevents Firebase from being loaded on web where it's not needed

/// Stub class for FirebaseMessaging on web
class FirebaseMessaging {
  static FirebaseMessaging? _instance;
  
  static FirebaseMessaging get instance {
    _instance ??= FirebaseMessaging._();
    return _instance!;
  }
  
  FirebaseMessaging._();
  
  /// Stub method for onBackgroundMessage
  static void onBackgroundMessage(Function handler) {
    // No-op on web
  }
}

/// Stub class for RemoteMessage
class RemoteMessage {
  String? messageId;
  Map<String, dynamic> data = {};
  
  RemoteMessage({this.messageId, this.data = const {}});
}

/// Stub function that does nothing on web
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // No-op on web
}
