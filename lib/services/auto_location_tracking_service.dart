import 'dart:async';

import 'background_location_service.dart';
import 'location_service.dart';

class AutoLocationTrackingService {
  // Singleton pattern
  static final AutoLocationTrackingService _instance = AutoLocationTrackingService._internal();
  factory AutoLocationTrackingService() => _instance;
  AutoLocationTrackingService._internal();

  // Location service instance
  final LocationService _locationService = LocationService();

  // Background location service instance
  final BackgroundLocationService _backgroundService = BackgroundLocationService();

  // Status flags
  bool _isInitialized = false;
  bool _isEnabled = false;
  String _errorMessage = '';

  // Driver ID for tracking
  int _driverId = 0;

  // User ID for tracking
  int _userId = 0;

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isEnabled => _isEnabled;
  bool get isTracking => _locationService.isTracking;
  String get errorMessage => _errorMessage;
  LocationService get locationService => _locationService;

  // Initialize the service with user ID
  Future<bool> initialize(int userId, {int? tripId, int? driverId}) async {
    if (_isInitialized) {
      return true; // Already initialized
    }

    try {
      // Store user ID for tracking
      _userId = userId;

      // Set user ID in location service
      _locationService.setUserId(userId);

      // Set trip ID if provided
      if (tripId != null) {
        _locationService.setTripId(tripId);
      }

      // Store driver ID for availability checks
      // If not provided, use the user ID as a fallback
      _driverId = driverId ?? userId;

      // Initialize location service
      final bool initialized = await _locationService.initialize();
      if (!initialized) {
        _errorMessage = _locationService.errorMessage;
        return false;
      }

      // Initialize background service
      final bool backgroundInitialized = await _backgroundService.initialize();
      if (!backgroundInitialized) {
        _errorMessage = 'Failed to initialize background service';
        return false;
      }

      _isInitialized = true;
      return true;
    } catch (e) {
      _errorMessage = 'Error initializing auto location tracking service: $e';
      return false;
    }
  }

  // Enable automatic tracking
  Future<void> enable() async {
    if (!_isInitialized) {
      _errorMessage = 'Service not initialized. Call initialize() first.';
      return;
    }

    if (_isEnabled) {
      return; // Already enabled
    }

    _isEnabled = true;

    // Start tracking immediately
    await _startTracking();

    // Start background service for persistent tracking
    final isBackgroundRunning = await _backgroundService.isRunning();
    if (!isBackgroundRunning) {
      await _backgroundService.startService(
        userId: _userId,
        driverId: _driverId,
      );
      print('Background location service started for persistent tracking');
    } else {
      print('Background location service already running');
    }

    // Tracking is now enabled
    print('Auto location tracking enabled');
  }

  // Disable automatic tracking
  Future<void> disable() async {
    if (!_isEnabled) {
      return; // Already disabled
    }

    // Cancel force update timer
    _forceUpdateTimer?.cancel();
    _forceUpdateTimer = null;

    // Stop tracking if active
    if (_locationService.isTracking) {
      _locationService.stopTracking();
    }

    // Stop background service
    final isBackgroundRunning = await _backgroundService.isRunning();
    if (isBackgroundRunning) {
      await _backgroundService.stopService();
      print('Background location service stopped');
    }

    _isEnabled = false;

    // Tracking is now disabled
    print('Auto location tracking disabled');
  }



  // Start tracking
  Future<void> _startTracking() async {
    try {
      final bool started = await _locationService.startTracking();

      if (!started) {
        _errorMessage = _locationService.errorMessage;
        print('Failed to start auto location tracking: $_errorMessage');
        return;
      }

      // Start a timer to force location updates every minute regardless of position
      _startForceUpdateTimer();

      print('Auto location tracking started');
    } catch (e) {
      _errorMessage = 'Error starting auto location tracking: $e';
      print(_errorMessage);
    }
  }

  // Timer for forced location updates
  Timer? _forceUpdateTimer;

  // Start timer to force location updates every minute
  void _startForceUpdateTimer() {
    // Cancel existing timer if any
    _forceUpdateTimer?.cancel();

    // Create a new timer that fires every minute
    _forceUpdateTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _checkAndForceUpdate();
    });

    print('Force update timer started - location will be updated every minute when conditions are met');
  }

  // Check conditions and force update if needed
  Future<void> _checkAndForceUpdate() async {
    if (_isEnabled && _isInitialized) {
      // Force location update if tracking is enabled
      await _forceLocationUpdate();
    }
  }

  // Force a location update regardless of position change
  Future<void> _forceLocationUpdate() async {
    try {
      // Use the public method to get current location and send to server
      final success = await _locationService.getCurrentLocationAndSend();

      if (success) {
        final now = DateTime.now().toLocal();
        print('Forced location update at ${now.toString()} (local time)');
      } else {
        print('Failed to force location update: ${_locationService.errorMessage}');
      }
    } catch (e) {
      print('Error forcing location update: $e');
    }
  }

  // Force an immediate location update
  Future<bool> forceLocationUpdate() async {
    if (!_isInitialized) {
      _errorMessage = 'Service not initialized. Call initialize() first.';
      return false;
    }

    // Force update in background service if running
    final isBackgroundRunning = await _backgroundService.isRunning();
    if (isBackgroundRunning) {
      await _backgroundService.forceLocationUpdate();
      print('Forced location update in background service');
    }

    // Also update in foreground service
    return await _locationService.getCurrentLocationAndSend();
  }
}


