import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../generated/l10n/app_localizations.dart';

/// Service for managing app localization and language preferences
class LocalizationService {
  // Singleton pattern
  static final LocalizationService _instance = LocalizationService._internal();
  factory LocalizationService() => _instance;
  LocalizationService._internal();

  // Storage key for language preference
  static const String _languageKey = 'selected_language';

  // Default locale
  static const Locale defaultLocale = Locale('en');

  // Available locales
  static const List<Locale> supportedLocales = [
    Locale('en'), // English
    Locale('id'), // Indonesian
  ];

  // Language display names
  static const Map<String, String> languageNames = {
    'en': 'English',
    'id': 'Bahasa Indonesia',
  };

  /// Get the current saved locale preference
  Future<Locale> getSavedLocale() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final languageCode = prefs.getString(_languageKey);
      
      if (languageCode != null) {
        // Check if the saved language is supported
        for (final locale in supportedLocales) {
          if (locale.languageCode == languageCode) {
            return locale;
          }
        }
      }
    } catch (e) {
      print('LocalizationService: Error getting saved locale: $e');
    }
    
    return defaultLocale;
  }

  /// Save the selected locale preference
  Future<void> saveLocale(Locale locale) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_languageKey, locale.languageCode);
    } catch (e) {
      print('LocalizationService: Error saving locale: $e');
    }
  }

  /// Get localized strings for the current context
  static AppLocalizations? of(BuildContext context) {
    return AppLocalizations.of(context);
  }

  /// Get language name by language code
  static String getLanguageName(String languageCode) {
    return languageNames[languageCode] ?? languageCode;
  }

  /// Check if a locale is supported
  static bool isLocaleSupported(Locale locale) {
    return supportedLocales.any((supportedLocale) => 
        supportedLocale.languageCode == locale.languageCode);
  }
}



