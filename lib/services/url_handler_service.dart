import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../generated/l10n/app_localizations.dart';
import '../main.dart';
import '../models/user.dart';
import '../screens/driver_trips_screen.dart';
import '../screens/login_screen.dart';
import '../screens/requestor_dashboard_screen.dart';
import '../screens/trip_manager_dashboard_screen.dart';
import '../services/auth_service.dart';
import '../services/webview_bridge.dart';

/// Service to handle URL navigation and deep links
class UrlHandlerService {
  /// Navigate to the appropriate screen based on the URL
  static Future<bool> handleUrl(BuildContext context, String url) async {
    debugPrint('handleUrl: Processing URL: $url');
    try {
      // Parse the URL
      final uri = Uri.parse(url);

      // Extract the path and host
      final path = uri.path;
      final host = uri.host;

      debugPrint('handleUrl: URL components - scheme: ${uri.scheme}, host: $host, path: $path');
      debugPrint('handleUrl: Query parameters: ${uri.queryParameters}');

      // Handle SSO callback URL (all formats)
      if ((host == 'auth' && path.startsWith('/callback')) ||
          (host == '' && path.startsWith('/callback')) ||
          (host == '' && path == '/callback') ||
          (uri.queryParameters.containsKey('code'))) {
        debugPrint('handleUrl: Detected SSO callback URL');
        // Check if we have a token or code in the query parameters
        final queryParams = uri.queryParameters;
        String? token;

        // Check for Microsoft Entra format (code parameter)
        if (queryParams.containsKey('code')) {
          token = queryParams['code']!;
          debugPrint('handleUrl: Received SSO callback with code parameter: $token');
          debugPrint('handleUrl: Code length: ${token.length}');
          if (token.length > 20) {
            debugPrint('handleUrl: Code preview: ${token.substring(0, 20)}...');
          } else {
            debugPrint('handleUrl: Code full: $token');
          }

          // Check if the code is valid (not HTML)
          if (token.contains('<') || token.contains('>') || token.contains('DOCTYPE')) {
            debugPrint('handleUrl: Invalid code format detected - contains HTML');
            token = null; // Set to null to trigger the error handling below
          }
        }
        // Check for our standard format (token parameter)
        else if (queryParams.containsKey('token')) {
          token = queryParams['token']!;
          debugPrint('handleUrl: Received SSO callback with token parameter: $token');
        }
        // Check if code is in the URL fragment or path
        else if (uri.fragment.isNotEmpty && uri.fragment.contains('code=')) {
          // Extract code from fragment
          final codeParam = uri.fragment.split('code=');
          if (codeParam.length > 1) {
            token = codeParam[1];
            // If there are additional parameters, extract just the code
            if (token.contains('&')) {
              token = token.split('&')[0];
            }
            debugPrint('handleUrl: Found code in fragment: $token');
          }
        }
        // Check if the entire URL contains code= (for malformed URLs)
        else if (url.contains('code=')) {
          // Extract code from the full URL
          final codeParam = url.split('code=');
          if (codeParam.length > 1) {
            token = codeParam[1];
            // If there are additional parameters, extract just the code
            if (token.contains('&')) {
              token = token.split('&')[0];
            }
            debugPrint('handleUrl: Found code in full URL: $token');
          }
        }

        if (token != null) {

          // Make sure the context is still valid
          if (!context.mounted) {
            debugPrint('handleUrl: Context is no longer mounted');
            return true;
          }

          try {
            debugPrint('handleUrl: Exchanging token/code for auth data');
            // Exchange the token for auth data
            final authData = await AuthService.exchangeToken(token);
            debugPrint('handleUrl: Token/code exchange successful');
            final user = User.fromJson(authData['user']);
            debugPrint('handleUrl: User data parsed: ${user.name}, role: ${user.role}');

            // Make sure the context is still valid after the async operation
            if (!context.mounted) {
              debugPrint('handleUrl: Context is no longer mounted after token exchange');
              return true;
            }

            // Update the app state with the logged-in user
            debugPrint('handleUrl: Updating app state with user');
            Provider.of<MyAppState>(context, listen: false).setCurrentUser(user);
            debugPrint('handleUrl: App state updated');

            // Save tokens to secure storage
            debugPrint('handleUrl: Saving tokens to secure storage');
            await AuthService.saveTokens(authData['access_token'], authData['id_token']);
            debugPrint('handleUrl: SSO login successful, tokens saved');

            // Navigate to the appropriate screen based on user role
            if (!context.mounted) {
              debugPrint('handleUrl: Context is no longer mounted before navigation');
              return true;
            }

            debugPrint('handleUrl: Navigating based on user role: ${user.role}');
            debugPrint('handleUrl: User role checks - isAdmin: ${user.isAdmin}, isTripManager: ${user.isTripManager}, isDriver: ${user.isDriver}');

            // Use a more direct navigation approach
            if (user.isAdmin || user.isTripManager) {
              debugPrint('handleUrl: Navigating to trip manager dashboard');
              // Create and push the screen directly instead of using named routes
              Navigator.of(context).pushAndRemoveUntil(
                MaterialPageRoute(
                  builder: (context) => const TripManagerDashboardScreen(),
                ),
                (route) => false, // Remove all previous routes
              );
            } else if (user.isDriver) {
              debugPrint('handleUrl: Navigating to driver trips screen');
              Navigator.of(context).pushAndRemoveUntil(
                MaterialPageRoute(
                  builder: (context) => DriverTripsScreen(driverCode: ''),
                ),
                (route) => false, // Remove all previous routes
              );
            } else {
              debugPrint('handleUrl: Navigating to requestor dashboard');
              Navigator.of(context).pushAndRemoveUntil(
                MaterialPageRoute(
                  builder: (context) => const RequestorDashboardScreen(),
                ),
                (route) => false, // Remove all previous routes
              );
            }
            debugPrint('handleUrl: Navigation completed');

            return true;
          } catch (e) {
            debugPrint('Error exchanging token/code: $e');

            // Check if the error is related to HTML response
            final errorMsg = e.toString().toLowerCase();
            String userMessage = 'Authentication failed';

            if (errorMsg.contains('formatexception') ||
                errorMsg.contains('unexpected character') ||
                errorMsg.contains('<!doctype html>') ||
                errorMsg.contains('non-json response')) {
              debugPrint('handleUrl: Detected HTML response error - likely invalid code or expired code');
              userMessage = 'Authentication failed: Invalid or expired authentication code';
            } else {
              userMessage = 'Authentication failed: ${e.toString()}';
            }

            // Show an error message
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(userMessage))
              );
            }
            return false;
          }
        } else {          debugPrint('handleUrl: No token or code found in callback URL');
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(AppLocalizations.of(context).authenticationFailed))
            );
          }
          return false;
        }
      }

      // Handle regular navigation paths
      if (path.startsWith('/trip-manager-dashboard')) {
        if (context.mounted) {
          debugPrint('handleUrl: Navigating to trip manager dashboard');
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(
              builder: (context) => const TripManagerDashboardScreen(),
            ),
            (route) => false, // Remove all previous routes
          );
        }
        return true;
      } else if (path.startsWith('/driver-trips')) {
        if (context.mounted) {
          debugPrint('handleUrl: Navigating to driver trips screen');
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(
              builder: (context) => DriverTripsScreen(driverCode: ''),
            ),
            (route) => false, // Remove all previous routes
          );
        }
        return true;
      } else if (path.startsWith('/requestor-dashboard')) {
        if (context.mounted) {
          debugPrint('handleUrl: Navigating to requestor dashboard');
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(
              builder: (context) => const RequestorDashboardScreen(),
            ),
            (route) => false, // Remove all previous routes
          );
        }
        return true;
      } else if (path.startsWith('/login')) {
        if (context.mounted) {
          debugPrint('handleUrl: Navigating to login screen');
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(
              builder: (context) => const LoginScreen(),
            ),
            (route) => false, // Remove all previous routes
          );
        }
        return true;
      }

      // Handle query parameters if needed
      final queryParams = uri.queryParameters;
      if (queryParams.containsKey('auth_token')) {
        final token = queryParams['auth_token']!;
        debugPrint('Found auth_token in URL: $token');

        try {
          debugPrint('handleUrl: Exchanging auth_token for auth data');
          // Exchange the token for auth data
          final authData = await AuthService.exchangeToken(token);
          debugPrint('handleUrl: Token exchange successful');
          final user = User.fromJson(authData['user']);
          debugPrint('handleUrl: User data parsed: ${user.name}, role: ${user.role}');

          // Make sure the context is still valid after the async operation
          if (!context.mounted) {
            debugPrint('handleUrl: Context is no longer mounted after token exchange');
            return true;
          }

          // Update the app state with the logged-in user
          debugPrint('handleUrl: Updating app state with user');
          Provider.of<MyAppState>(context, listen: false).setCurrentUser(user);
          debugPrint('handleUrl: App state updated');

          // Save tokens to secure storage
          debugPrint('handleUrl: Saving tokens to secure storage');
          await AuthService.saveTokens(authData['access_token'], authData['id_token']);
          debugPrint('handleUrl: Auth token login successful, tokens saved');

          // Navigate to the appropriate screen based on user role
          if (!context.mounted) {
            debugPrint('handleUrl: Context is no longer mounted before navigation');
            return true;
          }

          debugPrint('handleUrl: Navigating based on user role: ${user.role}');
          debugPrint('handleUrl: User role checks - isAdmin: ${user.isAdmin}, isTripManager: ${user.isTripManager}, isDriver: ${user.isDriver}');

          // Use a more direct navigation approach
          if (user.isAdmin || user.isTripManager) {
            debugPrint('handleUrl: Navigating to trip manager dashboard');
            Navigator.of(context).pushAndRemoveUntil(
              MaterialPageRoute(
                builder: (context) => const TripManagerDashboardScreen(),
              ),
              (route) => false, // Remove all previous routes
            );
          } else if (user.isDriver) {
            debugPrint('handleUrl: Navigating to driver trips screen');
            Navigator.of(context).pushAndRemoveUntil(
              MaterialPageRoute(
                builder: (context) => DriverTripsScreen(driverCode: ''),
              ),
              (route) => false, // Remove all previous routes
            );
          } else {
            debugPrint('handleUrl: Navigating to requestor dashboard');
            Navigator.of(context).pushAndRemoveUntil(
              MaterialPageRoute(
                builder: (context) => const RequestorDashboardScreen(),
              ),
              (route) => false, // Remove all previous routes
            );
          }
          debugPrint('handleUrl: Navigation completed');

          return true;
        } catch (e) {
          debugPrint('Error exchanging token from URL: $e');
          return false;
        }
      }

      return false;
    } catch (e) {
      debugPrint('Error handling URL: $e');
      return false;
    }
  }

  /// Handle deep links from web to app
  static Future<bool> handleDeepLink(BuildContext context, String url) async {
    debugPrint('UrlHandlerService: Received deep link: $url');

    // Special handling for SSO callback URLs - handle all formats
    if (url.startsWith('com.example.fleex://auth/callback') ||
        url.startsWith('com.example.fleex:/auth/callback') ||
        url.startsWith('com.example.fleex://callback') ||
        url.startsWith('com.example.fleex:/callback') ||
        (url.startsWith('com.example.fleex:') && url.contains('/auth/callback')) ||
        (url.startsWith('com.example.fleex:') && url.contains('/callback')) ||
        (url.startsWith('com.example.fleex:') && url.contains('code='))) {
      debugPrint('UrlHandlerService: Detected SSO callback URL, handling directly');
      // For SSO callbacks, we'll handle them directly
      if (!context.mounted) {
        debugPrint('UrlHandlerService: Context is no longer mounted for SSO callback');
        // Use the global handler as a fallback
        try {
          // Import the global handler from main.dart
          await handleSsoCallback(url);
          return true;
        } catch (e) {
          debugPrint('UrlHandlerService: Error using global SSO handler: $e');
          return false;
        }
      }
      return handleUrl(context, url);
    }

    if (kIsWeb) {
      debugPrint('UrlHandlerService: Handling web deep link');
      // Web implementation
      return handleUrl(context, url);
    } else {
      debugPrint('UrlHandlerService: Handling mobile deep link');
      // Mobile implementation
      try {
        debugPrint('UrlHandlerService: Trying WebViewBridge.handleDeepLink');
        final handled = await WebViewBridge.handleDeepLink(url);
        debugPrint('UrlHandlerService: WebViewBridge.handleDeepLink result: $handled');

        if (!handled) {
          debugPrint('UrlHandlerService: WebViewBridge couldn\'t handle it, trying our own handler');
          // If the WebViewBridge couldn't handle it, try our own handler
          // Make sure the context is still valid
          if (!context.mounted) {
            debugPrint('UrlHandlerService: Context is no longer mounted');
            return false;
          }
          return handleUrl(context, url);
        }
        return handled;
      } catch (e) {
        debugPrint('UrlHandlerService: Error handling deep link: $e');
        // If there's an error with WebViewBridge, try our own handler
        if (!context.mounted) return false;
        return handleUrl(context, url);
      }
    }
  }
}



