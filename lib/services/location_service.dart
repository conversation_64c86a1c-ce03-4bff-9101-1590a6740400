import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';

import '../services/api_service.dart';

class LocationService {
  // Singleton pattern
  static final LocationService _instance = LocationService._internal();
  factory LocationService() => _instance;
  LocationService._internal();

  // Stream controller for location updates
  StreamSubscription<Position>? _positionStreamSubscription;

  // Current position
  Position? _currentPosition;

  // Status flags
  bool _isTracking = false;
  String _errorMessage = '';
  DateTime? _lastUpdateTime;
  int _userId = 0; // User ID for API calls
  int? _tripId; // Optional trip ID for API calls

  // Rate limiting
  static const Duration _minimumUpdateInterval = Duration(minutes: 5);

  // Getters
  bool get isTracking => _isTracking;
  String get errorMessage => _errorMessage;
  Position? get currentPosition => _currentPosition;
  DateTime? get lastUpdateTime => _lastUpdateTime;

  // Set user ID for API calls
  void setUserId(int userId) {
    _userId = userId;
  }

  // Set trip ID for API calls (optional)
  void setTripId(int? tripId) {
    _tripId = tripId;
  }

  // Initialize location service
  Future<bool> initialize() async {
    try {
      // Check if location services are enabled
      final bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        _errorMessage = 'Location services are disabled. Please enable them in your device settings.';
        return false;
      }

      // Check location permission
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        final LocationPermission requestedPermission = await Geolocator.requestPermission();
        permission = requestedPermission;
        if (permission == LocationPermission.denied) {
          _errorMessage = 'Location permissions are denied. Please enable them in your device settings.';
          return false;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        _errorMessage = 'Location permissions are permanently denied. Please enable them in your device settings.';
        return false;
      }

      // Get current position
      _currentPosition = await Geolocator.getCurrentPosition();

      // No need to explicitly enable background mode as it's handled by the foregroundNotificationConfig

      return true;
    } catch (e) {
      _errorMessage = 'Error initializing location service: $e';
      return false;
    }
  }

  // Start tracking location
  Future<bool> startTracking() async {
    if (_isTracking) {
      return true; // Already tracking
    }

    try {
      // Initialize location service if not already initialized
      if (_currentPosition == null) {
        final bool initialized = await initialize();
        if (!initialized) {
          return false;
        }
      }

      // Define location settings
      LocationSettings locationSettings;

      // Configure settings based on platform
      if (defaultTargetPlatform == TargetPlatform.android) {
        locationSettings = AndroidSettings(
          accuracy: LocationAccuracy.high,
          distanceFilter: 500, // Update when moved 500 meters
          forceLocationManager: false,
          intervalDuration: const Duration(minutes: 5), // Force update every 5 minutes
          foregroundNotificationConfig: const ForegroundNotificationConfig(
            notificationTitle: 'FLEEX Driver Tracking',
            notificationText: 'Tracking active - updates every 5 min or 500m',
            enableWakeLock: true,
            notificationChannelName: 'Location Tracking',
          ),
        );
      } else if (defaultTargetPlatform == TargetPlatform.iOS) {
        locationSettings = AppleSettings(
          accuracy: LocationAccuracy.high,
          activityType: ActivityType.automotiveNavigation,
          distanceFilter: 500, // Update when moved 500 meters
          pauseLocationUpdatesAutomatically: false, // Don't pause updates automatically
          showBackgroundLocationIndicator: true,
        );
      } else {
        locationSettings = const LocationSettings(
          accuracy: LocationAccuracy.high,
          distanceFilter: 500, // Update when moved 500 meters
        );
      }

      // Start position stream
      _positionStreamSubscription = Geolocator.getPositionStream(
        locationSettings: locationSettings,
      ).listen(
        (Position position) {
          _currentPosition = position;
          _sendLocationToServer(position);
        },
        onError: (error) {
          _errorMessage = 'Error tracking location: $error';
          _isTracking = false;
        },
      );

      _isTracking = true;
      return true;
    } catch (e) {
      _errorMessage = 'Error starting location tracking: $e';
      _isTracking = false;
      return false;
    }
  }

  // Stop tracking location
  void stopTracking() {
    _positionStreamSubscription?.cancel();
    _positionStreamSubscription = null;
    _isTracking = false;
  }

  // Send location to server
  Future<void> _sendLocationToServer(Position position) async {
    try {
      if (_userId <= 0) {
        _errorMessage = 'User ID not set. Cannot send location to server.';
        return;
      }

      // Format location data
      final String latLngStr = '${position.latitude},${position.longitude}';

      // Send location to server
      final response = await ApiService.post(
        'driver-locations',
        {
          'user_id': _userId,
          'trip_id': _tripId,
          'latitude_longitude': latLngStr,
        },
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        // Update last update time
        _lastUpdateTime = DateTime.now();
        _errorMessage = '';
      } else {
        _errorMessage = 'Failed to send location to server: ${response.statusCode}';
      }
    } catch (e) {
      _errorMessage = 'Error sending location to server: $e';
    }
  }

  // Get current location and send to server
  Future<bool> getCurrentLocationAndSend() async {
    // Check if we've updated recently (rate limiting)
    if (_lastUpdateTime != null) {
      final timeSinceLastUpdate = DateTime.now().difference(_lastUpdateTime!);
      if (timeSinceLastUpdate < _minimumUpdateInterval) {
        // Calculate remaining time until next update is allowed
        final remainingTime = _minimumUpdateInterval - timeSinceLastUpdate;
        final remainingSeconds = remainingTime.inSeconds;
        _errorMessage = 'Please wait $remainingSeconds seconds before updating again';
        return false;
      }
    }
    try {
      // Check if location services are enabled
      final bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        _errorMessage = 'Location services are disabled. Please enable them in your device settings.';
        return false;
      }

      // Check location permission
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        final LocationPermission requestedPermission = await Geolocator.requestPermission();
        permission = requestedPermission;
        if (permission == LocationPermission.denied) {
          _errorMessage = 'Location permissions are denied. Please enable them in your device settings.';
          return false;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        _errorMessage = 'Location permissions are permanently denied. Please enable them in your device settings.';
        return false;
      }

      // Get current position
      _currentPosition = await Geolocator.getCurrentPosition();

      // Send to server
      await _sendLocationToServer(_currentPosition!);

      return true;
    } catch (e) {
      _errorMessage = 'Error getting current location: $e';
      return false;
    }
  }
}


