import 'package:FLEEX/generated/l10n/app_localizations.dart';
import 'package:FLEEX/models/notification.dart' as app_notification;
import 'package:flutter/material.dart';

class NotificationTranslationService {
  /// Translate notification title using localization data
  static String translateTitle(BuildContext context, app_notification.Notification notification) {
    try {
      // Check if we can parse localization data from the notification
      final localizationData = _parseLocalizationDataFromNotification(notification);
      if (localizationData == null) {
        // Fallback to original title if no localization data
        return notification.title;
      }

      final localizations = AppLocalizations.of(context);
      final titleKey = localizationData['titleKey'] as String?;
      final params = localizationData['params'] as Map<String, dynamic>? ?? {};

      if (titleKey == null) {
        return notification.title;
      }

      // Use the new notification localization methods
      return _getLocalizedTitle(localizations, titleKey, params) ?? notification.title;
    } catch (e) {
      print('Error translating notification title: $e');
      return notification.title;
    }
  }

  /// Translate notification message using localization data
  static String translateMessage(BuildContext context, app_notification.Notification notification) {
    try {
      // Check if we can parse localization data from the notification
      final localizationData = _parseLocalizationDataFromNotification(notification);
      if (localizationData == null) {
        // Fallback to original message if no localization data
        return notification.message;
      }

      final localizations = AppLocalizations.of(context);
      final messageKey = localizationData['messageKey'] as String?;
      final params = localizationData['params'] as Map<String, dynamic>? ?? {};

      if (messageKey == null) {
        return notification.message;
      }

      // Use the new notification localization methods
      return _getLocalizedMessage(localizations, messageKey, params) ?? notification.message;
    } catch (e) {
      print('Error translating notification message: $e');
      return notification.message;
    }
  }
  /// Translate push notification content without requiring BuildContext
  /// This is used for push notifications where we don't have access to BuildContext
  static Map<String, String>? translatePushNotification(Map<String, dynamic> localizationData, {String locale = 'en'}) {
    try {
      final titleKey = localizationData['titleKey'] as String?;
      final messageKey = localizationData['messageKey'] as String?;
      final params = localizationData['params'] as Map<String, dynamic>? ?? {};

      if (titleKey == null || messageKey == null) {
        return null;
      }

      // Support multiple languages based on locale parameter
      final title = _getContextFreeTitle(titleKey, params, locale);
      final message = _getContextFreeMessage(messageKey, params, locale);

      if (title == null || message == null) {
        return null;
      }

      return {
        'title': title,
        'body': message,
      };
    } catch (e) {
      print('Error translating push notification: $e');
      return null;
    }
  }
  /// Get translated title without BuildContext (supports multiple languages)
  static String? _getContextFreeTitle(String titleKey, Map<String, dynamic> params, [String locale = 'en']) {
    final tripCode = params['tripCode'] ?? '';
      switch (locale) {
      case 'id': // Indonesian (Bahasa Indonesia)
        return _getIndonesianTitle(titleKey, tripCode);
      case 'es': // Spanish
        return _getSpanishTitle(titleKey, tripCode);
      case 'fr': // French  
        return _getFrenchTitle(titleKey, tripCode);
      case 'en':
      default: // English (fallback)
        return _getEnglishTitle(titleKey, tripCode);
    }
  }

  /// Get translated message without BuildContext (supports multiple languages)
  static String? _getContextFreeMessage(String messageKey, Map<String, dynamic> params, [String locale = 'en']) {    switch (locale) {
      case 'id': // Indonesian (Bahasa Indonesia)
        return _getIndonesianMessage(messageKey, params);
      case 'es': // Spanish
        return _getSpanishMessage(messageKey, params);
      case 'fr': // French
        return _getFrenchMessage(messageKey, params);
      case 'en':
      default: // English (fallback)
        return _getEnglishMessage(messageKey, params);
    }
  }

  /// English title translations
  static String? _getEnglishTitle(String titleKey, String tripCode) {
    
    switch (titleKey) {
      case 'notificationTripAssigned':
        return 'Trip Assigned: $tripCode';
      case 'notificationNewTripAssignment':
        return 'New Trip Assignment: $tripCode';
      case 'notificationTripApproved':
        return 'Trip Approved: $tripCode';
      case 'notificationTripRejected':
        return 'Trip Rejected: $tripCode';
      case 'notificationTripCompleted':
        return 'Trip Completed: $tripCode';
      case 'notificationTripStatusUpdate':
        return 'Trip Status Update: $tripCode';
      case 'notificationTripRated':
        return 'Trip Rated: $tripCode';
      case 'notificationTripReturnedToRequest':
        return 'Trip Returned to Request: $tripCode';
      case 'notificationTripDeleted':
        return 'Trip Deleted: $tripCode';
      case 'notificationBackToBaseTripCompleted':
        return 'Back to Base Trip Completed: $tripCode';
      case 'notificationTripAssignedToOnlineTaxi':
        return 'Trip Assigned to Online Taxi: $tripCode';
      case 'notificationOnlineTaxiTripCompleted':
        return 'Online Taxi Trip Completed: $tripCode';
      case 'notificationBackToBaseTripCreated':
        return 'Back to Base Trip Created: $tripCode';
      case 'notificationTripChangesApproved':
        return 'Trip Changes Approved: $tripCode';
      case 'notificationNewTripRequest':
        return 'New Trip Request: $tripCode';
      case 'notificationTripEditRequest':
        return 'Trip Edit Request: $tripCode';      case 'notificationTripCancellationRequest':
        return 'Trip Cancellation Request: $tripCode';
      case 'notificationTripStarted':
        return 'Trip Started: $tripCode';      case 'notificationDriverConfirmedTrip':
        return 'Driver Confirmed Trip: $tripCode';
      case 'notificationDriverRejectedTrip':
        return 'Driver Rejected Trip: $tripCode';
      case 'notificationTripChangesRejected':
        return 'Trip Changes Rejected: $tripCode';
      case 'notificationTripCancellationRejected':
        return 'Trip Cancellation Rejected: $tripCode';
      default:
        return null;
    }
  }
  /// English message translations
  static String? _getEnglishMessage(String messageKey, Map<String, dynamic> params) {
    final tripCode = params['tripCode'] ?? '';
    final date = params['date'] ?? '';
    final time = params['time'] ?? '';
    final fromDestination = params['fromDestination'] ?? '';
    final toDestination = params['toDestination'] ?? '';
    
    switch (messageKey) {
      case 'notificationMessageTripAssignedToDriver':
        return 'Your trip $tripCode has been assigned to a driver and is scheduled for $date at $time.';
      case 'notificationMessageDriverAssignedToTrip':
        return 'You have been assigned to trip $tripCode from $fromDestination to $toDestination on $date at $time.';
      case 'notificationMessageTripApproved':
        return 'Your trip $tripCode from $fromDestination to $toDestination has been approved.';
      case 'notificationMessageTripRejected':
        final rejectionReason = params['rejectionReason'] ?? 'No reason provided';
        return 'Your trip $tripCode has been rejected. Reason: $rejectionReason.';
      case 'notificationMessageTripRejectedNoReason':
        return 'Your trip $tripCode has been rejected. Reason: No reason provided.';
      case 'notificationMessageTripStatusChanged':
        final oldStatus = params['oldStatus'] ?? '';
        final newStatus = params['newStatus'] ?? '';
        return 'Your trip $tripCode status has changed from $oldStatus to $newStatus.';
      case 'notificationMessageDriverTripStatusChanged':
        final oldStatus = params['oldStatus'] ?? '';
        final newStatus = params['newStatus'] ?? '';
        return 'Trip $tripCode status has changed from $oldStatus to $newStatus.';
      case 'notificationMessageTripRated':
        final rating = params['rating'] ?? '';
        final ratingMessage = params['ratingMessage'] ?? '';
        return 'Your trip $tripCode has been rated $rating stars by the passenger. $ratingMessage';
      case 'notificationMessageTripRatedNoMessage':
        final rating = params['rating'] ?? '';
        return 'Your trip $tripCode has been rated $rating stars by the passenger.';
      case 'notificationMessageTripReturnedToRequestor':
        return 'Your trip $tripCode from $fromDestination to $toDestination has been returned to REQUEST status by a trip manager.';
      case 'notificationMessageTripReturnedToDriver':
        return 'Trip $tripCode from $fromDestination to $toDestination has been returned to REQUEST status by a trip manager.';
      case 'notificationMessageTripDeletedRequestor':
        return 'Your trip $tripCode from $fromDestination to $toDestination has been deleted by a trip manager.';
      case 'notificationMessageTripDeletedDriver':
        return 'Trip $tripCode from $fromDestination to $toDestination has been deleted by a trip manager.';
      case 'notificationMessageTripCompletedRequestor':
        return 'Your trip $tripCode has been completed successfully. Thank you for using our service!';
      case 'notificationMessageTripCompletedManager':
        return 'Trip $tripCode has been marked as completed by the driver.';
      case 'notificationMessageBackToBaseTripCompletedRequestor':
        return 'Your back to base trip $tripCode has been completed successfully.';
      case 'notificationMessageBackToBaseTripCompletedManager':
        return 'Back to base trip $tripCode has been completed.';
      case 'notificationMessageTripAssignedToOnlineTaxi':
        return 'Your trip $tripCode from $fromDestination to $toDestination has been assigned to an online taxi service.';
      case 'notificationMessageOnlineTaxiTripCompletedRequestor':
        return 'Your online taxi trip $tripCode has been successfully completed. Thank you for using our service!';
      case 'notificationMessageOnlineTaxiTripCompletedManager':
        return 'Online taxi trip $tripCode has been marked as completed by the requestor.';
      case 'notificationMessageBackToBaseTripCreated':
        final driverName = params['driverName'] ?? 'Unknown Driver';
        return '$driverName has created a "Back to Base" trip $tripCode from $fromDestination to $toDestination on $date at $time.';
      case 'notificationMessageTripChangesApproved':
        return 'Your changes to trip $tripCode from $fromDestination to $toDestination have been approved. The trip is now back to REQUEST status.';
      case 'notificationMessageNewTripRequest':
        final requestorName = params['requestorName'] ?? 'User';
        return '$requestorName has requested a new trip $tripCode from $fromDestination to $toDestination on $date at $time.';
      case 'notificationMessageTripEditRequest':
        final requestorName = params['requestorName'] ?? 'User';
        return '$requestorName has requested changes to trip $tripCode from $fromDestination to $toDestination on $date at $time.';      case 'notificationMessageTripCancellationRequest':
        final requestorName = params['requestorName'] ?? 'User';
        return '$requestorName has requested to cancel trip $tripCode from $fromDestination to $toDestination on $date at $time.';
      case 'notificationMessageTripStarted':
        return 'Trip $tripCode has been started by the driver.';      case 'notificationMessageDriverConfirmedTrip':
        final driverName = params['driverName'] ?? 'Unknown Driver';
        // Handle cases where "The driver" is extracted as driver name
        final displayDriverName = (driverName == 'The driver') 
            ? 'The driver' 
            : driverName;
        return '$displayDriverName has confirmed trip $tripCode.';
      case 'notificationMessageDriverRejectedTrip':
        final driverName = params['driverName'] ?? 'Driver';
        final rejectionReason = params['rejectionReason'] ?? 'No reason provided';
        // Handle cases where "The driver" is extracted as driver name
        final displayDriverName = (driverName == 'The driver') 
            ? 'The driver' 
            : driverName;
        return '$displayDriverName has rejected trip $tripCode. Reason: $rejectionReason.';
      case 'notificationMessageDriverRejectedTripGeneric':
        final rejectionReason = params['rejectionReason'] ?? 'No reason provided';
        return 'The driver has rejected trip $tripCode. Reason: $rejectionReason.';
      case 'notificationMessageTripChangesRejected':
        final rejectionReason = params['rejectionReason'] ?? 'No reason provided';
        return 'The requested changes to trip $tripCode have been rejected. Reason: $rejectionReason.';
      case 'notificationMessageTripCancellationRejected':
        final rejectionReason = params['rejectionReason'] ?? 'No reason provided';
        return 'The cancellation request for trip $tripCode has been rejected. Reason: $rejectionReason.';
      default:
        return null;
    }  }

  /// Indonesian title translations (Bahasa Indonesia)
  static String? _getIndonesianTitle(String titleKey, String tripCode) {
    switch (titleKey) {
      case 'notificationTripAssigned':
        return 'Perjalanan Ditugaskan: $tripCode';
      case 'notificationNewTripAssignment':
        return 'Penugasan Perjalanan Baru: $tripCode';
      case 'notificationTripApproved':
        return 'Perjalanan Disetujui: $tripCode';
      case 'notificationTripRejected':
        return 'Perjalanan Ditolak: $tripCode';
      case 'notificationTripCompleted':
        return 'Perjalanan Selesai: $tripCode';
      case 'notificationTripStatusUpdate':
        return 'Update Status Perjalanan: $tripCode';
      case 'notificationTripRated':
        return 'Perjalanan Dinilai: $tripCode';
      case 'notificationTripReturnedToRequest':
        return 'Perjalanan Dikembalikan ke Permintaan: $tripCode';
      case 'notificationTripDeleted':
        return 'Perjalanan Dihapus: $tripCode';
      case 'notificationBackToBaseTripCompleted':
        return 'Perjalanan Kembali ke Base Selesai: $tripCode';
      case 'notificationTripAssignedToOnlineTaxi':
        return 'Perjalanan Ditugaskan ke Taksi Online: $tripCode';
      case 'notificationOnlineTaxiTripCompleted':
        return 'Perjalanan Taksi Online Selesai: $tripCode';
      case 'notificationBackToBaseTripCreated':
        return 'Perjalanan Kembali ke Base Dibuat: $tripCode';
      case 'notificationTripChangesApproved':
        return 'Perubahan Perjalanan Disetujui: $tripCode';
      case 'notificationNewTripRequest':
        return 'Permintaan Perjalanan Baru: $tripCode';
      case 'notificationTripEditRequest':
        return 'Permintaan Edit Perjalanan: $tripCode';      case 'notificationTripCancellationRequest':
        return 'Permintaan Pembatalan Perjalanan: $tripCode';
      case 'notificationTripStarted':
        return 'Perjalanan Dimulai: $tripCode';      case 'notificationDriverConfirmedTrip':
        return 'Sopir Konfirmasi Perjalanan: $tripCode';
      case 'notificationDriverRejectedTrip':
        return 'Sopir Menolak Perjalanan: $tripCode';
      case 'notificationTripChangesRejected':
        return 'Perubahan Perjalanan Ditolak: $tripCode';
      case 'notificationTripCancellationRejected':
        return 'Pembatalan Perjalanan Ditolak: $tripCode';
      default:
        return null;
    }
  }

  /// Indonesian message translations (Bahasa Indonesia)
  static String? _getIndonesianMessage(String messageKey, Map<String, dynamic> params) {
    final tripCode = params['tripCode'] ?? '';
    final date = params['date'] ?? '';
    final time = params['time'] ?? '';
    final fromDestination = params['fromDestination'] ?? '';
    final toDestination = params['toDestination'] ?? '';
    
    switch (messageKey) {
      case 'notificationMessageTripAssignedToDriver':
        return 'Perjalanan Anda $tripCode telah ditugaskan ke driver dan dijadwalkan pada $date pukul $time.';
      case 'notificationMessageDriverAssignedToTrip':
        return 'Anda telah ditugaskan ke perjalanan $tripCode dari $fromDestination ke $toDestination pada $date pukul $time.';
      case 'notificationMessageTripApproved':
        return 'Perjalanan Anda $tripCode dari $fromDestination ke $toDestination telah disetujui.';
      case 'notificationMessageTripRejected':
        final rejectionReason = params['rejectionReason'] ?? 'Tidak ada alasan yang diberikan';
        return 'Perjalanan Anda $tripCode telah ditolak. Alasan: $rejectionReason.';
      case 'notificationMessageTripRejectedNoReason':
        return 'Perjalanan Anda $tripCode telah ditolak. Alasan: Tidak ada alasan yang diberikan.';
      case 'notificationMessageTripStatusChanged':
        final oldStatus = params['oldStatus'] ?? '';
        final newStatus = params['newStatus'] ?? '';
        return 'Status perjalanan Anda $tripCode telah berubah dari $oldStatus menjadi $newStatus.';
      case 'notificationMessageDriverTripStatusChanged':
        final oldStatus = params['oldStatus'] ?? '';
        final newStatus = params['newStatus'] ?? '';
        return 'Status perjalanan $tripCode telah berubah dari $oldStatus menjadi $newStatus.';
      case 'notificationMessageTripRated':
        final rating = params['rating'] ?? '';
        final ratingMessage = params['ratingMessage'] ?? '';
        return 'Perjalanan Anda $tripCode telah dinilai $rating bintang oleh penumpang. $ratingMessage';
      case 'notificationMessageTripRatedNoMessage':
        final rating = params['rating'] ?? '';
        return 'Perjalanan Anda $tripCode telah dinilai $rating bintang oleh penumpang.';
      case 'notificationMessageTripReturnedToRequestor':
        return 'Perjalanan Anda $tripCode dari $fromDestination ke $toDestination telah dikembalikan ke status PERMINTAAN oleh manajer perjalanan.';
      case 'notificationMessageTripReturnedToDriver':
        return 'Perjalanan $tripCode dari $fromDestination ke $toDestination telah dikembalikan ke status PERMINTAAN oleh manajer perjalanan.';
      case 'notificationMessageTripDeletedRequestor':
        return 'Perjalanan Anda $tripCode dari $fromDestination ke $toDestination telah dihapus oleh manajer perjalanan.';
      case 'notificationMessageTripDeletedDriver':
        return 'Perjalanan $tripCode dari $fromDestination ke $toDestination telah dihapus oleh manajer perjalanan.';
      case 'notificationMessageTripCompletedRequestor':
        return 'Perjalanan Anda $tripCode telah berhasil diselesaikan. Terima kasih telah menggunakan layanan kami!';
      case 'notificationMessageTripCompletedManager':
        return 'Perjalanan $tripCode telah ditandai selesai oleh driver.';
      case 'notificationMessageBackToBaseTripCompletedRequestor':
        return 'Perjalanan kembali ke base Anda $tripCode telah berhasil diselesaikan.';
      case 'notificationMessageBackToBaseTripCompletedManager':
        return 'Perjalanan kembali ke base $tripCode telah selesai.';
      case 'notificationMessageTripAssignedToOnlineTaxi':
        return 'Perjalanan Anda $tripCode dari $fromDestination ke $toDestination telah ditugaskan ke layanan taksi online.';
      case 'notificationMessageOnlineTaxiTripCompletedRequestor':
        return 'Perjalanan taksi online Anda $tripCode telah berhasil diselesaikan. Terima kasih telah menggunakan layanan kami!';
      case 'notificationMessageOnlineTaxiTripCompletedManager':
        return 'Perjalanan taksi online $tripCode telah ditandai selesai oleh peminta.';
      case 'notificationMessageBackToBaseTripCreated':
        final driverName = params['driverName'] ?? 'Driver Tidak Dikenal';
        return '$driverName telah membuat perjalanan "Kembali ke Base" $tripCode dari $fromDestination ke $toDestination pada $date pukul $time.';
      case 'notificationMessageTripChangesApproved':
        return 'Perubahan Anda pada perjalanan $tripCode dari $fromDestination ke $toDestination telah disetujui. Perjalanan sekarang kembali ke status PERMINTAAN.';
      case 'notificationMessageNewTripRequest':
        final requestorName = params['requestorName'] ?? 'Pengguna';
        return '$requestorName telah meminta perjalanan baru $tripCode dari $fromDestination ke $toDestination pada $date pukul $time.';
      case 'notificationMessageTripEditRequest':
        final requestorName = params['requestorName'] ?? 'Pengguna';
        return '$requestorName telah meminta perubahan pada perjalanan $tripCode dari $fromDestination ke $toDestination pada $date pukul $time.';      case 'notificationMessageTripCancellationRequest':
        final requestorName = params['requestorName'] ?? 'Pengguna';
        return '$requestorName telah meminta pembatalan perjalanan $tripCode dari $fromDestination ke $toDestination pada $date pukul $time.';
      case 'notificationMessageTripStarted':
        return 'Perjalanan $tripCode telah dimulai oleh sopir.';      case 'notificationMessageDriverConfirmedTrip':
        final driverName = params['driverName'] ?? 'Sopir Tidak Dikenal';
        // Only use generic terms when we truly don't have a specific driver name
        final displayDriverName = (driverName == 'The driver' || driverName == 'Driver' || driverName == 'Unknown Driver' || driverName == 'Sopir Tidak Dikenal') 
            ? 'Sopir' 
            : driverName;
        return '$displayDriverName telah mengkonfirmasi perjalanan $tripCode.';      case 'notificationMessageDriverRejectedTrip':
        final driverName = params['driverName'] ?? 'Sopir';
        final rejectionReason = params['rejectionReason'] ?? 'Tidak ada alasan yang diberikan';
        // Only use generic terms when we truly don't have a specific driver name
        final displayDriverName = (driverName == 'The driver' || driverName == 'Driver' || driverName == 'Unknown Driver' || driverName == 'Sopir') 
            ? 'Sopir' 
            : driverName;
        return '$displayDriverName telah menolak perjalanan $tripCode. Alasan: $rejectionReason.';
      case 'notificationMessageDriverRejectedTripGeneric':
        final rejectionReason = params['rejectionReason'] ?? 'Tidak ada alasan yang diberikan';
        return 'Sopir telah menolak perjalanan $tripCode. Alasan: $rejectionReason.';
      case 'notificationMessageTripChangesRejected':
        final rejectionReason = params['rejectionReason'] ?? 'Tidak ada alasan yang diberikan';
        return 'Permintaan perubahan untuk perjalanan $tripCode telah ditolak. Alasan: $rejectionReason.';
      case 'notificationMessageTripCancellationRejected':
        final rejectionReason = params['rejectionReason'] ?? 'Tidak ada alasan yang diberikan';
        return 'Permintaan pembatalan untuk perjalanan $tripCode telah ditolak. Alasan: $rejectionReason.';
      default:
        return null;
    }
  }

  /// Spanish title translations
  static String? _getSpanishTitle(String titleKey, String tripCode) {
    switch (titleKey) {
      case 'notificationTripAssigned':
        return 'Viaje Asignado: $tripCode';
      case 'notificationNewTripAssignment':
        return 'Nueva Asignación de Viaje: $tripCode';
      case 'notificationTripApproved':
        return 'Viaje Aprobado: $tripCode';
      case 'notificationTripRejected':
        return 'Viaje Rechazado: $tripCode';
      case 'notificationTripCompleted':
        return 'Viaje Completado: $tripCode';
      case 'notificationBackToBaseTripCreated':
        return 'Viaje de Regreso Creado: $tripCode';
      // Add more Spanish titles as needed
      default:
        return null;
    }
  }

  /// Spanish message translations
  static String? _getSpanishMessage(String messageKey, Map<String, dynamic> params) {
    final tripCode = params['tripCode'] ?? '';
    final date = params['date'] ?? '';
    final time = params['time'] ?? '';
    final fromDestination = params['fromDestination'] ?? '';
    final toDestination = params['toDestination'] ?? '';
    
    switch (messageKey) {
      case 'notificationMessageDriverAssignedToTrip':
        return 'Has sido asignado al viaje $tripCode de $fromDestination a $toDestination el $date a las $time.';
      case 'notificationMessageTripApproved':
        return 'Tu viaje $tripCode de $fromDestination a $toDestination ha sido aprobado.';
      case 'notificationMessageTripRejected':
        final rejectionReason = params['rejectionReason'] ?? 'Sin razón proporcionada';
        return 'Tu viaje $tripCode ha sido rechazado. Razón: $rejectionReason.';
      // Add more Spanish messages as needed
      default:
        return null;
    }
  }

  /// French title translations
  static String? _getFrenchTitle(String titleKey, String tripCode) {
    switch (titleKey) {
      case 'notificationTripAssigned':
        return 'Voyage Assigné: $tripCode';
      case 'notificationNewTripAssignment':
        return 'Nouvelle Assignation de Voyage: $tripCode';
      case 'notificationTripApproved':
        return 'Voyage Approuvé: $tripCode';
      case 'notificationTripRejected':
        return 'Voyage Rejeté: $tripCode';
      case 'notificationTripCompleted':
        return 'Voyage Terminé: $tripCode';
      case 'notificationBackToBaseTripCreated':
        return 'Voyage de Retour Créé: $tripCode';
      // Add more French titles as needed
      default:
        return null;
    }
  }

  /// French message translations
  static String? _getFrenchMessage(String messageKey, Map<String, dynamic> params) {
    final tripCode = params['tripCode'] ?? '';
    final date = params['date'] ?? '';
    final time = params['time'] ?? '';
    final fromDestination = params['fromDestination'] ?? '';
    final toDestination = params['toDestination'] ?? '';
    
    switch (messageKey) {
      case 'notificationMessageDriverAssignedToTrip':
        return 'Vous avez été assigné au voyage $tripCode de $fromDestination à $toDestination le $date à $time.';
      case 'notificationMessageTripApproved':
        return 'Votre voyage $tripCode de $fromDestination à $toDestination a été approuvé.';
      case 'notificationMessageTripRejected':
        final rejectionReason = params['rejectionReason'] ?? 'Aucune raison fournie';
        return 'Votre voyage $tripCode a été rejeté. Raison: $rejectionReason.';
      // Add more French messages as needed
      default:
        return null;
    }
  }

  /// Get localized title based on key and parameters
  static String? _getLocalizedTitle(AppLocalizations localizations, String titleKey, Map<String, dynamic> params) {
    try {
      switch (titleKey) {
        case 'notificationTripAssigned':
          return localizations.notificationTripAssigned(params['tripCode'] ?? '');
        case 'notificationNewTripAssignment':
          return localizations.notificationNewTripAssignment(params['tripCode'] ?? '');
        case 'notificationTripApproved':
          return localizations.notificationTripApproved(params['tripCode'] ?? '');
        case 'notificationTripRejected':
          return localizations.notificationTripRejected(params['tripCode'] ?? '');
        case 'notificationTripCompleted':
          return localizations.notificationTripCompleted(params['tripCode'] ?? '');
        case 'notificationTripStatusUpdate':
          return localizations.notificationTripStatusUpdate(params['tripCode'] ?? '');
        case 'notificationTripRated':
          return localizations.notificationTripRated(params['tripCode'] ?? '');
        case 'notificationTripReturnedToRequest':
          return localizations.notificationTripReturnedToRequest(params['tripCode'] ?? '');
        case 'notificationTripDeleted':
          return localizations.notificationTripDeleted(params['tripCode'] ?? '');
        case 'notificationBackToBaseTripCompleted':
          return localizations.notificationBackToBaseTripCompleted(params['tripCode'] ?? '');
        case 'notificationTripAssignedToOnlineTaxi':
          return localizations.notificationTripAssignedToOnlineTaxi(params['tripCode'] ?? '');
        case 'notificationOnlineTaxiTripCompleted':
          return localizations.notificationOnlineTaxiTripCompleted(params['tripCode'] ?? '');
        case 'notificationBackToBaseTripCreated':
          return localizations.notificationBackToBaseTripCreated(params['tripCode'] ?? '');
        case 'notificationTripChangesApproved':
          return localizations.notificationTripChangesApproved(params['tripCode'] ?? '');
        case 'notificationNewTripRequest':
          return localizations.notificationNewTripRequest(params['tripCode'] ?? '');
        case 'notificationTripEditRequest':
          return localizations.notificationTripEditRequest(params['tripCode'] ?? '');        case 'notificationTripCancellationRequest':
          return localizations.notificationTripCancellationRequest(params['tripCode'] ?? '');
        case 'notificationTripStarted':
          return localizations.notificationTripStarted(params['tripCode'] ?? '');
        case 'notificationDriverConfirmedTrip':
          return localizations.notificationDriverConfirmedTrip(params['tripCode'] ?? '');
        case 'notificationDriverRejectedTrip':
          return localizations.notificationDriverRejectedTrip(params['tripCode'] ?? '');
        case 'notificationTripChangesRejected':
          return localizations.notificationTripChangesRejected(params['tripCode'] ?? '');
        case 'notificationTripCancellationRejected':
          return localizations.notificationTripCancellationRejected(params['tripCode'] ?? '');
        default:
          return null;
      }
    } catch (e) {
      print('Error getting localized title for key $titleKey: $e');
      return null;
    }
  }

  /// Get localized message based on key and parameters
  static String? _getLocalizedMessage(AppLocalizations localizations, String messageKey, Map<String, dynamic> params) {
    try {
      switch (messageKey) {
        case 'notificationMessageTripAssignedToDriver':
          return localizations.notificationMessageTripAssignedToDriver(
            params['tripCode'] ?? '',
            params['date'] ?? '',
            params['time'] ?? '',
          );
        case 'notificationMessageDriverAssignedToTrip':
          return localizations.notificationMessageDriverAssignedToTrip(
            params['tripCode'] ?? '',
            params['fromDestination'] ?? '',
            params['toDestination'] ?? '',
            params['date'] ?? '',
            params['time'] ?? '',
          );
        case 'notificationMessageTripApproved':
          return localizations.notificationMessageTripApproved(
            params['tripCode'] ?? '',
            params['fromDestination'] ?? '',
            params['toDestination'] ?? '',
          );
        case 'notificationMessageTripRejected':
          final rejectionReason = params['rejectionReason'] ?? '';
          final reasonText = rejectionReason == 'noReasonProvided' 
              ? localizations.noReasonProvided 
              : rejectionReason;
          return localizations.notificationMessageTripRejected(
            params['tripCode'] ?? '',
            reasonText,
          );
        case 'notificationMessageTripRejectedNoReason':
          return localizations.notificationMessageTripRejectedNoReason(
            params['tripCode'] ?? '',
          );
        case 'notificationMessageTripStatusChanged':
          return localizations.notificationMessageTripStatusChanged(
            params['tripCode'] ?? '',
            params['oldStatus'] ?? '',
            params['newStatus'] ?? '',
          );
        case 'notificationMessageDriverTripStatusChanged':
          return localizations.notificationMessageDriverTripStatusChanged(
            params['tripCode'] ?? '',
            params['oldStatus'] ?? '',
            params['newStatus'] ?? '',
          );
        case 'notificationMessageTripRated':
          return localizations.notificationMessageTripRated(
            params['tripCode'] ?? '',
            params['rating'] ?? '',
            params['ratingMessage'] ?? '',
          );
        case 'notificationMessageTripRatedNoMessage':
          return localizations.notificationMessageTripRatedNoMessage(
            params['tripCode'] ?? '',
            params['rating'] ?? '',
          );
        case 'notificationMessageTripReturnedToRequestor':
          return localizations.notificationMessageTripReturnedToRequestor(
            params['tripCode'] ?? '',
            params['fromDestination'] ?? '',
            params['toDestination'] ?? '',
          );
        case 'notificationMessageTripReturnedToDriver':
          return localizations.notificationMessageTripReturnedToDriver(
            params['tripCode'] ?? '',
            params['fromDestination'] ?? '',
            params['toDestination'] ?? '',
          );
        case 'notificationMessageTripDeletedRequestor':
          return localizations.notificationMessageTripDeletedRequestor(
            params['tripCode'] ?? '',
            params['fromDestination'] ?? '',
            params['toDestination'] ?? '',
          );
        case 'notificationMessageTripDeletedDriver':
          return localizations.notificationMessageTripDeletedDriver(
            params['tripCode'] ?? '',
            params['fromDestination'] ?? '',
            params['toDestination'] ?? '',
          );
        case 'notificationMessageTripCompletedRequestor':
          return localizations.notificationMessageTripCompletedRequestor(
            params['tripCode'] ?? '',
          );
        case 'notificationMessageTripCompletedManager':
          return localizations.notificationMessageTripCompletedManager(
            params['tripCode'] ?? '',
          );
        case 'notificationMessageBackToBaseTripCompletedRequestor':
          return localizations.notificationMessageBackToBaseTripCompletedRequestor(
            params['tripCode'] ?? '',
          );
        case 'notificationMessageBackToBaseTripCompletedManager':
          return localizations.notificationMessageBackToBaseTripCompletedManager(
            params['tripCode'] ?? '',
          );
        case 'notificationMessageTripAssignedToOnlineTaxi':
          return localizations.notificationMessageTripAssignedToOnlineTaxi(
            params['tripCode'] ?? '',
            params['fromDestination'] ?? '',
            params['toDestination'] ?? '',
          );
        case 'notificationMessageOnlineTaxiTripCompletedRequestor':
          return localizations.notificationMessageOnlineTaxiTripCompletedRequestor(
            params['tripCode'] ?? '',
          );
        case 'notificationMessageOnlineTaxiTripCompletedManager':
          return localizations.notificationMessageOnlineTaxiTripCompletedManager(
            params['tripCode'] ?? '',
          );
        case 'notificationMessageBackToBaseTripCreated':
          final driverName = params['driverName'] ?? '';
          final driverDisplayName = driverName == 'unknownDriver' 
              ? localizations.unknownDriver 
              : driverName;
          return localizations.notificationMessageBackToBaseTripCreated(
            driverDisplayName,
            params['tripCode'] ?? '',
            params['fromDestination'] ?? '',
            params['toDestination'] ?? '',
            params['date'] ?? '',
            params['time'] ?? '',
          );
        case 'notificationMessageTripChangesApproved':
          return localizations.notificationMessageTripChangesApproved(
            params['tripCode'] ?? '',
            params['fromDestination'] ?? '',
            params['toDestination'] ?? '',
          );
        case 'notificationMessageNewTripRequest':
          final requestorName = params['requestorName'] ?? '';
          final requestorDisplayName = requestorName == 'user' 
              ? localizations.user 
              : requestorName;
          return localizations.notificationMessageNewTripRequest(
            requestorDisplayName,
            params['tripCode'] ?? '',
            params['fromDestination'] ?? '',
            params['toDestination'] ?? '',
            params['date'] ?? '',
            params['time'] ?? '',
          );
        case 'notificationMessageTripEditRequest':
          final requestorName = params['requestorName'] ?? '';
          final requestorDisplayName = requestorName == 'user' 
              ? localizations.user 
              : requestorName;
          return localizations.notificationMessageTripEditRequest(
            requestorDisplayName,
            params['tripCode'] ?? '',
            params['fromDestination'] ?? '',
            params['toDestination'] ?? '',
            params['date'] ?? '',
            params['time'] ?? '',
          );
        case 'notificationMessageTripCancellationRequest':
          final requestorName = params['requestorName'] ?? '';
          final requestorDisplayName = requestorName == 'user' 
              ? localizations.user 
              : requestorName;
          return localizations.notificationMessageTripCancellationRequest(
            requestorDisplayName,
            params['tripCode'] ?? '',
            params['fromDestination'] ?? '',
            params['toDestination'] ?? '',            params['date'] ?? '',
            params['time'] ?? '',
          );
        case 'notificationMessageTripStarted':
          return localizations.notificationMessageTripStarted(
            params['tripCode'] ?? '',
          );        case 'notificationMessageDriverConfirmedTrip':
          final driverName = params['driverName'] ?? '';
          // Handle translation of generic driver terms to localized versions
          String driverDisplayName;
          if (driverName == 'unknownDriver') {
            driverDisplayName = localizations.unknownDriver;
          } else if (driverName == 'Driver' || driverName == 'The driver') {
            // For generic "Driver" or "The driver", use "Sopir" for Indonesian consistency
            driverDisplayName = 'Sopir';
          } else {
            driverDisplayName = driverName;
          }
          return localizations.notificationMessageDriverConfirmedTrip(
            driverDisplayName,
            params['tripCode'] ?? '',
          );        case 'notificationMessageDriverRejectedTrip':
          final driverName = params['driverName'] ?? '';
          // Handle translation of generic driver terms to localized versions
          String driverDisplayName;
          if (driverName == 'driver' || driverName == 'Driver' || driverName == 'The driver') {
            // For generic driver terms, use "Sopir" for Indonesian consistency
            driverDisplayName = 'Sopir';
          } else {
            driverDisplayName = driverName;
          }
          final rejectionReason = params['rejectionReason'] ?? '';
          final reasonText = rejectionReason == 'noReasonProvided' 
              ? localizations.noReasonProvided 
              : rejectionReason;
          return localizations.notificationMessageDriverRejectedTrip(
            driverDisplayName,
            params['tripCode'] ?? '',
            reasonText,
          );
        case 'notificationMessageDriverRejectedTripGeneric':
          final rejectionReason = params['rejectionReason'] ?? '';
          final reasonText = rejectionReason == 'noReasonProvided' 
              ? localizations.noReasonProvided 
              : rejectionReason;
          return localizations.notificationMessageDriverRejectedTripGeneric(
            params['tripCode'] ?? '',
            reasonText,
          );
        case 'notificationMessageTripChangesRejected':
          final rejectionReason = params['rejectionReason'] ?? '';
          final reasonText = rejectionReason == 'noReasonProvided' 
              ? localizations.noReasonProvided 
              : rejectionReason;
          return localizations.notificationMessageTripChangesRejected(
            params['tripCode'] ?? '',
            reasonText,
          );
        case 'notificationMessageTripCancellationRejected':
          final rejectionReason = params['rejectionReason'] ?? '';
          final reasonText = rejectionReason == 'noReasonProvided' 
              ? localizations.noReasonProvided 
              : rejectionReason;
          return localizations.notificationMessageTripCancellationRejected(
            params['tripCode'] ?? '',            reasonText,
          );
        default:
          return null;
      }
    } catch (e) {
      print('Error getting localized message for key $messageKey: $e');
      return null;
    }  }

  /// Parse notification content from title and message strings
  /// This is used for push notifications where we only have the title and message
  static Map<String, dynamic>? parseNotificationContent({
    required String title,
    required String message,
  }) {
    try {
      print('⚠️ Parsing push notification content for title: $title');
      print('🔍 Message: $message');
      
      // Use the same pattern matching logic as the private method
      return _parseNotificationFromStrings(title, message);
    } catch (e) {
      print('❌ Error parsing notification content: $e');
      return null;
    }
  }
  /// Extract pattern matching logic into a reusable method
  static Map<String, dynamic>? _parseNotificationFromStrings(String title, String message) {
    // Trip Changes Approved patterns - Check for multiple variations
    if (title.contains('Trip Changes Approved:') || 
        message.contains('changes to trip') && message.contains('have been approved')) {
      
      // Extract trip code from title or message
      RegExpMatch? tripCodeMatch = RegExp(r'Trip Changes Approved: (\w+)').firstMatch(title);
      if (tripCodeMatch == null) {
        tripCodeMatch = RegExp(r'changes to trip (\w+)').firstMatch(message);
      }
      
      if (tripCodeMatch != null) {
        // Extract destinations from message
        final destinationMatch = RegExp(r'trip (\w+) from (.+?) to (.+?) have been approved').firstMatch(message);
        return {
          'titleKey': 'notificationTripChangesApproved',
          'messageKey': 'notificationMessageTripChangesApproved',
          'params': {
            'tripCode': tripCodeMatch.group(1),
            'fromDestination': destinationMatch?.group(2) ?? '',
            'toDestination': destinationMatch?.group(3) ?? '',
          }
        };
      }
    }
    
    // Trip Assigned patterns
    if (title.contains('Trip Assigned:')) {
      final tripCodeMatch = RegExp(r'Trip Assigned: (\w+)').firstMatch(title);
      if (tripCodeMatch != null) {
        // Extract date and time from message if possible
        final dateTimeMatch = RegExp(r'scheduled for (.+?) at (.+?)\.').firstMatch(message);
        return {
          'titleKey': 'notificationTripAssigned',
          'messageKey': 'notificationMessageTripAssignedToDriver',
          'params': {
            'tripCode': tripCodeMatch.group(1),
            'date': dateTimeMatch?.group(1) ?? '',
            'time': dateTimeMatch?.group(2) ?? '',
          }
        };
      }
    }
    
    // Trip Deleted patterns
    if (title.contains('Trip Deleted:') || title.contains('Deleted:') || message.contains('has been deleted by a trip manager')) {
      // Try to extract trip code from title first
      RegExpMatch? tripCodeMatch = RegExp(r'Trip Deleted: (\w+)').firstMatch(title);
      if (tripCodeMatch == null) {
        tripCodeMatch = RegExp(r'trip (\w+)').firstMatch(message);
      }
      
      if (tripCodeMatch != null) {
        // Extract destinations from message
        final destinationMatch = RegExp(r'trip (\w+) from (.+?) to (.+?) has been deleted').firstMatch(message);
        // Determine if it's for requestor or driver based on message content
        final isRequestorMessage = message.contains('Your trip');
        
        return {
          'titleKey': 'notificationTripDeleted',
          'messageKey': isRequestorMessage ? 
            'notificationMessageTripDeletedRequestor' : 
            'notificationMessageTripDeletedDriver',
          'params': {
            'tripCode': tripCodeMatch.group(1),
            'fromDestination': destinationMatch?.group(2) ?? '',
            'toDestination': destinationMatch?.group(3) ?? '',
          }
        };
      }    }
    
    // Driver Confirmed Trip patterns  
    if (title.contains('Driver Confirmed Trip:') || message.contains('has confirmed your trip') || message.contains('has confirmed trip')) {
      // Try to extract trip code from title first
      RegExpMatch? tripCodeMatch = RegExp(r'Driver Confirmed Trip: (\w+)').firstMatch(title);
      if (tripCodeMatch == null) {
        // Try to extract from message
        tripCodeMatch = RegExp(r'trip (\w+)').firstMatch(message);
      }
      
      if (tripCodeMatch != null) {
        // Extract driver name from message - handle different message formats
        RegExpMatch? driverMatch;
        String? driverName;
        
        // Try pattern 1: "DriverName has confirmed trip TRIPCODE"
        driverMatch = RegExp(r'(.+?) has confirmed trip (\w+)\.?').firstMatch(message);
        if (driverMatch != null) {
          final extractedName = driverMatch.group(1);
          // Check if it's a generic reference like "The driver"
          if (extractedName == 'The driver') {
            driverName = 'Driver'; // Will be converted to "Sopir" in Indonesian
          } else {
            driverName = extractedName;
          }
        } else {
          // Try pattern 2: "The driver has confirmed your trip TRIPCODE"
          driverMatch = RegExp(r'The driver has confirmed your trip (\w+)').firstMatch(message);
          if (driverMatch != null) {
            driverName = 'Driver'; // Generic driver name - will be converted to "Sopir" in Indonesian
          }
        }
        
        print('✅ Matched Driver Confirmed Trip pattern with tripCode: ${tripCodeMatch.group(1)}, driverName: $driverName');
        return {
          'titleKey': 'notificationDriverConfirmedTrip',
          'messageKey': 'notificationMessageDriverConfirmedTrip',
          'params': {
            'tripCode': tripCodeMatch.group(1),
            'driverName': driverName ?? 'Driver',
          }
        };
      }
    }
    
    // Driver Rejected Trip patterns  
    if (title.contains('Driver Rejected Trip:')) {
      final tripCodeMatch = RegExp(r'Driver Rejected Trip: (\w+)').firstMatch(title);
      if (tripCodeMatch != null) {
        // Extract driver name and reason
        RegExpMatch? driverNameMatch = RegExp(r'(.+?) has rejected trip (\w+)').firstMatch(message);
        String? driverName;
        String messageKey;
        
        if (driverNameMatch != null) {
          final extractedName = driverNameMatch.group(1);
          if (extractedName == 'The driver') {
            messageKey = 'notificationMessageDriverRejectedTripGeneric';
          } else {
            driverName = extractedName;
            messageKey = 'notificationMessageDriverRejectedTrip';
          }
        } else {
          messageKey = 'notificationMessageDriverRejectedTripGeneric';
        }
        
        final reasonMatch = RegExp(r'Reason: (.+?)\.?$').firstMatch(message);
        final rejectionReason = reasonMatch?.group(1) ?? 'No reason provided';
        
        return {
          'titleKey': 'notificationDriverRejectedTrip',
          'messageKey': messageKey,
          'params': {
            'tripCode': tripCodeMatch.group(1),
            'driverName': driverName ?? 'Driver',
            'rejectionReason': rejectionReason,
          }
        };
      }    }
    
    // Back to Base Trip Created patterns
    if (title.contains('Back to Base Trip Created:')) {
      final tripCodeMatch = RegExp(r'Back to Base Trip Created: (\w+)').firstMatch(title);
      if (tripCodeMatch != null) {
        // Extract driver name, destinations, date and time from message
        // Pattern: "DRIVERNAME has created a "Back to Base" trip TRIPCODE from ORIGIN to DESTINATION on DATE at TIME."
        final detailsMatch = RegExp(r'(.+?) has created a "Back to Base" trip (\w+) from (.+?) to (.+?) on (.+?) at (.+?)\.').firstMatch(message);
        if (detailsMatch != null) {
          print('✅ Matched Back to Base Trip Created pattern (push) with tripCode: ${tripCodeMatch.group(1)}');
          return {
            'titleKey': 'notificationBackToBaseTripCreated',
            'messageKey': 'notificationMessageBackToBaseTripCreated',
            'params': {
              'tripCode': tripCodeMatch.group(1),
              'driverName': detailsMatch.group(1) ?? 'Unknown Driver',
              'fromDestination': detailsMatch.group(3) ?? '',
              'toDestination': detailsMatch.group(4) ?? '',
              'date': detailsMatch.group(5) ?? '',
              'time': detailsMatch.group(6) ?? '',
            }
          };
        }
      }
    }
    
    // Add more pattern matching as needed...
    
    return null;
  }  /// Test method to verify pattern matching works for common notification types
  static void testPatternMatching() {
    print('🧪 Testing notification pattern matching...');
    
    // Test Trip Changes Approved
    final testResult1 = parseNotificationContent(
      title: 'Trip Changes Approved: ORD0234',
      message: 'Your changes to trip ORD0234 from yifiyf to Custom Location have been approved. The trip is now back to REQUEST status.',
    );
    print('✅ Trip Changes Approved test result: $testResult1');
    
    // Test Trip Deleted
    final testResult2 = parseNotificationContent(
      title: 'Trip Deleted: ORD0235',
      message: 'Your trip ORD0235 from Office to Home has been deleted by a trip manager.',
    );
    print('✅ Trip Deleted test result: $testResult2');
    
    // Test Driver Rejected Trip
    final testResult3 = parseNotificationContent(
      title: 'Driver Rejected Trip: ORD0236',
      message: 'John Doe has rejected trip ORD0236. Reason: Vehicle unavailable.',
    );
    print('✅ Driver Rejected Trip test result: $testResult3');
    
    // Test Driver Confirmed Trip
    final testResult4 = parseNotificationContent(
      title: 'Driver Confirmed Trip: ORD0271',
      message: 'The driver has confirmed your trip ORD0271 from Custom Location to Custom Location.',
    );
    print('✅ Driver Confirmed Trip test result: $testResult4');
    
    // Test Back to Base Trip Created
    final testResult5 = parseNotificationContent(
      title: 'Back to Base Trip Created: ORD0269',
      message: 'TAKUMI has created a "Back to Base" trip ORD0269 from CURRENT DRIVER LOCATION to BASE on Fri Jun 20 2025 00:00:00 GMT+0700 (Indochina Time) at 09:38:00.',
    );
    print('✅ Back to Base Trip Created test result: $testResult5');
  }
  /// Parse localization data from notification
  /// This will attempt to extract localization data from the notification
  /// In the future, when the backend is updated, this will come directly from a field
  static Map<String, dynamic>? _parseLocalizationDataFromNotification(app_notification.Notification notification) {
    try {
      // First, try to use localization_data field if available
      if (notification.localizationData != null) {
        print('✅ Using localization_data from notification: ${notification.localizationData}');
        return notification.localizationData;
      }
        print('⚠️ No localization_data available, falling back to pattern matching for notification: ${notification.title}');
      print('🔍 Notification message: ${notification.message}');
      
      // Fallback to pattern matching for notifications without localization_data
      // This provides backward compatibility with old notifications
      
      final title = notification.title;
      final message = notification.message;
      
      // Trip Assigned patterns
      if (title.contains('Trip Assigned:')) {
        final tripCodeMatch = RegExp(r'Trip Assigned: (\w+)').firstMatch(title);
        if (tripCodeMatch != null) {
          // Extract date and time from message if possible
          final dateTimeMatch = RegExp(r'scheduled for (.+?) at (.+?)\.').firstMatch(message);
          return {
            'titleKey': 'notificationTripAssigned',
            'messageKey': 'notificationMessageTripAssignedToDriver',
            'params': {
              'tripCode': tripCodeMatch.group(1),
              'date': dateTimeMatch?.group(1) ?? '',
              'time': dateTimeMatch?.group(2) ?? '',
            }
          };
        }
      }
        // New Trip Assignment patterns
      if (title.contains('New Trip Assignment:')) {
        final tripCodeMatch = RegExp(r'New Trip Assignment: (\w+)').firstMatch(title);
        if (tripCodeMatch != null) {
          // Extract destinations, date and time from message - handle different formats
          RegExpMatch? detailsMatch;
          
          // Try pattern 1: "You have been assigned to trip TRIPCODE from ORIGIN to DESTINATION on DATE at TIME."
          detailsMatch = RegExp(r'(?:You have been assigned to )?trip (\w+) from (.+?) to (.+?) on (.+?) at (.+?)\.?').firstMatch(message);
          
          if (detailsMatch == null) {
            // Try pattern 2: More flexible extraction
            final tripInMessage = RegExp(r'trip (\w+)').firstMatch(message);
            final fromTo = RegExp(r'from (.+?) to (.+?)').firstMatch(message);
            final dateTime = RegExp(r'on (.+?) at (.+?)\.?').firstMatch(message);
            
            if (tripInMessage != null && fromTo != null) {
              print('✅ Matched New Trip Assignment pattern (flexible) with tripCode: ${tripCodeMatch.group(1)}');
              return {
                'titleKey': 'notificationNewTripAssignment',
                'messageKey': 'notificationMessageDriverAssignedToTrip',
                'params': {
                  'tripCode': tripCodeMatch.group(1),
                  'fromDestination': fromTo.group(1) ?? '',
                  'toDestination': fromTo.group(2) ?? '',
                  'date': dateTime?.group(1) ?? '',
                  'time': dateTime?.group(2) ?? '',
                }
              };
            }
          } else {
            print('✅ Matched New Trip Assignment pattern with tripCode: ${tripCodeMatch.group(1)}');
            return {
              'titleKey': 'notificationNewTripAssignment',
              'messageKey': 'notificationMessageDriverAssignedToTrip',
              'params': {
                'tripCode': tripCodeMatch.group(1),
                'fromDestination': detailsMatch.group(2) ?? '',
                'toDestination': detailsMatch.group(3) ?? '',
                'date': detailsMatch.group(4) ?? '',
                'time': detailsMatch.group(5) ?? '',
              }
            };
          }
        }
      }
        // Trip Approved patterns
      if (title.contains('Trip Approved:')) {
        final tripCodeMatch = RegExp(r'Trip Approved: (\w+)').firstMatch(title);
        if (tripCodeMatch != null) {
          // Extract destinations from message - handle different formats
          RegExpMatch? destinationMatch;
          
          // Try pattern 1: "Your trip TRIPCODE from ORIGIN to DESTINATION has been approved"
          destinationMatch = RegExp(r'(?:Your )?trip (\w+) from (.+?) to (.+?) has been approved').firstMatch(message);
          
          if (destinationMatch == null) {
            // Try pattern 2: More flexible extraction of from/to
            final fromTo = RegExp(r'from (.+?) to (.+?)').firstMatch(message);
            if (fromTo != null) {
              print('✅ Matched Trip Approved pattern (flexible) with tripCode: ${tripCodeMatch.group(1)}');
              return {
                'titleKey': 'notificationTripApproved',
                'messageKey': 'notificationMessageTripApproved',
                'params': {
                  'tripCode': tripCodeMatch.group(1),
                  'fromDestination': fromTo.group(1) ?? '',
                  'toDestination': fromTo.group(2) ?? '',
                }
              };
            }
          } else {
            print('✅ Matched Trip Approved pattern with tripCode: ${tripCodeMatch.group(1)}');
            return {
              'titleKey': 'notificationTripApproved',
              'messageKey': 'notificationMessageTripApproved',
              'params': {
                'tripCode': tripCodeMatch.group(1),
                'fromDestination': destinationMatch.group(2) ?? '',
                'toDestination': destinationMatch.group(3) ?? '',
              }
            };
          }
        }
      }
      
      // Trip Rejected patterns
      if (title.contains('Trip Rejected:')) {
        final tripCodeMatch = RegExp(r'Trip Rejected: (\w+)').firstMatch(title);
        if (tripCodeMatch != null) {
          // Extract rejection reason from message
          final reasonMatch = RegExp(r'Reason: (.+?)\.').firstMatch(message);
          final rejectionReason = reasonMatch?.group(1) ?? 'No reason provided';
          
          return {
            'titleKey': 'notificationTripRejected',
            'messageKey': rejectionReason == 'No reason provided' ? 
              'notificationMessageTripRejectedNoReason' : 
              'notificationMessageTripRejected',
            'params': {
              'tripCode': tripCodeMatch.group(1),
              'rejectionReason': rejectionReason,
            }
          };
        }
      }
      
      // Trip Assigned to Online Taxi patterns
      if (title.contains('Trip Assigned to Online Taxi:')) {
        final tripCodeMatch = RegExp(r'Trip Assigned to Online Taxi: (\w+)').firstMatch(title);
        if (tripCodeMatch != null) {
          // Extract destinations from message
          final destinationMatch = RegExp(r'trip (\w+) from (.+?) to (.+?) has been assigned').firstMatch(message);
          return {
            'titleKey': 'notificationTripAssignedToOnlineTaxi',
            'messageKey': 'notificationMessageTripAssignedToOnlineTaxi',
            'params': {
              'tripCode': tripCodeMatch.group(1),
              'fromDestination': destinationMatch?.group(2) ?? '',
              'toDestination': destinationMatch?.group(3) ?? '',
            }
          };
        }
      }
      
      // Trip Completed patterns
      if (title.contains('Trip Completed:')) {
        final tripCodeMatch = RegExp(r'Trip Completed: (\w+)').firstMatch(title);
        if (tripCodeMatch != null) {
          // Determine if it's for requestor or manager based on message content
          final isRequestorMessage = message.contains('Thank you for using our service');
          return {
            'titleKey': 'notificationTripCompleted',
            'messageKey': isRequestorMessage ? 
              'notificationMessageTripCompletedRequestor' : 
              'notificationMessageTripCompletedManager',
            'params': {
              'tripCode': tripCodeMatch.group(1),
            }
          };
        }
      }
      
      // Trip Returned to Request patterns
      if (title.contains('Trip Returned to Request:')) {
        final tripCodeMatch = RegExp(r'Trip Returned to Request: (\w+)').firstMatch(title);
        if (tripCodeMatch != null) {
          // Determine if it's for requestor or driver based on message content
          final isRequestorMessage = message.contains('Your trip');
          final destinationMatch = RegExp(r'trip (\w+) from (.+?) to (.+?) has been returned').firstMatch(message);
          
          return {
            'titleKey': 'notificationTripReturnedToRequest',
            'messageKey': isRequestorMessage ? 
              'notificationMessageTripReturnedToRequestor' : 
              'notificationMessageTripReturnedToDriver',
            'params': {
              'tripCode': tripCodeMatch.group(1),
              'fromDestination': destinationMatch?.group(2) ?? '',
              'toDestination': destinationMatch?.group(3) ?? '',
            }
          };        }
      }
      
      // Trip Edit Request patterns
      if (title.contains('Trip Edit Request:')) {
        final tripCodeMatch = RegExp(r'Trip Edit Request: (\w+)').firstMatch(title);
        if (tripCodeMatch != null) {
          // Extract user name, destinations, date and time from message
          final detailsMatch = RegExp(r'(.+?) has requested changes to trip (\w+) from (.+?) to (.+?) on (.+?) at (.+?)\.').firstMatch(message);
          return {            'titleKey': 'notificationTripEditRequest',
            'messageKey': 'notificationMessageTripEditRequest',
            'params': {
              'tripCode': tripCodeMatch.group(1),
              'requestorName': detailsMatch?.group(1) ?? 'User',
              'fromDestination': detailsMatch?.group(3) ?? '',
              'toDestination': detailsMatch?.group(4) ?? '',
              'date': detailsMatch?.group(5) ?? '',
              'time': detailsMatch?.group(6) ?? '',
            }
          };
        }
      }
      
      // Trip Cancellation Request patterns
      if (title.contains('Trip Cancellation Request:')) {
        final tripCodeMatch = RegExp(r'Trip Cancellation Request: (\w+)').firstMatch(title);
        if (tripCodeMatch != null) {
          // Extract user name, destinations, date and time from message
          final detailsMatch = RegExp(r'(.+?) has requested to cancel trip (\w+) from (.+?) to (.+?) on (.+?) at (.+?)\.').firstMatch(message);
          return {            'titleKey': 'notificationTripCancellationRequest',
            'messageKey': 'notificationMessageTripCancellationRequest',
            'params': {
              'tripCode': tripCodeMatch.group(1),
              'requestorName': detailsMatch?.group(1) ?? 'User',
              'fromDestination': detailsMatch?.group(3) ?? '',
              'toDestination': detailsMatch?.group(4) ?? '',
              'date': detailsMatch?.group(5) ?? '',
              'time': detailsMatch?.group(6) ?? '',
            }
          };
        }
      }
        // Trip Started patterns
      if (title.contains('Trip Started:')) {
        final tripCodeMatch = RegExp(r'Trip Started: (\w+)').firstMatch(title);
        if (tripCodeMatch != null) {
          print('✅ Matched Trip Started pattern with tripCode: ${tripCodeMatch.group(1)}');
          return {
            'titleKey': 'notificationTripStarted',
            'messageKey': 'notificationMessageTripStarted',
            'params': {
              'tripCode': tripCodeMatch.group(1),
            }
          };
        }
      }      // Driver Confirmed Trip patterns
      if (title.contains('Driver Confirmed Trip:')) {
        final tripCodeMatch = RegExp(r'Driver Confirmed Trip: (\w+)').firstMatch(title);
        if (tripCodeMatch != null) {
          // Extract driver name from message - handle different message formats
          RegExpMatch? driverMatch;
          String? driverName;
            // Try pattern 1: "DriverName has confirmed trip TRIPCODE"
          driverMatch = RegExp(r'(.+?) has confirmed trip (\w+)\.?').firstMatch(message);
          if (driverMatch != null) {
            driverName = driverMatch.group(1);
          } else {
            // Try pattern 2: "The driver has confirmed your trip TRIPCODE"
            driverMatch = RegExp(r'The driver has confirmed your trip (\w+)').firstMatch(message);
            if (driverMatch != null) {
              driverName = 'Driver'; // Generic driver name when specific name not available
            } else {
              // Try pattern 3: Mixed language patterns like "The driver telah mengkonfirmasi perjalanan TRIPCODE"
              driverMatch = RegExp(r'(.+?) telah mengkonfirmasi perjalanan (\w+)').firstMatch(message);
              if (driverMatch != null) {
                final extractedName = driverMatch.group(1);
                // Check if it's a generic reference like "The driver"
                if (extractedName == 'The driver' || extractedName == 'Driver') {
                  driverName = 'Driver'; // Generic driver name
                } else {
                  driverName = extractedName;
                }
              }
            }
          }
          
          print('✅ Matched Driver Confirmed Trip pattern with tripCode: ${tripCodeMatch.group(1)}, driverName: $driverName');
          return {
            'titleKey': 'notificationDriverConfirmedTrip',
            'messageKey': 'notificationMessageDriverConfirmedTrip',
            'params': {
              'tripCode': tripCodeMatch.group(1),
              'driverName': driverName ?? 'Unknown Driver',
            }
          };
        }      }
      
      // New Trip Request patterns
      if (title.contains('New Trip Request:')) {
        final tripCodeMatch = RegExp(r'New Trip Request: (\w+)').firstMatch(title);
        if (tripCodeMatch != null) {
          // Extract requester name, destinations, date and time from message
          final detailsMatch = RegExp(r'(.+?) has requested a new trip (\w+) from (.+?) to (.+?) on (.+?) at (.+?)\.').firstMatch(message);
          return {            'titleKey': 'notificationNewTripRequest',
            'messageKey': 'notificationMessageNewTripRequest',
            'params': {
              'tripCode': tripCodeMatch.group(1),
              'requestorName': detailsMatch?.group(1) ?? 'User',
              'fromDestination': detailsMatch?.group(3) ?? '',
              'toDestination': detailsMatch?.group(4) ?? '',
              'date': detailsMatch?.group(5) ?? '',
              'time': detailsMatch?.group(6) ?? '',
            }
          };
        }
      }        // Driver Rejected Trip patterns
      if (title.contains('Driver Rejected Trip:')) {
        final tripCodeMatch = RegExp(r'Driver Rejected Trip: (\w+)').firstMatch(title);
        if (tripCodeMatch != null) {
          // Check if message contains driver name or is generic - handle different formats
          RegExpMatch? driverNameMatch;
          String? driverName;
          String messageKey;
            // Try pattern 1: "DriverName has rejected trip TRIPCODE"
          driverNameMatch = RegExp(r'(.+?) has rejected trip (\w+)').firstMatch(message);
          if (driverNameMatch != null) {
            final extractedName = driverNameMatch.group(1);
            // Check if it's a generic reference like "The driver"
            if (extractedName == 'The driver') {
              driverName = 'Driver'; // Generic driver name
              messageKey = 'notificationMessageDriverRejectedTripGeneric';
            } else {
              driverName = extractedName;
              messageKey = 'notificationMessageDriverRejectedTrip';
            }
          } else {
            // Try pattern 2: "The driver has rejected trip TRIPCODE" or similar generic patterns
            driverNameMatch = RegExp(r'The driver has rejected (?:your )?trip (\w+)').firstMatch(message);
            if (driverNameMatch != null) {
              driverName = 'Driver'; // Generic driver name
              messageKey = 'notificationMessageDriverRejectedTripGeneric';
            } else {
              // Try pattern 3: Mixed language patterns like "The driver telah menolak perjalanan TRIPCODE"
              driverNameMatch = RegExp(r'(.+?) telah menolak perjalanan (\w+)').firstMatch(message);
              if (driverNameMatch != null) {
                final extractedName = driverNameMatch.group(1);
                // Check if it's a generic reference like "The driver"
                if (extractedName == 'The driver' || extractedName == 'Driver') {
                  driverName = 'Driver'; // Generic driver name
                  messageKey = 'notificationMessageDriverRejectedTripGeneric';
                } else {
                  driverName = extractedName;
                  messageKey = 'notificationMessageDriverRejectedTrip';
                }
              } else {
                // Fallback
                driverName = 'Driver';
                messageKey = 'notificationMessageDriverRejectedTripGeneric';
              }
            }
          }
          
          final reasonMatch = RegExp(r'Reason: (.+?)\.?$').firstMatch(message);
          final rejectionReason = reasonMatch?.group(1) ?? 'No reason provided';
          
          print('✅ Matched Driver Rejected Trip pattern with tripCode: ${tripCodeMatch.group(1)}, driverName: $driverName, reason: $rejectionReason, messageKey: $messageKey');
          
          return {
            'titleKey': 'notificationDriverRejectedTrip',
            'messageKey': messageKey,
            'params': {
              'tripCode': tripCodeMatch.group(1),
              'driverName': driverName,
              'rejectionReason': rejectionReason,
            }
          };
        }
      }
      
      // Trip Changes Rejected patterns
      if (title.contains('Trip Changes Rejected:')) {
        final tripCodeMatch = RegExp(r'Trip Changes Rejected: (\w+)').firstMatch(title);
        if (tripCodeMatch != null) {
          final reasonMatch = RegExp(r'Reason: (.+?)\.').firstMatch(message);
          return {
            'titleKey': 'notificationTripChangesRejected',
            'messageKey': 'notificationMessageTripChangesRejected',
            'params': {
              'tripCode': tripCodeMatch.group(1),
              'rejectionReason': reasonMatch?.group(1) ?? 'No reason provided',
            }
          };
        }
      }
      
      // Trip Cancellation Rejected patterns
      if (title.contains('Trip Cancellation Rejected:')) {
        final tripCodeMatch = RegExp(r'Trip Cancellation Rejected: (\w+)').firstMatch(title);
        if (tripCodeMatch != null) {
          final reasonMatch = RegExp(r'Reason: (.+?)\.').firstMatch(message);
          return {
            'titleKey': 'notificationTripCancellationRejected',
            'messageKey': 'notificationMessageTripCancellationRejected',
            'params': {
              'tripCode': tripCodeMatch.group(1),
              'rejectionReason': reasonMatch?.group(1) ?? 'No reason provided',
            }
          };
        }      }      // Trip Deleted patterns
      if (title.contains('Trip Deleted:') || title.contains('Deleted:') || message.contains('has been deleted by a trip manager')) {
        // Try to extract trip code from title first
        RegExpMatch? tripCodeMatch = RegExp(r'Trip Deleted: (\w+)').firstMatch(title);
        if (tripCodeMatch == null) {
          // If not found in title, try to extract from message
          tripCodeMatch = RegExp(r'trip (\w+) from .+ to .+ has been deleted').firstMatch(message);
        }
        
        if (tripCodeMatch != null) {
          // Determine if it's for requestor or driver based on message content
          final isRequestorMessage = message.contains('Your trip');
          final destinationMatch = RegExp(r'trip (\w+) from (.+?) to (.+?) has been deleted').firstMatch(message);
          
          print('✅ Matched Trip Deleted pattern with tripCode: ${tripCodeMatch.group(1)}, isRequestor: $isRequestorMessage');
          print('🔍 From: ${destinationMatch?.group(2)}, To: ${destinationMatch?.group(3)}');
          
          return {
            'titleKey': 'notificationTripDeleted',
            'messageKey': isRequestorMessage ? 
              'notificationMessageTripDeletedRequestor' : 
              'notificationMessageTripDeletedDriver',
            'params': {
              'tripCode': tripCodeMatch.group(1),
              'fromDestination': destinationMatch?.group(2) ?? '',
              'toDestination': destinationMatch?.group(3) ?? '',
            }
          };
        } else {
          print('❌ Trip Deleted pattern detected but could not extract trip code');
          print('🔍 Title: $title');
          print('🔍 Message: $message');
        }
      }
        // Trip Changes Approved patterns
      if (title.contains('Trip Changes Approved:')) {
        final tripCodeMatch = RegExp(r'Trip Changes Approved: (\w+)').firstMatch(title);
        if (tripCodeMatch != null) {
          final destinationMatch = RegExp(r'trip (\w+) from (.+?) to (.+?) have been approved').firstMatch(message);
          return {
            'titleKey': 'notificationTripChangesApproved',
            'messageKey': 'notificationMessageTripChangesApproved',
            'params': {
              'tripCode': tripCodeMatch.group(1),
              'fromDestination': destinationMatch?.group(2) ?? '',
              'toDestination': destinationMatch?.group(3) ?? '',
            }
          };
        }
      }
        // Trip Changes Approved patterns (based on message content)
      if (message.contains('changes to trip') && message.contains('have been approved') && message.contains('back to REQUEST status')) {
        // Extract trip code and destinations from message
        final tripCodeMatch = RegExp(r'changes to trip (\w+)').firstMatch(message);
        final destinationMatch = RegExp(r'trip \w+ from (.+?) to (.+?) have been approved').firstMatch(message);
          if (tripCodeMatch != null) {
          print('✅ Matched Trip Changes Approved pattern (by message content) with tripCode: ${tripCodeMatch.group(1)}');
          return {
            'titleKey': 'notificationTripChangesApproved',
            'messageKey': 'notificationMessageTripChangesApproved',
            'params': {
              'tripCode': tripCodeMatch.group(1),
              'fromDestination': destinationMatch?.group(1) ?? '',
              'toDestination': destinationMatch?.group(2) ?? '',
            }
          };
        }
      }
      
      // Back to Base Trip Created patterns
      if (title.contains('Back to Base Trip Created:')) {
        final tripCodeMatch = RegExp(r'Back to Base Trip Created: (\w+)').firstMatch(title);
        if (tripCodeMatch != null) {
          // Extract driver name, destinations, date and time from message
          // Pattern: "DRIVERNAME has created a "Back to Base" trip TRIPCODE from ORIGIN to DESTINATION on DATE at TIME."
          final detailsMatch = RegExp(r'(.+?) has created a "Back to Base" trip (\w+) from (.+?) to (.+?) on (.+?) at (.+?)\.').firstMatch(message);
          if (detailsMatch != null) {
            print('✅ Matched Back to Base Trip Created pattern with tripCode: ${tripCodeMatch.group(1)}');
            return {
              'titleKey': 'notificationBackToBaseTripCreated',
              'messageKey': 'notificationMessageBackToBaseTripCreated',
              'params': {
                'tripCode': tripCodeMatch.group(1),
                'driverName': detailsMatch.group(1) ?? 'Unknown Driver',
                'fromDestination': detailsMatch.group(3) ?? '',
                'toDestination': detailsMatch.group(4) ?? '',
                'date': detailsMatch.group(5) ?? '',
                'time': detailsMatch.group(6) ?? '',
              }
            };
          }
        }
      }
      
      // Additional pattern matching for notifications that might not have proper title format
      // Trip Assignment patterns (flexible)
      if (message.contains('has been assigned to a driver') || message.contains('assigned to trip')) {
        final tripCodeMatch = RegExp(r'trip (\w+)').firstMatch(message);
        if (tripCodeMatch != null) {
          if (message.contains('has been assigned to a driver')) {
            // Driver assignment notification for requestor
            final dateTimeMatch = RegExp(r'scheduled for (.+?) at (.+?)\.').firstMatch(message);
            print('✅ Matched Trip Assignment pattern (by message content) with tripCode: ${tripCodeMatch.group(1)}');
            return {
              'titleKey': 'notificationTripAssigned',
              'messageKey': 'notificationMessageTripAssignedToDriver',
              'params': {
                'tripCode': tripCodeMatch.group(1),
                'date': dateTimeMatch?.group(1) ?? '',
                'time': dateTimeMatch?.group(2) ?? '',
              }
            };
          } else if (message.contains('assigned to trip')) {
            // Driver assignment notification for driver
            final fromTo = RegExp(r'from (.+?) to (.+?)').firstMatch(message);
            final dateTime = RegExp(r'on (.+?) at (.+?)\.?').firstMatch(message);
            print('✅ Matched Driver Assignment pattern (by message content) with tripCode: ${tripCodeMatch.group(1)}');
            return {
              'titleKey': 'notificationNewTripAssignment',
              'messageKey': 'notificationMessageDriverAssignedToTrip',
              'params': {
                'tripCode': tripCodeMatch.group(1),
                'fromDestination': fromTo?.group(1) ?? '',
                'toDestination': fromTo?.group(2) ?? '',
                'date': dateTime?.group(1) ?? '',
                'time': dateTime?.group(2) ?? '',
              }
            };
          }
        }
      }
      
      // Trip Approved patterns (flexible)
      if (message.contains('has been approved') && !message.contains('changes')) {
        final tripCodeMatch = RegExp(r'trip (\w+)').firstMatch(message);
        final fromTo = RegExp(r'from (.+?) to (.+?)').firstMatch(message);
        if (tripCodeMatch != null) {
          print('✅ Matched Trip Approved pattern (by message content) with tripCode: ${tripCodeMatch.group(1)}');
          return {
            'titleKey': 'notificationTripApproved',
            'messageKey': 'notificationMessageTripApproved',
            'params': {
              'tripCode': tripCodeMatch.group(1),
              'fromDestination': fromTo?.group(1) ?? '',
              'toDestination': fromTo?.group(2) ?? '',
            }
          };
        }
      }
      
      // Add more pattern matching for other notification types as needed
      // This is a transitional approach until the backend sends localization data directly
      
      print('❌ No pattern matched for notification title: $title');
      
      // Generic fallback - try to extract trip code from any notification
      final genericTripCodeMatch = RegExp(r'(\w+)').firstMatch(title);
      if (genericTripCodeMatch != null && title.contains(':')) {
        final tripCode = title.split(':').last.trim();
        print('🔄 Generic fallback extracted tripCode: $tripCode from title');
        // Return a basic structure that might help with debugging
        return {
          'titleKey': 'generic_notification',
          'messageKey': 'generic_notification_message',
          'params': {
            'tripCode': tripCode,
            'originalTitle': title,
            'originalMessage': message,
          }
        };
      }
      
      return null;
    } catch (e) {
      print('Error parsing localization data from notification: $e');
      return null;
    }
  }
}
