
// This is a stub implementation for non-web platforms
class AuthDataStorage {
  static bool hasAuthData() {
    return false;
  }

  static Map<String, dynamic>? getAuthData() {
    return null;
  }

  static void clearAuthData() {
    // Do nothing
  }

  // Stub methods for auth token
  static bool hasAuthToken() {
    return false;
  }

  static String? getAuthToken() {
    return null;
  }

  static void clearAuthToken() {
    // Do nothing
  }

  // Stub methods for access token
  static String? getAccessToken() {
    return null;
  }

  static void storeAccessToken(String token) {
    // Do nothing
  }
}


