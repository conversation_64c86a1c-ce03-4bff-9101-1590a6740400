import 'package:FLEEX/main.dart'; // Import to access MyAppState
import 'package:FLEEX/services/notification_service.dart';
import 'package:FLEEX/widgets/floating_notification_panel.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class NotificationIcon extends StatefulWidget {
  const NotificationIcon({super.key});

  @override
  State<NotificationIcon> createState() => _NotificationIconState();
}

class _NotificationIconState extends State<NotificationIcon> {
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  bool _isOpen = false;
  @override
  void initState() {
    super.initState();
    // Fetch notifications when the widget is first created
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchNotifications();
    });
  }

  void _fetchNotifications() {
    final appState = Provider.of<MyAppState>(context, listen: false);
    final user = appState.currentUser;
    if (user != null) {
      final notificationService = Provider.of<NotificationService>(context, listen: false);
      notificationService.fetchUnreadCount(user.userId); // Pass userId directly without parsing

      // If the panel is open, also fetch the full notifications
      if (_isOpen) {
        notificationService.fetchNotifications(user.userId, includeRead: false);
      }
    }
  }

  // Create the overlay entry for the floating panel
  OverlayEntry _createOverlayEntry() {
    // Find the RenderBox of the notification icon
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;
    final offset = renderBox.localToGlobal(Offset.zero);

    // Calculate position for the panel
    // Position it below the notification icon and aligned to the right edge
    return OverlayEntry(
      builder: (context) => Positioned(
        left: offset.dx - 320 + size.width, // Align right edge of panel with right edge of icon
        top: offset.dy + size.height,
        width: 350,
        child: CompositedTransformFollower(
          link: _layerLink,
          followerAnchor: Alignment.topRight,
          targetAnchor: Alignment.bottomRight,
          offset: const Offset(0, 8), // Offset to position below the icon
          child: FloatingNotificationPanel(
            onClose: _toggleNotificationPanel,
            refreshNotifications: _fetchNotifications,
          ),
        ),
      ),
    );
  }

  // Toggle the notification panel
  void _toggleNotificationPanel() {
    final appState = Provider.of<MyAppState>(context, listen: false);
    final user = appState.currentUser;

    if (user == null) return;

    setState(() {
      if (_isOpen) {
        // Close the panel
        _overlayEntry?.remove();
        _overlayEntry = null;
      } else {
        // Open the panel and fetch notifications
        _overlayEntry = _createOverlayEntry();
        Overlay.of(context).insert(_overlayEntry!);

        // Fetch notifications to display in the panel
        final notificationService = Provider.of<NotificationService>(context, listen: false);
        notificationService.fetchNotifications(user.userId, includeRead: false); // Initially fetch only unread
      }
      _isOpen = !_isOpen;
    });
  }

  @override
  void dispose() {
    // Make sure to remove the overlay when the widget is disposed
    _overlayEntry?.remove();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<MyAppState>(context);
    final user = appState.currentUser;

    // If no user is logged in, don't show the notification icon
    if (user == null) {
      return const SizedBox.shrink();
    }

    return CompositedTransformTarget(
      link: _layerLink,
      child: Consumer<NotificationService>(
        builder: (context, notificationService, child) {
          return Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                icon: Icon(
                  Icons.notifications,
                  color: _isOpen ? Colors.white.withAlpha(204) : Colors.white, // 204 is ~80% opacity
                ),
                onPressed: _toggleNotificationPanel,
              ),
              if (notificationService.unreadCount > 0)
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    constraints: const BoxConstraints(
                      minWidth: 16,
                      minHeight: 16,
                    ),
                    child: Text(
                      notificationService.unreadCount > 9
                          ? '9+'
                          : notificationService.unreadCount.toString(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
            ],
          );
        },
      ),
    );
  }
}


