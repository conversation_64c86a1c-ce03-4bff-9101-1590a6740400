import 'dart:async';

import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../generated/l10n/app_localizations.dart';
import '../main.dart';
import '../models/user.dart';
import '../widgets/notification_icon.dart';
import '../widgets/flag_button.dart';

class CommonAppBar extends StatefulWidget implements PreferredSizeWidget {
  final String title;
  final VoidCallback? onRefresh;
  final List<Widget>? additionalActions;
  final bool showMenuIcon;
  final bool automaticallyImplyLeading;

  const CommonAppBar({
    Key? key,
    required this.title,
    this.onRefresh,
    this.additionalActions,
    this.showMenuIcon = false,
    this.automaticallyImplyLeading = true,
  }) : super(key: key);

  @override
  State<CommonAppBar> createState() => _CommonAppBarState();

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class _CommonAppBarState extends State<CommonAppBar> {
  late Timer _timer;
  late DateTime _currentDateTime;

  @override
  void initState() {
    super.initState();
    _currentDateTime = DateTime.now();
    // Update the time every second
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          _currentDateTime = DateTime.now();
        });
      }
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<MyAppState>(context);
    final currentUser = appState.currentUser;

    // Check if we're on a small screen (mobile)
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600 && !kIsWeb;

    // Format current date and time
    final String formattedDateTime = DateFormat(
      isSmallScreen ? 'HH:mm' : 'dd-MMM-yyyy HH:mm:ss'
    ).format(_currentDateTime);    return AppBar(
      backgroundColor: const Color(0xFF0D47A1),
      foregroundColor: Colors.white,
      automaticallyImplyLeading: widget.automaticallyImplyLeading,
      leading: widget.showMenuIcon ? IconButton(
        icon: const Icon(Icons.menu),
        onPressed: () {
          Scaffold.of(context).openDrawer();
        },
      ) : null,
      title: Text(
        widget.title,
        style: TextStyle(
          fontSize: isSmallScreen ? 18 : 20,
          fontWeight: FontWeight.bold,
        ),
      ),      actions: [
        // Additional actions if provided
        if (widget.additionalActions != null)
          ...widget.additionalActions!,

        // Language flags - always show
        const LanguageFlagButtons(),

        // Notification icon - always show on all screen sizes
        if (currentUser != null)
          Provider<User?>.value(
            value: currentUser,
            child: const NotificationIcon(),
          ),

        // Greeting with username - only show on larger screens
        if (currentUser != null && !isSmallScreen)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),            child: Center(
              child: Text(
                '${AppLocalizations.of(context).welcome} ${currentUser.name} (${currentUser.role}) | $formattedDateTime',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.white,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),        // Refresh button
        if (widget.onRefresh != null)
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: AppLocalizations.of(context).refreshNow,
            onPressed: widget.onRefresh,
            iconSize: isSmallScreen ? 22 : 24,
          ),

        // Only show logout in app bar on larger screens
        // On mobile, logout is in the drawer for better UX
        if (!isSmallScreen)
          IconButton(
            icon: const Icon(Icons.logout),
            tooltip: AppLocalizations.of(context).logout,
            onPressed: () {
              // Use the dedicated logout route which handles both local and server-side logout
              Navigator.of(context).pushReplacementNamed('/logout');
            },
            iconSize: isSmallScreen ? 22 : 24,
          ),
      ],
    );
  }
}


