import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import '../generated/l10n/app_localizations.dart';
import '../services/api_service.dart';

/// A standalone dialog for updating car odometer readings.
/// This is designed to be completely isolated from the parent widget tree
/// to avoid state management issues.
class OdometerDialog {  /// Shows a dialog to input odometer reading and updates the car's odometer.
  ///
  /// Returns a `Future<bool>` that resolves to true if the odometer was successfully updated,
  /// or false if the update failed or was cancelled.
  static Future<bool> showAndUpdateOdometer(
    BuildContext context,
    dynamic carId,
    String carCode,
    dynamic currentOdometer, {
    dynamic tripId,
    dynamic driverId,
  }) async {
    // Ensure currentOdometer is an integer
    final int safeCurrentOdometer = currentOdometer is int
        ? currentOdometer
        : (int.tryParse(currentOdometer?.toString() ?? '0') ?? 0);

    // Show the dialog to get the new odometer reading and additional data
    final Map<String, dynamic>? dialogResult = await _showDialog(context, carCode, safeCurrentOdometer);

    // If user cancelled or provided invalid input
    if (dialogResult == null) {
      return false;
    }

    final int newOdometer = dialogResult['odometerReading'];
    final String notes = dialogResult['notes'] ?? '';
    final String? imageData = dialogResult['imageData'];

    // Update the car odometer with additional data
    final result = await _updateCarOdometer(
      carId, 
      newOdometer, 
      tripId: tripId, 
      driverId: driverId,
      notes: notes,
      imageData: imageData,
    );

    // Return true if update was successful, false otherwise
    return result['success'] == true;
  }  /// Shows a dialog to input odometer reading with optional photo and notes.
  /// Returns a map with odometer reading, notes, and image data or null if cancelled.
  static Future<Map<String, dynamic>?> _showDialog(BuildContext context, String carCode, int currentOdometer) async {
    return await showDialog<Map<String, dynamic>>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return _OdometerDialogContent(
          carCode: carCode,
          currentOdometer: currentOdometer,
          parentContext: context,
        );
      },
    );
  }

  /// Updates the car odometer in the database.
  /// Returns a map with 'success' boolean and optional 'error' message.
  static Future<Map<String, dynamic>> _updateCarOdometer(
    dynamic carId,
    int odometerReading, {
    dynamic tripId,
    dynamic driverId,
    String? notes,
    String? imageData,
  }) async {
    // Ensure carId is properly formatted
    final safeCarId = carId is int ? carId : int.tryParse(carId?.toString() ?? '0') ?? 0;

    try {
      // Get current date and time in local timezone for the notes
      final now = DateTime.now().toLocal();
      final localDate = '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';
      final localTime = '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')}';

      // Combine default notes with user notes
      String finalNotes = 'Updated at trip start by driver app on $localDate at $localTime (local time)';
      if (notes != null && notes.isNotEmpty) {
        finalNotes += '\n\nAdditional notes: $notes';
      }

      final Map<String, dynamic> requestBody = {
        'odometer': odometerReading,
        'notes': finalNotes,
      };

      // Add trip ID if provided
      if (tripId != null) {
        // Ensure tripId is an integer
        final safeTripId = tripId is int ? tripId : int.tryParse(tripId.toString()) ?? 0;
        requestBody['tripId'] = safeTripId;
      }

      // Add driver ID if provided
      if (driverId != null) {
        // Ensure driverId is an integer
        final safeDriverId = driverId is int ? driverId : int.tryParse(driverId.toString()) ?? 0;
        requestBody['driverId'] = safeDriverId;
      }

      // Add image data if provided
      if (imageData != null && imageData.isNotEmpty) {
        print('Adding image data to odometer update request: ${imageData.length} characters');
        requestBody['image_data'] = imageData;
      }

      final response = await ApiService.put(
        'cars/$safeCarId/odometer',
        requestBody,
      );

      if (response.statusCode == 200) {
        // Parse the response to get the history ID
        final responseData = jsonDecode(response.body);
        print('Odometer history recorded: ${responseData['history']}');

        // Log the date information
        if (responseData['history'] != null) {
          final history = responseData['history'];
          print('Recorded at UTC: ${history['recorded_at']}');
          print('UTC date: ${history['utc_date']}');
          print('Local date: ${history['local_date']}');
        }

        return {'success': true};
      } else {
        print('Error updating car odometer: ${response.statusCode}');
        print('Response body: ${response.body}');

        // Try to parse the error message from the response
        String errorMessage = 'Failed to update car odometer';
        int? currentOdometer;

        try {
          final errorData = jsonDecode(response.body);
          if (errorData['error'] != null) {
            errorMessage = errorData['error'];
          }
          if (errorData['currentOdometer'] != null) {
            currentOdometer = errorData['currentOdometer'];
          }
        } catch (e) {
          print('Error parsing error response: $e');
        }

        return {
          'success': false,
          'error': errorMessage,
          'currentOdometer': currentOdometer,
        };
      }
    } catch (e) {
      print('Exception updating car odometer: $e');
      return {'success': false, 'error': e.toString()};
    }
  }
}

/// Internal stateful widget for the odometer dialog content
class _OdometerDialogContent extends StatefulWidget {
  final String carCode;
  final int currentOdometer;
  final BuildContext parentContext;

  const _OdometerDialogContent({
    required this.carCode,
    required this.currentOdometer,
    required this.parentContext,
  });

  @override
  State<_OdometerDialogContent> createState() => _OdometerDialogContentState();
}

class _OdometerDialogContentState extends State<_OdometerDialogContent> {
  final TextEditingController _odometerController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();
  
  String? _localBase64Image;
  Uint8List? _localImageBytes;

  @override
  void dispose() {
    _odometerController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(AppLocalizations.of(widget.parentContext).updateOdometerFor(widget.carCode)),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(AppLocalizations.of(widget.parentContext).firstTripOfDay),
            Text(AppLocalizations.of(widget.parentContext).pleaseEnterOdometerReading),
            const SizedBox(height: 8),
            Row(
              children: [
                const Text('Current reading: ', style: TextStyle(fontWeight: FontWeight.bold)),
                Text('${widget.currentOdometer}', style: const TextStyle(fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: 16),
            
            // Odometer reading input
            TextField(
              controller: _odometerController,
              decoration: InputDecoration(
                labelText: 'Odometer Reading *',
                border: const OutlineInputBorder(),
                hintText: AppLocalizations.of(widget.parentContext).enterNewOdometerReading,
                helperText: AppLocalizations.of(widget.parentContext).odometerReadingHelper,
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Notes input
            TextField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'Notes (Optional)',
                border: OutlineInputBorder(),
                hintText: 'Enter any additional notes about the odometer reading',
              ),
              maxLines: 2,
            ),
            
            const SizedBox(height: 16),
              // Photo section
            const Text(
              'Photo *',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            
            // Display selected image or placeholder
            _localImageBytes != null
                ? Container(
                    height: 120,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(4),
                      child: kIsWeb && _localBase64Image != null
                          ? Image.network(
                              _localBase64Image!,
                              fit: BoxFit.cover,
                            )
                          : Image.memory(
                              _localImageBytes!,
                              fit: BoxFit.cover,
                            ),
                    ),
                  )
                : Container(
                    height: 120,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(color: Colors.grey),
                    ),                    child: const Center(
                      child: Text(
                        'Please take a photo of the odometer',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Colors.red,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
            
            const SizedBox(height: 8),            // Take photo button
            Center(
              child: ElevatedButton.icon(
                onPressed: () async {
                  // Store scaffold messenger reference before async operation
                  final scaffoldMessenger = ScaffoldMessenger.of(context);
                  
                  try {
                    final ImagePicker picker = ImagePicker();
                    final XFile? image = await picker.pickImage(
                      source: kIsWeb ? ImageSource.gallery : ImageSource.camera,
                      imageQuality: 70,
                      maxWidth: 400,
                      maxHeight: 400,
                    );

                    // Check if widget is still mounted after async operation
                    if (!mounted) return;

                    if (image != null) {
                      final bytes = await image.readAsBytes();

                      // Check if widget is still mounted after async operation
                      if (!mounted) return;

                      // Determine the correct MIME type
                      String mimeType = image.mimeType ?? 'image/jpeg';
                      if (mimeType.isEmpty) {
                        // Default to jpeg if no mime type is available
                        mimeType = 'image/jpeg';
                      }

                      final base64Image = base64Encode(bytes);
                      final dataUrl = 'data:$mimeType;base64,$base64Image';

                      print('Image format: $mimeType');
                      print('Image path: ${image.path}');
                      print('Image size: ${bytes.length} bytes');

                      // Update the state
                      if (mounted) {
                        setState(() {
                          _localImageBytes = bytes;
                          _localBase64Image = dataUrl;
                        });
                      }

                      print('Image selected successfully: ${bytes.length} bytes');
                    }
                  } catch (e) {
                    print('Error picking image: $e');
                    // Use the captured scaffold messenger reference
                    if (mounted) {
                      scaffoldMessenger.showSnackBar(
                        SnackBar(
                          content: Text('Error selecting image: $e'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                },                icon: const Icon(Icons.camera_alt),
                label: const Text('Take Photo *'),
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop(null); // Cancel
          },
          child: Text(AppLocalizations.of(widget.parentContext).cancel),
        ),
        ElevatedButton(
          onPressed: () {
            // Validate input is not empty
            if (_odometerController.text.isEmpty) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(AppLocalizations.of(context).odometerReadingRequired),
                  backgroundColor: Colors.red,
                ),
              );
              return;
            }

            // Validate input is a valid number
            final odometerReading = int.tryParse(_odometerController.text);
            if (odometerReading == null) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(AppLocalizations.of(context).pleaseEnterValidNumber),
                  backgroundColor: Colors.red,
                ),
              );
              return;
            }            // Validate input is equal to or greater than current odometer
            if (odometerReading < widget.currentOdometer) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(AppLocalizations.of(widget.parentContext).newReadingMustBeAtLeast(widget.currentOdometer.toString())),
                  backgroundColor: Colors.red,
                ),
              );
              return;
            }

            // Validate that a photo is provided (mandatory)
            if (_localBase64Image == null || _localBase64Image!.isEmpty) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('A photo of the odometer is required'),
                  backgroundColor: Colors.red,
                ),
              );
              return;
            }

            // Return the validated data
            Navigator.of(context).pop({
              'odometerReading': odometerReading,
              'notes': _notesController.text.trim(),
              'imageData': _localBase64Image,
            });
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
          ),
          child: Text(AppLocalizations.of(widget.parentContext).update),
        ),
      ],
    );
  }
}


