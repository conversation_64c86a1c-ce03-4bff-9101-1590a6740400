import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../generated/l10n/app_localizations.dart';
import '../main.dart';

class MobileBottomNav extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const MobileBottomNav({
    Key? key,
    required this.currentIndex,
    required this.onTap,
  }) : super(key: key);
  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<MyAppState>(context);
    final user = appState.currentUser;
    
    // Check roles based on current active role for multi-role support
    final isAdmin = user?.isCurrentlyTripManager ?? false;
    final isSuperAdmin = user?.isSuperAdmin ?? false;
    final isDriver = user?.isCurrentlyDriver ?? false;

    // Different navigation items based on user role
    if (isAdmin) {
      // Trip manager navigation
      return BottomNavigationBar(
        currentIndex: currentIndex,
        onTap: onTap,
        type: BottomNavigationBarType.fixed,
        backgroundColor: const Color(0xFF0D47A1),
        selectedItemColor: Colors.white,
        unselectedItemColor: Colors.white70,        items: [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: AppLocalizations.of(context).dashboard,
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.calendar_today),
            label: AppLocalizations.of(context).tripMonitoring,
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.approval),
            label: AppLocalizations.of(context).tripApprovals,
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: AppLocalizations.of(context).settings,
          ),
        ],
      );
    } else if (isDriver) {      // Driver navigation
      return BottomNavigationBar(
        currentIndex: currentIndex,
        onTap: onTap,
        backgroundColor: const Color(0xFF0D47A1),
        selectedItemColor: Colors.white,
        unselectedItemColor: Colors.white70,
        items: [
          BottomNavigationBarItem(
            icon: Icon(Icons.directions_car),
            label: AppLocalizations.of(context).myTrips,
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.fact_check),
            label: AppLocalizations.of(context).checkIn,
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.notifications),
            label: AppLocalizations.of(context).notifications,
          ),
        ],
      );
    } else if (isSuperAdmin) {      // Super admin navigation
      return BottomNavigationBar(
        currentIndex: currentIndex,
        onTap: onTap,
        backgroundColor: const Color(0xFF0D47A1),
        selectedItemColor: Colors.white,
        unselectedItemColor: Colors.white70,
        items: [
          BottomNavigationBarItem(
            icon: Icon(Icons.admin_panel_settings),
            label: AppLocalizations.of(context).admin,
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.password),
            label: AppLocalizations.of(context).resetPassword,
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.notifications),
            label: AppLocalizations.of(context).notifications,
          ),
        ],
      );
    } else {      // Regular user navigation
      return BottomNavigationBar(
        currentIndex: currentIndex,
        onTap: onTap,
        type: BottomNavigationBarType.fixed,
        backgroundColor: const Color(0xFF0D47A1),
        selectedItemColor: Colors.white,
        unselectedItemColor: Colors.white70,
        items: [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: AppLocalizations.of(context).dashboard,
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.add_circle),
            label: AppLocalizations.of(context).requestTrip,
          ),          BottomNavigationBarItem(
            icon: Icon(Icons.star),
            label: AppLocalizations.of(context).rateTrips,
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.history),
            label: AppLocalizations.of(context).tripHistory,
          ),
        ],
      );
    }
  }
}


