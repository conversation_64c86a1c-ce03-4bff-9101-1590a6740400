import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../generated/l10n/app_localizations.dart';
import '../main.dart';

class RoleSwitcher extends StatelessWidget {
  const RoleSwitcher({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<MyAppState>(
      builder: (context, appState, child) {
        final user = appState.currentUser;
        
        // Don't show if no user or user doesn't have multiple roles
        if (user == null || !appState.hasMultipleRoles) {
          return const SizedBox.shrink();
        }

        return Container(
          margin: const EdgeInsets.all(16.0),
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(8.0),
            border: Border.all(color: Colors.blue.shade200),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.swap_horiz, color: Colors.blue.shade700),
                  const SizedBox(width: 8),
                  Text(
                    AppLocalizations.of(context).switchRole,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade700,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),              Text(
                '${AppLocalizations.of(context).currentRole}: ${_getRoleDisplayName(context, user.currentRole)}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                ),
              ),
              const SizedBox(height: 12),
              Wrap(
                spacing: 8.0,
                children: user.availableRoles.map((role) {
                  final isCurrentRole = role.toLowerCase() == user.currentRole.toLowerCase();
                  return ChoiceChip(
                    label: Text(_getRoleDisplayName(context, role)),
                    selected: isCurrentRole,
                    selectedColor: const Color(0xFF0D47A1),
                    backgroundColor: Colors.grey.shade200,
                    labelStyle: TextStyle(
                      color: isCurrentRole ? Colors.white : Colors.black87,
                      fontWeight: isCurrentRole ? FontWeight.bold : FontWeight.normal,
                    ),
                    onSelected: (selected) {
                      if (selected && !isCurrentRole) {
                        _showRoleSwitchConfirmation(context, role, appState);
                      }
                    },
                  );
                }).toList(),
              ),
            ],
          ),
        );
      },
    );
  }

  String _getRoleDisplayName(BuildContext context, String role) {
    switch (role.toLowerCase()) {
      case 'trip manager':
        return AppLocalizations.of(context).tripManager;
      case 'requestor':
      case 'user':
        return AppLocalizations.of(context).requestor;
      case 'driver':
        return AppLocalizations.of(context).driver;
      case 'super admin':
        return AppLocalizations.of(context).superAdmin;
      default:
        return role;
    }
  }  void _showRoleSwitchConfirmation(BuildContext context, String newRole, MyAppState appState) {
    final roleDisplayName = _getRoleDisplayName(context, newRole);
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Text(AppLocalizations.of(context).confirmRoleSwitch),
          content: Text(
            AppLocalizations.of(context).switchRoleMessage(roleDisplayName),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: Text(AppLocalizations.of(context).cancel),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
                _switchRole(context, newRole, appState);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF0D47A1),
                foregroundColor: Colors.white,
              ),
              child: Text(AppLocalizations.of(context).switchRole),
            ),
          ],
        );
      },
    );
  }
  void _switchRole(BuildContext context, String newRole, MyAppState appState) {
    try {
      appState.switchUserRole(newRole);
      
      final roleDisplayName = _getRoleDisplayName(context, newRole);
        // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            AppLocalizations.of(context).roleSwitchedSuccessfully(roleDisplayName),
          ),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );

      // Navigate to the appropriate dashboard based on the new role
      _navigateToRoleDashboard(context, newRole);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${AppLocalizations.of(context).error}: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }  void _navigateToRoleDashboard(BuildContext context, String role) {
    // Navigate back to home route and let the Consumer<MyAppState> builder
    // handle the role-based routing automatically
    Navigator.of(context).pushNamedAndRemoveUntil(
      '/', // Home route
      (route) => false, // Remove all previous routes
    );
  }
}
