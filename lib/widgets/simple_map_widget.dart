import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart';

class SimpleMapWidget extends StatefulWidget {
  final Map<int, LatLng> driverLocations;
  final Map<int, String> driverNames;
  final Map<int, String> driverInitials;
  final int? selectedDriverId;
  final Function(int) onDriverSelected;

  const SimpleMapWidget({
    super.key,
    required this.driverLocations,
    required this.driverNames,
    required this.driverInitials,
    this.selectedDriverId,
    required this.onDriverSelected,
  });

  @override
  State<SimpleMapWidget> createState() => _SimpleMapWidgetState();
}

class _SimpleMapWidgetState extends State<SimpleMapWidget> {
  // Jakarta coordinates as reference point
  final LatLng _jakartaCenter = const LatLng(-6.2088, 106.8456);

  // Scaling factors for converting lat/lng to screen coordinates
  final double _latScale = 100.0;
  final double _lngScale = 100.0;

  // Pan and zoom state
  Offset _panOffset = Offset.zero;
  double _zoomLevel = 1.0;

  // For double tap zoom
  int _lastTapTime = 0;
  Offset _lastTapPosition = Offset.zero;

  @override
  void initState() {
    super.initState();

    // Center on selected driver if available
    if (widget.selectedDriverId != null &&
        widget.driverLocations.containsKey(widget.selectedDriverId)) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _centerOnDriver(widget.selectedDriverId!);
      });
    }
  }

  void _centerOnDriver(int driverId) {
    if (!widget.driverLocations.containsKey(driverId)) return;

    final location = widget.driverLocations[driverId]!;
    setState(() {
      // Reset zoom and calculate new pan offset to center on driver
      _zoomLevel = 1.5;

      final screenPos = _latLngToScreenPosition(location);
      final screenWidth = MediaQuery.of(context).size.width;
      final screenHeight = MediaQuery.of(context).size.height;

      _panOffset = Offset(
        (screenWidth / 2) - screenPos.dx,
        (screenHeight / 2) - screenPos.dy
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onScaleUpdate: (details) {
        setState(() {
          // If scale is 1.0, it's just a pan operation
          if (details.scale == 1.0) {
            _panOffset += details.focalPointDelta;
          } else {
            // Otherwise it's a zoom operation
            _zoomLevel = (_zoomLevel * details.scale).clamp(0.5, 5.0);
          }
        });
      },
      onTapDown: (details) {
        // Handle double tap to zoom
        final now = DateTime.now().millisecondsSinceEpoch;
        if (now - _lastTapTime < 300) {
          // Double tap detected
          final tapPosition = details.localPosition;
          if ((tapPosition - _lastTapPosition).distance < 30) {
            setState(() {
              _zoomLevel = math.min(5.0, _zoomLevel * 1.5);
            });
          }
        }
        _lastTapTime = now;
        _lastTapPosition = details.localPosition;
      },
      child: Container(
        color: const Color(0xFFE0E0E0),
        child: Stack(
          children: [
            // Grid lines for reference
            CustomPaint(
              size: Size.infinite,
              painter: GridPainter(
                panOffset: _panOffset,
                zoomLevel: _zoomLevel,
              ),
            ),

            // Driver markers
            for (final entry in widget.driverLocations.entries)
              Builder(builder: (context) {
                final driverId = entry.key;
                final location = entry.value;
                final name = widget.driverNames[driverId] ?? 'Unknown';
                final initial = widget.driverInitials[driverId] ?? 'U';

                // Convert lat/lng to screen coordinates
                final screenPos = _latLngToScreenPosition(location);

                return Positioned(
                  left: screenPos.dx + _panOffset.dx,
                  top: screenPos.dy + _panOffset.dy,
                  child: GestureDetector(
                    onTap: () {
                      widget.onDriverSelected(driverId);
                      _centerOnDriver(driverId);
                    },
                    child: Column(
                      children: [
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: widget.selectedDriverId == driverId ? Colors.green : Colors.blue,
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: widget.selectedDriverId == driverId ? Colors.yellow : Colors.white,
                              width: widget.selectedDriverId == driverId ? 3 : 2,
                            ),
                          ),
                          child: Center(
                            child: Text(
                              initial,
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.all(2),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            name,
                            style: const TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }),

            // Map controls
            Positioned(
              right: 16,
              bottom: 16,
              child: Column(
                children: [
                  FloatingActionButton(
                    mini: true,
                    child: const Icon(Icons.add),
                    onPressed: () {
                      setState(() {
                        _zoomLevel = (_zoomLevel * 1.2).clamp(0.5, 5.0);
                      });
                    },
                  ),
                  const SizedBox(height: 8),
                  FloatingActionButton(
                    mini: true,
                    child: const Icon(Icons.remove),
                    onPressed: () {
                      setState(() {
                        _zoomLevel = (_zoomLevel / 1.2).clamp(0.5, 5.0);
                      });
                    },
                  ),
                  const SizedBox(height: 8),
                  FloatingActionButton(
                    mini: true,
                    child: const Icon(Icons.center_focus_strong),
                    onPressed: () {
                      setState(() {
                        _panOffset = Offset.zero;
                        _zoomLevel = 1.0;
                      });
                    },
                  ),
                ],
              ),
            ),

            // Map info
            Positioned(
              left: 16,
              top: 16,
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withAlpha(204),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Drivers: ${widget.driverLocations.length}'),
                    Text('Zoom: ${_zoomLevel.toStringAsFixed(1)}x'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Convert lat/lng to screen position
  Offset _latLngToScreenPosition(LatLng location) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    final centerX = screenWidth / 2;
    final centerY = screenHeight / 2;

    final latDiff = (location.latitude - _jakartaCenter.latitude) * _latScale * _zoomLevel;
    final lngDiff = (location.longitude - _jakartaCenter.longitude) * _lngScale * _zoomLevel;

    return Offset(
      centerX + lngDiff,
      centerY - latDiff, // Negative because latitude increases northward
    );
  }
}

// Simple grid painter for reference
class GridPainter extends CustomPainter {
  final Offset panOffset;
  final double zoomLevel;

  GridPainter({
    required this.panOffset,
    required this.zoomLevel,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey.withAlpha(77)
      ..strokeWidth = 1.0;

    final gridSize = 50.0 * zoomLevel;

    // Calculate grid offset based on pan
    final offsetX = panOffset.dx % gridSize;
    final offsetY = panOffset.dy % gridSize;

    // Draw vertical lines
    for (double x = offsetX; x < size.width; x += gridSize) {
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }

    // Draw horizontal lines
    for (double y = offsetY; y < size.height; y += gridSize) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }

    // Draw center marker
    final centerX = size.width / 2 + panOffset.dx;
    final centerY = size.height / 2 + panOffset.dy;

    final centerPaint = Paint()
      ..color = Colors.red.withAlpha(128)
      ..strokeWidth = 2.0;

    canvas.drawCircle(
      Offset(centerX, centerY),
      10.0,
      centerPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}


