import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../main.dart';

class FlagButton extends StatelessWidget {
  final String languageCode;
  final String flag;
  final String tooltip;
  final VoidCallback? onPressed;
  final bool isSelected;

  const FlagButton({
    Key? key,
    required this.languageCode,
    required this.flag,
    required this.tooltip,
    this.onPressed,
    this.isSelected = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 2),
      decoration: BoxDecoration(
        border: isSelected ? Border.all(color: Colors.white, width: 2) : null,
        borderRadius: BorderRadius.circular(4),
      ),
      child: IconButton(
        onPressed: onPressed,
        tooltip: tooltip,
        icon: Text(
          flag,
          style: const TextStyle(fontSize: 20),
        ),
        constraints: const BoxConstraints(
          minWidth: 32,
          minHeight: 32,
        ),
        padding: const EdgeInsets.all(4),
      ),
    );
  }
}

class LanguageFlagButtons extends StatelessWidget {
  const LanguageFlagButtons({Key? key}) : super(key: key);

  Future<void> _changeLanguage(BuildContext context, String languageCode) async {
    try {
      final localeNotifier = Provider.of<LocaleNotifier>(context, listen: false);
      await localeNotifier.setLocale(Locale(languageCode));
      
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Language changed to ${languageCode == 'en' ? 'English' : 'Bahasa Indonesia'}'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error changing language: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<LocaleNotifier>(
      builder: (context, localeNotifier, child) {
        final currentLanguage = localeNotifier.locale.languageCode;
        
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            FlagButton(
              languageCode: 'en',
              flag: '🇺🇸',
              tooltip: 'English',
              isSelected: currentLanguage == 'en',
              onPressed: () => _changeLanguage(context, 'en'),
            ),
            FlagButton(
              languageCode: 'id',
              flag: '🇮🇩',
              tooltip: 'Bahasa Indonesia',
              isSelected: currentLanguage == 'id',
              onPressed: () => _changeLanguage(context, 'id'),
            ),
          ],
        );
      },
    );
  }
}
