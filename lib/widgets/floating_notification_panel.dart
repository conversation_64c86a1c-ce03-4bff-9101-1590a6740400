import 'package:FLEEX/main.dart'; // Import to access MyAppState
import 'package:FLEEX/models/notification.dart' as app_notification;
import 'package:FLEEX/services/notification_service.dart';
import 'package:FLEEX/services/notification_translation_service.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../generated/l10n/app_localizations.dart';

class FloatingNotificationPanel extends StatefulWidget {
  final Function onClose;
  final Function refreshNotifications;

  const FloatingNotificationPanel({
    super.key,
    required this.onClose,
    required this.refreshNotifications,
  });

  @override
  State<FloatingNotificationPanel> createState() => _FloatingNotificationPanelState();
}

class _FloatingNotificationPanelState extends State<FloatingNotificationPanel> {
  bool _includeRead = false;

  // Pagination variables
  int _currentPage = 1;
  int _itemsPerPage = 10; // Default to 10 items per page
  int _totalPages = 1;

  // Options for items per page
  final List<int> _itemsPerPageOptions = [5, 10, 25, 50, 100];

  @override
  void dispose() {
    super.dispose();
  }

  // Get paginated notifications for the current page
  List<app_notification.Notification> _getPaginatedNotifications(List<app_notification.Notification> allNotifications) {
    if (allNotifications.isEmpty) return [];

    final int startIndex = (_currentPage - 1) * _itemsPerPage;
    final int endIndex = startIndex + _itemsPerPage > allNotifications.length
        ? allNotifications.length
        : startIndex + _itemsPerPage;

    if (startIndex >= allNotifications.length) return [];

    return allNotifications.sublist(startIndex, endIndex);
  }

  // Calculate total pages based on notifications count
  int _calculateTotalPages(int notificationsCount) {
    final totalPages = (notificationsCount / _itemsPerPage).ceil();
    return totalPages < 1 ? 1 : totalPages;
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      elevation: 8,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        width: 350,
        constraints: const BoxConstraints(maxHeight: 400),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header with title and close button
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: const Color(0xFF0D47A1),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Notifications',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                    onPressed: () => widget.onClose(),
                  ),
                ],
              ),
            ),

            // Filter options and pagination settings
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Column(
                children: [
                  // Show read notifications checkbox
                  Row(
                    children: [
                      SizedBox(
                        height: 24,
                        width: 24,
                        child: Checkbox(
                          value: _includeRead,
                          onChanged: (value) {
                            setState(() {
                              _includeRead = value ?? false;
                              // Reset to first page when changing filter
                              _currentPage = 1;
                            });
                            _fetchNotifications();
                          },
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            setState(() {
                              _includeRead = !_includeRead;
                              // Reset to first page when changing filter
                              _currentPage = 1;
                            });
                            _fetchNotifications();
                          },
                          child: const Text(
                            'Show read notifications',
                            style: TextStyle(fontSize: 12),
                          ),
                        ),
                      ),
                    ],
                  ),

                  // Items per page dropdown
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      const Text(
                        'Items:',
                        style: TextStyle(fontSize: 10),
                      ),
                      const SizedBox(width: 8),
                      // Use a simpler approach with a Row of buttons instead of a dropdown
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: _itemsPerPageOptions.map((int value) {
                          final isSelected = _itemsPerPage == value;
                          return InkWell(
                            onTap: () {
                              if (!isSelected) {
                                setState(() {
                                  _itemsPerPage = value;
                                  // Reset to first page when changing items per page
                                  _currentPage = 1;

                                  // Recalculate total pages
                                  final notificationService = Provider.of<NotificationService>(context, listen: false);
                                  _totalPages = _calculateTotalPages(notificationService.notifications.length);
                                });
                              }
                            },
                            child: Container(
                              margin: const EdgeInsets.only(right: 4),
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: isSelected ? const Color(0xFF0D47A1) : Colors.grey.shade200,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                '$value',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: isSelected ? Colors.white : Colors.black,
                                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                                ),
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Notification list
            Flexible(
              child: Consumer<NotificationService>(
                builder: (context, notificationService, child) {
                  if (notificationService.isLoading) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  if (notificationService.error.isNotEmpty) {
                    return Center(child: Text('Error: ${notificationService.error}'));
                  }                  if (notificationService.notifications.isEmpty) {
                    return Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Center(
                        child: Text(AppLocalizations.of(context).noNotifications),
                      ),
                    );
                  }

                  // Calculate total pages and update state variables
                  _totalPages = _calculateTotalPages(notificationService.notifications.length);

                  // Ensure current page is valid
                  if (_currentPage > _totalPages) {
                    _currentPage = 1;
                  }

                  // Get paginated notifications
                  final paginatedNotifications = _getPaginatedNotifications(notificationService.notifications);

                  return Column(
                    children: [
                      Expanded(
                        child: Scrollbar(
                          thumbVisibility: true,
                          thickness: 6.0,
                          radius: const Radius.circular(10.0),
                          child: ListView.separated(
                            shrinkWrap: true,
                            physics: const AlwaysScrollableScrollPhysics(),
                            itemCount: paginatedNotifications.length,
                            separatorBuilder: (context, index) => const Divider(height: 1),
                            itemBuilder: (context, index) {
                              final notification = paginatedNotifications[index];
                              return _buildNotificationItem(context, notification);
                            },
                          ),
                        ),
                      ),

                      // Pagination controls
                      if (notificationService.notifications.length > _itemsPerPage)
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              // Previous button
                              IconButton(
                                icon: const Icon(Icons.chevron_left),
                                onPressed: _currentPage > 1
                                    ? () {
                                        setState(() {
                                          _currentPage--;
                                        });
                                      }
                                    : null,
                                iconSize: 20,
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(),
                                color: _currentPage > 1 ? const Color(0xFF0D47A1) : Colors.grey,
                              ),

                              // Page indicator
                              Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                                child: Text(
                                  '$_currentPage / $_totalPages',
                                  style: const TextStyle(fontSize: 12),
                                ),
                              ),

                              // Next button
                              IconButton(
                                icon: const Icon(Icons.chevron_right),
                                onPressed: _currentPage < _totalPages
                                    ? () {
                                        setState(() {
                                          _currentPage++;
                                        });
                                      }
                                    : null,
                                iconSize: 20,
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(),
                                color: _currentPage < _totalPages ? const Color(0xFF0D47A1) : Colors.grey,
                              ),
                            ],
                          ),
                        ),
                    ],
                  );
                },
              ),
            ),

            // Footer with actions
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(8),
                  bottomRight: Radius.circular(8),
                ),
              ),
              child: Center(
                child: Consumer<NotificationService>(
                  builder: (context, service, _) {
                    // Disable the button if showing read notifications or if there are no notifications
                    final bool hasUnreadNotifications = service.notifications.any((notification) => !notification.isRead);
                    final bool isDisabled = _includeRead || service.notifications.isEmpty || !hasUnreadNotifications;

                    return TextButton.icon(
                      icon: Icon(
                        Icons.mark_email_read,
                        color: isDisabled ? Colors.grey.shade400 : null,
                      ),
                      label: Text(
                        'Mark all as read',
                        style: TextStyle(
                          color: isDisabled ? Colors.grey.shade400 : null,
                        ),
                      ),
                      onPressed: isDisabled
                        ? null
                        : () {
                            _markAllAsRead(context);
                            widget.refreshNotifications();
                          },
                    );
                  }
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationItem(BuildContext context, app_notification.Notification notification) {
    // Get icon and color based on notification title
    IconData iconData;
    Color iconColor;

    if (notification.title.contains('Assigned')) {
      iconData = Icons.assignment;
      iconColor = Colors.blue;
    } else if (notification.title.contains('Status')) {
      iconData = Icons.update;
      iconColor = Colors.purple;
    } else if (notification.title.contains('Approved')) {
      iconData = Icons.check_circle;
      iconColor = Colors.green;
    } else if (notification.title.contains('Rejected')) {
      iconData = Icons.cancel;
      iconColor = Colors.red;
    } else if (notification.title.contains('Rated')) {
      iconData = Icons.star;
      iconColor = Colors.amber;
    } else if (notification.title.contains('Driver Rejected')) {
      iconData = Icons.person_off;
      iconColor = Colors.orange;
    } else {
      iconData = Icons.notifications;
      iconColor = Colors.blue;
    }

    return ListTile(
      dense: true,
      leading: CircleAvatar(
        backgroundColor: Colors.grey.shade100,
        radius: 16,
        child: Icon(iconData, color: iconColor, size: 16),
      ),      title: Text(
        NotificationTranslationService.translateTitle(context, notification),
        style: TextStyle(
          fontWeight: notification.isRead ? FontWeight.normal : FontWeight.bold,
          fontSize: 14,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            NotificationTranslationService.translateMessage(context, notification),
            style: const TextStyle(fontSize: 12),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          Text(
            notification.getRelativeTime(),
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
      tileColor: notification.isRead ? null : Colors.blue.shade50,
      onTap: () {
        // Mark as read when tapped
        if (!notification.isRead) {
          Provider.of<NotificationService>(context, listen: false)
              .markAsRead(notification.notificationId);
          widget.refreshNotifications();
        }

        // Show notification details
        _showNotificationDetails(context, notification);
      },
    );
  }

  void _showNotificationDetails(BuildContext context, app_notification.Notification notification) {
    // We'll position the details at the top of the screen

    // Create an overlay entry to show the notification details above the notification list
    final OverlayState overlayState = Overlay.of(context);

    // Create a variable to hold the overlay entry
    late OverlayEntry overlayEntry;

    // Define the close function
    void closeOverlay() {
      overlayEntry.remove();
    }

    // Create the overlay entry
    overlayEntry = OverlayEntry(
      builder: (context) => Stack(
        children: [
          // Semi-transparent dark overlay to focus on the notification details
          // Also acts as a tap detector to close the details when tapped outside
          Positioned.fill(
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: closeOverlay,
              // Semi-transparent black background to create a focus effect
              // Using withAlpha instead of withOpacity to avoid deprecation warning
              child: Container(color: Colors.black.withAlpha(128)),
            ),
          ),
          // The actual notification details panel
          Positioned(
            // Position it centered horizontally on the screen
            left: (MediaQuery.of(context).size.width - 350) / 2,
            // Position it above the notification panel
            // We want it to appear at the top of the screen, but below the app bar
            top: 60,
            width: 350,
            child: Material(
              // Increased elevation to make it stand out more against the dark background
              elevation: 16,
              borderRadius: BorderRadius.circular(8),
              child: Container(
                constraints: const BoxConstraints(maxHeight: 300),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade300),
                  // Add a subtle shadow for better visual separation
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(50),
                      blurRadius: 10,
                      spreadRadius: 1,
                    ),
                  ],
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Title and close button
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [                          Expanded(
                            child: Text(
                              NotificationTranslationService.translateTitle(context, notification),
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                          ),
                          IconButton(
                            icon: const Icon(Icons.close),
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                            onPressed: closeOverlay,
                          ),
                        ],
                      ),
                      const Divider(),                      // Message
                      const SizedBox(height: 8),
                      Text(
                        NotificationTranslationService.translateMessage(context, notification),
                        style: const TextStyle(fontSize: 14),
                      ),
                      const SizedBox(height: 16),
                      // Timestamp
                      Text(
                        'Received: ${notification.createdAt.toString().substring(0, 19)}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 16),
                      // Close button
                      Align(
                        alignment: Alignment.centerRight,
                        child: TextButton(
                          onPressed: closeOverlay,
                          child: const Text('Close'),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );

    // Add the overlay entry to show the notification details
    overlayState.insert(overlayEntry);

    // No auto-close functionality - notification details will remain open until user closes them
  }

  void _fetchNotifications() {
    final appState = Provider.of<MyAppState>(context, listen: false);
    final user = appState.currentUser;

    if (user == null) return;

    final notificationService = Provider.of<NotificationService>(context, listen: false);
    notificationService.fetchNotifications(user.userId, includeRead: _includeRead);
  }

  void _markAllAsRead(BuildContext context) async {
    final appState = Provider.of<MyAppState>(context, listen: false);
    final user = appState.currentUser;

    if (user == null) return;

    final notificationService = Provider.of<NotificationService>(context, listen: false);
    final success = await notificationService.markAllAsRead(user.userId);

    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(success
            ? 'All notifications marked as read'
            : 'Failed to mark notifications as read'
          ),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );
    }
  }
}


