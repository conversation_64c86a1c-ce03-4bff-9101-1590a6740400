class Car {
  final int? carId;
  final String carCode;
  final String manufacturer;
  final String modelName;
  final int odometer;
  final String type;
  final String plateNumber;
  final String color;
  final bool isAssigned;
  final bool isActive;

  Car({
    this.carId,
    required this.carCode,
    required this.manufacturer,
    required this.modelName,
    required this.odometer,
    required this.type,
    required this.plateNumber,
    required this.color,
    this.isAssigned = false,
    this.isActive = true,
  });

  factory Car.fromJson(Map<String, dynamic> json) {
    return Car(
      carId: json['car_id'] is String ? int.tryParse(json['car_id']) : json['car_id'],
      carCode: json['car_code'] ?? '',
      manufacturer: json['manufacturer'] ?? '',
      modelName: json['model_name'] ?? '',
      odometer: json['odometer'] is String
          ? int.tryParse(json['odometer']) ?? 0
          : json['odometer'] ?? 0,
      type: json['type'] ?? '',
      plateNumber: json['plate_number'] ?? '',
      color: json['color'] ?? '',
      isAssigned: json['is_assigned'] == true,
      isActive: json['is_active'] == null ? true : json['is_active'] == true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'car_id': carId,
      'car_code': carCode,
      'manufacturer': manufacturer,
      'model_name': modelName,
      'odometer': odometer,
      'type': type,
      'plate_number': plateNumber,
      'color': color,
      'is_assigned': isAssigned,
      'is_active': isActive,
    };
  }

  /// Get a display name for the car
  String get displayName => '$carCode - $manufacturer $modelName';

  /// Get a shorter display name for the car (for dropdowns)
  String get shortDisplayName => '$carCode - $modelName';

  /// Get a detailed display name for the car
  String get detailedDisplayName => '$manufacturer $modelName ($plateNumber)';

  @override
  String toString() {
    return 'Car{carId: $carId, carCode: $carCode, manufacturer: $manufacturer, modelName: $modelName, type: $type, plateNumber: $plateNumber, color: $color, isAssigned: $isAssigned, isActive: $isActive}';
  }
}




