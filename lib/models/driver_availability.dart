import 'package:flutter/material.dart';

class DriverAvailability {
  final int driverId;
  final String driverName;
  final DateTime date;
  final String status; // 'AVAILABLE', 'UNAVAILABLE'
  final String? notes;

  DriverAvailability({
    required this.driverId,
    required this.driverName,
    required this.date,
    required this.status,
    this.notes,
  });

  // Helper method to get default status based on day of week
  static String getDefaultStatusForDate(DateTime date) {
    // Ensure we're using local date and check if it's a weekend (Saturday or Sunday)
    final localDate = date.toLocal();
    final bool isWeekend = localDate.weekday == DateTime.saturday || localDate.weekday == DateTime.sunday;

    // Debug print to help diagnose the issue
    if (isWeekend) {
      print('getDefaultStatusForDate: Date ${localDate.toString()} is a weekend (${localDate.weekday}), returning UNAVAILABLE');
      print('  - Raw date provided: ${date.toString()}, after toLocal(): ${localDate.toString()}');
    } else {
      print('getDefaultStatusForDate: Date ${localDate.toString()} is a weekday (${localDate.weekday}), returning AVAILABLE');
      print('  - Raw date provided: ${date.toString()}, after toLocal(): ${localDate.toString()}');
    }

    // Weekdays: AVAILABLE, Weekends: UNAVAILABLE
    return isWeekend ? 'UNAVAILABLE' : 'AVAILABLE';
  }

  factory DriverAvailability.fromJson(Map<String, dynamic> json) {
    // Parse driver_id, handling both string and int formats
    int driverId = 0;
    if (json['driver_id'] != null) {
      if (json['driver_id'] is String) {
        driverId = int.tryParse(json['driver_id']) ?? 0;
      } else if (json['driver_id'] is int) {
        driverId = json['driver_id'];
      }
    }

    return DriverAvailability(
      driverId: driverId,
      driverName: json['driver_name'] ?? '',
      date: DateTime.parse(json['date']).toLocal(),
      status: json['status'],
      notes: json['notes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'driver_id': driverId,
      'driver_name': driverName,
      'date': date.toLocal().toIso8601String().split('T')[0], // Format as YYYY-MM-DD
      'status': status,
      'notes': notes,
    };
  }

  // Helper method to get color based on status
  static Color getStatusColor(String status) {
    switch (status.toUpperCase()) {
      case 'AVAILABLE':
        return Colors.green.shade100;
      case 'UNAVAILABLE':
        return Colors.red.shade100;
      default:
        return Colors.grey.shade100;
    }
  }

  // Helper method to get text color based on status
  static Color getStatusTextColor(String status) {
    switch (status.toUpperCase()) {
      case 'AVAILABLE':
        return Colors.green.shade800;
      case 'UNAVAILABLE':
        return Colors.red.shade800;
      default:
        return Colors.grey.shade800;
    }
  }

  // Check if a reason is required for this status
  static bool isReasonRequired(String status) {
    return false; // Notes are now optional for all statuses
  }
}




