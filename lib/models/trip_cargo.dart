class TripCargo {
  final String id;
  final String name;
  final String? code;

  TripCargo({
    required this.id,
    required this.name,
    this.code,
  });

  factory TripCargo.fromJson(Map<String, dynamic> json) {
    return TripCargo(
      id: json['id']?.toString() ?? json['cargo_id']?.toString() ?? '',
      name: json['name']?.toString() ?? json['cargo_name']?.toString() ?? '',
      code: json['code']?.toString() ?? json['cargo_code']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'code': code,
    };
  }
}


