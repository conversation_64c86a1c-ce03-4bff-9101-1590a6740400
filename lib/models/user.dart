class User {
  final String userId;
  final String name;
  final String role;
  final String? driverCode; // Added driver code field
  final String? activeRole; // Current active role for multi-role users

  User({
    required this.userId,
    required this.name,
    required this.role,
    this.driverCode,
    this.activeRole,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      userId: json['user_id']?.toString() ?? '',
      name: json['name']?.toString() ?? '',
      role: json['role']?.toString() ?? 'Requestor',
      driverCode: json['driver_code']?.toString(),
      activeRole: json['active_role']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'name': name,
      'role': role,
      'driver_code': driverCode,
      'active_role': activeRole,
    };
  }

  // Get the currently active role (for display and navigation purposes)
  String get currentRole => activeRole ?? role;

  // Primary role checks (based on the main role assigned to the user)
  bool get isTripManager => role.toLowerCase() == 'trip manager' ||
                       (role.toLowerCase().contains('trip') && role.toLowerCase().contains('manager'));
  // Legacy method maintained for backward compatibility, but now just redirects to isTripManager
  bool get isAdmin => isTripManager;
  bool get isDriver => role.toLowerCase() == 'driver' || role.toLowerCase().contains('driver');
  bool get isSuperAdmin => role.toLowerCase() == 'super admin' || role.toLowerCase().contains('super');

  // Active role checks (based on currently selected role)
  bool get isCurrentlyTripManager => currentRole.toLowerCase() == 'trip manager' ||
                                   (currentRole.toLowerCase().contains('trip') && currentRole.toLowerCase().contains('manager'));
  bool get isCurrentlyRequestor => currentRole.toLowerCase() == 'requestor' || currentRole.toLowerCase() == 'user';
  bool get isCurrentlyDriver => currentRole.toLowerCase() == 'driver' || currentRole.toLowerCase().contains('driver');

  // Get available roles for this user
  List<String> get availableRoles {
    final List<String> roles = [role]; // Always include the primary role
    
    // Trip Managers can also act as Requestors
    if (isTripManager && !roles.contains('Requestor')) {
      roles.add('Requestor');
    }
    
    return roles;
  }

  // Check if user can switch to a specific role
  bool canSwitchToRole(String targetRole) {
    return availableRoles.map((r) => r.toLowerCase()).contains(targetRole.toLowerCase());
  }

  // Create a copy of the user with a different active role
  User copyWithActiveRole(String newActiveRole) {
    if (!canSwitchToRole(newActiveRole)) {
      throw ArgumentError('User cannot switch to role: $newActiveRole');
    }
    
    return User(
      userId: userId,
      name: name,
      role: role,
      driverCode: driverCode,
      activeRole: newActiveRole,
    );
  }
}






