class Driver {
  final int driverId; // Add driver_id field
  final String driverCode;
  final String name;
  final String initial;
  final String carCode;
  final DateTime? lastUpdated; // Add last updated timestamp
  final bool isActive; // Add is_active field

  Driver({
    required this.driverId,
    required this.driverCode,
    required this.name,
    required this.initial,
    required this.carCode,
    this.lastUpdated,
    this.isActive = true,
  });

  factory Driver.fromJson(Map<String, dynamic> json) {
    // Parse driver_id, handling both string and int formats
    int driverId = 0;
    if (json['driver_id'] != null) {
      if (json['driver_id'] is String) {
        driverId = int.tryParse(json['driver_id']) ?? 0;
      } else if (json['driver_id'] is int) {
        driverId = json['driver_id'];
      }
    }

    // Parse timestamp if available
    DateTime? lastUpdated;
    if (json['timestamp'] != null) {
      try {
        lastUpdated = DateTime.parse(json['timestamp']);
      } catch (e) {
        print('Error parsing timestamp: $e');
      }
    }

    return Driver(
      driverId: driverId,
      driverCode: json['driver_code'],
      name: json['name'],
      initial: json['initial'],
      carCode: json['car_code'] ?? '',
      lastUpdated: lastUpdated,
      isActive: json['is_active'] == null ? true : json['is_active'] == true,
    );
  }
}


