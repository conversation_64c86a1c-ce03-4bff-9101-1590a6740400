class Cargo {
  final String cargoCode;
  final String cargoName;
  final bool isActive;

  Cargo({
    required this.cargoCode,
    required this.cargoName,
    this.isActive = true,
  });

  factory Cargo.fromJson(Map<String, dynamic> json) {
    return Cargo(
      cargoCode: json['cargoCode'] ?? json['cargo_code'],  // Handle both formats
      cargoName: json['cargoName'] ?? json['cargo_name'],  // Handle both formats
      isActive: json['isActive'] == null ? true : json['isActive'] == true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'cargo_code': cargoCode,
      'cargo_name': cargoName,
      'is_active': isActive,
    };
  }
}


