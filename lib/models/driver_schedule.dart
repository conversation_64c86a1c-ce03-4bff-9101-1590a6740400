import 'package:intl/intl.dart';

class DriverSchedule {
  final int driverId;
  final String driverCode; // From driver_detail table
  final String driverName;
  final int? tripId; // Added trip_id for API updates
  final String tripCode;
  final DateTime startTime;
  DateTime endTime; // Changed from final to allow updating
  final String fromDestination;
  final String toDestination;
  String? fromDestinationInitial;
  String? toDestinationInitial;
  final String status;  DateTime? totalDuration; // Added total_duration field
  final DateTime? originalStartTime; // Added to store the original start time for cross-date trips
  final int? carId; // ID of the car assigned to this trip
  final String? carCode; // Code of the car assigned to this trip
  final String? carPlateNumber; // Plate number of the car assigned to this trip
  final bool? isWaiting; // Whether this is a waiting trip

  DriverSchedule({
    required this.driverId,
    required this.driverCode,
    required this.driverName,
    this.tripId,
    required this.tripCode,
    required this.startTime,
    required this.endTime,
    required this.fromDestination,
    required this.toDestination,
    this.fromDestinationInitial,
    this.toDestinationInitial,
    required this.status,
    this.totalDuration,
    this.originalStartTime,
    this.carId,
    this.carCode,
    this.carPlateNumber,
    this.isWaiting,
  }) {
    // Debug print for car information
    print('DriverSchedule constructor for $tripCode: carId=$carId, carCode=$carCode, carPlateNumber=$carPlateNumber');
  }

  factory DriverSchedule.fromJson(Map<String, dynamic> json) {
    // Parse the start and end times
    final DateTime startTime = DateTime.parse('${json['date']} ${json['time']}');

    // Parse trip_id, handling both string and int formats
    int? tripId;
    if (json['trip_id'] != null) {
      if (json['trip_id'] is String) {
        tripId = int.tryParse(json['trip_id']);
      } else if (json['trip_id'] is int) {
        tripId = json['trip_id'];
      }
    }

    // Parse total_duration if available
    DateTime? totalDuration;
    if (json['total_duration'] != null && json['total_duration'].toString().isNotEmpty) {
      try {
        // Parse the total_duration timestamp
        totalDuration = DateTime.parse(json['total_duration']);
      } catch (e) {
        // Error parsing total_duration, will use default
      }
    }

    // Calculate end time based on total_duration or default to 30 minutes
    DateTime endTime;
    if (totalDuration != null) {
      // Use the total_duration as the end time
      endTime = totalDuration;
    } else {
      // Default to 30 minutes after start time if total_duration is null
      endTime = startTime.add(const Duration(minutes: 30));
    }

    // Parse driver_id, handling both string and int formats
    int driverId = 0;
    if (json['driver_id'] != null) {
      if (json['driver_id'] is String) {
        driverId = int.tryParse(json['driver_id']) ?? 0;
      } else if (json['driver_id'] is int) {
        driverId = json['driver_id'];
      }
    }

    // Get destination names and initials
    final String fromDest = json['from_destination'] ?? '';
    final String toDest = json['to_destination'] ?? '';

    // Get initials if provided, otherwise use 'OTH' for custom destinations
    String? fromInitial = json['from_destination_initial'];
    String? toInitial = json['to_destination_initial'];

    // If custom destination or no initials, set to 'OTH'
    if (json['custom_from'] != null || fromInitial == null) {
      fromInitial = 'OTH';
    }

    // If custom destination or no initials, set to 'OTH'
    if (json['custom_to'] != null || toInitial == null) {
      toInitial = 'OTH';
    }

    // Parse car_id, handling both string and int formats
    int? carId;
    if (json['car_id'] != null) {
      if (json['car_id'] is String) {
        carId = int.tryParse(json['car_id']);
      } else if (json['car_id'] is int) {
        carId = json['car_id'];
      }
    }

    return DriverSchedule(
      driverId: driverId,
      driverCode: json['driver_code'] ?? '',
      driverName: json['driver_name'] ?? '',
      tripId: tripId,
      tripCode: json['trip_code'] ?? '',
      startTime: startTime,
      endTime: endTime,
      fromDestination: fromDest,
      toDestination: toDest,
      fromDestinationInitial: fromInitial,
      toDestinationInitial: toInitial,      status: json['status'] ?? '',
      totalDuration: totalDuration,
      originalStartTime: null, // This will be set by the calling code if needed
      carId: carId,
      carCode: json['car_code'],
      carPlateNumber: json['plate_number'],
      isWaiting: json['is_waiting'] ?? false,
    );
  }

  // Helper method to format time for display
  String formatTime(DateTime time) {
    return DateFormat('HH:mm').format(time);
  }

  // Check if this schedule overlaps with a given time slot
  bool isInTimeSlot(DateTime slotStart, DateTime slotEnd) {
    // Special case for cross-date trips on subsequent days
    if (originalStartTime != null) {
      // This is a cross-date trip displayed on a subsequent day

      // For subsequent days, we want the trip to start at 00:00
      // So we check if this is the 00:00 time slot
      final int slotHour = slotStart.hour;
      final int slotMinute = slotStart.minute;

      // If this is the 00:00 time slot, this is the starting slot for this day
      if (slotHour == 0 && slotMinute == 0) {
        print('Cross-date trip $tripCode on subsequent date: Starting at 00:00');
        return true;
      }

      // For any other time slot on a subsequent day, this is not the starting slot
      return false;
    }

    // Check if we're dealing with a cross-date trip
    final bool isCrossDateTrip = (totalDuration != null &&
                                 (startTime.year != totalDuration!.year ||
                                  startTime.month != totalDuration!.month ||
                                  startTime.day != totalDuration!.day)) ||
                                 (startTime.year != endTime.year ||
                                  startTime.month != endTime.month ||
                                  startTime.day != endTime.day);

    // For cross-date trips, we need to check if the slot date matches the trip start date
    if (isCrossDateTrip) {
      // Create date-only objects for comparison
      final DateTime tripStartDate = DateTime(
        startTime.year, startTime.month, startTime.day
      );

      // Get the end date of the trip
      DateTime tripEndDate;
      if (totalDuration != null) {
        tripEndDate = DateTime(
          totalDuration!.year, totalDuration!.month, totalDuration!.day
        );
      } else {
        tripEndDate = DateTime(
          endTime.year, endTime.month, endTime.day
        );
      }

      final DateTime slotDate = DateTime(
        slotStart.year, slotStart.month, slotStart.day
      );

      // We don't need the end date for isInTimeSlot, only for continuesIntoTimeSlot
      // This is because isInTimeSlot only checks if this is the starting slot

      // For the starting slot, we only care about the start date
      final bool isStartDate = slotDate.isAtSameMomentAs(tripStartDate);

      // Check if the slot date is between the trip start and end dates (inclusive)
      final bool isDateInRange = (slotDate.isAtSameMomentAs(tripStartDate) ||
                                 slotDate.isAfter(tripStartDate)) &&
                                (slotDate.isAtSameMomentAs(tripEndDate) ||
                                 slotDate.isBefore(tripEndDate));

      // Log for debugging
      print('Cross-date trip $tripCode start date check:');
      print('  Trip start date: ${tripStartDate.toIso8601String()}');
      print('  Trip end date: ${tripEndDate.toIso8601String()}');
      print('  Slot date: ${slotDate.toIso8601String()}');
      print('  Is start date: $isStartDate');
      print('  Is date in range: $isDateInRange');

      // If this is not the start date, this is not the starting slot
      // (it might be a continuation slot, which is handled by continuesIntoTimeSlot)
      if (!isStartDate) {
        print('  Not the start date, returning false');
        return false;
      }

      // If we're here, this is the start date, so continue with the regular time slot check
    }

    // Continue with the regular time slot check
    final int slotHour = slotStart.hour;
    final int slotMinute = slotStart.minute;

    // Use the actual start time for the check
    final int scheduleHour = startTime.hour;
    final int scheduleMinute = startTime.minute;

    // Check if the schedule starts in this time slot
    if (slotHour == scheduleHour) {
      // For 00 minute slots (e.g., 09:00), include schedules from 00 to 29
      if (slotMinute == 0 && scheduleMinute < 30) {
        return true;
      }
      // For 30 minute slots (e.g., 09:30), include schedules from 30 to 59
      if (slotMinute == 30 && scheduleMinute >= 30) {
        return true;
      }
    }

    return false;
  }

  // Check if this schedule continues into a given time slot
  bool continuesIntoTimeSlot(DateTime slotStart, DateTime slotEnd) {
    // This is used to determine if a schedule should be shown in a time slot that's not its starting slot

    // First, check if this is the starting slot (already handled by isInTimeSlot)
    final bool isStartingSlot = isInTimeSlot(slotStart, slotEnd);
    if (isStartingSlot) {
      return false; // Don't count as continuation if it's the starting slot
    }

    // For cross-date trips on subsequent days, we need a special approach
    if (originalStartTime != null) {
      // This is a cross-date trip displayed on a subsequent day

      // Create date-only objects for comparison
      final DateTime slotDate = DateTime(
        slotStart.year, slotStart.month, slotStart.day
      );

      // Get the end date of the trip
      DateTime tripEndDate;
      if (totalDuration != null) {
        tripEndDate = DateTime(
          totalDuration!.year, totalDuration!.month, totalDuration!.day
        );
      } else {
        tripEndDate = DateTime(
          endTime.year, endTime.month, endTime.day
        );
      }

      // Check if the slot date is on or before the trip end date
      final bool isBeforeOrOnEndDate = slotDate.isAtSameMomentAs(tripEndDate) ||
                                      slotDate.isBefore(tripEndDate);

      if (!isBeforeOrOnEndDate) {
        // If the slot date is after the trip end date, this trip doesn't continue into this slot
        return false;
      }

      // If we're here, the slot date is on or before the trip end date

      // For the start time, use the beginning of the day (00:00)
      final DateTime scheduleStart = DateTime(
        slotStart.year, slotStart.month, slotStart.day, 0, 0
      );

      // For the end time, use the actual end time if we're on the end date, otherwise use end of day
      DateTime scheduleEnd;
      if (slotDate.isAtSameMomentAs(tripEndDate)) {
        // We're on the end date, use the actual end time
        if (totalDuration != null) {
          scheduleEnd = DateTime(
            totalDuration!.year, totalDuration!.month, totalDuration!.day,
            totalDuration!.hour, totalDuration!.minute
          );
        } else {
          scheduleEnd = DateTime(
            endTime.year, endTime.month, endTime.day,
            endTime.hour, endTime.minute
          );
        }
      } else {
        // We're on a date between start and end, use end of day
        scheduleEnd = DateTime(
          slotStart.year, slotStart.month, slotStart.day, 23, 59
        );
      }

      // Check if the slot starts after the schedule starts and before the schedule ends
      final bool result = slotStart.isAfter(scheduleStart) && slotStart.isBefore(scheduleEnd);

      // Debug logging
      print('Cross-date trip $tripCode on subsequent date:');
      print('  Slot date: ${slotDate.toIso8601String()}');
      print('  Trip end date: ${tripEndDate.toIso8601String()}');
      print('  Is before or on end date: $isBeforeOrOnEndDate');
      print('  Schedule start: ${scheduleStart.toIso8601String()}');
      print('  Schedule end: ${scheduleEnd.toIso8601String()}');
      print('  Slot start: ${slotStart.toIso8601String()}');
      print('  Slot is after schedule start: ${slotStart.isAfter(scheduleStart)}');
      print('  Slot is before schedule end: ${slotStart.isBefore(scheduleEnd)}');
      print('  Result: $result');

      return result;
    }

    // Check if we're dealing with a cross-date trip
    final bool isCrossDateTrip = (totalDuration != null &&
                                 (startTime.year != totalDuration!.year ||
                                  startTime.month != totalDuration!.month ||
                                  startTime.day != totalDuration!.day)) ||
                                 (startTime.year != endTime.year ||
                                  startTime.month != endTime.month ||
                                  startTime.day != endTime.day);

    DateTime scheduleStart;
    DateTime scheduleEnd;

    if (isCrossDateTrip) {
      // For cross-date trips, we need to use the actual dates
      final DateTime tripStartDate = DateTime(
        startTime.year, startTime.month, startTime.day
      );

      final DateTime slotDate = DateTime(
        slotStart.year, slotStart.month, slotStart.day
      );

      // Determine the end date of the trip
      DateTime tripEndDate;
      if (totalDuration != null) {
        tripEndDate = DateTime(
          totalDuration!.year, totalDuration!.month, totalDuration!.day
        );
      } else {
        tripEndDate = DateTime(
          endTime.year, endTime.month, endTime.day
        );
      }

      // Check if the slot date is between the trip start and end dates (inclusive)
      // For a date to be in range, it must be:
      // 1. On or after the trip start date AND
      // 2. On or before the trip end date
      final bool isDateInRange = (slotDate.isAtSameMomentAs(tripStartDate) ||
                                 slotDate.isAfter(tripStartDate)) &&
                                (slotDate.isAtSameMomentAs(tripEndDate) ||
                                 slotDate.isBefore(tripEndDate));

      // Debug logging for cross-date trip detection
      print('Cross-date trip $tripCode continuation check:');
      print('  Trip start date: ${tripStartDate.toIso8601String()}');
      print('  Trip end date: ${tripEndDate.toIso8601String()}');
      print('  Slot date: ${slotDate.toIso8601String()}');
      print('  Is on or after start date: ${slotDate.isAtSameMomentAs(tripStartDate) || slotDate.isAfter(tripStartDate)}');
      print('  Is on or before end date: ${slotDate.isAtSameMomentAs(tripEndDate) || slotDate.isBefore(tripEndDate)}');
      print('  Is date in range: $isDateInRange');

      // If the date is not in range, this trip doesn't continue into this slot
      if (!isDateInRange) {
        print('  Date not in range, returning false');
        return false;
      }

      // If we're here, the date is in range, so continue with the time slot check

      // Now handle the time part based on which date we're on
      if (slotDate.isAtSameMomentAs(tripStartDate)) {
        // We're on the start date of the trip
        scheduleStart = DateTime(
          startTime.year, startTime.month, startTime.day,
          startTime.hour, startTime.minute
        );
      } else {
        // We're on a different date (middle or end date)
        // Use the beginning of the day as the start time for continuation
        scheduleStart = DateTime(
          slotStart.year, slotStart.month, slotStart.day, 0, 0
        );
      }

      // For the end time, use the actual end time if we're on the end date
      if (slotDate.isAtSameMomentAs(tripEndDate)) {
        // We're on the end date, use the actual end time
        if (totalDuration != null) {
          scheduleEnd = DateTime(
            totalDuration!.year, totalDuration!.month, totalDuration!.day,
            totalDuration!.hour, totalDuration!.minute
          );
        } else {
          scheduleEnd = DateTime(
            endTime.year, endTime.month, endTime.day,
            endTime.hour, endTime.minute
          );
        }
      } else {
        // We're on a date between start and end, use end of day
        scheduleEnd = DateTime(
          slotStart.year, slotStart.month, slotStart.day, 23, 59
        );
      }

      // Log for debugging
      print('Cross-date trip $tripCode on ${slotDate.toIso8601String()}: '
            'Start=${scheduleStart.toIso8601String()}, End=${scheduleEnd.toIso8601String()}');
    } else {
      // For same-day trips, use the original logic
      scheduleStart = DateTime(
        slotStart.year, slotStart.month, slotStart.day,
        startTime.hour, startTime.minute
      );

      // Calculate the duration in minutes
      final int durationMinutes = getDurationMinutes();

      // Create the end time by adding the duration to the start time
      scheduleEnd = scheduleStart.add(Duration(minutes: durationMinutes));
    }

    // Check if the slot starts after the schedule starts and before the schedule ends
    final bool result = slotStart.isAfter(scheduleStart) && slotStart.isBefore(scheduleEnd);

    // Debug logging
    print('  Slot start: ${slotStart.toIso8601String()}');
    print('  Schedule start: ${scheduleStart.toIso8601String()}');
    print('  Schedule end: ${scheduleEnd.toIso8601String()}');
    print('  Slot is after schedule start: ${slotStart.isAfter(scheduleStart)}');
    print('  Slot is before schedule end: ${slotStart.isBefore(scheduleEnd)}');
    print('  Result: $result');

    return result;
  }

  // Get the duration in minutes
  int getDurationMinutes() {
    // Use originalStartTime if available, otherwise use startTime
    final DateTime effectiveStartTime = originalStartTime ?? startTime;

    // If totalDuration is available, use it to calculate the duration
    if (totalDuration != null) {
      try {
        // For all cross-date trips, always calculate the total duration from the original start time
        // to the end time, regardless of which date is being viewed
        return totalDuration!.difference(effectiveStartTime).inMinutes;
      } catch (e) {
        // If there's an error, fall back to using endTime
        print('Error calculating duration from totalDuration: $e');
        // Continue to the endTime calculation below
      }
    }

    // Fall back to using endTime if totalDuration is not available or there was an error
    try {
      // For all cross-date trips, always calculate the total duration from the original start time
      // to the end time, regardless of which date is being viewed
      return endTime.difference(effectiveStartTime).inMinutes;
    } catch (e) {
      print('Error calculating duration from endTime: $e');
      // Default to 30 minutes if all calculations fail
      return 30;
    }
  }

  // Update the end time
  void updateEndTime(DateTime newEndTime) {
    // Update the end time
    endTime = newEndTime;

    // Check if the new end time is on the same day as the start time
    final bool isSameDay = startTime.year == newEndTime.year &&
                          startTime.month == newEndTime.month &&
                          startTime.day == newEndTime.day;

    // Update the total_duration to match the end time
    if (isSameDay) {
      // Same day - create a new DateTime with the same date as the start time
      totalDuration = DateTime(
        startTime.year, startTime.month, startTime.day,
        newEndTime.hour, newEndTime.minute
      );
    } else {
      // Cross-date trip - preserve the actual date of the end time
      totalDuration = DateTime(
        newEndTime.year, newEndTime.month, newEndTime.day,
        newEndTime.hour, newEndTime.minute
      );
    }

    // End time and total_duration updated
    print('Updated trip duration: ${getDurationMinutes()} minutes');
  }



  // Convert to a map for API updates
  Map<String, dynamic> toJson() {
    // Format the total_duration as an ISO string
    String? formattedTotalDuration;
    if (totalDuration != null) {
      formattedTotalDuration = totalDuration!.toIso8601String();
      // Total duration formatted for API
    }

    return {
      'trip_id': tripId,
      'total_duration': formattedTotalDuration,
      'car_id': carId,
      'car_code': carCode,
      'plate_number': carPlateNumber,
    };
  }
}


