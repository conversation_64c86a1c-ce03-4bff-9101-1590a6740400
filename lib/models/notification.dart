import 'dart:convert';

class Notification {
  final int notificationId;
  final dynamic userId; // Changed from int to dynamic to handle both string and int
  final String title;
  final String message;
  final bool isRead;
  final DateTime createdAt;
  final Map<String, dynamic>? localizationData; // Add localization data field

  Notification({
    required this.notificationId,
    required this.userId,
    required this.title,
    required this.message,
    required this.isRead,
    required this.createdAt,
    this.localizationData,
  });

  factory Notification.fromJson(Map<String, dynamic> json) {
    // Debug: Print the raw JSON data
    print('Parsing notification JSON: $json');

    try {
      // Convert notification_id to int if it's a string
      int notificationId;
      if (json['notification_id'] is String) {
        notificationId = int.parse(json['notification_id']);
      } else {
        notificationId = json['notification_id'];
      }

      // Keep userId as dynamic to handle both string and int
      final userId = json['user_id'];

      // Convert is_read to bool if it's not already
      bool isRead;
      if (json['is_read'] is String) {
        isRead = json['is_read'].toLowerCase() == 'true';
      } else {
        isRead = json['is_read'] ?? false;
      }

      // Parse created_at as DateTime
      DateTime createdAt;
      if (json['created_at'] is String) {
        createdAt = DateTime.parse(json['created_at']);
      } else {
        // Default to current time if missing or invalid
        createdAt = DateTime.now();
      }

      // Parse localization_data if present
      Map<String, dynamic>? localizationData;
      if (json['localization_data'] != null) {
        if (json['localization_data'] is String) {
          // If it's a JSON string, decode it
          try {
            localizationData = Map<String, dynamic>.from(
              jsonDecode(json['localization_data'])
            );
          } catch (e) {
            print('Error parsing localization_data JSON string: $e');
          }
        } else if (json['localization_data'] is Map) {
          // If it's already a Map, use it directly
          localizationData = Map<String, dynamic>.from(json['localization_data']);
        }
      }

      return Notification(
        notificationId: notificationId,
        userId: userId,
        title: json['title'] ?? '',
        message: json['message'] ?? '',
        isRead: isRead,
        createdAt: createdAt,
        localizationData: localizationData,
      );
    } catch (e) {
      print('Error parsing notification: $e');
      print('JSON data: $json');

      // Return a default notification instead of rethrowing
      return Notification(
        notificationId: 0,
        userId: 0,
        title: 'Error parsing notification',
        message: 'There was an error parsing this notification: ${e.toString()}',
        isRead: false,
        createdAt: DateTime.now(),
        localizationData: null,
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'notification_id': notificationId,
      'user_id': userId,
      'title': title,
      'message': message,
      'is_read': isRead,
      'created_at': createdAt.toIso8601String(),
      'localization_data': localizationData != null ? jsonEncode(localizationData) : null,
    };
  }

  // Helper method to get an icon name
  String get iconName {
    // Determine icon based on title content
    if (title.contains('Assigned')) {
      return 'assignment';
    } else if (title.contains('Status')) {
      return 'update';
    } else if (title.contains('Approved')) {
      return 'check_circle';
    } else if (title.contains('Rejected')) {
      return 'cancel';
    } else if (title.contains('Rated')) {
      return 'star';
    } else if (title.contains('Driver Rejected')) {
      return 'person_off';
    } else {
      return 'notifications';
    }
  }

  // Helper method to get a color name
  String get colorName {
    // Determine color based on title content
    if (title.contains('Assigned')) {
      return 'blue';
    } else if (title.contains('Status')) {
      return 'purple';
    } else if (title.contains('Approved')) {
      return 'green';
    } else if (title.contains('Rejected')) {
      return 'red';
    } else if (title.contains('Rated')) {
      return 'amber';
    } else if (title.contains('Driver Rejected')) {
      return 'orange';
    } else {
      return 'blue';
    }
  }

  // Helper method to get a relative time string
  String getRelativeTime() {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 7) {
      return '${createdAt.year}-${createdAt.month.toString().padLeft(2, '0')}-${createdAt.day.toString().padLeft(2, '0')}';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} ${difference.inDays == 1 ? 'day' : 'days'} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ${difference.inHours == 1 ? 'hour' : 'hours'} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} ${difference.inMinutes == 1 ? 'minute' : 'minutes'} ago';
    } else {
      return 'Just now';
    }
  }
}


