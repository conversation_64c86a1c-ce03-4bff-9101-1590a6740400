import 'package:intl/intl.dart';
import 'passenger.dart';
import 'trip_cargo.dart';

class Trip {
  final int? tripId;
  final String tripCode;
  final String fromDestination;
  final String toDestination;
  final String? fromDestinationInitial; // Initial for from destination
  final String? toDestinationInitial; // Initial for to destination
  final String date;
  final String originalDate; // Store the original date for filtering
  final String time;
  final String status;
  final String? requestorId;
  final String requestorName;
  final int? driverId;
  final String? driverCode; // From driver_detail table
  final String? driverName;
  final int? carId; // ID of the car assigned to this trip
  final String? carCode; // Code of the car assigned to this trip
  final String? plateNumber; // Plate number of the car assigned to this trip
  final int? rating; // Rating given by the requestor (1-5 stars)
  final String? comments; // Comments provided by the requestor when rating
  final String? rejectionReason; // Reason provided when rejecting a trip
  final String? completionNotes; // Notes provided by the driver when completing the trip
  final String? completionImagePath; // Path to the image uploaded by the driver when completing the trip
  final String? imageData; // Base64 encoded image data for completion photos
  final String? totalDuration; // Total duration of the trip (ISO 8601 timestamp)
  final String? notes; // Optional notes for the trip provided by the requestor
  final bool? isWaiting; // Whether this is a waiting trip
  final List<Passenger>? passengers; // List of passengers assigned to this trip
  final List<TripCargo>? cargos; // List of cargos assigned to this trip

  Trip({
    this.tripId,
    required this.tripCode,
    required this.fromDestination,
    required this.toDestination,
    this.fromDestinationInitial,
    this.toDestinationInitial,
    required this.date,
    required this.originalDate,
    required this.time,
    required this.status,
    this.requestorId,
    this.requestorName = 'Unknown',
    this.driverId,
    this.driverCode,
    this.driverName,
    this.carId,
    this.carCode,
    this.plateNumber,
    this.rating,
    this.comments,    this.rejectionReason,
    this.completionNotes,
    this.completionImagePath,
    this.imageData,
    this.totalDuration,
    this.notes,
    this.isWaiting,
    this.passengers,
    this.cargos,
  }) {
    // Debug print for notes in constructor
    print('Trip constructor for $tripCode: notes="$notes"');
  }

  factory Trip.fromJson(Map<String, dynamic> json) {
    // Get the formatted date directly from the API if available
    String formattedDate;
    String originalDate;

    if (json.containsKey('formatted_date') && json['formatted_date'] != null) {
      // Use the formatted date from the server
      originalDate = json['formatted_date'];
      // Parse it to create a display date
      try {
        final DateTime parsedDate = DateFormat('yyyy-MM-dd').parse(originalDate);
        formattedDate = DateFormat('E, dd-MMM-yy').format(parsedDate);
      } catch (e) {
        // If there's an error parsing the date, use the original
        formattedDate = originalDate;
      }
    } else {
      // Fall back to parsing the date from the API

      // Parse the date without applying timezone offset
      final DateTime parsedDate = DateTime.parse(json['date']);

      // For display, we want the date as it appears in the database
      formattedDate = DateFormat('E, dd-MMM-yy').format(parsedDate);

      // For filtering, we want the date in YYYY-MM-DD format as stored in the database
      originalDate = DateFormat('yyyy-MM-dd').format(parsedDate);

    }

    // Parse and format the time to 24-hour format
    String formattedTime;
    try {
      final DateTime parsedTime = DateFormat('HH:mm').parse(json['time']);
      formattedTime = DateFormat('HH:mm').format(parsedTime);
    } catch (e) {
      formattedTime = json['time'];
    }

    // Ensure tripId is an integer
    int? tripId;
    if (json['trip_id'] != null) {
      // Handle both string and int formats
      tripId = json['trip_id'] is String
          ? int.tryParse(json['trip_id'])
          : json['trip_id'] as int?;
    }

    // Parse driver_id if present
    int? driverId;
    if (json['driver_id'] != null) {
      // Handle both string and int formats
      driverId = json['driver_id'] is String
          ? int.tryParse(json['driver_id'])
          : json['driver_id'] as int?;
    }

    // Parse car_id if present
    int? carId;
    if (json['car_id'] != null) {
      // Handle both string and int formats
      carId = json['car_id'] is String
          ? int.tryParse(json['car_id'])
          : json['car_id'] as int?;
    }

    // Process trips with rejection_reason

    // Debug print for notes in fromJson
    print('Trip.fromJson for ${json['trip_code']}: notes="${json['notes'] ?? 'null'}"');

    // Get destination initials or generate them
    String? fromDestInitial = json['from_destination_initial'];
    String? toDestInitial = json['to_destination_initial'];

    // Debug print for destination initials
    print('Trip.fromJson for ${json['trip_code']}: from_destination_initial="${fromDestInitial ?? 'null'}", to_destination_initial="${toDestInitial ?? 'null'}"');
    print('Trip.fromJson for ${json['trip_code']}: custom_from="${json['custom_from'] ?? 'null'}", custom_to="${json['custom_to'] ?? 'null'}"');

    // If initials are not provided, set to 'OTH'
    if (json['custom_from'] != null || fromDestInitial == null) {
      fromDestInitial = 'OTH';
    }

    if (json['custom_to'] != null || toDestInitial == null) {
      toDestInitial = 'OTH';
    }

    // Debug print after setting initials
    print('Trip.fromJson for ${json['trip_code']}: FINAL from_destination_initial="$fromDestInitial", to_destination_initial="$toDestInitial"');

    return Trip(
      tripId: tripId,
      tripCode: json['trip_code'] ?? '',
      fromDestination: json['from_destination'] ?? '',
      toDestination: json['to_destination'] ?? '',
      fromDestinationInitial: fromDestInitial,
      toDestinationInitial: toDestInitial,
      date: formattedDate,
      originalDate: originalDate,  // Store the date in YYYY-MM-DD format for filtering
      time: formattedTime,
      status: json['status'] ?? '',
      requestorId: json['requestor_id']?.toString(),
      requestorName: json['requestor_name'] ?? 'Unknown',
      driverId: driverId,
      driverCode: json['driver_code'],
      driverName: json['driver_name'] ?? 'Unassigned',
      carId: carId,
      carCode: json['car_code'],
      plateNumber: json['plate_number'],
      rating: json['rating'] != null ? int.tryParse(json['rating'].toString()) : null,
      comments: json['comments'],
      rejectionReason: json['rejection_reason'],
      completionNotes: json['completion_notes'],
      completionImagePath: json['completion_image_path'],      totalDuration: json['total_duration'],
      notes: json['notes'] ?? '',
      isWaiting: json['is_waiting'] ?? false,
      passengers: json['passengers'] != null 
          ? (json['passengers'] as List)
              .map((passengerJson) => Passenger.fromJson(passengerJson))
              .toList()
          : null,
      cargos: json['cargos'] != null 
          ? (json['cargos'] as List)
              .map((cargoJson) => TripCargo.fromJson(cargoJson))
              .toList()
          : null,
    );
  }

  // Calculate trip duration in minutes
  int getDurationMinutes() {
    // Default duration in minutes
    const int defaultDuration = 30;

    try {
      // If no total_duration is set, return the default
      if (totalDuration == null || totalDuration!.isEmpty) {
        return defaultDuration;
      }

      // For waiting trips, the total_duration is the end time
      // First, get the trip start time
      final String tripDate = originalDate.isNotEmpty ? originalDate : DateFormat('yyyy-MM-dd').format(DateTime.now());
      final String tripTime = time.isNotEmpty ? time : '00:00';

      // Create a DateTime object for the trip start time
      DateTime tripStartTime;
      try {
        tripStartTime = DateTime.parse('${tripDate}T$tripTime:00');
      } catch (e) {
        return defaultDuration;
      }

      // Try to parse the total_duration in various formats
      DateTime? endTime;

      // Try ISO 8601 format first
      try {
        endTime = DateTime.parse(totalDuration!);
      } catch (e) {
        // If that failed, try PostgreSQL timestamp format
        try {
          final parts = totalDuration!.split(' ');
          if (parts.length == 2) {
            final datePart = parts[0];
            final timePart = parts[1];
            endTime = DateTime.parse('${datePart}T$timePart');
          }
        } catch (e) {
          // If that failed, try to parse as a duration string (HH:MM:SS)
          try {
            final durationParts = totalDuration!.split(':');
            if (durationParts.length >= 2) {
              final hours = int.tryParse(durationParts[0]) ?? 0;
              final minutes = int.tryParse(durationParts[1]) ?? 0;
              final seconds = durationParts.length > 2 ? (int.tryParse(durationParts[2]) ?? 0) : 0;

              final duration = Duration(hours: hours, minutes: minutes, seconds: seconds);
              return duration.inMinutes > 0 ? duration.inMinutes : defaultDuration;
            }
          } catch (e) {
            // If all parsing attempts fail, return the default duration
            return defaultDuration;
          }
        }
      }

      // If we have an end time, calculate the duration
      if (endTime != null) {
        final durationMinutes = endTime.difference(tripStartTime).inMinutes;

        // Return the duration, with a minimum of 1 minute (no maximum)
        if (durationMinutes <= 0) {
          return defaultDuration;
        } else {
          // Return the actual duration without limiting it to 24 hours
          return durationMinutes;
        }
      }

      // If all parsing attempts fail, return the default duration
      return defaultDuration;
    } catch (e) {
      return defaultDuration; // Default to 30 minutes if there's an error
    }
  }
}


