import 'package:flutter/material.dart';

class CarAvailability {
  final int carId;
  final String carCode;
  final DateTime date;
  final String status; // 'AVAILABLE', 'UNAVAILABLE'
  final String? notes;

  CarAvailability({
    required this.carId,
    required this.carCode,
    required this.date,
    required this.status,
    this.notes,
  });

  // Helper method to get default status based on day of week
  static String getDefaultStatusForDate(DateTime date) {
    // Ensure we're using local date
    final localDate = date.toLocal();
    final bool isWeekend = localDate.weekday == DateTime.saturday || localDate.weekday == DateTime.sunday;

    // Debug print to help diagnose the issue
    if (isWeekend) {
      print('getDefaultStatusForDate: Date ${localDate.toString()} is a weekend (${localDate.weekday}), returning AVAILABLE');
      print('  - Raw date provided: ${date.toString()}, after toLocal(): ${localDate.toString()}');
    } else {
      print('getDefaultStatusForDate: Date ${localDate.toString()} is a weekday (${localDate.weekday}), returning AVAILABLE');
      print('  - Raw date provided: ${date.toString()}, after toLocal(): ${localDate.toString()}');
    }

    // Always return AVAILABLE regardless of weekday/weekend
    return 'AVAILABLE';
  }

  factory CarAvailability.fromJson(Map<String, dynamic> json) {
    // Parse car_id, handling both string and int formats
    int carId = 0;
    if (json['car_id'] != null) {
      if (json['car_id'] is String) {
        carId = int.tryParse(json['car_id']) ?? 0;
      } else if (json['car_id'] is int) {
        carId = json['car_id'];
      }
    }

    return CarAvailability(
      carId: carId,
      carCode: json['car_code'] ?? '',
      date: DateTime.parse(json['date']).toLocal(),
      status: json['status'],
      notes: json['notes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'car_id': carId,
      'car_code': carCode,
      'date': date.toLocal().toIso8601String().split('T')[0], // Format as YYYY-MM-DD
      'status': status,
      'notes': notes,
    };
  }

  // Helper method to get color based on status
  static Color getStatusColor(String status) {
    switch (status.toUpperCase()) {
      case 'AVAILABLE':
        return Colors.green.shade100;
      case 'UNAVAILABLE':
        return Colors.red.shade100;
      default:
        return Colors.grey.shade100;
    }
  }

  // Helper method to get text color based on status
  static Color getStatusTextColor(String status) {
    switch (status.toUpperCase()) {
      case 'AVAILABLE':
        return Colors.green.shade800;
      case 'UNAVAILABLE':
        return Colors.red.shade800;
      default:
        return Colors.grey.shade800;
    }
  }

  // Check if a reason is required for this status
  static bool isReasonRequired(String status) {
    return false; // Notes are now optional for all statuses
  }
}




