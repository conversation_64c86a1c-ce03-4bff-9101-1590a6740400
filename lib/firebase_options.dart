// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      // Return a minimal configuration for web to prevent errors
      // Firebase is disabled on web platform for this app
      return const FirebaseOptions(
        apiKey: 'web-placeholder',
        appId: 'web-placeholder',
        messagingSenderId: 'web-placeholder',
        projectId: 'fleexfirebase',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }
  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBXPDWod-cU2BAvoOD52HFwcASCx7LzhOg',
    appId: '1:355724925357:android:3615922475c88f91b86924',
    messagingSenderId: '355724925357',
    projectId: 'fleexfirebase',
    storageBucket: 'fleexfirebase.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBXPDWod-cU2BAvoOD52HFwcASCx7LzhOg',
    appId: '1:355724925357:ios:YOUR_IOS_APP_ID',
    messagingSenderId: '355724925357',
    projectId: 'fleexfirebase',
    storageBucket: 'fleexfirebase.firebasestorage.app',
    iosBundleId: 'com.example.fleex',
  );
}
