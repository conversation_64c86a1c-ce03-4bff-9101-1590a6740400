// Main application file
import 'dart:convert';

// Firebase imports - only imported on non-web platforms to avoid web compilation issues
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'generated/l10n/app_localizations.dart';
import 'models/user.dart';
import 'screens/availability_management_screen.dart';
import 'screens/car_availability_management_screen.dart';
import 'screens/car_odometer_history_screen.dart';
import 'screens/car_settings_screen.dart';
import 'screens/cargo_settings_screen.dart';
import 'screens/destination_settings_screen.dart';
import 'screens/driver_check_in_management_screen.dart';
import 'screens/driver_check_in_screen.dart';
import 'screens/driver_checkout_screen.dart';
import 'screens/driver_settings_screen.dart';
import 'screens/driver_tracking_screen.dart';
import 'screens/driver_trip_history_screen.dart';
import 'screens/driver_trips_screen.dart';
import 'screens/language_selection_screen.dart';
import 'screens/login_screen.dart';
import 'screens/my_trip_history_screen.dart';
import 'screens/rating_screen.dart';
import 'screens/request_trip_screen.dart';
import 'screens/requestor_dashboard_screen.dart';
import 'screens/reset_password_screen.dart';
import 'screens/trip_approval_screen.dart';
import 'screens/trip_history_screen.dart';
import 'screens/trip_manager_dashboard_screen.dart';
import 'screens/trip_monitoring_screen.dart';
import 'screens/trip_rating_list_screen.dart';
import 'services/api_service.dart';
import 'services/auth_service.dart';
import 'services/auto_location_tracking_service.dart';
import 'services/background_location_service.dart';
import 'services/firebase_notification_service.dart';
import 'services/localization_service.dart';
import 'services/notification_service.dart';
import 'services/state_persistence_service.dart';
import 'services/url_handler_service.dart';
import 'services/web_storage.dart'
    if (dart.library.html) 'services/web_storage_web.dart';
import 'services/webview_bridge.dart';
import 'utils/web_utils.dart';

// Create a global navigation key that can be used for navigation without context
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

// Global variables to store pending authentication data
Map<String, dynamic>? _pendingAuthData;
User? _pendingUser;

// Global function to handle SSO callback
Future<void> handleSsoCallback(String url) async {
  print('Global handleSsoCallback: Processing URL: $url');
  try {
    // Parse the URL
    final uri = Uri.parse(url);
    print('Global handleSsoCallback: Parsed URI - scheme: ${uri.scheme}, host: ${uri.host}, path: ${uri.path}');
    print('Global handleSsoCallback: Query parameters: ${uri.queryParameters}');

    // Extract the token from the query parameters
    final queryParams = uri.queryParameters;
    String? token;

    // Check for Microsoft Entra format (code parameter)
    if (queryParams.containsKey('code')) {
      token = queryParams['code']!;
      print('Global handleSsoCallback: Found code parameter (Microsoft Entra format): $token');
      print('Global handleSsoCallback: Code length: ${token.length}');
      if (token.length > 20) {
        print('Global handleSsoCallback: Code preview: ${token.substring(0, 20)}...');
      } else {
        print('Global handleSsoCallback: Code full: $token');
      }

      // Check if the code is valid (not HTML)
      if (token.contains('<') || token.contains('>') || token.contains('DOCTYPE')) {
        print('Global handleSsoCallback: Invalid code format detected - contains HTML');
        token = null; // Set to null to trigger the error handling below
      }
    }
    // Check for our standard format (token parameter)
    else if (queryParams.containsKey('token')) {
      token = queryParams['token']!;
      print('Global handleSsoCallback: Found token parameter: $token');
    }
    // Check if the code is in the path segment (as seen in the error)
    else if (uri.path.contains('callback') && uri.path.contains('code=')) {
      // Extract code from path
      final pathParts = uri.path.split('code=');
      if (pathParts.length > 1) {
        token = pathParts[1];
        // If there are additional parameters, extract just the code
        if (token.contains('&')) {
          token = token.split('&')[0];
        }
        print('Global handleSsoCallback: Found code in path: $token');
      }
    }
    // Check if code is in the URL fragment
    else if (uri.fragment.isNotEmpty && uri.fragment.contains('code=')) {
      // Extract code from fragment
      final codeParam = uri.fragment.split('code=');
      if (codeParam.length > 1) {
        token = codeParam[1];
        // If there are additional parameters, extract just the code
        if (token.contains('&')) {
          token = token.split('&')[0];
        }
        print('Global handleSsoCallback: Found code in fragment: $token');
      }
    }
    // Check if the entire URL contains code= (for malformed URLs)
    else if (url.contains('code=')) {
      // Extract code from the full URL
      final codeParam = url.split('code=');
      if (codeParam.length > 1) {
        token = codeParam[1];
        // If there are additional parameters, extract just the code
        if (token.contains('&')) {
          token = token.split('&')[0];
        }
        print('Global handleSsoCallback: Found code in full URL: $token');
      }
    }

    if (token != null) {

      try {
        // Exchange the token for auth data
        print('Global handleSsoCallback: Exchanging token for auth data');
        print('Global handleSsoCallback: Token length: ${token.length}');
        final previewLength = token.length > 20 ? 20 : token.length;
        print('Global handleSsoCallback: Token preview: ${token.substring(0, previewLength)}...');

        final authData = await AuthService.exchangeToken(token);
        print('Global handleSsoCallback: Token exchange successful');
        print('Global handleSsoCallback: Auth data: ${authData.keys.join(', ')}');

        // Parse the user data
        final user = User.fromJson(authData['user']);
        print('Global handleSsoCallback: User data parsed: ${user.name}, role: ${user.role}');

        // Save tokens to secure storage
        print('Global handleSsoCallback: Saving tokens to secure storage');
        await AuthService.saveTokens(authData['access_token'], authData['id_token']);
        print('Global handleSsoCallback: Tokens saved successfully');

        // Update the app state
        print('Global handleSsoCallback: Updating app state');
        if (navigatorKey.currentContext != null) {
          final MyAppState appState = navigatorKey.currentContext!.read<MyAppState>();
          appState.setCurrentUser(user);
          print('Global handleSsoCallback: App state updated');

          // Navigate to the appropriate screen based on user role
          print('Global handleSsoCallback: Navigating based on user role: ${user.role}');
          if (user.isAdmin || user.isTripManager) {
            print('Global handleSsoCallback: Navigating to trip manager dashboard');
            navigatorKey.currentState!.pushAndRemoveUntil(
              MaterialPageRoute(builder: (context) => const TripManagerDashboardScreen()),
              (route) => false,
            );
          } else if (user.isDriver) {
            print('Global handleSsoCallback: Navigating to driver trips screen');
            navigatorKey.currentState!.pushAndRemoveUntil(
              MaterialPageRoute(builder: (context) => DriverTripsScreen(driverCode: '')),
              (route) => false,
            );
          } else {
            print('Global handleSsoCallback: Navigating to requestor dashboard');
            navigatorKey.currentState!.pushAndRemoveUntil(
              MaterialPageRoute(builder: (context) => const RequestorDashboardScreen()),
              (route) => false,
            );
          }
          print('Global handleSsoCallback: Navigation completed');
        } else {
          print('Global handleSsoCallback: Navigator context is null, cannot navigate');
          // This is a critical error - we need to handle it better
          print('Global handleSsoCallback: Attempting to restart the app flow');

          // Store the user data in a global variable or static field
          // that can be accessed when the app restarts
          print('Global handleSsoCallback: Storing auth data for later use');
          _pendingAuthData = authData;
          _pendingUser = user;

          // Force the app to restart or go to login screen
          // This is a last resort approach
          print('Global handleSsoCallback: Restarting app');
          runApp(
            MultiProvider(
              providers: [
                ChangeNotifierProvider(create: (context) => MyAppState()),
                ChangeNotifierProvider(create: (context) => NotificationService()),
              ],
              child: UserApp(),
            ),
          );
        }
      } catch (e, stackTrace) {
        print('Global handleSsoCallback: Error during token exchange or navigation: $e');
        print('Global handleSsoCallback: Stack trace: $stackTrace');

        // Check if the error is related to HTML response
        final errorMsg = e.toString().toLowerCase();
        String userMessage = 'Authentication failed';

        if (errorMsg.contains('formatexception') ||
            errorMsg.contains('unexpected character') ||
            errorMsg.contains('<!doctype html>') ||
            errorMsg.contains('<html') ||
            errorMsg.contains('non-json response') ||
            errorMsg.contains('invalid token format') ||
            errorMsg.contains('html content') ||
            errorMsg.contains('html error page')) {
          print('Global handleSsoCallback: Detected HTML response error - likely invalid code or expired code');
          userMessage = 'Authentication failed: Invalid or expired authentication code';
        } else {
          userMessage = 'Authentication failed: $e';
        }

        // Show an error message to the user
        if (navigatorKey.currentContext != null) {
          ScaffoldMessenger.of(navigatorKey.currentContext!).showSnackBar(
            SnackBar(content: Text(userMessage)),
          );
        } else {
          // If we can't show a snackbar, at least log the error
          print('Global handleSsoCallback: Cannot show error message - navigator context is null');

          // Restart the app with a loading screen instead of an error screen
          print('Global handleSsoCallback: Restarting app with loading screen');
          runApp(
            MultiProvider(
              providers: [
                ChangeNotifierProvider(create: (context) => MyAppState()),
                ChangeNotifierProvider(create: (context) => NotificationService()),
              ],
              child: UserApp(),
            ),
          );
        }
      }
    } else {
      print('Global handleSsoCallback: No token or code found in URL');
      // Try to extract the token from a different part of the URL
      final pathSegments = uri.pathSegments;
      if (pathSegments.isNotEmpty) {
        // Check for token in path segment
        if (pathSegments.last.contains('token=')) {
          final tokenParam = pathSegments.last.split('token=');
          if (tokenParam.length > 1) {
            final token = tokenParam[1];
            print('Global handleSsoCallback: Found token in path segment: $token');
            // Call this function recursively with the corrected URL
            await handleSsoCallback('com.example.fleex://auth/callback?token=$token');
            return;
          }
        }
        // Check for code in path segment
        else if (pathSegments.last.contains('code=')) {
          final codeParam = pathSegments.last.split('code=');
          if (codeParam.length > 1) {
            final code = codeParam[1];
            print('Global handleSsoCallback: Found code in path segment: $code');
            // Call this function recursively with the corrected URL
            // Try both formats of the callback URL
            try {
              await handleSsoCallback('com.example.fleex://callback?code=$code');
            } catch (e) {
              print('Global handleSsoCallback: First callback format failed, trying alternative format');
              await handleSsoCallback('com.example.fleex:/callback?code=$code');
            }
            return;
          }
        }
      }

      // If we still can't find a token or code, show an error
      if (navigatorKey.currentContext != null) {
        ScaffoldMessenger.of(navigatorKey.currentContext!).showSnackBar(
          const SnackBar(content: Text('Authentication failed: No authentication code found in URL')),
        );
      }
    }
  } catch (e, stackTrace) {
    print('Global handleSsoCallback: Error handling SSO callback: $e');
    print('Global handleSsoCallback: Stack trace: $stackTrace');
  }
}

void main() async {
  // Ensure Flutter binding is initialized before doing anything else
  WidgetsFlutterBinding.ensureInitialized();  // Initialize Firebase and notification services for mobile only
  if (!kIsWeb) {
    // Initialize Firebase notification service with comprehensive error handling
    try {
      await FirebaseNotificationService().initialize();
      print('Firebase notification service initialized');
      
      // Set up background message handler only after successful initialization
      try {
        FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);
        print('Firebase background message handler set up');
      } catch (e) {
        print('Error setting up Firebase background message handler: $e');
      }
    } catch (e) {
      print('Error initializing Firebase notification service: $e');
      // Continue app initialization even if Firebase fails
    }

    // Initialize WebViewBridge for Android 12 compatibility
    WebViewBridge.initialize();

    // Initialize state persistence service for mobile
    await StatePersistenceService().initialize();
    print('State persistence service initialized');

    // Initialize background location service
    final backgroundService = BackgroundLocationService();
    await backgroundService.initialize();
    print('Background location service initialized');
  }

  // Add a global error handler to catch and log all errors
  FlutterError.onError = (FlutterErrorDetails details) {
    print('Flutter error: ${details.exception}');
    print('Stack trace: ${details.stack}');
    FlutterError.presentError(details);
  };
  // Create the app state
  final appState = MyAppState();
  
  // Create locale notifier and initialize it
  final localeNotifier = LocaleNotifier();
  await localeNotifier.initialize();

  // Try to load persisted user data before running the app
  if (!kIsWeb) {
    print('main: Attempting to load persisted user data before app starts');
    try {
      final loaded = await appState.loadPersistedUserData();
      print('main: Persisted user data load result: $loaded');
    } catch (e) {
      print('main: Error loading persisted user data: $e');
    }
  }

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => appState),
        ChangeNotifierProvider(create: (context) => NotificationService()),
        ChangeNotifierProvider(create: (context) => localeNotifier),
      ],
      child: UserApp(),
    ),
  );
}

class MyAppState extends ChangeNotifier {
  User? currentUser;
  final AutoLocationTrackingService _autoLocationTrackingService = AutoLocationTrackingService();
  final StatePersistenceService _persistenceService = StatePersistenceService();
  void setCurrentUser(User? user) {
    currentUser = user;

    // Initialize Firebase notifications for mobile users
    if (user != null && !kIsWeb) {
      _initializeFirebaseForUser(user);
    }

    // Initialize auto location tracking for drivers
    if (user != null && (user.isDriver || user.role.toLowerCase().contains('driver'))) {
      _initializeDriverLocationTracking(user);

      // Save user data for persistence (mobile only)
      if (!kIsWeb) {
        saveCurrentUserState();
      }
    } else {
      // Disable tracking for non-drivers
      _autoLocationTrackingService.disable();

      // Still save non-driver user data for persistence (mobile only)
      if (!kIsWeb && user != null) {
        saveCurrentUserState();
      }
    }

    notifyListeners();
  }

  // Save the current user state to persistent storage
  Future<void> saveCurrentUserState() async {
    if (kIsWeb || currentUser == null) {
      return;
    }

    try {
      print('MyAppState - saveCurrentUserState: Saving state for ${currentUser!.name}');

      // First, save user data to both SharedPreferences and secure storage
      await _persistenceService.saveUser(currentUser!);

      // Try to get an existing token first
      String? existingToken = await _persistenceService.loadAccessToken();

      // If no existing token, generate a dummy one
      if (existingToken == null) {
        existingToken = 'dummy_token_${DateTime.now().millisecondsSinceEpoch}';
        print('MyAppState - saveCurrentUserState: No existing token found, generated dummy token');
      } else {
        print('MyAppState - saveCurrentUserState: Using existing token');
      }

      // Save the token to both AuthService's secure storage and our persistence service
      await AuthService.saveTokens(existingToken, existingToken);
      await _persistenceService.saveTokens(existingToken, existingToken);

      // Verify the data was saved
      final userVerify = await _persistenceService.hasUserData();
      final tokenVerify = await _persistenceService.loadAccessToken() != null;

      print('MyAppState - saveCurrentUserState: Verification - User data saved: $userVerify, Token saved: $tokenVerify');

      if (userVerify && tokenVerify) {
        print('MyAppState - saveCurrentUserState: State saved successfully');
      } else {
        print('MyAppState - saveCurrentUserState: WARNING - State may not have been saved completely');
      }
    } catch (e) {
      print('MyAppState - saveCurrentUserState: Error saving state: $e');
    }  }  // Initialize Firebase notifications for user
  Future<void> _initializeFirebaseForUser(User user) async {
    // Skip Firebase initialization on web platform
    if (kIsWeb) {
      print('Skipping Firebase initialization on web platform for user: ${user.name}');
      return;
    }
    
    print('🔥 Initializing Firebase for user: ${user.name} (ID: ${user.userId})');
    
    try {
      final firebaseService = FirebaseNotificationService();
      
      // Initialize Firebase if not already done
      if (!firebaseService.isInitialized) {
        print('🔄 Firebase not initialized, initializing now...');
        await firebaseService.initialize();
        print('✅ Firebase initialization completed');
      } else {
        print('✅ Firebase already initialized');
      }
      
      // Update FCM token for the current user
      print('🔄 Updating FCM token for user: ${user.userId}');
      await firebaseService.updateTokenForUser(user.userId);
      
      print('✅ Firebase notifications initialized for user: ${user.name}');
    } catch (e) {
      print('❌ Error initializing Firebase for user: $e');
      print('Stack trace: ${e.toString()}');
    }
  }

  // Initialize driver location tracking
  Future<void> _initializeDriverLocationTracking(User user) async {
    try {
      // Parse user ID to int
      final userId = int.tryParse(user.userId) ?? 0;
      if (userId <= 0) {
        print('Invalid user ID for location tracking: ${user.userId}');
        return;
      }

      print('Initializing automatic location tracking for driver: ${user.name}');

      // Try to get the driver ID from the API
      int? driverId;
      try {
        final response = await ApiService.get(
          'drivers/user/${user.userId}',
        );

        if (response.statusCode == 200) {
          final data = jsonDecode(response.body);
          // Get the driver detail ID for tracking
          final driverDetailId = data['driver_detail_id'];

          if (driverDetailId != null) {
            driverId = driverDetailId is String
                ? int.tryParse(driverDetailId)
                : driverDetailId;
            print('Found driver ID for tracking: $driverId');
          }
        }
      } catch (e) {
        print('Error getting driver ID for tracking: $e');
        // Continue with userId as fallback
      }

      // Initialize the auto tracking service with driver ID if available
      final initialized = await _autoLocationTrackingService.initialize(
        userId,
        driverId: driverId,
      );

      if (!initialized) {
        print('Failed to initialize auto location tracking: ${_autoLocationTrackingService.errorMessage}');
        return;
      }

      // Enable automatic tracking
      await _autoLocationTrackingService.enable();
      print('Automatic location tracking enabled for driver: ${user.name}');
    } catch (e) {
      print('Error initializing auto location tracking: $e');
    }
  }

  void logout() async {
    print('MyAppState - logout: Starting logout process');

    // Disable location tracking when logging out
    _autoLocationTrackingService.disable();
    print('MyAppState - logout: Location tracking disabled');

    // Clear persisted data (mobile only)
    if (!kIsWeb) {
      try {
        // First clear through our persistence service
        await _persistenceService.clearAll();
        print('MyAppState - logout: Persisted data cleared through service');

        // Also try to clear all secure storage directly
        try {
          final secureStorage = FlutterSecureStorage();
          await secureStorage.deleteAll();
          print('MyAppState - logout: All secure storage deleted directly');
        } catch (e) {
          print('MyAppState - logout: Error deleting all secure storage: $e');
        }

        // Try to clear SharedPreferences directly
        try {
          final prefs = await SharedPreferences.getInstance();
          await prefs.clear();
          print('MyAppState - logout: All SharedPreferences cleared directly');
        } catch (e) {
          print('MyAppState - logout: Error clearing SharedPreferences: $e');
        }

        // Verify everything is cleared
        final hasUserData = await _persistenceService.hasUserData();
        if (hasUserData) {
          print('MyAppState - logout: WARNING - User data still exists after clearing!');
        } else {
          print('MyAppState - logout: Verification successful - all user data cleared');
        }
      } catch (e) {
        print('MyAppState - logout: Error clearing persisted data: $e');
      }
    }

    currentUser = null;
    notifyListeners();
    print('MyAppState - logout: User logged out successfully');
  }

  // Load persisted user data
  Future<bool> loadPersistedUserData() async {
    if (kIsWeb) {
      print('MyAppState - loadPersistedUserData: Web platform detected, no persistence');
      return false; // No persistence for web
    }

    if (isLoggedIn) {
      print('MyAppState - loadPersistedUserData: Already logged in, no need to load persisted data');
      return true;
    }

    try {
      print('MyAppState - loadPersistedUserData: Attempting to load persisted user data');

      // First try to load directly from StatePersistenceService
      User? user;
      String? accessToken;

      try {
        // Try to load user data directly from persistence service first
        user = await _persistenceService.loadUser();
        if (user != null) {
          print('MyAppState - loadPersistedUserData: Found user data in persistence service for ${user.name}, role: ${user.role}');

          // Try to load token
          accessToken = await _persistenceService.loadAccessToken();
          if (accessToken != null) {
            print('MyAppState - loadPersistedUserData: Found access token in persistence service');
          } else {
            print('MyAppState - loadPersistedUserData: No access token found in persistence service');
          }
        } else {
          print('MyAppState - loadPersistedUserData: No user data found in persistence service');
        }
      } catch (e) {
        print('MyAppState - loadPersistedUserData: Error loading from persistence service: $e');
      }

      // If direct loading failed, try through AuthService
      if (user == null) {
        print('MyAppState - loadPersistedUserData: Trying to load through AuthService');
        user = await AuthService.checkPersistedUserData();

        if (user != null) {
          print('MyAppState - loadPersistedUserData: Found user data through AuthService for ${user.name}, role: ${user.role}');

          // Try to load token if not already loaded
          if (accessToken == null) {
            accessToken = await AuthService.getAccessToken();
            if (accessToken != null) {
              print('MyAppState - loadPersistedUserData: Found access token through AuthService');
            } else {
              print('MyAppState - loadPersistedUserData: No access token found through AuthService');
            }
          }
        } else {
          print('MyAppState - loadPersistedUserData: No user data found through AuthService');
        }
      }

      // If we have user data, restore the session
      if (user != null) {
        print('MyAppState - loadPersistedUserData: Restoring session for ${user.name}');

        // If we don't have a token, generate a dummy one
        if (accessToken == null) {
          accessToken = 'dummy_token_${DateTime.now().millisecondsSinceEpoch}';
          print('MyAppState - loadPersistedUserData: Generated dummy token');
        }

        // Set the current user
        setCurrentUser(user);

        // Save the token to both storage mechanisms
        await AuthService.saveTokens(accessToken, accessToken);
        await _persistenceService.saveTokens(accessToken, accessToken);

        print('MyAppState - loadPersistedUserData: Successfully restored user session');
        return true;
      } else {
        print('MyAppState - loadPersistedUserData: No persisted user data found in any storage');
      }

      return false;
    } catch (e) {
      print('MyAppState - loadPersistedUserData: Error loading persisted user data: $e');
      return false;
    }
  }

  bool get isLoggedIn => currentUser != null;

  // Switch the current user's active role
  void switchUserRole(String newRole) {
    if (currentUser == null) {
      throw Exception('No user is currently logged in');
    }

    if (!currentUser!.canSwitchToRole(newRole)) {
      throw Exception('User cannot switch to role: $newRole');
    }

    print('MyAppState - switchUserRole: Switching from ${currentUser!.currentRole} to $newRole');

    // Create a new user instance with the updated active role
    final updatedUser = currentUser!.copyWithActiveRole(newRole);
    currentUser = updatedUser;

    // Handle role-specific initialization
    if (newRole.toLowerCase() == 'driver' || newRole.toLowerCase().contains('driver')) {
      _initializeDriverLocationTracking(updatedUser);
    } else {
      // Disable tracking for non-driver roles
      _autoLocationTrackingService.disable();
    }

    // Save updated user state for persistence (mobile only)
    if (!kIsWeb) {
      saveCurrentUserState();
    }

    notifyListeners();
    print('MyAppState - switchUserRole: Role switched successfully to $newRole');
  }

  // Get available roles for the current user
  List<String> get availableRoles {
    return currentUser?.availableRoles ?? [];
  }

  // Check if the current user has multi-role capabilities
  bool get hasMultipleRoles {
    return availableRoles.length > 1;
  }
}

/// Notifier for managing locale changes
class LocaleNotifier extends ChangeNotifier {
  Locale _locale = const Locale('en');
  final LocalizationService _localizationService = LocalizationService();

  Locale get locale => _locale;

  /// Initialize with saved locale preference
  Future<void> initialize() async {
    _locale = await _localizationService.getSavedLocale();
    notifyListeners();
  }

  /// Change the app locale
  Future<void> setLocale(Locale locale) async {
    if (_locale != locale) {
      _locale = locale;
      await _localizationService.saveLocale(locale);
      notifyListeners();
    }
  }
}

class UserApp extends StatefulWidget {
  const UserApp({super.key});

  @override
  State<UserApp> createState() => _UserAppState();
}

class _UserAppState extends State<UserApp> with WidgetsBindingObserver {
  bool _isFirstRun = true;
  DateTime? _lastPauseTime;

  @override
  void initState() {
    super.initState();

    // Register for lifecycle events
    WidgetsBinding.instance.addObserver(this);

    print('UserApp: App starting, isFirstRun: $_isFirstRun');

    // Check if we have pending auth data from a previous SSO callback
    if (_pendingAuthData != null && _pendingUser != null) {
      print('UserApp: Found pending auth data, applying it');
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // Update the app state with the pending user
        Provider.of<MyAppState>(context, listen: false).setCurrentUser(_pendingUser);

        // Save tokens to secure storage
        AuthService.saveTokens(_pendingAuthData!['access_token'], _pendingAuthData!['id_token']);

        // Clear the pending data
        _pendingAuthData = null;
        _pendingUser = null;

        print('UserApp: Applied pending auth data');
      });
    }

    // Initialize deep link handling and check for persisted data
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      // For web: Check for auth data in the URL
      if (kIsWeb) {
        _checkForAuthData();
      } else {
        // For mobile: Check if we're already logged in (from main() loading persisted data)
        final appState = Provider.of<MyAppState>(context, listen: false);

        if (appState.isLoggedIn) {
          print('UserApp: User already logged in from persisted data loaded in main()');
        } else {
          print('UserApp: Not logged in yet, checking for persisted user data');
          // Try to load persisted user data as a backup
          final loaded = await appState.loadPersistedUserData();

          if (loaded) {
            print('UserApp: Successfully loaded persisted user data');
          } else {
            print('UserApp: No persisted user data found or failed to load');
          }
        }

        // Set up deep link listener regardless of whether we loaded persisted data
        _setupDeepLinkListener();
      }
    });
  }

  @override
  void dispose() {
    // Unregister from lifecycle events
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    print('UserApp: App lifecycle state changed to: $state');

    if (state == AppLifecycleState.paused) {
      // App is going to background
      _lastPauseTime = DateTime.now();
      print('UserApp: App paused at $_lastPauseTime');

      // Save any state that needs to be persisted
      if (!kIsWeb) {
        final appState = Provider.of<MyAppState>(context, listen: false);
        if (appState.isLoggedIn) {
          print('UserApp: Saving user state before app pause');
          // Force save the current user state
          appState.saveCurrentUserState();
        }
      }
    } else if (state == AppLifecycleState.resumed) {
      // App is coming back to foreground
      final now = DateTime.now();
      final pauseDuration = _lastPauseTime != null
          ? now.difference(_lastPauseTime!)
          : Duration.zero;

      print('UserApp: App resumed after ${pauseDuration.inSeconds} seconds');

      // If the app was paused for more than 5 minutes, treat it as a "restart"
      if (pauseDuration.inMinutes >= 5) {
        print('UserApp: Long pause detected (${pauseDuration.inMinutes} minutes), checking persisted data');

        // Check for persisted user data
        if (!kIsWeb) {
          WidgetsBinding.instance.addPostFrameCallback((_) async {
            final appState = Provider.of<MyAppState>(context, listen: false);
            if (!appState.isLoggedIn) {
              print('UserApp: Not logged in after long pause, trying to load persisted data');
              final loaded = await appState.loadPersistedUserData();
              print('UserApp: Persisted data load result after long pause: $loaded');
            }
          });
        }
      }
    }
  }

  void _setupDeepLinkListener() {
    if (!kIsWeb) {
      print('Setting up deep link listener for mobile app');

      // Set up a dedicated method channel for handling deep links
      const methodChannel = MethodChannel('com.example.fleex/webview_bridge');

      methodChannel.setMethodCallHandler((call) async {
        print('Main: Received method call: ${call.method}');
        if (call.method == 'fromWeb') {
          print('Main: Received fromWeb event: ${call.arguments['event']}');
          if (call.arguments['event'] == 'deepLink') {
            final data = call.arguments['data'];
            print('Main: Received deep link data: $data');
            if (data is String) {
              if (data.startsWith('com.example.fleex://auth/callback') ||
                  data.startsWith('com.example.fleex:/auth/callback') ||
                  data.startsWith('com.example.fleex://callback') ||
                  data.startsWith('com.example.fleex:/callback') ||
                  (data.startsWith('com.example.fleex:') && data.contains('/auth/callback')) ||
                  (data.startsWith('com.example.fleex:') && data.contains('/callback')) ||
                  (data.startsWith('com.example.fleex:') && data.contains('code='))) {
                print('Main: Detected SSO callback URL, handling directly');
                try {
                  await handleSsoCallback(data);
                } catch (e) {
                  print('Main: Error handling SSO callback: $e');
                }
              } else if (mounted) {
                // Handle other deep links normally if context is valid
                UrlHandlerService.handleDeepLink(context, data);
              } else {
                print('Main: Context is no longer mounted, cannot handle deep link');
              }
            } else {
              print('Main: Received non-string deep link data: $data');
            }
          }
        }
        return null;
      });

      print('Deep link listener setup complete');
    }
  }

  void _checkForAuthData() async {
    if (kIsWeb) {
      print('Checking for auth data in web app');
      // First check if we have auth_token in localStorage (from our secure redirect)
      if (AuthDataStorage.hasAuthToken()) {
        print('Auth token found in localStorage');
        final token = AuthDataStorage.getAuthToken()!;
        print('Retrieved token from localStorage');

        try {
          print('Exchanging token for auth data');
          // Exchange the token for auth data
          final authData = await AuthService.exchangeToken(token);
          print('Token exchange successful');
          final user = User.fromJson(authData['user']);

          // Check if widget is still mounted before updating state
          if (!mounted) return;

          // Update the app state
          Provider.of<MyAppState>(context, listen: false).setCurrentUser(user);
          print('User state updated');

          // Store the user role in localStorage for navigation after redirect
          if (kIsWeb) {
            WebUtils.storeUserRole(user.role);
            print('User role stored in localStorage: ${user.role}');
          }

          // Save tokens to secure storage
          await AuthService.saveTokens(authData['access_token'], authData['id_token']);
          print('Tokens saved to secure storage');

          // For debugging, verify the token is accessible
          final accessToken = await AuthService.getAccessToken();
          print('Access token verification: ${accessToken != null ? 'token exists' : 'token is null'}');

          // Clear the auth token from localStorage
          AuthDataStorage.clearAuthToken();
          print('Auth token cleared from localStorage');

          // Navigate to the appropriate dashboard based on user role
          final currentUri = Uri.parse(Uri.base.toString());
          final baseUrl = Uri.parse(currentUri.origin + currentUri.path);
          String targetRoute = baseUrl.toString();

          print('User role: ${user.role}');
          print('User role lowercase: ${user.role.toLowerCase()}');
          print('isTripManager check: ${user.isTripManager}');
          print('isAdmin check: ${user.isAdmin}');
          print('isDriver check: ${user.isDriver}');
          print('isSuperAdmin check: ${user.isSuperAdmin}');

          // Use the correct route based on user role
          final roleLower = user.role.toLowerCase();

          if (user.isTripManager || user.isAdmin || user.isSuperAdmin) {
            targetRoute = '$baseUrl#/trip-manager-dashboard';
            print('Redirecting to trip manager dashboard');
          } else if (user.isDriver || roleLower.contains('driver')) {
            targetRoute = '$baseUrl#/driver-trips';
            print('Redirecting to driver dashboard');
          } else {
            // Default to requestor dashboard
            targetRoute = '$baseUrl#/requestor-dashboard';
            print('Redirecting to requestor dashboard');
          }

          // Use our utility class to update the URL without reloading the page
          print('Navigating to: $targetRoute');
          WebUtils.updateUrl(targetRoute);
          return;
        } catch (e) {
          print('Error exchanging token from localStorage: $e');
          // Continue to check other sources
        }
      } else {
        print('No auth token found in localStorage');
      }

      // As a fallback, check if we have auth_token in the URL (for backward compatibility)
      final currentUri = Uri.parse(Uri.base.toString());
      if (currentUri.queryParameters.containsKey('auth_token')) {
        final token = currentUri.queryParameters['auth_token']!;

        try {
          // Exchange the token for auth data
          final authData = await AuthService.exchangeToken(token);
          final user = User.fromJson(authData['user']);

          // Check if widget is still mounted before updating state
          if (!mounted) return;

          // Update the app state
          Provider.of<MyAppState>(context, listen: false).setCurrentUser(user);

          // Store the user role in localStorage for navigation after redirect
          if (kIsWeb) {
            WebUtils.storeUserRole(user.role);
            print('User role stored in localStorage: ${user.role}');
          }

          // Save tokens to secure storage
          await AuthService.saveTokens(authData['access_token'], authData['id_token']);
          print('Tokens saved to secure storage');

          // For debugging, verify the token is accessible
          final accessToken = await AuthService.getAccessToken();
          print('Access token verification: ${accessToken != null ? 'token exists' : 'token is null'}');

          // Remove the auth_token from the URL and navigate to the appropriate dashboard
          // This is important for security reasons
          final baseUrl = Uri.parse(currentUri.origin + currentUri.path);

          // Navigate to the appropriate dashboard based on user role
          String targetRoute;
          print('User role: ${user.role}');
          print('User role lowercase: ${user.role.toLowerCase()}');
          print('isTripManager check: ${user.isTripManager}');
          print('isAdmin check: ${user.isAdmin}');
          print('isDriver check: ${user.isDriver}');
          print('isSuperAdmin check: ${user.isSuperAdmin}');

          // Use the correct route based on user role
          final roleLower = user.role.toLowerCase();

          if (user.isTripManager || user.isAdmin || user.isSuperAdmin) {
            targetRoute = '${baseUrl.toString()}#/trip-manager-dashboard';
            print('Redirecting to trip manager dashboard');
          } else if (user.isDriver || roleLower.contains('driver')) {
            targetRoute = '${baseUrl.toString()}#/driver-trips';
            print('Redirecting to driver dashboard');
          } else {
            // Default to requestor dashboard
            targetRoute = '${baseUrl.toString()}#/requestor-dashboard';
            print('Redirecting to requestor dashboard');
          }

          // Use our utility class to update the URL without reloading the page
          print('Navigating to: $targetRoute');
          WebUtils.updateUrl(targetRoute);
          return;
        } catch (e) {
          print('Error exchanging token from URL: $e');
          // Continue to check localStorage as fallback
        }
      }

      // If not in URL or auth_token, check if we have auth data in localStorage
      if (AuthDataStorage.hasAuthData()) {
        final authData = AuthDataStorage.getAuthData()!;
        final user = User.fromJson(authData['user']);

        // Check if widget is still mounted before updating state
        if (!mounted) return;

        // Update the app state
        Provider.of<MyAppState>(context, listen: false).setCurrentUser(user);

        // Save tokens to secure storage
        AuthService.saveTokens(authData['access_token'], authData['id_token']);

        // Clear the auth data from localStorage
        AuthDataStorage.clearAuthData();
      }
    }
  }  @override
  Widget build(BuildContext context) {
    return Consumer<LocaleNotifier>(
      builder: (context, localeNotifier, child) {        return MaterialApp(
          navigatorKey: navigatorKey, // Use the global navigation key
          locale: localeNotifier.locale,
          localizationsDelegates: AppLocalizations.localizationsDelegates,
          supportedLocales: AppLocalizations.supportedLocales,
          scrollBehavior: kIsWeb ? WebScrollBehavior() : null,
      theme: ThemeData(
        primaryColor: const Color(0xFF0D47A1),
        primarySwatch: Colors.blue,
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF0D47A1),
          primary: const Color(0xFF0D47A1),
        ),
        useMaterial3: true,
      ),
      home: Consumer<MyAppState>(
        builder: (context, appState, child) {
          if (!appState.isLoggedIn) {
            return LoginScreen();
          }          // Get the current user and their role
          final user = appState.currentUser!;
          final currentRole = user.currentRole.toLowerCase();

          print('Home routing - User primary role: ${user.role}');
          print('Home routing - User current role: ${user.currentRole}');
          print('Home routing - isTripManager check: ${user.isTripManager}');
          print('Home routing - isCurrentlyTripManager check: ${user.isCurrentlyTripManager}');
          print('Home routing - isCurrentlyRequestor check: ${user.isCurrentlyRequestor}');
          print('Home routing - isDriver check: ${user.isDriver}');
          print('Home routing - isSuperAdmin check: ${user.isSuperAdmin}');

          // Route based on current active role (for multi-role support)
          if (user.isCurrentlyTripManager || user.isSuperAdmin) {
            print('Home routing - Showing trip manager dashboard');
            return const TripManagerDashboardScreen();
          } else if (user.isCurrentlyRequestor) {
            print('Home routing - Showing requestor dashboard');
            return const RequestorDashboardScreen();
          } else if (user.isCurrentlyDriver || currentRole.contains('driver')) {
            print('Home routing - Showing driver dashboard');
            // Navigate to driver trips screen with empty driver code
            // The actual driver code will be fetched when needed
            return DriverTripsScreen(driverCode: '');
          } else {
            print('Home routing - Showing requestor dashboard');
            return const RequestorDashboardScreen(); // Requestor dashboard
          }
        },
      ),
      onGenerateRoute: (settings) {
        switch (settings.name) {
          case '/driver-settings':
            return MaterialPageRoute(
              builder: (context) => DriverSettingsScreen(),
            );
          case '/car-settings':
            return MaterialPageRoute(
              builder: (context) => CarSettingsScreen(),
            );
          case '/cargo-settings':            return MaterialPageRoute(
              builder: (context) => const CargoSettingsScreen(),
            );
          case '/destination-settings':
            return MaterialPageRoute(
              builder: (context) => const DestinationSettingsScreen(),
            );
          case '/language-selection':
            return MaterialPageRoute(
              builder: (context) => const LanguageSelectionScreen(),
            );
          case '/request-trip':
            return MaterialPageRoute(
              builder: (context) => const RequestTripScreen(),
            );
          case '/trip-approval':
            return MaterialPageRoute(
              builder: (context) => const TripApprovalScreen(),
            );
          case '/admin-dashboard':
          case '/trip-manager-dashboard': // Add new route while keeping old for backward compatibility
            return MaterialPageRoute(
              builder: (context) => const TripManagerDashboardScreen(),
            );
          case '/requestor-dashboard':
            return MaterialPageRoute(
              builder: (context) => const RequestorDashboardScreen(),
            );
          case '/trip-monitoring':
            return MaterialPageRoute(
              builder: (context) => const TripMonitoringScreen(),
            );
          case '/availability':
            return MaterialPageRoute(
              builder: (context) => const AvailabilityManagementScreen(),
            );
          case '/car-availability':
            return MaterialPageRoute(
              builder: (context) => const CarAvailabilityManagementScreen(),
            );
          case '/reset-password':
            return MaterialPageRoute(
              builder: (context) => ResetPasswordScreen(),
            );
          case '/driver-trips':
            // Extract driverCode and activeCheckIn from arguments
            final args = settings.arguments as Map<String, dynamic>?;
            final driverCode = args?['driverCode'] as String? ?? '';
            final activeCheckIn = args?['activeCheckIn'] as Map<String, dynamic>?;
            return MaterialPageRoute(
              builder: (context) => DriverTripsScreen(
                driverCode: driverCode,
                activeCheckIn: activeCheckIn,
              ),
            );
          case '/trip-rating':
            // Check if trip is passed as an argument
            final ratingArgs = settings.arguments as Map<String, dynamic>?;
            if (ratingArgs != null && ratingArgs.containsKey('trip')) {
              return MaterialPageRoute(
                builder: (context) => RatingScreen(trip: ratingArgs['trip']),
              );
            }
            // If no trip is provided, show the trip rating list screen
            return MaterialPageRoute(
              builder: (context) => const TripRatingListScreen(),
            );
          // Notification screen removed
          case '/my-trip-history':
            return MaterialPageRoute(
              builder: (context) => const MyTripHistoryScreen(),
            );
          case '/trip-history':
            return MaterialPageRoute(
              builder: (context) => const TripHistoryScreen(),
            );
          case '/driver-tracking':
            return MaterialPageRoute(
              builder: (context) => const DriverTrackingScreen(),
            );
          case '/driver-check-in':
            // Extract driverCode and lastOdometerReading from arguments
            final checkInArgs = settings.arguments as Map<String, dynamic>?;
            final checkInDriverCode = checkInArgs?['driverCode'] as String? ?? '';
            return MaterialPageRoute(
              builder: (context) => DriverCheckInScreen(
                driverCode: checkInDriverCode,
              ),
            );
          case '/driver-checkout':
            // Extract driverCode and activeCheckIn from arguments
            final checkoutArgs = settings.arguments as Map<String, dynamic>?;
            final checkoutDriverCode = checkoutArgs?['driverCode'] as String? ?? '';
            final activeCheckIn = checkoutArgs?['activeCheckIn'] as Map<String, dynamic>? ?? {};
            return MaterialPageRoute(
              builder: (context) => DriverCheckoutScreen(
                driverCode: checkoutDriverCode,
                activeCheckIn: activeCheckIn,
              ),
            );
          case '/driver-check-in-management':
            return MaterialPageRoute(
              builder: (context) => const DriverCheckInManagementScreen(),
            );
          case '/car-odometer-history':
            return MaterialPageRoute(
              builder: (context) => const CarOdometerHistoryScreen(),
            );
          case '/driver-trip-history':
            // Extract driverCode from arguments
            final tripHistoryArgs = settings.arguments as Map<String, dynamic>?;
            final tripHistoryDriverCode = tripHistoryArgs?['driverCode'] as String? ?? '';
            return MaterialPageRoute(
              builder: (context) => DriverTripHistoryScreen(
                driverCode: tripHistoryDriverCode,
              ),
            );
          // Driver location tracking screen removed - now using automatic tracking
          case '/logout':
            // Handle logout route
            return MaterialPageRoute(
              builder: (context) {
                // Perform logout
                WidgetsBinding.instance.addPostFrameCallback((_) async {
                  // Store the navigator state before async operations
                  final navigator = Navigator.of(context);

                  try {
                    print('Logout route: Starting logout process');

                    // Get the app state before async operations
                    final appState = Provider.of<MyAppState>(context, listen: false);

                    // First call the AuthService logout method to clear tokens
                    await AuthService.logout();
                    print('Logout route: AuthService.logout completed');

                    // Check if still mounted before using the app state
                    if (navigator.mounted) {
                      // Then clear user data in the app state
                      appState.logout();
                      print('Logout route: App state cleared');
                    }

                    // Double-check that all persisted data is cleared
                    final persistenceService = StatePersistenceService();
                    await persistenceService.clearAll();
                    print('Logout route: Additional clearAll completed');

                    // Verify data was cleared
                    final hasUserData = await persistenceService.hasUserData();
                    final hasToken = await persistenceService.loadAccessToken() != null;
                    print('Logout route: Verification - User data exists: $hasUserData, Token exists: $hasToken');

                    // Check if the widget is still mounted before navigating
                    if (navigator.mounted) {
                      print('Logout route: Navigating to login screen');
                      // Navigate to login screen
                      navigator.pushReplacementNamed('/');
                    }
                  } catch (e) {
                    print('Logout route: Error during logout: $e');
                    // Still try to navigate to login screen even if there was an error
                    if (navigator.mounted) {
                      navigator.pushReplacementNamed('/');
                    }
                  }
                });
                // Show a loading screen while logout is processing
                return const Scaffold(
                  body: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text('Logging out...'),
                      ],
                    ),
                  ),
                );
              },            );
          case '/login':
            return MaterialPageRoute(
              builder: (context) => LoginScreen(),
            );
          case '/auth/callback':
            // Handle SSO callback route with a simple loading screen
            return MaterialPageRoute(
              builder: (context) => Scaffold(
                body: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: const [
                      CircularProgressIndicator(),
                      SizedBox(height: 20),
                      Text('Processing authentication...'),
                    ],
                  ),
                ),
              ),
            );
          default:
            // For debugging purposes, log the unknown route but show a loading screen instead of an error
            print('Route not found: ${settings.name}');
            return MaterialPageRoute(
              builder: (context) => Scaffold(
                body: Center(                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [                    const CircularProgressIndicator(),
                      const SizedBox(height: 20),
                      Text(AppLocalizations.of(context).loading),
                    ],
                  ),
                ),              ),            );
        }
      },
        );
      },
    );
  }
}

// Custom scroll behavior for web to enable scrolling with mouse and touch
class WebScrollBehavior extends MaterialScrollBehavior {
  @override
  Set<PointerDeviceKind> get dragDevices => {
    PointerDeviceKind.touch,
    PointerDeviceKind.mouse,
    PointerDeviceKind.stylus,
    PointerDeviceKind.trackpad,
  };
  
  @override
  ScrollPhysics getScrollPhysics(BuildContext context) {
    return const ClampingScrollPhysics();
  }
  
  @override
  Widget buildScrollbar(BuildContext context, Widget child, ScrollableDetails details) {
    return Scrollbar(
      controller: details.controller,
      child: child,
    );
  }
}




