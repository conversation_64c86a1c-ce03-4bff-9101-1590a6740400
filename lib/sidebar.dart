import 'dart:math' as math;

import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'generated/l10n/app_localizations.dart';
import 'main.dart';

class SidebarNavigation extends StatelessWidget {
  const SidebarNavigation({super.key});
  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<MyAppState>(context);
    final user = appState.currentUser;
      // Check roles based on current active role for multi-role support
    final isAdmin = user?.isCurrentlyTripManager ?? false;
    final isSuperAdmin = user?.isSuperAdmin ?? false;
    final isDriver = user?.isCurrentlyDriver ?? false;

    // Responsive width calculation
    final screenWidth = MediaQuery.of(context).size.width;
    // Determine if we're on a small screen for adjusting UI elements
    final bool isSmallScreen = screenWidth < 600 && !kIsWeb;

    // Use a different width for mobile vs web
    // For very small screens, use a larger percentage of the screen
    final double drawerWidth = kIsWeb
        ? 300.0
        : isSmallScreen
            ? math.min(screenWidth * 0.85, 300.0)
            : math.min(screenWidth * 0.7, 300.0);

    return Drawer(
      width: drawerWidth,
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              color: Color(0xFF0D47A1),
            ),
            padding: EdgeInsets.symmetric(
              horizontal: 16,
              vertical: isSmallScreen ? 12 : 16
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // User avatar and name in a row for better space usage on mobile
                Row(
                  children: [
                    // User avatar
                    CircleAvatar(
                      backgroundColor: Colors.white,
                      radius: isSmallScreen ? 24 : 30,
                      child: Icon(
                        Icons.person,
                        size: isSmallScreen ? 32 : 40,
                        color: Color(0xFF0D47A1),
                      ),
                    ),
                    SizedBox(width: 12),
                    // User info in a column
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,                        children: [
                          // User name if available
                          if (appState.currentUser != null)
                            Text(
                              appState.currentUser!.name,
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: isSmallScreen ? 16 : 18,
                                fontWeight: FontWeight.bold,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          SizedBox(height: 4),                          // Dashboard title - show current active role
                          Text(
                            isSuperAdmin ? AppLocalizations.of(context).superAdmin :
                            isAdmin ? AppLocalizations.of(context).tripManager :
                            isDriver ? AppLocalizations.of(context).driver : 
                            AppLocalizations.of(context).requestor,
                            style: TextStyle(
                              color: Colors.white70,
                              fontSize: isSmallScreen ? 14 : 16,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),          ),          // Add a divider after the header
          const Divider(height: 1),
          
          // Apply mobile-friendly styling to all menu items
          // For all user types, make the menu items more compact on mobile
          if (isSuperAdmin) ...[
            ListTile(
              leading: const Icon(Icons.password),
              title: Text(AppLocalizations.of(context).resetPassword),
              dense: isSmallScreen, // More compact on mobile
              visualDensity: isSmallScreen ? VisualDensity.compact : VisualDensity.standard,
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/reset-password');
              },
            ),          ] else if (isAdmin) ...[
            ListTile(
              leading: const Icon(Icons.dashboard),
              title: Text(AppLocalizations.of(context).dashboard),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/trip-manager-dashboard');
              },
            ),
            ListTile(
              leading: const Icon(Icons.password),
              title: Text(AppLocalizations.of(context).resetPassword),
              dense: isSmallScreen, // More compact on mobile
              visualDensity: isSmallScreen ? VisualDensity.compact : VisualDensity.standard,
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/reset-password');
              },
            ),
          ],          if (isAdmin) ...[
            ExpansionTile(
              leading: const Icon(Icons.settings),
              title: Text(AppLocalizations.of(context).commonSettings),
              children: [
                ListTile(
                  leading: const Icon(Icons.person_outline),
                  title: Text(AppLocalizations.of(context).driverSetting),
                  contentPadding: const EdgeInsets.only(left: 72.0),
                  onTap: () {
                    Navigator.pop(context); // Close drawer first
                    Navigator.pushNamed(context, '/driver-settings');
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.directions_car_outlined),
                  title: Text(AppLocalizations.of(context).carSetting),
                  contentPadding: const EdgeInsets.only(left: 72.0),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/car-settings');
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.local_shipping_outlined),
                  title: Text(AppLocalizations.of(context).cargoSetting),
                  contentPadding: const EdgeInsets.only(left: 72.0),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/cargo-settings');
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.location_on_outlined),
                  title: Text(AppLocalizations.of(context).destinationSetting),
                  contentPadding: const EdgeInsets.only(left: 72.0),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/destination-settings');
                  },
                ),
              ],
            ),            ListTile(
              leading: const Icon(Icons.monitor),
              title: Text(AppLocalizations.of(context).tripMonitoring),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/trip-monitoring');
              },
            ),
            ListTile(
              leading: const Icon(Icons.location_on),
              title: Text(AppLocalizations.of(context).driverTracking),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/driver-tracking');
              },
            ),
            ListTile(
              leading: const Icon(Icons.approval),
              title: Text(AppLocalizations.of(context).tripChangeApproval),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/trip-approval');
              },
            ),
            ListTile(
              leading: const Icon(Icons.calendar_today),
              title: Text(AppLocalizations.of(context).driverAvailabilityManagement),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/availability');
              },
            ),
            ListTile(
              leading: const Icon(Icons.directions_car),
              title: Text(AppLocalizations.of(context).carAvailabilityManagement),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/car-availability');
              },
            ),
            ListTile(
              leading: const Icon(Icons.history),
              title: Text(AppLocalizations.of(context).tripHistory),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/trip-history');
              },
            ),
            ListTile(
              leading: const Icon(Icons.fact_check),
              title: Text(AppLocalizations.of(context).driverCheckInManagement),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/driver-check-in-management');
              },
            ),
            ListTile(
              leading: const Icon(Icons.speed),
              title: Text(AppLocalizations.of(context).carOdometerHistory),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/car-odometer-history');
              },
            ),          ] else if (isDriver) ...[  // Driver menu items
            ListTile(
              leading: const Icon(Icons.dashboard),
              title: Text(AppLocalizations.of(context).dashboard),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/driver-trips');
              },
            ),
            ListTile(
              leading: const Icon(Icons.location_on),
              title: Text(AppLocalizations.of(context).locationTracking),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/driver-location-tracking');
              },
            ),
            ListTile(
              leading: const Icon(Icons.fact_check),
              title: Text(AppLocalizations.of(context).driverCheckIn),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/driver-check-in');
              },
            ),          ] else if (!isSuperAdmin && !isAdmin && !isDriver) ...[  // Requestor menu items
            ListTile(
              leading: const Icon(Icons.dashboard),
              title: Text(AppLocalizations.of(context).dashboard),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/requestor-dashboard');
              },
            ),
            ListTile(
              leading: const Icon(Icons.add_circle),
              title: Text(AppLocalizations.of(context).requestTrip),
              onTap: () {
                Navigator.pop(context); // Close the drawer
                Navigator.pushNamed(context, '/request-trip');  // Fixed navigation call
              },
            ),
            ListTile(
              leading: const Icon(Icons.star),
              title: Text(AppLocalizations.of(context).tripRating),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/trip-rating');
              },
            ),
            ListTile(
              leading: const Icon(Icons.history),
              title: Text(AppLocalizations.of(context).myTripHistory),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/my-trip-history');
              },
            ),
          ],// Removed notification screen navigation
          const Divider(),
          ListTile(
            leading: const Icon(Icons.home),
            title: Text(AppLocalizations.of(context).backToHome),
            dense: isSmallScreen, // More compact on mobile
            visualDensity: isSmallScreen ? VisualDensity.compact : VisualDensity.standard,
            onTap: () {
              Navigator.pop(context);
              if (isAdmin) {
                Navigator.pushNamed(context, '/trip-manager-dashboard');              } else if (!isSuperAdmin && !isAdmin) {
                Navigator.pushNamed(context, '/requestor-dashboard');
              }
            },
          ),
          // Logout button at the bottom
          ListTile(
            leading: const Icon(Icons.logout, color: Colors.red),
            title: Text(AppLocalizations.of(context).logout, style: const TextStyle(color: Colors.red)),
            dense: isSmallScreen, // More compact on mobile
            visualDensity: isSmallScreen ? VisualDensity.compact : VisualDensity.standard,
            onTap: () {
              Navigator.pop(context);
              // Use the dedicated logout route which handles both local and server-side logout
              Navigator.of(context).pushReplacementNamed('/logout');
            },
          ),        ],
      ),    );
  }
}


