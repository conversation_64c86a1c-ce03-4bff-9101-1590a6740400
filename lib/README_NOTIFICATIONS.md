# FLEEX Notification System

This document describes the notification system implemented in the FLEEX application.

## Overview

The notification system provides real-time updates to users about important events in the application, such as:

- Trip assignments
- Trip status changes
- Trip approvals and rejections
- Trip ratings
- Driver rejections

## Architecture

### Database

The notification system uses a dedicated `notification` table in the PostgreSQL database with the following structure:

- `notification_id` (SERIAL PRIMARY KEY) - Unique identifier for the notification
- `user_id` (bigint) - Foreign key to the user table, identifying the recipient
- `title` (text) - Short title for the notification
- `message` (text) - Detailed message content
- `type` (text) - Category of notification (e.g., TRIP_ASSIGNED, TRIP_APPROVED)
- `is_read` (boolean) - Flag indicating whether the notification has been read
- `created_at` (timestamp) - When the notification was created
- `related_entity_id` (bigint) - Optional ID of the related entity (e.g., trip_id)
- `related_entity_type` (text) - Optional type of the related entity (e.g., TRIP)

### Backend

The backend provides several API endpoints for managing notifications:

- `GET /api/notifications` - Get all notifications for a user
- `GET /api/notifications/count` - Get count of unread notifications for a user
- `PUT /api/notifications/:id/read` - Mark a notification as read
- `PUT /api/notifications/read-all` - Mark all notifications as read for a user
- `DELETE /api/notifications/:id` - Delete a notification
- `POST /api/notifications/test` - Create a test notification (for development)

The backend also includes a utility module (`notification-utils.js`) with functions for creating notifications for different events:

- `notifyTripAssigned` - When a trip is assigned to a driver
- `notifyTripStatusChanged` - When a trip's status changes
- `notifyTripApproved` - When a trip is approved
- `notifyTripRejected` - When a trip is rejected
- `notifyTripRated` - When a trip is rated
- `notifyDriverRejected` - When a driver rejects a trip

### Frontend

The frontend includes several components for displaying and managing notifications:

- `NotificationIcon` - Icon in the app bar showing unread notification count
- `NotificationScreen` - Screen for viewing and managing notifications
- `NotificationService` - Service for fetching and managing notifications

## Notification Types

The system supports the following notification types:

- `TRIP_ASSIGNED` - Sent to drivers when they are assigned to a trip
- `TRIP_STATUS_CHANGED` - Sent to requestors and drivers when a trip's status changes
- `TRIP_APPROVED` - Sent to requestors when their trip is approved
- `TRIP_REJECTED` - Sent to requestors when their trip is rejected
- `TRIP_RATED` - Sent to drivers when a trip is rated
- `DRIVER_REJECTED` - Sent to requestors and trip managers when a driver rejects a trip
- `SYSTEM` - System notifications (general announcements, etc.)

## Integration Points

The notification system is integrated with the following features:

1. **Trip Approval/Rejection**: When a trip manager approves or rejects a trip, notifications are sent to the requestor.

2. **Trip Rating**: When a requestor rates a trip, a notification is sent to the driver.

3. **Trip Status Changes**: When a trip's status changes, notifications are sent to both the requestor and the assigned driver.

4. **Driver Assignment**: When a driver is assigned to a trip, a notification is sent to the driver.

5. **Driver Rejection**: When a driver rejects a trip, notifications are sent to the requestor and trip managers.

## Future Enhancements

Potential future enhancements for the notification system:

1. **Real-time notifications**: Implement WebSockets for real-time notification delivery.

2. **Push notifications**: Add support for mobile push notifications.

3. **Email notifications**: Send important notifications via email.

4. **Notification preferences**: Allow users to configure which notifications they want to receive.

5. **Notification grouping**: Group similar notifications to reduce clutter.

## Troubleshooting

If notifications are not appearing:

1. Check that the notification table exists in the database.
2. Verify that the notification service is properly initialized in the app.
3. Check the server logs for any errors related to notification creation.
4. Ensure the user ID is correctly passed to the notification endpoints.
5. Verify that the notification icon is properly displaying the unread count.
