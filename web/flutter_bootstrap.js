// Flutter web bootstrap script
// This script handles initialization and communication between web and mobile

// Wait for the DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
  console.log('Flutter bootstrap script loaded');
  
  // Create a global object for Flutter communication
  window.flutterBridge = {
    // Send message to Flutter
    sendToFlutter: function(event, data) {
      if (window.flutter_inappwebview) {
        // If running in a WebView, use the inappwebview channel
        window.flutter_inappwebview.callHandler('fromWeb', event, data);
      } else {
        // Otherwise, dispatch a custom event that Flutter can listen to
        window.dispatchEvent(new CustomEvent('fromWeb', {
          detail: {
            event: event,
            data: data
          }
        }));
      }
    },
    
    // Register a callback to receive messages from Flutter
    registerCallback: function(event, callback) {
      window.addEventListener('fromFlutter', function(e) {
        if (e.detail && e.detail.event === event) {
          callback(e.detail.data);
        }
      });
    }
  };
  
  // Handle deep links
  function handleDeepLink() {
    var url = window.location.href;
    
    // Check if we have a hash route
    if (window.location.hash) {
      console.log('Detected hash route:', window.location.hash);
      
      // Send the hash route to Flutter
      window.flutterBridge.sendToFlutter('deepLink', url);
      
      // If we're in a WebView, we might want to remove the hash to avoid duplicate navigation
      if (window.deviceInfo && window.deviceInfo.isWebView) {
        // Remove the hash but keep the rest of the URL
        var newUrl = window.location.href.split('#')[0];
        window.history.replaceState({}, document.title, newUrl);
      }
    }
    
    // Check for query parameters
    var urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('auth_token')) {
      console.log('Detected auth_token in URL');
      
      // Store the token in localStorage for Flutter to retrieve
      var token = urlParams.get('auth_token');
      localStorage.setItem('auth_token', token);
      
      // Remove the token from the URL for security
      urlParams.delete('auth_token');
      var newUrl = window.location.pathname;
      if (urlParams.toString()) {
        newUrl += '?' + urlParams.toString();
      }
      if (window.location.hash) {
        newUrl += window.location.hash;
      }
      window.history.replaceState({}, document.title, newUrl);
      
      // Notify Flutter about the token
      window.flutterBridge.sendToFlutter('authToken', token);
    }
  }
  
  // Handle deep links when the page loads
  handleDeepLink();
  
  // Also handle deep links when the hash changes
  window.addEventListener('hashchange', handleDeepLink);
  
  // Notify that bootstrap is complete
  console.log('Flutter bootstrap complete');
});
